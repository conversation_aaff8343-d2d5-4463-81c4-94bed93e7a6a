const development = require('./config-files/development.json');
const qa = require('./config-files/qa.json');
const staging = require('./config-files/staging.json');
const production = require('./config-files/production.json');

const envVariable = (phase) => {
  const ENVIRONMENT = 'production';

  const configs = {
    development,
    qa,
    staging,
    production,
  }[ENVIRONMENT];

  const env = {
    ENVIRONMENT,
    CONFIG: configs,
  };

  return env;
};

const moduleExports = {
  // your existing module.exports
  env: envVariable(),
};

module.exports = moduleExports;
