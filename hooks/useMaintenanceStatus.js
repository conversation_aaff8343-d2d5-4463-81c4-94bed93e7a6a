import { useContext } from 'react';
import useS<PERSON> from 'swr';
import ENDPOINTS from '../constants/endpoints.json';
import { UserContext } from '../pages/_app';

const useMaintenanceStatus = () => {
  const { user } = useContext(UserContext);

  // Fetcher function for SWR
  const fetcher = async () => {
    if (!user?.id) return null;

    const token = localStorage.getItem('token');
    const response = await fetch(ENDPOINTS.TEAL_DOT_STATUS, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({ userId: user.id }),
    });

    const data = await response.json();
    return data?.data;
  };

  const { data, error, mutate } = useSWR(
    // Only fetch if we have a user ID
    user?.id ? ['maintenanceStatus', user.id] : null,
    fetcher,
    {
      revalidateOnFocus: false,
      refreshInterval: 5 * 60 * 1000, // Refresh every 5 minutes
      dedupingInterval: 2000, // Dedupe requests within 2 seconds
    },
  );

  return {
    maintenanceStatus: data?.maintenanceStatus,
    tealDotStatus: data?.tealDotStatus,
    loading: !error && !data,
    error,
    refetchStatus: mutate,
  };
};

export default useMaintenanceStatus;
