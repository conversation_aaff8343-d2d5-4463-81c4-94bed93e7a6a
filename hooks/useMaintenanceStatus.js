import { useState, useEffect, useContext } from 'react';
import ENDPOINTS from '../constants/endpoints.json';
import { UserContext } from '../pages/_app';

const useMaintenanceStatus = () => {
  const [status, setStatus] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const { user } = useContext(UserContext);

  const fetchMaintenanceStatus = async () => {
    if (!user?.id) return;

    setLoading(true);
    setError(null);

    try {
      const token = localStorage.getItem('token');
      const response = await fetch(ENDPOINTS.TEAL_DOT_STATUS, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({ userId: user.id }),
      });

      const data = await response.json();
      setStatus(data?.data);
    } catch (error) {
      console.error("Maintenance Status Error:", error);
      setError(error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchMaintenanceStatus();
  }, [user?.id]);

  return {
    maintenanceStatus: status?.maintenanceStatus,
    tealDotStatus: status?.tealDotStatus,
    loading,
    error,
    refetchStatus: fetchMaintenanceStatus
  };
};

export default useMaintenanceStatus;
