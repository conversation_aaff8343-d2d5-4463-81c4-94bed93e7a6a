.str-chat,
.str-chat__theme-light {
	--str-chat__primary-color: #0a8089;
	/* --str-chat__attachment-margin: 0.125rem 0 */
}


/* Transforming the attachment max width variable */
.str-chat__attachment-list {
	--str-chat__attachment-max-width: 350px !important
}

.str-chat__channel-list .str-chat__load-more-button__button {
	background-color: #0a8089
}

.str-chat * {
	font-family: 'Ubuntu' !important;
}

.channel-preview__container {
	height: 63px;
	cursor: pointer;
	display: flex;
	align-items: center;
}

.avatar-channel-name-preview {
	height: 55px !important;
}

.str-chat__avatar.str-chat__avatar--circle.str-chat__message-sender-avatar {
	min-width: 38px !important;
	border-radius: 100% !important;
}

.str-chat .channel-preview__container:hover,
.group-participant-list-modal .channel-preview__container:hover {
	background: #ffffff;
	transition: background 0.1s ease-in-out;
}

.main-channel-list.channel-preview__container.selected {
	background: #f2f2f2;
	transition: background 0.1s ease-in-out;
}

.selected .str-chat__avatar .str-chat__avatar-fallback,
.group-info .group-members-list .str-chat__avatar-fallback,
.group-members-modal .group-members-list .str-chat__avatar-fallback {
	background: #ffffff !important;
}

.my-group-members .str-chat__avatar .str-chat__avatar-fallback {
	background: #ffffff !important;
}

.main-channel-list.channel-preview__container.hovered,
.avatar-channel-name-preview:hover {
	background: rgba(9, 128, 137, .1);
	transition: background 0.1s ease-in-out;
}

.str-chat .rta__autocomplete.str-chat__suggestion-list-container.str-chat__emojisearch {
	max-width: 100% !important;
	position: absolute !important;
	margin-bottom: 25px;
	overflow-y: scroll;
}

.channel-preview__content-wrapper {
	display: flex;
	flex-direction: column;
	justify-content: center;
}

.channel-preview__info-wrapper {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
}

.channel-preview__content-name {
	white-space: nowrap;
	overflow: hidden;
	display: block;
	justify-content: space-between;
	align-items: center;
	text-overflow: ellipsis !important;
}

.channel-preview__info-buttons {
	display: flex;
	justify-content: flex-end;
	width: 100%;
	align-items: center;
}

.channel-preview__content-message {
	overflow: hidden;
	display: inline !important;
	text-overflow: ellipsis !important;
	white-space: nowrap;
}

.channel-preview__avatars {
	display: flex;
	align-items: center;
	height: 40px;
	min-width: 40px;
	max-width: 40px;
	border-radius: 20px;
	overflow: hidden;
}

.str-chat .channel-preview__avatars.two span {
	width: 20px;
	overflow: hidden;
}

.channel-preview__avatars.three span {
	width: 20px;
	overflow: hidden;
}

.unread-message-counter {
	height: 19px !important;
	width: 19px !important;
	border-radius: 100% !important;
	font-weight: 400 !important;
	font-size: 10px !important;
	display: flex !important;
	align-items: center !important;
	justify-content: center !important;
	color: #F2F2F2 !important;
}

.str-chat__avatar .str-chat__avatar-fallback {
	background: #f2f2f2 !important;
	color: #098089 !important;
	font-weight: 500 !important;
	font-size: 25px !important;
}

.deleted-user-channel .str-chat__avatar .str-chat__avatar-fallback {
	color: #CCCCCC !important;
	font-weight: 500 !important;
	font-size: 25px !important;
}

.deleted-user-channel.channel-preview__container.hovered {
	background: rgba(9, 128, 137, .1);
	transition: background 0.1s ease-in-out;
}

.deleted-user-channel.channel-preview__container.selected {
	background: #f2f2f2;
	transition: background 0.1s ease-in-out;
}

/* Disabling the cursor-pointer property for Header */
.channel-header .str-chat__message-sender-avatar {
	cursor: auto;
}

.str-chat__avatar.str-chat__message-sender-avatar.str-chat__avatar--one-letter {
	height: 38px !important;
	width: 38px !important;
	display: flex !important;
	align-items: center !important;
	justify-content: center !important;
}

.message-list .str-chat__avatar .str-chat__avatar-fallback {
	background: #ffffff !important;
	font-size: 20px !important;
}

#forward-message .str-chat__avatar .str-chat__avatar-fallback {
	color: #f2f2f2 !important;
	background: #098089 !important;
	border-radius: 100%;
}

.str-chat__message-text-inner.str-chat__message-simple-text-inner {
	font-size: 14px;
}

.str-chat__message-text-inner p {
	word-break: break-word;
}

.str-chat__message.str-chat__message-simple.str-chat__message--regular.str-chat__message--received.str-chat__message--other.str-chat__message--has-text .str-chat__message-bubble {
	background-color: #ffffff;
	color: #333333;
}

.str-chat__message.str-chat__message-simple.str-chat__message--regular.str-chat__message--received.str-chat__message--me.str-chat__message-simple--me.str-chat__message--has-text .str-chat__message-bubble {
	background-color: #098089;
	color: #ffffff;
}

.str-chat.str-chat__theme-light.str-chat-channel-list.str-chat__channel-list.str-chat__channel-list-react,
.chat-left-menu {
	width: 35%;
	/* height: 100%; */
	max-height: 100%;
	-ms-overflow-style: none;
	/* Internet Explorer 10+ */
	scrollbar-width: none;
	/* Firefox */
	overflow-y: hidden;
	border-right: 1px solid #f2f2f2 !important
}

.str-chat.str-chat__theme-light.str-chat-channel-list.str-chat__channel-list.str-chat__channel-list-react::-webkit-scrollbar,
.chat-left-menu::-webkit-scrollbar {
	display: none;
	/* Safari and Chrome */
}

.str-chat.str-chat__theme-light.str-chat-channel.str-chat__channel {
	width: 74%;
}

.str-chat__container {
	height: 100%;
}

.str-chat__main-panel {
	height: 100%;
}

.str-chat__list-notifications {
	background-color: #f2f2f2 !important;
}

.str-chat__list,
.str-chat__empty-channel {
	background-color: #f2f2f2 !important;
	height: 90vh !important;
	padding-bottom: 20px !important;
}

.str-chat button.str-chat__channel-preview-messenger.str-chat__channel-preview.str-chat__channel-preview-messenger--active {
	background-color: #f2f2f2;
}

.str-chat button.str-chat__channel-preview-messenger.str-chat__channel-preview.str-chat__channel-preview-messenger--active {
	height: 72px !important;
}

.str-chat .channel-header {
	height: 58px !important;
	align-items: center !important;
	/* min-height: 54px; */
	padding: 0px 16px;
}

.sticky-header {
	position: -webkit-sticky !important;
	position: sticky !important;
	top: 0;
	z-index: 1;
}

.divider-t {
	border-top: 0.5px solid rgba(153, 153, 153, 0.5);
}

.divider-b {
	border-bottom: 0.5px solid rgba(153, 153, 153, 0.5);
}

.divider-light-t {
	border-top: 1px solid rgba(128, 128, 128, 0.15);
}

.newchat-friendlist {
	background: white;
}

.newchat-friendlist .search-input {
	position: -webkit-sticky !important;
	position: sticky !important;
	top: 57px;
	z-index: 1;
}

/* Commenting the below as rendering is not working fine */
/* .str-chat .newchat-friendlist {
	animation: appear-from-right 0.3s linear 50ms;
}

.str-chat .channel-list-paginator {
	animation: appear-from-left 0.3s linear 50ms;
} */

textarea.rta__textarea.str-chat__textarea__textarea.str-chat__message-textarea {
	width: 100%;
	background-color: #F2F2F2;
	padding: 12px 20px;
	/* height: 45px; */
	border-radius: 10px;
	resize: none;
	height: fit-content;
	overflow-y: auto;
	display: block;
	color: #333333
}

.str-chat__typing-indicator {
	display: none;
}

.str-chat__message-text-inner.str-chat__message-simple-text-inner p {
	white-space: pre-line;
}

@keyframes appear-from-left {
	0% {
		opacity: 0;
		margin-left: -50%;
	}

	100% {
		opacity: 1;
		margin-left: 0%;
	}
}

@keyframes appear-from-right {
	0% {
		opacity: 0;
		margin-left: 50%;
	}

	100% {
		opacity: 1;
		margin-left: 0%;
	}
}

.str-chat__channel-search-input--wrapper {
	border: 0;
	padding: 0;
}

.str-chat__channel-search-bar {
	padding: 0;
}

.str-chat__channel-search .str-chat__channel-search-bar {
	padding: 0;
	height: auto;
}

.str-chat__channel-search.str-chat__channel-search--inline {
	height: auto !important;
	min-height: auto;
}

.str-chat__channel-search .str-chat__channel-search-bar .str-chat__channel-search-input--wrapper {
	flex: auto;
	padding: 0;
}

.str-chat__channel-search-input--clear-button,
.str-chat__channel-search-input--icon {
	display: none !important;
}

.custom-chat-screen-layout {
	height: calc(100vh - 104px);
}

/* The below 2 stylings are for hiding the scrollbar in the friend listing chat component that has infinite scroll */
.infinite-scroll-component {
	-ms-overflow-style: none;
	/* Internet Explorer 10+ */
	scrollbar-width: none;
	/* Firefox */
	/* max-height: 50% !important; */
	/* max-height: 273px !important; */
}

.infinite-scroll-component::-webkit-scrollbar {
	display: none;
	/* Safari and Chrome */
}

/* For showing the message list in reverse direction */
.str-chat__list {
	display: flex;
	flex-direction: column-reverse;
}

.str-chat .str-chat__quoted-message-reply-to-message {
	display: none !important;
}

.str-chat__list-notifications {
	margin: 0 !important
}

.str-chat__attachment-preview-list {
	margin: 0;
	background: #f2f2f2;
	border-bottom-right-radius: 0;
	border-bottom-left-radius: 0;
}

.str-chat__attachment-preview-file {
	background-color: #ffffff !important;
	border: 0 !important;
	height: 70px !important;
}

.cus-input-container {
	width: 87% !important;
	min-width: 80% !important;
}

.flex.pr-sm.icon--wrapper.cursor-pointer {
	padding-right: 0px !important;
}

.with-attachment textarea.rta__textarea.str-chat__textarea__textarea.str-chat__message-textarea {
	border-top-left-radius: 0;
	border-top-right-radius: 0;
}

.str-chat__attachment-list-scroll-container {
	height: 90px;
	-ms-overflow-style: none;
	/* Internet Explorer 10+ */
	scrollbar-width: none;
	/* Firefox */
}

.str-chat__attachment-list-scroll-container::-webkit-scrollbar {
	display: none;
	/* Safari and Chrome */
}

.str-chat .str-chat__attachment-list-scroll-container .str-chat__attachment-preview-image {
	flex-shrink: 0;
}

.str-chat__attachment-preview-list {
	justify-content: flex-start;
	padding: 12px 14px 0px 14px;
	height: 92px;
}

.str-chat__attachment-preview-delete {
	width: 16px;
	height: 16px;
	top: -5px !important;
	right: -4px !important;
}

.str-chat__attachment-preview-delete svg {
	width: 16px !important;
	height: 16px !important;
	background-color: rgba(15, 24, 40, 0.64) !important;
}

.str-chat__attachment-preview-thumbnail {
	border-radius: 8px !important;
	height: 70px !important;
	width: 70px !important;
}

.str-chat__attachment-preview-list .str-chat__attachment-preview-image {
	height: 70px;
	width: 70px;
}

.str-chat .more-attachments-not-allowed:hover .tooltip-tip {
	display: block;
	z-index: 2;
}

.str-chat .more-attachments-not-allowed img {
	opacity: 0.6;
}

/* CSS for file attachments */
.str-chat__message-attachment-file--item {
	padding: 10px !important;
	border-radius: 12px !important;
	min-width: 300px !important;
}

.str-chat__message-attachment-file--item a {
	color: #333333
}

#other-file-attachment .str-chat__message-attachment-file--item {
	background: #F2F2F2 !important;
	max-width: 350px !important;
}

/* CSS for rounding the Avatar */
img.str-chat__avatar-image.str-chat__avatar-image--loaded {
	border-radius: 100% !important;
	object-fit: fill
}

.str-chat__message-url-link {
	margin-bottom: 10px;
}

.friend-list-empty-screen {
	height: calc(100vh - 270px);
}

.str-chat__message-attachment.str-chat__message-attachment--image {
	border-bottom-right-radius: 0;
}

.mobile-text-input {
	border-radius: 10px !important;
	width: 80% !important;
}

.mobile-text-input .str-chat__textarea.str-chat__message-textarea-react-host {
	width: 90% !important;
}

.str-chat__message-attachment {
	border-radius: 0.75em !important;
}

.has--no-text-message .message-timestamp {
	height: 20px;
	position: absolute;
	box-sizing: border-box;
	width: 100%;
	align-items: flex-end;
	justify-content: flex-end;
	display: flex;
	margin: 0;
	bottom: 0;
	right: 0;
	color: #ffffff !important;
}

.is-self-message.has--no-text-message .message-timestamp {
	border-bottom-left-radius: 0.75rem;
	background: linear-gradient(to top, rgb(11 20 26 / 37%), rgb(11 20 26 / 0%));
}

.not--my-message.has--no-text-message .message-timestamp {
	border-bottom-right-radius: 0.75rem;
	background: linear-gradient(to top, rgb(11 20 26 / 37%), rgb(11 20 26 / 0%));
}

.has--no-text-message .message-timestamp time {
	padding-right: 10px;
	padding-bottom: 6px;
}

.has--text-message .message-timestamp {
	margin: -5px 0 0px -2px;
	text-align: right;
	width: 100%;
}

.has--attachments.has--text-message .message-timestamp {
	margin: -5px 0 0px -9px;
	text-align: right;
	width: 100%;
}

.str-chat__message-attachment-file--item {
	border-radius: 0.75em !important;
}

.has--attachments .str-chat__message-text {
	margin-bottom: 8px;
}

/* CSS for changing emoji size in messages */
.str-chat span.inline-text-emoji {
	font-size: 22px;
}

/* Hiding the scrollbar of the message input */
.str-chat__message-textarea {
	-ms-overflow-style: none;
	/* Internet Explorer 10+ */
	scrollbar-width: none;
	/* Firefox */
	overflow-y: auto;
}

.str-chat__message-textarea::-webkit-scrollbar {
	display: none;
	/* Safari and Chrome */
}

/* CSS of links in messages */
a.str-chat__message-url-link:hover {
	text-decoration: underline;
}

.is-self-message a.str-chat__message-url-link {
	color: #81edff;
}

.not--my-message a.str-chat__message-url-link {
	color: #027eb5;
}

.message-options-wrapper {
	position: absolute;
	top: 0
}

.is-self-message .message-options-wrapper {
	left: -20px
}

.not--my-message .message-options-wrapper {
	right: -20px
}

.message-options-wrapper.bottom {
	top: 100%;
	/* Position below */
}

.message-options-wrapper.top {
	bottom: 100%;
	/* Position above */
}


.is-self-message .message-timestamp {
	--text-opacity: 1;
	color: #f2f2f2;
	color: rgba(242, 242, 242, var(--text-opacity));
}

.not--my-message .message-timestamp {
	--text-opacity: 1;
	color: #808080;
	color: rgba(128, 128, 128, var(--text-opacity));
}

.str-chat .custom-message-bubble {
	display: flex;
	position: relative;
	padding: 3px;
	border-radius: 0.75rem;
	min-width: 125px;
}

.not--my-message.custom-message-bubble {
	border-bottom-left-radius: 0;
}

.is-self-message.custom-message-bubble {
	border-bottom-right-radius: 0;
}

.custom-message-bubble.has--attachments {
	padding-top: 3px;
}

.custom-message-bubble.deleted--message {
	opacity: 0.75;
	font-style: italic;
	padding-bottom: 8px;
}

.custom-message-bubble.highlighted {
	background-color: #0a808a12 !important;
}

.custom-message-bubble.is-self-message {
	--bg-opacity: 1;
	background-color: #098089;
	background-color: rgba(9, 128, 137, var(--bg-opacity));
	--text-opacity: 1;
	color: #fff;
	color: rgba(255, 255, 255, var(--text-opacity));
}

.custom-message-bubble.not--my-message {
	--bg-opacity: 1;
	background-color: #fff;
	background-color: rgba(255, 255, 255, var(--bg-opacity));
	--text-opacity: 1;
	color: #333333;
	color: rgba(51, 51, 51, var(--text-opacity));
}

.cust-quoted-message .str-chat__message-text-inner p {
	padding-left: 3px;
	/* width: 89.5%; */
}

.quoted-message-parent,
.cust-link-header {
	margin: 0.125rem;
}

.cust-quoted-message.has--text-message .message-timestamp {
	margin: -0 0 0px -2px;
	text-align: right;
	width: 100%;
}

.cust-quoted-message.has--attachments.has--text-message .message-timestamp {
	margin: 0 0 0px -9px;
	text-align: right;
	width: 100%;
}

.channel-header .channel-options,
.icon--wrapper {
	justify-content: center;
	align-items: center;
	align-self: center;
}

.icon--wrapper {
	width: 32px;
	height: 32px;
	border-radius: 100%;
	display: flex;
}

.icon--wrapper:hover {
	background: #F2F2F2;
}

.str-chat__channel .str-chat__container .str-chat__main-panel {
	flex-shrink: 0.6;
}

.my-group-details {
	top: 0px;
	right: 0;
	z-index: 100 !important;
}

.str-chat .group-info .channel-preview__container,
.group-members-modal .channel-preview__container,
.my-group-details .channel-preview__container {
	padding-left: 10px;
	padding-right: 10px;
}

.my-tg-group .str-chat .group-info .channel-preview__container {
	padding-left: 0px;
	padding-right: 0px;
}

.my-group-details .channel-preview__container:hover {
	background-color: #ffffff;
}


.str-chat .group-info .group-info-avatar .str-chat__avatar-fallback,
.group-members-modal .group-info-avatar .str-chat__avatar-fallback {
	font-size: 70px !important;
}

.profile-info .str-chat__avatar-fallback {
	font-size: 70px !important;
}

.group-info .channel-preview__container:hover .str-chat__avatar-fallback,
.group-members-modal .channel-preview__container:hover .str-chat__avatar-fallback {
	background: #f2f2f2 !important;
}

.group-member-preview .options--button {
	margin-right: 10px;
}

.relative.options--button.self-center {
	margin-right: 10px;

}


.group-member-preview .options--button img {
	float: right;
}

.group-member-preview .group--admin {
	background-color: rgba(9, 128, 137, 0.1);
	color: #098089;
	border-radius: 4px;
}

.str-chat__message--system {
	color: #ffffff;
	background: rgba(51, 51, 51, 0.2);
	border-radius: 12px;
	width: fit-content;
	margin: 2px auto;
	padding: 0 10px;
}

.str-chat__message--system__text {
	padding: 1px 20px;
}

.slide-from-bottom {
	transform-origin: bottom;
	transition: all 0.6s ease;
}

.slide-up {
	transform: scale(100%, 100%);
	animation: smooth-appear 1s ease;
}

.slide-down {
	transform: scale(100%, 0%);
	animation: smooth-disappear 1s ease;
}

.slide-from-right {
	animation: slideFromRight 1s ease;
}

/* Adding css for Virtualized message List component */
.str-chat__virtual-list {
	background: #f2f2f2 !important;
}

.str-chat__virtual-list__loading {
	/* display: none !important; */
	/* position: fixed !important; */
	display: flex !important;
	flex-direction: column !important;
	z-index: 1000 !important;
	position: absolute !important;
	background-color: #f2f2f2 !important;
	width: "100%" !important;
}

@keyframes slideFromRight {
	0% {
		transform: translateX(100%);
	}

	100% {
		transform: translateX(0);
	}
}

.slide-from-left {
	animation: slideFromLeft 1s ease;
}

@keyframes slideFromLeft {
	0% {
		transform: translateX(-60%);
	}

	100% {
		transform: translateX(0%);
	}
}

.slide-left {
	transform: translateX(0px);
	animation: smooth-appear 1s ease;
}

.slide-right {
	transform: scale(100%, 0%);
	animation: smooth-disappear 1s ease;
}

.fade-in {
	animation: smooth-appear 1s ease;
}

@keyframes smooth-disappear {
	0% {
		opacity: 1;
	}

	50% {
		opacity: 0;
	}
}

@keyframes smooth-appear {
	0% {
		opacity: 0;
	}

	50% {
		opacity: 1;
	}
}

.str-chat__avatar .str-chat__avatar-fallback {
	box-shadow: var(--str-chat__avatar-box-shadow);
	border-block-start: var(--str-chat__avatar-border-block-start);
	border-block-end: var(--str-chat__avatar-border-block-end);
	border-inline-end: var(--str-chat__avatar-border-inline-end);
	text-transform: uppercase;
	object-fit: cover;
}

.str-chat__avatar.str-chat__avatar--circle.str-chat__message-sender-avatar .str-chat__avatar-fallback,
.str-chat__avatar.str-chat__avatar--circle.str-chat__message-sender-avatar .str-chat__avatar-image.str-chat__avatar-image--loaded {
	border-radius: 100%;
}

.str-chat__avatar.str-chat__avatar--rounded.str-chat__message-sender-avatar .str-chat__avatar-fallback,
.str-chat__avatar.str-chat__avatar--rounded.str-chat__message-sender-avatar .str-chat__avatar-image.str-chat__avatar-image--loaded {
	border-radius: 4px !important;
}

#forward-message {
	width: 100%;
}

.forward-message-list-wrapper {
	max-height: 450px;
	height: 450px;
}

.group-participants-wrapper-new-group .group-participants {
	max-height: calc(100vh - 270px);
}

.add-participant-list {
	height: 300px;
	max-height: 300px;
}

.group-participant-list-modal {
	overflow-y: auto;
	height: 400px;
	max-height: 400px;
}

.group-participant-list-modal .group-member-preview {
	padding: 0 25px;
}

.mt-20percent {
	margin-top: 20%;
}

.str-chat__message-sender-avatar {
	cursor: pointer;
	border-radius: 100% !important;
}

.no-search-results-wrapper {
	display: flex;
	justify-content: center;
	align-items: center;
}

.no-search-results-wrapper.friend-list {
	height: calc(100vh - 296px);
}

/* Disabling the cursor-pointer property for TG Public chat */
.tg-public-chat .str-chat__message-sender-avatar {
	cursor: auto;
}

.channel-search-list .str-chat__channel-search {
	height: 100% !important;
}

.str-chat__channel-search-result-list {
	display: block !important;
	height: 100% !important;
	overflow-y: scroll !important;
}

.mentioned {
	color: #66CDFF !important;
	cursor: pointer;
}

.deleted-mentioned {
	color: #f2f2f2 !important;
	font-style: italic;
	font-weight: 300;
	cursor: not-allowed;
}

.str-chat__message-text-inner.str-chat__message-simple-text-inner ol {
	list-style: auto;
	margin-left: 12px;
}

.request-chat textarea.rta__textarea.str-chat__textarea__textarea.str-chat__message-textarea {
	min-height: 32px !important;
	padding-left: 5px !important
}

.request-chat .str-chat__message-input .str-chat__message-input-inner .str-chat__message-textarea-container .str-chat__message-textarea-with-emoji-picker {
	padding: 5px 5px 5px 5px !important;
}

/* Chat Screens for Ipad */

@media screen and (max-width: 1024px) {

	.str-chat__attachment-preview-file {
		background-color: #f2f2f2 !important;
		max-width: 71px !important;
	}

	.str-chat__attachment-preview-file-end {
		display: none !important;
		width: 0px !important;
	}

	.str-chat__attachment-preview-list {
		background-color: white !important;
	}

	.str-chat.str-chat__theme-light.str-chat-channel-list.str-chat__channel-list.str-chat__channel-list-react,
	.chat-left-menu {
		width: 36%;
	}

	.str-chat.str-chat__theme-light.str-chat-channel.str-chat__channel {
		width: 100%;
	}

	.tablet-screen-no-details,
	.tablet-screen-details {
		width: 64% !important;
	}

	.tablet-screen-details .str-chat__channel .str-chat__container .str-chat__main-panel {
		width: 0px !important;
		display: none;
	}

	.custom-message-bubble.flex-col.not--my-message.no--attachments.no--url-message.has--text-message .str-chat__message-text {
		max-width: 400px !important;
	}

	.str-chat__message-text-inner.str-chat__message-simple-text-inner .code {
		max-width: 400px !important;
	}

	.custom-message-bubble.flex-col.not--my-message.no--attachments.no--url-message.has--text-message .p {
		word-break: break-all;
	}
}

@media screen and (max-width: 768px) {

	.str-chat.str-chat__theme-light.str-chat-channel-list.str-chat__channel-list.str-chat__channel-list-react,
	.chat-left-menu {
		width: 100%;
	}

	.str-chat.str-chat__theme-light.str-chat-channel.str-chat__channel {
		width: 100%;
	}

	.str-chat__attachment-preview-list {
		background: #ffffff
	}

	.mobile-screen-details .str-chat__channel .str-chat__container .str-chat__main-panel {
		width: 0px !important;
		display: none;
	}

	.str-chat__message-attachment.str-chat__message-attachment-dynamic-size.str-chat__message-attachment--image {
		max-width: 300px !important;
	}

	#other-file-attachment .str-chat__message-attachment-file--item {
		max-width: 300px !important;
	}

	.str-chat__message-attachment.str-chat__message-attachment-dynamic-size.str-chat__message-attachment--media.str-chat__message-attachment--video {
		max-width: 300px !important;
	}

	.str-chat__attachment-list {
		--str-chat__attachment-max-width: 300px !important
	}

	.str-chat__gallery.str-chat__gallery--square.str-chat__gallery-two-rows {
		max-width: 300px !important;
		width: 300px !important;
		min-width: 300px !important;
	}
}