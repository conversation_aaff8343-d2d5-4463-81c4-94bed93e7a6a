body {
    font-family: 'Ubuntu', sans-serif;
}

.tooltip {
    cursor: pointer;
    width: 13px;
    height: 13px;
    background-image: url('/svg/info.svg');
    background-size: cover;
}

.tooltip-tip {
    position: absolute;
    display: none;
    font-size: 12px;
    font-weight: 300;
    left: 0px;
    background-color: #f2f2f2;
    padding: 10px;
    bottom: calc(100% + 10px);
}

.tooltip:hover {
    background-image: url('/images/InfoFilled.png');
}

.tooltip:hover .tooltip-tip {
    display: block !important;
    z-index: 1000000;
}

.react-timerange-picker__wrapper {
    display: flex;
}

.react-timerange-picker__inputGroup input {
    width: 22px !important;
    color: black;
    -webkit-appearance: none !important;
}

.react-timerange-picker__inputGroup input::-webkit-outter-spin-button,
.react-timerange-picker__inputGroup input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

.react-timerange-picker__inputGroup select {
    color: black !important;
    -webkit-appearance: none;
    margin: 0;
    padding-left: 5px;
    padding-right: 5px;
}

span.react-time-picker__inputGroup__divider {
    background-color: white !important;
    color: black !important;
    height: 24px !important;
}

.react-timerange-picker__inputGroup {
    display: flex;
}

span.react-timerange-picker__inputGroup__leadingZero {
    background-color: white !important;
    color: black !important;
    height: 24px !important;
}

input.react-timerange-picker__inputGroup__input.react-timerange-picker__inputGroup__minute.react-timerange-picker__inputGroup__input--hasLeadingZero {
    padding-left: 0px;
    width: 10px !important;
}

input.react-timerange-picker__inputGroup__input.react-timerange-picker__inputGroup__hour {
    text-align: right;
}

.listicon-1 {
    fill: none;
    stroke: currentColor;
    stroke-linecap: round;
    stroke-miterlimit: 10;
    stroke-width: 2px;
}

.listicon-2 {
    fill: currentColor;
}

/* Chrome, Safari, Edge, Opera */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
    margin: 0 !important;
}

/* Firefox */
input[type='number'] {
    -moz-appearance: textfield !important;
}

@keyframes load {
    from {
        top: -150px;
    }

    to {
        top: 100%;
    }
}

.loading-list-item {
    content: '';
    display: block;
    position: absolute;
    left: 0px;
    right: 0px;
    top: -150px;
    height: 150px;
    width: 100%;
    background: linear-gradient(to bottom,
            transparent 0%,
            #f2f2f2 50%,
            transparent 100%);
    animation: load 1s cubic-bezier(0.4, 0, 0.2, 1) infinite;
}

textarea::-webkit-input-placeholder {
    /* Chrome/Opera/Safari */
    white-space: pre-line;
    position: relative;
    top: -7px;
}

textarea::-moz-placeholder {
    /* Firefox 19+ */
    white-space: pre-line;
    position: relative;
    top: -7px;
}

textarea:-ms-input-placeholder {
    /* IE 10+ */
    white-space: pre-line;
    position: relative;
    top: -7px;
}

textarea:-moz-placeholder {
    /* Firefox 18- */
    white-space: pre-line;
    position: relative;
    top: -7px;
}

.react-datepicker__input-container input {
    width: 100% !important;
}

.ql-editor {
    min-height: 280px !important;
    max-height: 280px !important;
    overflow: hidden;
    overflow-y: hidden;
    overflow-x: hidden;
    padding: 12px 0px !important;
}
.event-details .ql-editor {
    min-height: 100px !important;
    overflow: hidden;
    overflow-y: hidden;
    overflow-x: hidden;
    padding: 12px 0px !important;
}
.benefit-details .ql-editor {
    min-height: 100px !important;
    overflow: hidden;
    overflow-y: hidden;
    overflow-x: hidden;
    padding: 12px 0px !important;
}


.dragging {
    cursor: move !important
}

.rdrMonths .rdrMonth {
    display: block;
    margin: 0 auto;
}

/* Custom Tooltip - by @ayushi */
.tooltip-tip-custom {
    position: absolute;
    display: none;
    font-size: 12px;
    font-weight: 300;
    left: -180px;
    background-color: #f2f2f2;
    padding: 5px;
    bottom: calc(100% + 3px);
    width: 180px;
}

.tooltip-custom:hover .tooltip-tip-custom {
    display: block !important;
    z-index: 1000000;
}

.tooltip-tip-custom-chat {
    position: absolute;
    display: none;
    font-size: 12px;
    font-weight: 300;
    left: -180px;
    background-color: #f2f2f2;
    padding: 5px;
    bottom: calc(100% + -13px);
    width: 230px;
}

.tooltip-custom-chat:hover .tooltip-tip-custom-chat {
    display: block !important;
    z-index: 1000000;
}


/* React Date Picker Custom CSS -  by @ayushi */

.react-datepicker__day--disabled.react-datepicker__day--excluded {
    background-color: transparent !important;
    color: #ccc !important;
}

.react-datepicker__day--keyboard-selected,
.react-datepicker__quarter-text--keyboard-selected,
.react-datepicker__year-text--keyboard-selected {
    background-color: #2a87d000 !important;
    color: #333333 !important;
}

.react-datepicker__day--selected,
.react-datepicker__day--in-selecting-range,
.react-datepicker__day--in-range,
.react-datepicker__month-text--selected,
.react-datepicker__month-text--in-selecting-range,
.react-datepicker__month-text--in-range,
.react-datepicker__quarter-text--selected,
.react-datepicker__quarter-text--in-selecting-range,
.react-datepicker__quarter-text--in-range,
.react-datepicker__year-text--selected,
.react-datepicker__year-text--in-selecting-range,
.react-datepicker__year-text--in-range {
    background-color: #0a8088 !important;
}

.react-datepicker__day--selected:hover,
.react-datepicker__day--in-selecting-range:hover,
.react-datepicker__day--in-range:hover,
.react-datepicker__month-text--selected:hover,
.react-datepicker__month-text--in-selecting-range:hover,
.react-datepicker__month-text--in-range:hover,
.react-datepicker__quarter-text--selected:hover,
.react-datepicker__quarter-text--in-selecting-range:hover,
.react-datepicker__quarter-text--in-range:hover,
.react-datepicker__year-text--selected:hover,
.react-datepicker__year-text--in-selecting-range:hover,
.react-datepicker__year-text--in-range:hover,
.react-datepicker__day--keyboard-selected:hover,
.react-datepicker__month-text--keyboard-selected:hover,
.react-datepicker__day--selected,
.react-datepicker__day--in-selecting-range,
.react-datepicker__day--in-range,
.react-datepicker__month-text--in-selecting-range,
.react-datepicker__month-text--in-range,
.react-datepicker__quarter-text--selected,
.react-datepicker__quarter-text--in-selecting-range,
.react-datepicker__quarter-text--in-range,
.react-datepicker__year-text--selected,
.react-datepicker__year-text--in-selecting-range,
.react-datepicker__year-text--in-range {
    background-color: #199da7 !important;
}

.react-datepicker__day--in-selecting-range:not(.react-datepicker__day--in-range, .react-datepicker__month-text--in-range, .react-datepicker__quarter-text--in-range, .react-datepicker__year-text--in-range),
.react-datepicker__month-text--in-selecting-range:not(.react-datepicker__day--in-range, .react-datepicker__month-text--in-range, .react-datepicker__quarter-text--in-range, .react-datepicker__year-text--in-range),
.react-datepicker__quarter-text--in-selecting-range:not(.react-datepicker__day--in-range, .react-datepicker__month-text--in-range, .react-datepicker__quarter-text--in-range, .react-datepicker__year-text--in-range),
.react-datepicker__year-text--in-selecting-range:not(.react-datepicker__day--in-range, .react-datepicker__month-text--in-range, .react-datepicker__quarter-text--in-range, .react-datepicker__year-text--in-range) {
    background-color: #0980896e;
}

.react-datepicker {
    border: none !important;
    width: 100%;
    margin-bottom: 0;
    z-index: 999999;
    font-family: 'Ubuntu' !important;
}

.request-against-offer .react-datepicker__header {
    left: 45px !important;
}

.react-datepicker .react-datepicker__month-container {
    width: 50%;
    z-index: 999999;
}

.react-datepicker .react-datepicker__month {
    min-height: 191px;
    z-index: 999999;
}

.react-datepicker .react-datepicker__header {
    border: none;
    background-color: #f0f0f08f;
}

.react-datepicker__header {
    width: fit-content;
}

.react-datepicker .react-datepicker__navigation {
    top: 9px
}

.react-datepicker button:focus {
    outline: none !important;
}

.react-datepicker__day-name,
.react-datepicker__day,
.react-datepicker__time-name {
    width: 2.2rem !important;
    margin: 0.286rem !important
}

.react-datepicker__day {
    border-radius: 1.042em !important;
}

.react-datepicker__day--keyboard-selected.react-datepicker__day--in-range,
.react-datepicker__day--in-selecting-range.react-datepicker__day--selecting-range-start {
    color: #ffffff !important
}

.lowerVisibility:nth-child(odd) {
    margin-left: 8px;
}

/* .react-datepicker__day--disabled, .react-datepicker__month-text--disabled, .react-datepicker__quarter-text--disabled, 
.react-datepicker__year-text--disabled {
    background-color: transparent !important;
} */

/* .react-datepicker__day--keyboard-selected, .react-datepicker__month-text--keyboard-selected, 
.react-datepicker__day--keyboard-selected:hover, .react-datepicker__month-text--keyboard-selected:hover, 
.react-datepicker__quarter-text--keyboard-selected:hover, .react-datepicker__year-text--keyboard-selected:hover {
    color: #ffffff !important;
    
} */

.ql-container p {
    font-family: Ubuntu;
}

.membership-logs tbody {
    display: contents;
    overflow: auto;
    height: 600px;
    /* width: 100%; */
}

.membership-logs thead {
    display: table-caption;
}

.membership-logs thead tr {
    display: block;
}

.image-upload-area {
    background: linear-gradient(0deg, rgba(51, 51, 51, 0.1), rgba(51, 51, 51, 0.1)), linear-gradient(0deg, rgba(203, 222, 223, 0.3), rgba(203, 222, 223, 0.3));
    border: 2px solid #3333331A;
    border-style: dashed;
    border-radius: 8px;
}

.custom-bullets {
    list-style-type: disc;
    /* Options: disc, circle, square, etc. */
    margin-left: 20px;
    /* Adds space for indentation */
}

.custom-bullets li {
    margin-bottom: 8px;
    /* Adds spacing between the list items */
}

/* SWIPER */
/* /get-started */

.get-started .swiper {
    width: 100%;
    height: 100%;
    background-color: transparent !important;
}

.get-started.swiper.swiper-initialized.swiper-horizontal.mySwiper.swiper-backface-hidden {
    width: 100 !important;
    height: 100% !important;
    background-color: transparent !important;
}

.get-started .swiper-wrapper {
    background-color: transparent !important;
    height: 75%;
}

.get-started .swiper-wrapper div {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
}

.get-started.swiper-slide {
    text-align: center;
    font-size: 18px;
    /* Center slide text vertically */
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    transition: none !important;
}

.get-started .custom-shadow {
    box-shadow: 4px 2px 18px 7px rgb(0 0 0 / 10%);
}

.get-started .swiper-slide.swiper-slide-active {
    padding: 20px;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
}

.get-started span.swiper-pagination-bullet {
    height: 6px !important;
    width: 20px !important;
    background-color: #098089;
    border-radius: 6px !important;
}

.get-started .swiper-pagination.swiper-pagination-clickable.swiper-pagination-bullets.swiper-pagination-horizontal {
    top: 75% !important;
}

/* SWIPER- PRICING PAGE */
.pricing .swiper {
    width: 100%;
    height: 100% !important;
    background-color: transparent !important;
}

.pricing .swiper-wrapper {
    background-color: transparent !important;
    height: 75%;
}

.pricing.swiper-slide {
    text-align: center;
    font-size: 18px;
    /* Center slide text vertically */
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
}

.pricing .custom-shadow {
    box-shadow: 4px 2px 18px 7px rgb(0 0 0 / 10%);
}

.pricing .swiper-slide.swiper-slide-active {
    padding: 20px;
    width: 60% !important;
}

.pricing span.swiper-pagination-bullet {
    height: 4px !important;
    width: 4px !important;
    background-color: #000000 !important;
    opacity: 0.2 !important;
    border-radius: 100% !important;
}

.pricing span.swiper-pagination-bullet.swiper-pagination-bullet-active {
    height: 4px !important;
    width: 10px !important;
    background-color: #098089 !important;
    opacity: 0.2 !important;
    border-radius: 4px !important;
}

/* SWIPER- LANDING PAGE */
.landing-page-carousel .swiper {
    width: 90% !important;
    border-radius: 10px;
}

/* SWIPER- LANDING PAGE */
.landing-page .swiper {
    height: 100% !important;
    background-color: transparent !important;
}

.landing-page .swiper-wrapper {
    background-color: transparent !important;
    height: 75%;
    /* display: flex !important;
    flex-direction: row !important;
    align-items: center !important;
    justify-content: center !important; */
}

.landing-page .swiper-slide {
    text-align: center;
    font-size: 18px;
    /* Center slide text vertically */
    /* display: flex !important;
    justify-content: center !important;
    align-items: center !important; */
}

.landing-page .custom-shadow {
    box-shadow: 4px 2px 18px 7px rgb(0 0 0 / 10%);
}

.landing-page .swiper-slide.swiper-slide-active {}

.landing-page .swiper-slide.swiper-slide-next {
    scale: 0.7 !important;
}

.landing-page .swiper-slide.swiper-slide-prev {
    scale: 0.7 !important;
}

.landing-page span.swiper-pagination-bullet {
    height: 6px !important;
    width: 9px !important;
    background-color: #000000 !important;
    opacity: 0.2 !important;
    border-radius: 6px !important;
}

.landing-page span.swiper-pagination-bullet.swiper-pagination-bullet-active {
    height: 6px !important;
    width: 9px !important;
    background-color: #098089 !important;
    opacity: 1 !important;
    border-radius: 6px !important;
}

.rotate-active {
    animation: rotateIn 0.3s ease-in-out forwards;
}

.rotate-inactive {
    animation: rotateOut 0.3s ease-in-out forwards;
}

.react-datepicker-wrapper {
    width: 100% !important;
}

.react-datepicker-popper {
    z-index: 100000000 !important;
}

@keyframes rotateIn {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(45deg);
    }
}

@keyframes rotateOut {
    from {
        transform: rotate(45deg);
    }

    to {
        transform: rotate(0deg);
    }
}


/* Media Queries */

@media screen and (min-width: 360px) {

    .landing-page span.swiper-pagination-bullet.swiper-pagination-bullet-active {
        height: 6px !important;
        width: 30px !important;
        background-color: #098089 !important;
        opacity: 1 !important;
        border-radius: 6px !important;
    }

    .landing-page span.swiper-pagination-bullet {
        height: 6px !important;
        width: 30px !important;
        background-color: #000000 !important;
        opacity: 0.2 !important;
        border-radius: 6px !important;
    }
}

@media screen and (min-width: 1440px) {
    .landing-page span.swiper-pagination-bullet.swiper-pagination-bullet-active {
        height: 6px !important;
        width: 9px !important;
        background-color: #098089 !important;
        opacity: 1 !important;
        border-radius: 6px !important;
    }

    .landing-page span.swiper-pagination-bullet {
        height: 6px !important;
        width: 9px !important;
        background-color: #000000 !important;
        opacity: 0.2 !important;
        border-radius: 6px !important;
    }
}

@media screen and (max-width: 1024px) {
    .landing-page span.swiper-pagination-bullet.swiper-pagination-bullet-active {
        height: 6px !important;
        width: 9px !important;
        background-color: #098089 !important;
        opacity: 1 !important;
        border-radius: 6px !important;
    }

    .landing-page span.swiper-pagination-bullet {
        height: 6px !important;
        width: 9px !important;
        background-color: #000000 !important;
        opacity: 0.2 !important;
        border-radius: 6px !important;
    }
}

@media screen and (max-width: 768px) {
    .landing-page span.swiper-pagination-bullet.swiper-pagination-bullet-active {
        height: 6px !important;
        width: 30px !important;
        background-color: #098089 !important;
        opacity: 1 !important;
        border-radius: 6px !important;
    }

    .landing-page span.swiper-pagination-bullet {
        height: 6px !important;
        width: 30px !important;
        background-color: #000000 !important;
        opacity: 0.2 !important;
        border-radius: 6px !important;
    }
}

@media screen and (max-width: 425px) {
    .landing-page span.swiper-pagination-bullet.swiper-pagination-bullet-active {
        height: 6px !important;
        width: 30px !important;
        background-color: #098089 !important;
        opacity: 1 !important;
        border-radius: 6px !important;
    }

    .landing-page span.swiper-pagination-bullet {
        height: 6px !important;
        width: 30px !important;
        background-color: #000000 !important;
        opacity: 0.2 !important;
        border-radius: 6px !important;
    }
}

@media screen and (max-width: 375px) {
    .landing-page span.swiper-pagination-bullet.swiper-pagination-bullet-active {
        height: 6px !important;
        width: 30px !important;
        background-color: #098089 !important;
        opacity: 1 !important;
        border-radius: 6px !important;
    }

    .landing-page span.swiper-pagination-bullet {
        height: 6px !important;
        width: 30px !important;
        background-color: #000000 !important;
        opacity: 0.2 !important;
        border-radius: 6px !important;
    }
}

@media screen and (max-width: 320px) {
    .landing-page span.swiper-pagination-bullet.swiper-pagination-bullet-active {
        height: 6px !important;
        width: 30px !important;
        background-color: #098089 !important;
        opacity: 1 !important;
        border-radius: 6px !important;
    }

    .landing-page span.swiper-pagination-bullet {
        height: 6px !important;
        width: 30px !important;
        background-color: #000000 !important;
        opacity: 0.2 !important;
        border-radius: 6px !important;
    }
}

.getStarted--slider .custom-shadow {
    box-shadow: 4px 2px 18px 7px rgb(0 0 0 / 10%);
}


/* CSS for Welcome screen for Existing Users */

.ql-container p {
    font-family: Ubuntu;
}

.mobile-datepicker .react-datepicker .react-datepicker__month-container {
    width: 100%;
}

.landing-page-faq div#card {
    font-size: 20px;
    font-weight: 500;
}

.landing-page-faq .ql-container p {
    margin-left: 0px !important;
    font-size: 18px;
    font-weight: 400;
}

.help div#card {
    font-size: 16px;
    font-weight: 700;
}

.help .ql-container p {
    margin-left: 0px !important;
    font-weight: 400;
}



* {
    -webkit-tap-highlight-color: transparent;
}

div#__next {
    position: relative;
}

div#__next {
    position: relative;
}

:focus {
    outline: none !important;
}

textarea::-webkit-input-placeholder {
    padding-top: 8px;
}

textarea::-moz-placeholder {
    padding-top: 8px;
}

/* textarea::-ms-input-placeholder {
    padding-top: 8px;
    color: #666666;
} */
#my_friends input::placeholder {
    color: #666666;
}

.str-chat__avatar-fallback {
    background-color: #098089 !important;
}

/* @media screen and (-webkit-min-device-pixel-ratio:0) {
    select,
    textarea,
    input {
        font-size: 16px !important;
    }
} */

/* Scrolling will be done but scroll bad will not be visible */
::-webkit-scrollbar {
    display: none;
}

.pegboard-toast button.Toastify__close-button.Toastify__close-button--light {
    margin-top: 15px !important;
}

.pegboard-toast.Toastify__toast {
    border-radius: 8px !important;
    width: 325px !important;
}

.pegboard-toast .Toastify__progress-bar.Toastify__progress-bar--animated.Toastify__progress-bar-theme--light.Toastify__progress-bar--default {
    background: #0980891A !important;
}
.request-toast button.Toastify__close-button.Toastify__close-button--light {
    margin-top: 15px !important;
}

.request-toast.Toastify__toast {
    border-radius: 16px !important;
    width: 325px !important;
    background: #E2F6EC !important;
    border: 1px solid #BEE2D0 !important;
}

.request-toast .Toastify__progress-bar.Toastify__progress-bar--animated.Toastify__progress-bar-theme--light.Toastify__progress-bar--default {
    background: #FFFFFF !important;
}

/* CSS for adding custom toast notification */
.custom-toast button.Toastify__close-button.Toastify__close-button--light {
    margin-top: 15px !important;
}

.custom-toast.Toastify__toast {
    border-radius: 8px !important;
}

.custom-toast .Toastify__progress-bar.Toastify__progress-bar--animated.Toastify__progress-bar-theme--light.Toastify__progress-bar--default {
    background: #098089 !important;
}

.lightest-gray-border {
    border-color: #f2f2f2 !important;
    border-width: 1px;
}

/* For inputs to not zoom */
@supports (-webkit-touch-callout: none) {

    select,
    textarea,
    input {
        font-size: 16px !important;
    }
}

@media screen and (max-width: 1440px) {}

@media screen and (max-width: 1024px) {}

@media screen and (max-width: 768px) {}

@media screen and (max-width: 425px) {}

@media screen and (max-width: 375px) {}

@media screen and (min-width: 360px) {}

@media screen and (max-width: 320px) {}

.after-elm {
    position: relative;
}

.after-elm:after {
    content: '';
    position: absolute;
    width: calc(100% - 20px);
    height: 1px;
    left: 50%;
    transform: translateX(-50%);
    bottom: 0;
    background: rgba(128, 128, 128, 0.15);
}

.map-window:hover {
    background: rgba(9, 128, 137, .1);
    transition: background 0.1s ease-in-out;
}

.multi-member-avatar {
    box-shadow: 0px 1px 2px 0px #00000033;
}

.word-break {
    word-break: break-word;
}

.my-tg-group-image-col {
    flex: 0 0 17%
}

.str-chat__message-text-inner.str-chat__message-simple-text-inner pre {
    white-space: pre-wrap !important;
}

.str-chat__gallery-image {
    object-fit: cover;
    width: 100%;
    height: 100%;
}

.img-container span {
    height: 100%;
}

.ql-toolbar.ql-snow {
    border-radius: 8px 8px 0px 0px;
}

.ql-container.ql-snow {
    border-radius: 0px 0px 8px 8px;
}

.my-tg-group-toggle-active {
    background-color: #098089;
    color: #ffffff;
    font-size: 12px;
    border-radius: 8px;
    padding: 4px 16px;
    height: 22px;
    display: flex;
    justify-content: center;
    align-items: center;

}