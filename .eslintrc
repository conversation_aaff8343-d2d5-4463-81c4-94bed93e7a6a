{
  "env": {
    "node": true,
    "es2021": true,
    "browser": true,
    "jest": true
  },
  "parserOptions": {
    "ecmaVersion": 2021,
    "sourceType": "module"
  },
  "extends": [
    "plugin:react/recommended",
    "prettier"
  ],
  "plugins": [
    "react",
    "jest",
    "prettier"
  ],
  "rules": {
    "no-undef": "error",
    "no-console": "off",
    "no-unused-vars": "off",
    "react/react-in-jsx-scope": "off",
    "react/prop-types": "off",
    "react/no-unescaped-entities": "off", 
    "react/jsx-no-target-blank": "off",
    "react/display-name": "off",
  }
} 