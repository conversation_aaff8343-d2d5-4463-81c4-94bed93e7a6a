module.exports = {
  content: [
    './public/**/*.html',
    './{components,pages}/**/*.{js,jsx,ts,tsx,vue}',
    // "./{components,pages}/**/*.{js,ts,jsx,tsx}"
  ],
  theme: {
    screens: {
      xxxs: '320px',
      xxs: '375px',
      xs: '414px',
      sm: '640px',
      md: '769px',
      lg: '1025px',
      xl: '1280px',
      xxl: '1440px',
      xxxl: '1920px',
    },
    letterSpacing: {
      tightest: '-.075em',
      tighter: '-.05em',
      tight: '-.025em',
      normal: '0',
      wide: '.025em',
      wider: '.05em',
      widest: '.1em',
      widest: '.25em',
    },
    extend: {
      inset: {
        '10': '10px', // Add this line to define left: 10px
      },
      backgroundColor: {
        'custom-green': '#0980891F',
      },
      fontFamily: {
        ubuntu: 'Ubuntu',
        roboto: '<PERSON>o Slab',
      },
      margin: {
        xs: '4px',
        "6px": '6px',
        sm: '8px',
        "10": '10px',
        "12": '12px',
        md: '16px',
        "18px": '18px',
        "20px": '20px',
        lg: '24px',
        "21": '21px',
        "22": '22px',
        "25px": '25px',
        "26": '26px',
        "28px": '28px',
        '30px': '30px',
        '32px': '32px',
        "36": '36px',
        xl: '40px',
        "46": '46px',
        xxl: '48px',
        '55': '55px',
        '60': '60px',
        '80': '80px',
        '120': '120px',
        '150': '150px',
        '200': '200px',
        '16px': '16px',
        '40%': '40%',
        '50%': '50%',
        '75%': '75%',
        'm-7px': '-7px',
        '75px': '75px',
        '223px': '223px',
        xxxl: '58px',
      },
      padding: {
        '3px': '3px',
        xs: '4px',
        '6px': '6px',
        '7px': '7px',
        sm: '8px',
        '10px': '10px',
        '12': '12px',
        '13px': '13px',
        md: '16px',
        '18px': '18px',
        lm: '20px',
        '22px': '22px',
        lg: '24px',
        '25px': '25px',
        '28px': '28px',
        '30px': '30px',
        '32': '32px',
        xl: '40px',
        "46": '46px',
        xxl: '48px',
        '55': '55px',
        '56px': '56px',
        '60px': '60px',
        '58': '58px',
        '83': '83px',
        '100': '100px',
        '125': '125px',
        '149px': '149px',
        '160': '160px',
        '200': '200px',
        '250': '250px',
        '420': '420px'
      },
      fontSize: {
        9: ['9px', '10px'],
        10: ['10px', '13px'],
        11: ['11px', '15px'],
        12: ['12px', '18px'],
        13: ['13px'],
        14: ['14px', '20px'],
        16: ['16px', '24px'],
        18: ['18px', '26px'],
        20: ['20px', '28px'],
        22: ['22px', '30px'],
        21: ['21px', '30px'],
        24: ['24px', '32px'],
        26: ['26px', '32px'],
        28: ['28px', '32px'],
        30: ['30px', '34px'],
        32: ['32px', '36px'],
        34: ['34px', '37px'],
        35: ['35px', '37px'],
        36: ['36px', '38px'],
        38: ['38px', '46px'],
        40: ['40px', '48px'],
        46: ['46px', '58px'],
        48: ['48px', '60px'],
        64: ['64px', '74px'],
      },

      colors: {
        black: '#333333',
        gray: '#808080',
        offwhite: '#f6f6f6',
        offwhite2: '#00000017',
        offwhite3: '#F8F8F4',
        offwhite4: '#E2E2E2',
        offwhite5: '#D9D9D9',
        lightestgray: '#f2f2f2',
        lightergray: '#999999',
        hardgray: '#C4C4C4',
        grayLight: '#666666',
        newBackgroundgray: '#E5E5E5',
        offgray: '#DADADA',
        mediumgray: '#f9f9f9',
        darkgray: '#4A4A4A',
        lightgray: '#CCCCCC',
        placeholder: '#9499A2',
        filterbg: '#EBEBEB',
        dividerGray: 'rgba(153, 153, 153, 0.5)',
        lightteal: '#0AB4C0',
        teal: '#0797A4',
        mediumteal: '#0A808A',
        darkteal: '#098089',
        offteal: '#85DFE7',
        darkestteal: '#096269',
        teal20: 'rgba(9, 128, 137, 0.2)',
        red: '#E05E52',
        lightYellow: '#F1CB001F',
        lightRed: '#E05E521F',
        melon: '#FFB0AD',
        error: '#FF0000',
        buttonRed: '#e05e52',
        green: '#2E9360',
        darkgreen: '#06924B',
        lightestteal: '#0a808a12',
        lightestteal2: '#D8EEF0',
        stripeError: '#df1b41',
        tealTier: '#00828C',
        tealTierBg: '#0980891A',
        lightYello: 'F1CB001A',
        'teal-20': '#00808033',
        blackOverlay: 'rgba(0,0,0,0.5)',
        lighttealbackground2: '#0980891F',
        lighttealbackground: '#00828c0a',
        hover: '#0980891A',
        neonteal: '#67FFEC',
        lightGreen: '#0980891F',
        lightRed: '#E05E521A',
      },
      placeholderColor: {
        primary: '#9499A2',
      },
      width: {
        '1/24': '4.166665%',
        '3/24': '12.499995%',
        '5/24': '20.833325%',
        '6/24': '25%',
        '7/24': '29.166655%',
        '8/24': '30%',
        '9/24': '35%',
        '10/24': '41.66%',
        '11/24': '45.833315%',
        '13/24': '54.166645%',
        '14/24': '58.333%',
        '15/24': '62.5%',
        '16/24': '66.6%',
        '17/24': '70.833305',
        '19/24': '79.166635',
        '23/24': '91.66663',
        '14px': '14px',
        '30px': '30px',
        '48px': '48px',
        '80px': '80px',
        '25px': '25px',
        '240px': '240px',
        '250px': '250px',
        '289px': '289px',
        '405px': '405px',
        '99px': '99px',
        '147px': '147px',
        '595px': '595px',
        '489px': '489px',
      },
      height: {
        '1/10': '10%',
        '1/5': '20%',
        '3/10': '30%',
        '4/10': '40%',
        '1/2': '50%',
        '6/10': '60%',
        '7/10': '70%',
        '8/10': '80%',
        '9/10': '90%',
        'channel-preview': '72px',
        'search-input': '40px',
        'channel-list-header': '57px',
        'fit-content': 'fit-content',
        '80px': '80px',
        inherit: 'inherit',
        '63px': '63px',
        '50px': '50px',
        28: '28px',
        '40px': '40px',
        '525px': '525px',
      },
      zIndex: {
        '-10': '-10',
      },
      border: {
        red: '#E05E52',
      },
      borderRadius: {
        '7px': '7px',
        '16px': '16px',
      },
      flexGrow: {
        2: 2,
        0.7: 0.7,
        1: 1,
      },
      maxWidth: {
        '1/24': '4.166665%',
        '3/24': '12.499995%',
        '5/24': '20.833325%',
        '6/24': '25%',
        '7/24': '29.166655%',
        '8/24': '30%',
        '9/24': '35%',
        '10/24': '41.66%',
        '11/24': '45.833315%',
        '13/24': '54.166645%',
        '14/24': '58.333%',
        '15/24': '62.5%',
        '16/24': '66.6%',
        '17/24': '70.833305',
        '19/24': '79.166635',
        '23/24': '91.66663',
        317: '317px',
      },
      maxHeight: {
        '260px': '260px',
      },
      minHeight: {
        '260px': '260px',
      },
      keyframes: {
        pulse: {
          '0%': {
            opacity: 0,
          },
          '100%': {
            opacity: 1,
          },
          '50%': {
            opacity: 0.5,
          },
        },
      },
      animation: {
        'animate-pulse': `animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite`,
      },
      zIndex: {
        3: '3',
      },
    },
  },
  variants: {
    visibility: ['responsive', 'hover', 'focus'],
    display: ['responsive', 'hover', 'focus'],
    letterSpacing: ['hover'],
    zIndex: ['hover'],
    cursor: ['hover'],
  },
  plugins: [],
};
