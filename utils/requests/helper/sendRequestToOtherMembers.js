import moment from 'moment'
import constantOptions from '../../../constants/constantOptions'
import EMAILS from '../../../constants/emails'
import MESSAGES from '../../../constants/messages'
import adminClient from '../../../graphql/adminClient'
import { CREATE_NOTIFICATION } from '../../../graphql/mutations/notification'
import { getTier } from '../../tiers'
import pgPoolQuery from '../../db/pgQuery'
import requestsCompletedByUser from '../requestsCompletedByUser'
import { createCustomTokenWithExpiration } from '../../auth/firebaseAdmin'
import createDynamicLink from '../../notifications/createDynamicLink'
import getClubMembersForUser from '../../clubs/helper/getClubMembersForUser'
import checkFriend from '../../friends/checkFriend'
import dateFormatter from '../../helper/dateFormatter'

const FETCH_REQUEST = `query getRequest($reqId: uuid!) {
    request_by_pk(id: $reqId) {
      club_id
      eligible_hosts
      hosts_sent
      message
      number_of_players
      criteria
      start_date
      end_date
      user_id
      game_id
      status
      club {
        name
      }
      user {
      id
      tier
      clubs {
          club {
          name
          }
      }
      age
      gender
      handicap
      pace
      email
      }
      game {
        game_id
      }
    }
  }`

const {
    subject: hostSubject,
    template: hostTemplate,
    message: hostMessage,
    text_message: hostTextMessage,
} = EMAILS.SEND_REQUEST.HOSTS

/**
 * Function to send request to the pending club members
 * @param {*} param0
 * @returns
 */
const sendRequestToOtherMembers = async ({ requestId = '' }) => {
    try {
        const { request_by_pk } = await adminClient.request(FETCH_REQUEST, {
            reqId: requestId,
        })

        if (!request_by_pk) {
            return { status: 0, message: 'Invalid Request id.' }
        }

        const {
            club_id,
            criteria,
            user_id,
            eligible_hosts,
            hosts_sent,
        } = request_by_pk

        //Check whether the request is still in open, by checking if the game is already created or not
        if (request_by_pk?.game?.length) {
            //if request is accepted then do not send request to other members
            return {
                status: 1,
                message: 'Request is accepted by other members',
            }
        }

        //Check if the request is cancelled by the requested:
        if (request_by_pk?.status === 'cancelled') {
            return {
                status: 1,
                message: 'Request is cancelled by other the requester',
            }
        }

        //Get club data and eligible hosts
        let clubData = await getClubMembersForUser({
            userId: user_id,
            filters: { ...criteria, requestForAll: true },
            clubId: club_id,
        })

        let hostGroup2 = eligible_hosts
            .filter((id) => {
                return !hosts_sent.includes(id)
            })
            .filter((id) => {
                return clubData?.[0]?.user_array?.find(
                    (user) => user?.user_id === id
                )
            })

        //Insert these member into host_sent
        await pgPoolQuery(
            `UPDATE request
            SET hosts_sent = hosts_sent || jsonb_build_array(${hostGroup2
                .map((_, i) => `$${i + 2}`)
                .join(', ')})
                WHERE id = $1
            `,
            [requestId, ...hostGroup2]
        )

        //Send notification to the new hosts
        let hostNotifications = []
        // Creating short link for this request to send in text and email
        const { shortLink } = await createDynamicLink({
            webLink: `dashboard/play`,
            appParams: `type=requests&id=${requestId}`,
        })
        const shortLinkMessage = shortLink
            ? `Click here to check details: ${shortLink}`
            : ''

        // *** Finding requests completed by user as a requester and host, added as on 26th April 2023 ****
        let {
            hosted: hostedByRequester,
            requested: requestedByRequester,
        } = await requestsCompletedByUser(request_by_pk?.user_id)

        await Promise.all(
            hostGroup2.map(async (host) => {
                const token = await createCustomTokenWithExpiration(
                    request_by_pk?.user_id,
                    requestId
                )
                const customDeclineLink = `${process.env.CONFIG.baseURL}/sign-in-with-token/request-decline?requestId=${requestId}&tokenId=${token.id}`

                // If the requester has no accepted requests yet, then we will send an extra line in the mail to the hosts
                // https://thousandgreens.atlassian.net/browse/TG-6894
                let countOfAcceptedRequestsOfRequester = await pgPoolQuery(
                    `
                        SELECT
                            COUNT(game.game_id)::INT
                        FROM
                            request r
                        JOIN
                            game ON game.request_id = r.id
                        WHERE r.user_id = $1`,
                    [request_by_pk?.user_id]
                )
                const [{ count }] = await checkFriend({
                    senderId: user_id,
                    receiverId: host,
                })

                const mutualGroups = await pgPoolQuery(
                    `
                            SELECT 
                cc.id,
                cc.name
            FROM chat_channel cc
            JOIN chat_channel_member ccm1 ON ccm1.chat_channel_id = cc.id AND ccm1.user_id = $2
            JOIN chat_channel_member ccm2 ON ccm2.chat_channel_id = cc.id AND ccm2.user_id = $1
            WHERE cc.channel_type = 'my_tg_group'
            AND cc.is_active = TRUE
                            `,
                    [host, user_id]
                )

                const totalRequestedGames = await pgPoolQuery(
                    `
                                SELECT
                                    COUNT(*) AS requested
                                FROM 
                                    request
                                WHERE 
                                    request.user_id = $1
                                    AND request.status != 'cancelled';
                                `, [user_id]
                )

                countOfAcceptedRequestsOfRequester =
                    countOfAcceptedRequestsOfRequester?.[0]?.count


                const requestor = {
                    clubs: request_by_pk?.user?.clubs.reduce(
                        (clubNames, club, index) => {
                            if (index === 0) {
                                return club.club.name
                            } else if (index === 1) {
                                if (request_by_pk?.user?.clubs.length === 2) {
                                    return `${clubNames} and ${club.club.name}`
                                } else {
                                    return `${clubNames}, ${club.club.name}`
                                }
                            } else {
                                if (
                                    index ===
                                    request_by_pk?.user?.clubs.length - 1
                                ) {
                                    return `${clubNames}, and ${club.club.name}`
                                } else {
                                    return `${clubNames}, ${club.club.name}`
                                }
                            }
                        },
                        ''
                    ),
                    tier: getTier(request_by_pk?.user?.tier),
                }

                const dynamicMessage =
                    //Only Friend
                    `${(count !== '0' && (!mutualGroups || mutualGroups?.length === 0)) ? `One of your TG friends from ${requestor.clubs}`
                        //Only 1 common group
                        : (count === '0' && mutualGroups?.length > 0) ? `A member from one of your common groups ${mutualGroups[0].name} from ${requestor.clubs}`
                            //Friend & common group
                            : (count !== '0' && mutualGroups?.length > 0) ? `One of your TG friends from ${requestor.clubs}`
                                //None of above
                                : `A member from ${requestor.clubs}`}`;

                try {
                    // Push the result to the hostNotifications array
                    hostNotifications.push({
                        user_id: host,
                        type: 'host-request',
                        message: hostMessage({
                            gameId: request_by_pk.game_id,
                            clubName: request_by_pk?.club?.name,
                        }),
                        text_message: hostTextMessage({
                            gameId: request_by_pk.game_id,
                            clubs: requestor.clubs,
                            tier: requestor.tier,
                            clubName: request_by_pk?.club?.name,
                            shortLinkMessage,
                        }),
                        email_template_name: hostTemplate,
                        data: {
                            request_id: requestId,
                            template_content: [
                                {
                                    name: 'first_name',
                                    content: host.first_name,
                                },
                                {
                                    name: 'is_first_time_requester',
                                    content: request_by_pk?.is_first_request
                                        ? '(First Time Requester) '
                                        : '',
                                },
                                {
                                    name: 'requestor_club_name',
                                    content: `${requestor.clubs}`,
                                },
                                {
                                    name: 'requestor_tier',
                                    content: `${requestor.tier}`,
                                },
                                {
                                    name: 'host_club_name',
                                    content: `${request_by_pk?.club?.name}`,
                                },
                                {
                                    name: 'request_id',
                                    content: request_by_pk.game_id,
                                },
                                {
                                    name: 'request_dates',
                                    content: `${dateFormatter(
                                        moment.utc(request_by_pk.start_date),
                                        moment.utc(request_by_pk.end_date)
                                    )}`,
                                },
                                {
                                    name: 'max_players',
                                    content: request_by_pk.number_of_players,
                                },
                                {
                                    name: 'request_notes',
                                    content: request_by_pk.message,
                                },
                                {
                                    name: 'request_age',
                                    content: request_by_pk?.user?.age,
                                },
                                {
                                    name: 'request_handicap',
                                    content: request_by_pk?.user?.handicap,
                                },
                                {
                                    name: 'request_pace',
                                    content: request_by_pk?.user.pace,
                                },
                                {
                                    name: 'request_gender',
                                    content: request_by_pk?.user.gender,
                                },
                                {
                                    name: 'dynamic_link',
                                    content: shortLinkMessage,
                                },
                                {
                                    name: 'games_hosted',
                                    content: hostedByRequester,
                                },
                                {
                                    name: 'games_requested',
                                    content: requestedByRequester,
                                },
                                {
                                    name: 'decline_link',
                                    content: customDeclineLink,
                                },
                                {
                                    name: 'reply_link',
                                    content: shortLink,
                                },
                                {
                                    name: 'dynamic_message',
                                    content: dynamicMessage,
                                },
                                {
                                    name: 'total_games_requested',
                                    content: totalRequestedGames[0]?.requested
                                },
                            ],
                            email: request_by_pk?.user?.email,
                            subject: hostSubject(request_by_pk?.club?.name),
                        },
                    })
                } catch (error) { }
            })
        )

        if (hostNotifications.length > 0) {
            adminClient.request(CREATE_NOTIFICATION, {
                notifications: hostNotifications,
            })
        }
        return {
            status: 1,
            message: MESSAGES[200],
        }
    } catch (error) {
        console.log('🚀 ~ sendRequestToOtherMembers ~ error:', error)
    }
}

export default sendRequestToOtherMembers
