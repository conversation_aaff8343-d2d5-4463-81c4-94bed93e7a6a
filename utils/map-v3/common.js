const _ = require('lodash');
import pgPromise from 'pg-promise';
import constantOptions from '../../constants/constantOptions';
import pgPoolQuery from '../db/pgQuery';
import adminClient from '../../graphql/adminClient';
import {
  CTE_ALL_FRIENDS,
  CTE_MY_TG_GROUP_MEMBERS,
  MANDATORY_FROM_TABLES_TO_INCLUDE,
  WHERE_CLAUSE as OFFER_WHERE_CLAUSE,
} from '../offers/DB_QUERIES';

/**
 * Now using GraphQL since 22nd March 2024
 * @param {*} userId
 * @returns
 */
export const getUserDetail = async (userId) => {
  console.time('getUserDetailActual');

  // const { user_by_pk: user } = await adminClient.request(
  //   `
  // query fetchUserDetals($userId: uuid!) {
  //   user_by_pk(id: $userId) {
  //     visibleToPublic
  //     tier
  //     id
  //     gender
  //     playedClubs
  //     clubs {
  //       club_id
  //     }
  //     f_clubs: favorite_clubs {
  //       club_id
  //     }
  //   }
  // }`,
  //   {
  //     userId,
  //   },
  // );

  const userData = await pgPoolQuery(
    `
    SELECT 
      id,
      tier,
      "visibleToPublic",
      array_agg(DISTINCT "user_club"."club_id") as "clubs",
      array_agg(DISTINCT "favorite_club"."club_id") as "f_clubs",
      gender,
      "playedClubs"
    FROM "user" 
    LEFT JOIN "user_club" ON "user_club".user_id = "user".id
    LEFT JOIN "favorite_club" ON "favorite_club".user_id = "user".id
    WHERE id = $1
    GROUP BY id
  `,
    [userId],
  );

  if (userData.length === 0) {
    return null;
  }

  // const userDetail = {
  //   id: user?.id,
  //   tier: user.tier,
  //   visibleToPublic: user.visibleToPublic,
  //   clubs: _.uniq(_.map(user?.clubs, 'club_id')),
  //   fClubs: _.uniq(_.map(user?.f_clubs, 'club_id')),
  //   gender: user?.gender?.toLowerCase(),
  //   playedClubs: user?.playedClubs,
  // };

  const userDetail2 = {
    id: userData[0].id,
    tier: userData[0].tier,
    visibleToPublic: userData[0].visibleToPublic,
    clubs: userData[0].clubs,
    fClubs: userData[0].f_clubs,
    gender: userData[0].gender,
    playedClubs: userData[0].playedClubs,
  };

  console.timeEnd('getUserDetailActual');
  return userDetail2;
};

/**
 * Now using GraphQL since 22nd March 2024
 * @param {*} userId
 * @returns
 */
export const getUserContacts = async (userId) => {
  let contacts = await adminClient.request(
    `
  query fetch($userId: uuid!) {
    personal_network_contact(where: {user_id: {_eq: $userId}}) {
      club_id: course_id
    }
  }`,
    {
      userId,
    },
  );

  contacts = contacts?.personal_network_contact || [];

  return _.map(contacts, 'club_id');
};

/**
 * To fetch all the contact clubs only for the user, that do not have any user in them
 * @param {*} userId
 * @returns
 */
export const getUserOnlyContactClubDetail = async (userId) => {
  const clubs = await pgPoolQuery(`
    SELECT
      "courses"."id",
      "courses"."lng",
      "courses"."lat",
      "courses"."address",
      "courses"."name",
      "courses"."lowest_visible_tier",
      "courses"."club_type",
      jsonb_build_array() as "visible_to_tiers",
      jsonb_build_array() as "user_tiers",
      jsonb_build_array() as "user_favorite_restricted",
      jsonb_build_array() as "user_favorite_clubs",
      true AS "only_contact_club"
    FROM "courses"
    LEFT JOIN (
      SELECT 
        "user_club".club_id
      FROM "user_club"
    ) AS "user_club" ON "user_club".club_id = "courses".id
    INNER JOIN (
      SELECT
        "personal_network_contact"."course_id",
        "personal_network_contact"."user_id"
      FROM "personal_network_contact"
      WHERE "personal_network_contact"."user_id" = '${userId}'
    ) AS "personal_network_contact" ON "personal_network_contact"."course_id" = "courses"."id"
    WHERE 
      "courses"."deactivated" = false AND
      ("user_club".club_id IS NULL OR "courses"."club_type" != ${constantOptions?.CLUB_TYPES?.PRIVATE})
    GROUP BY "courses".id
    `);
  return clubs;
};

export const getEligibleOffers = async (userId) => {
  let offers = await pgPoolQuery(
    `
    WITH 
    ${CTE_ALL_FRIENDS},
    ${CTE_MY_TG_GROUP_MEMBERS}

    SELECT DISTINCT ON (offer.offer_club_id)
    offer.offer_club_id AS club_id,
    offer.offer_id
    ${MANDATORY_FROM_TABLES_TO_INCLUDE}

    WHERE
        ${OFFER_WHERE_CLAUSE}
  `,
    [userId],
  );

  return _.uniq(_.map(offers, 'club_id'));
};

/**
 * This function returns the clubs that has clubs that user can send request to, has fields to
 * comprehend if the user has any friend or contact in that club
 * and also othey grey clubs that we need to show on map
 *
 * Technically we have a FULL JOIN on personal_network_contact because we have to always keep the
 * clubs that are only contact clubs even if they don't have a member
 * @param {*} userId
 * @param {*} userClubIds
 * @returns
 */
export const getClubsList = async (userId, userClubIds = []) => {
  userClubIds = userClubIds.join(',');

  const clubs = await pgPoolQuery(
    `
    SELECT "courses"."id",
    "courses"."lng",
    "courses"."lat",
    "courses"."address",
    "courses"."name",
    "courses"."lowest_visible_tier",
    "courses"."club_type",
    "courses"."club_demand_type",
    "courses"."acceptance_percentage",
    array_agg(DISTINCT "user_club"."has_friend") as "has_friend",
    array_agg(DISTINCT personal_network_contact.id) AS "has_contacts",
    array_agg(DISTINCT "user_club".is_active_user) AS "active_members",
    array_to_json(
        array_agg(
            json_build_object(
                'userId', user_club.user_id,
                'clubLowerTierVisibility',
                "user_club"."visible_to_tiers",
                'user_tier',
                "user_club"."tier",
                'user_gender', "user_club".user_gender,
                'favorite_restricted',
                user_club.favorite_restricted,
                'is_favorited',
                user_club.is_favorited,
                'visibleToPublic',
                user_club.is_tg_visible,
                'is_active_user',
                user_club.is_active_user,
                'has_common_tg_group_member',
                has_common_tg_group_member,
                'genderPreference',
                user_club.gender_preference
            )
        )
    ) AS members,
    array_agg(DISTINCT "user_club"."has_common_tg_group_member") as "has_common_tg_group_member",
    array_agg(DISTINCT "user_club".play_as_couple) AS "has_play_as_couple",

    array_agg(DISTINCT "user_club"."has_female") as "has_female"

  FROM "courses"
    
    LEFT JOIN (
      SELECT DISTINCT "user_club".club_id,
            "user_club"."visible_to_tiers",
            "user_club"."user_id",
            "user"."tier",
            CASE
                WHEN "user"."visible_to_favorite_clubs" = true
                AND "user_club"."favorite_restricted" = true THEN true
                ELSE false
            END AS favorite_restricted,
            CASE
                WHEN (friends.id IS NOT NULL) THEN TRUE
                ELSE FALSE
            END AS has_friend,
            "user"."visibleToPublic" AS is_tg_visible,
            NOT "user_club"."muted"::BOOLEAN
            AND NOT "user"."muted"::BOOLEAN
            AND NOT "user"."deactivated"::BOOLEAN
            AND "user"."account_activated"::BOOLEAN AS is_active_user,
            favorite_club.club_id AS is_favorited,
            CASE
                WHEN ccm2.user_id IS NOT NULL THEN ccm2.chat_channel_id::INT
                ELSE NULL::INT
            END AS has_common_tg_group_member,
            "user"."playAsCouple" AS "play_as_couple",
            user_club.gender_preference AS "gender_preference",
            "user".gender as "user_gender",
            CASE
                WHEN "user".gender = '${constantOptions.GENDER.FEMALE}' THEN TRUE
                ELSE FALSE
            END AS "has_female"
        FROM "user"
            LEFT JOIN "user_club" ON "user".id = "user_club".user_id
            LEFT JOIN "favorite_club" ON (
                "user".id = "favorite_club".user_id
                AND favorite_club.club_id IN (${userClubIds})
            )
            LEFT JOIN friends ON (
                (
                    friends.sender_id = $1
                    AND friends.receiver_id = user_club.user_id
                )
                OR (
                    friends.receiver_id = $1
                    AND friends.sender_id = user_club.user_id
                )
            )
            LEFT JOIN chat_channel_member ccm1 ON ccm1.user_id = $1 -- fetching logged in user's MY TG channels
            LEFT JOIN chat_channel_member ccm2 ON (
              ccm1.chat_channel_id = ccm2.chat_channel_id AND -- fetching common members between logged in user and other users of the same channel
              ccm2.user_id = "user_club".user_id AND -- checking if the channel's member is this loop user
              
              (
              	user_club.club_id = ANY(ccm2.visible_clubs) OR -- check if he has enabled the club's visibility 
	              ((SELECT tier FROM "user" WHERE id = $1) <=  -- or the user's tier is lower then club tier/lower tier visibility 
		              (
		              	SELECT CASE WHEN uc.is_visibile_to_lower_tiers 
                        AND c.lowest_visible_tier < uc.visible_to_tiers
                        THEN uc.visible_to_tiers
                        ELSE c.lowest_visible_tier END
		              	FROM courses c
		              	LEFT JOIN user_club uc ON uc.club_id = c.id AND uc.user_id = ccm2.user_id
		              	WHERE id = user_club.club_id
		              )
	              )
              )
              AND
              ccm1.user_id != ccm2.user_id -- ensuring that the logged in user is not same as the other channel's user
              AND
              (
              	user_club.gender_preference = '${constantOptions.GENDER.BOTH}' OR
              	user_club.gender_preference = (SELECT LOWER(gender) FROM "user" WHERE id = $1)
              )
            )

        WHERE (
          (
            user_club.user_id = $1 
            OR
            ccm2.user_id IS NOT NULL
            OR
            (
              "user_club"."muted" = false
              AND "user"."muted" = false
            )
          )
          AND
           "user"."account_activated" = true
          AND "user"."deactivated" = false 
          AND "user_club".user_id IS NOT NULL
        )
    ) AS "user_club" ON "user_club".club_id = "courses".id
    FULL JOIN "personal_network_contact" ON (
        "personal_network_contact"."course_id" = "courses"."id"
        AND personal_network_contact.user_id = $1
    )
  WHERE "courses"."deactivated" = false
    AND (
        personal_network_contact.id IS NOT NULL
        OR has_friend = TRUE
        OR (
            "user_club"."tier" IS NOT NULL
            AND "courses"."club_type" = 0
        )
        OR (
            "user_club"."user_id" = $1
        )
    )
  GROUP BY "courses".id`,
    [userId],
  );

  return clubs;
};

/**
 * Now using GraphQL since 22nd March 2024
 * @param {*} clubId
 * @returns
 */
export const getRequestAcceptPercentage = async (clubId) => {
  try {
    const request = await adminClient.request(
      `
    query fetch($clubId: Int!) {
      game_aggregate(where: {request: {club_id: {_eq: $clubId}}}) {
          aggregate {
            count
          }
        }
      request_aggregate(where: {club_id: {_eq: $clubId}}) {
        aggregate {
          count
        }
      }
    }`,
      {
        clubId,
      },
    );

    const requestPercentage = parseFloat(
      (parseInt(request?.game_aggregate?.aggregate?.count || 0) /
        parseInt(request?.request_aggregate?.aggregate?.count || 0)) *
        100,
    ).toFixed(2);

    return isNaN(requestPercentage) ? '00.00' : requestPercentage;
  } catch (error) {
    return 0;
  }
};

export const getUserFriends = async (userId) => {
  let friends = await pgPoolQuery(`
    SELECT 
      CASE WHEN sender_id = '${userId}'
        THEN receiver_id
      WHEN receiver_id = '${userId}'
        THEN sender_id
      END AS friend_id
    FROM
      friends
    WHERE
      sender_id = '${userId}' OR receiver_id = '${userId}'
  `);

  friends = _.map(friends, 'friend_id');
  return friends;
};

export const getMapUserFriends = async (userId, clubId) => {
  const [course] = await pgPoolQuery(`SELECT name FROM courses WHERE id = $1`, [
    clubId,
  ]);

  let searchValue = `%${course.name}%`;

  const [friends] = await pgPoolQuery(
    `SELECT
        count(id)
        FROM
            friends
            WHERE
            (sender_id = $1 OR receiver_id = $1) 
            AND (
                (sender_id = $1 AND (SELECT "account_activated" FROM "user" WHERE id = friends.receiver_id) = true)
                OR
                (receiver_id = $1 AND (SELECT "account_activated" FROM "user" WHERE id = friends.sender_id) = true)
              )
            AND (
                CASE WHEN sender_id = $1 
                THEN
                (
                    receiver_info ->> 'name' ILIKE $2
                    OR receiver_info ->> 'email' ILIKE $2 
                    OR receiver_info #>> '{clubs}' ILIKE $2
                    OR receiver_info ->> 'phone' ILIKE $2
                )
                WHEN receiver_id = $1
                THEN
                (
                    sender_info ->> 'name' ILIKE $2
                    OR sender_info ->> 'email' ILIKE $2 
                    OR sender_info #>> '{clubs}' ILIKE $2
                    OR sender_info ->> 'phone' ILIKE $2
                )
                END
            )
            AND status = 1`,
    [userId, searchValue],
  );
  return friends;
};

export const getTgGroupMembers = async (userId, clubId) => {
  let groupMembers = await pgPoolQuery(
    `
                      SELECT 
                      "user"."id",
                      CASE 
                          WHEN "user"."muted" = true OR "user_club"."muted" = true THEN true
                          ELSE false
                      END AS "isMuted"
                      FROM "user"
                      LEFT JOIN "courses" ON "courses".id = $2
                      LEFT JOIN "user" self ON self.id = $1 -- the current user
                      LEFT JOIN "user_club" ON "user_club".user_id = "user".id AND "user_club".club_id = $2
                      LEFT JOIN "chat_channel_member" ON "chat_channel_member"."user_id" = "user"."id"
                      LEFT JOIN "chat_channel" ON "chat_channel"."id" = "chat_channel_member"."chat_channel_id"
                      WHERE 
                      "user"."account_activated" = true AND "user_club".club_id = $2
                      AND "user"."id" <> $1
                      AND NOT EXISTS (
                        SELECT 1
                        FROM blocked_user
                        WHERE (blocked_by_id = "user".id AND blocked_userid = $1)
                           OR (blocked_by_id = $1 AND blocked_userid = "user".id)
                      )
                      AND "chat_channel"."name" IS NOT NULL
                      AND 
                      (
                        EXISTS 
                        (
                          SELECT
                              1
                          FROM
                              chat_channel_member ccm1
                              JOIN chat_channel_member ccm2 ON ccm1.chat_channel_id = ccm2.chat_channel_id -- checking if the other user and current one are in the same MY TG Group
                          WHERE
                              ccm1.user_id = "user".id
                              AND ccm2.user_id = $1
                              AND 
                              (
                                EXISTS (
                                  SELECT 1
                                  FROM friends
                                  WHERE (sender_id = "user".id AND receiver_id = $1)
                                      OR (receiver_id = $1 AND sender_id = "user"."id")
                                ) 
                                OR $2= ANY(ccm1."visible_clubs") 
                                OR 
                                ( 
                                    "user"."visibleToPublic" = TRUE
                                    AND
                                    (
                                        -- If my tier is less then the club tier then user's club will be visible
                                        -- OR my tier is less than the user's club LTV settings
                                        "self".tier <= courses.lowest_visible_tier 
                                    OR 
                                    (
                                        CASE WHEN user_club.is_visibile_to_lower_tiers = TRUE
                                        AND "self".tier <= user_club.visible_to_tiers THEN
                                        TRUE
                                        ELSE
                                            FALSE
                                        END
                                    ) 
                                    )
                                )
                              )    
                              
                        )
                      )      
                      
                      
                      GROUP BY "user"."id", "user"."tier", "user"."visibleToPublic", "isMuted";
                      
                  `,
    [userId, clubId],
  );

  groupMembers = groupMembers ? groupMembers.length : 0;
  return groupMembers;
};

export const getUsersPlayedInClub = async (
  clubId,
  userId,
  excludeMyId = true,
) => {
  let whereQuery = `"user"."playedClubs" @> '[$1]'::jsonb`;

  if (excludeMyId) {
    whereQuery = `"user"."playedClubs" @> '[$1]'::jsonb AND "user".id != $2`;
  }
  const userPlayedInClub = await pgPoolQuery(
    pgPromise.as.format(
      `
    SELECT
      id
    FROM
      "user"
    WHERE ${whereQuery}
    `,
      [clubId, userId],
    ),
  );

  return _.flatMap(userPlayedInClub, (item) => [item.id]);
};

/**
 * Now using GraphQL since 22nd March 2024
 * @param {*} clubId
 * @returns
 */
export const getClubDetail = async (clubId) => {
  const club = await adminClient.request(
    `
  query fetch($clubId: Int!) {
    courses_by_pk(id: $clubId) {
      id
      lat
      lng
      address
      name
      lowest_visible_tier
      club_type
      closurePeriods
      guest_time_restrictions
      club_demand_type
    }
  }
  `,
    {
      clubId,
    },
  );

  return club?.courses_by_pk;
};

export const getClubUsers = async (clubId, userId) => {
  const clubUsers = await pgPoolQuery(`
    SELECT 
        "user_club".club_id,
        "user_club"."visible_to_tiers",
        "user_club"."user_id",
        "user"."visibleToPublic",
        "private_network_user"."private_network_id",
        CASE 
          WHEN "user"."visible_to_favorite_clubs" = true AND "user_club"."favorite_restricted" = true 
          THEN true
          ELSE false 
        END AS favorite_restricted,
        "favorite_club"."club_id" as "f_club_id"
      FROM "user"
      LEFT JOIN "user_club" ON "user".id = "user_club".user_id
      LEFT JOIN "favorite_club" ON "user".id = "favorite_club".user_id
      LEFT JOIN private_network_user ON public."user".id = private_network_user."user_id"
      WHERE
        "user_club".user_id IS NOT NULL AND
        (
          (
            "user_club"."muted" = false AND
            "user"."muted" = false AND
            "user"."account_activated" = true AND
            "user"."deactivated" = false
          ) OR "user".id = '${userId}'
        ) AND "user_club".club_id = ${clubId}
  `);
  const clubUsersModified = _.chain(clubUsers)
    .groupBy((x) => x.user_id)
    .map((value, key) => {
      return {
        club_id: value[0].club_id,
        userId: key,
        tier: value[0].tier,
        visibleToTiers: value[0].visible_to_tiers,
        visibleToPublic: value[0].visibleToPublic,
        fClubs: _.map(value, 'f_club_id'),
        fRestricted: value[0].favorite_restricted,
        private_network_id: value[0].private_network_id,
      };
    })
    .value();
  return clubUsersModified;
};

export const userFriendDetail = async (friendArray) => {
  const friends = await pgPoolQuery(
    `
    SELECT
      "user"."id",
      "user"."first_name",
      "user"."last_name",
      "user"."profilePhoto",
      "courses"."name" AS clubName
    FROM "user"
    LEFT JOIN "user_club" ON "user"."id" = "user_club"."user_id"
    LEFT JOIN "courses" ON "user_club"."club_id" = "courses"."id"
    WHERE "user"."id" = ANY($1::uuid[])
  `,
    [friendArray],
  );

  const clubFriends = _.chain(friends)
    .groupBy((x) => x.id)
    .map((value, key) => {
      return {
        clubs: _.map(value, 'clubname'),
        userId: key,
        firstName: value[0].first_name,
        lastName: value[0].last_name,
        profilePhoto: value[0].profilePhoto,
      };
    })
    .value();

  return clubFriends;
};

/**
 *
 * @param {*} members -> is_active_user, favorite_restricted, is_favorited, clubLowerTierVisibility, private_network_id, visibleToPublic
 * @param {*} user -> private_network_id, visibleToPublic
 * @param {*} clubTier
 */
export const filterMembers = (members = [], user, clubTier) => {
  const response = members.filter((member) => {
    //If host has turned off the all TG visibilty, we will not consider his as a eligible host
    if (!member.visibleToPublic) {
      return false;
    }
    if (!member?.is_active_user) {
      return false;
    }
    if (member?.favorite_restricted && !member?.is_favorited) {
      return false;
    }

    const hostClubTier = member?.clubLowerTierVisibility || clubTier;

    //If club tier or LTV is greater then then user tier, then that member is not eligible host in the club
    if (
      hostClubTier < user?.tier ||
      (member?.genderPreference !== 'both' &&
        member?.genderPreference?.toLowerCase() !== user?.gender?.toLowerCase())
    ) {
      return false;
    }

    // // ********** Checking on the basis of TG, PN or TG + PN members ***********
    // if (user?.private_network_id && !user?.visibleToPublic &&
    //     user?.private_network_id !== member.private_network_id) {
    //     /*
    //     * User is only a member of Private Network
    //     * No consideration of tier is required in this case
    //     */
    //     return false;
    // } else {
    //     /*
    //     * User is a member of TG and/or Private Network
    //     */
    //     const hostClubTier = member?.clubLowerTierVisibility || clubTier;

    //     if (user?.private_network_id && user?.visibleToPublic) {
    //       if ((member.private_network_id && user?.private_network_id === member.private_network_id) ||
    //           (member.visibleToPublic && hostClubTier >= user?.tier)
    //       ) {
    //           return true;
    //       }
    //       if (!member.private_network_id && hostClubTier >= user?.tier) {
    //           // To check if the members's tier is greater than the user's tier and member is not of PN
    //           return true;
    //       }
    //       return false;
    //     } else if (!user?.private_network_id) {

    //       if (member.private_network_id && !member.visibleToPublic) {
    //           // User should not be a member of PN alone
    //           return false;
    //       } else {
    //           if (hostClubTier >= user?.tier) {
    //               return true;
    //           }
    //           return false;
    //       }
    //   }
    // }
    return true;
  });

  return response;
};

//New update: Get the total member count on the basis of the club color
export const getTotalMemberCount = async ({ userId, clubId, clubColor }) => {
  try {
    if (!clubId || !clubColor) {
      return Promise.reject(
        `${getTotalMemberCount.name}: clubId & clubColor is required.`,
      );
    }
    switch (clubColor) {
      //Total count: Eligible Hosts: Unmuted + Active members to whom I can send requests in this club as per their settings
      case 'green':
      case 'green_contact': {
        return getEligibleHostInClub({ userId, clubId });
      }

      // Total Unmuted + Active Members in that club
      case 'grey':
      case 'grey_contact': {
        return await queryForTotalMember({ userId, clubId });
      }

      // If User’s Own locator: Show number of Total Unmuted + Active Members in that club including user himself
      case 'teal':
      case 'teal_contact': {
        return await queryForTotalMember({ clubId });
      }

      // Count depends on which locater it was before it converted to blue
      case 'blue_contact':
      case 'blue': {
        const membersCount = await getEligibleHostInClub(userId);

        if (membersCount) return membersCount;
        else return await queryForTotalMember({ userId, clubId });
      }
      default:
        return 0;
    }
  } catch (error) {
    console.log(error);
    return 0;
  }
};

/**
 * Function to find the total active members in the club
 *
 * @param  userId | When userId is provided, the the club member list does not include the that userId
 * clubId | To get the total active members in the club
 * @returns
 */
const queryForTotalMember = async ({ userId, clubId }) => {
  let whereQuery = '';
  if (userId) {
    whereQuery = `AND other.id <> '${userId}'`;
  }
  const clubData = await pgPoolQuery(
    `
        WITH club_subset AS (
          SELECT id, lowest_visible_tier
          FROM courses
          WHERE id = $2
          AND courses.club_type = ${constantOptions.CLUB_TYPES.PRIVATE}
        )
        
        SELECT
            c.id AS id,
            array_to_json(
                array_agg(
                    json_build_object(
                        'user_id', other.id
                    )
                )
            ) AS user_array,
            count(DISTINCT other.id) AS user_id_count
        FROM
            "user" other
        JOIN user_club oc ON oc.user_id = other.id AND NOT oc.muted
        JOIN club_subset c ON c.id = oc.club_id
        WHERE
            -- Other user be an active user
            NOT other.muted
            AND other.account_activated
            AND NOT other.deactivated
            AND other.declined IS NULL
            ${whereQuery}
        GROUP BY
            c.id;
      
        `,
    [userId, clubId],
  );

  return clubData?.length ? clubData[0]?.user_array?.length : 0;
};

const getEligibleHostInClub = async ({ userId, clubId }) => {
  const clubData = await pgPoolQuery(
    `
        WITH club_subset AS (
          SELECT id, lowest_visible_tier
          FROM courses
          WHERE id = $2
          AND courses.club_type = ${constantOptions.CLUB_TYPES.PRIVATE}
        ),
        favorite_club_subset AS (
            SELECT user_id, array_agg(club_id) AS fav_clubs
            FROM favorite_club fc
            GROUP BY user_id
        ),
        user_all_friends AS (
            SELECT id, sender_id, receiver_id
            FROM friends
            WHERE sender_id = $1 OR receiver_id = $1
        ),
        mytg_group_member AS (
            SELECT
                ccm1.chat_channel_id,
                ccm1.user_id AS "user_id",
                ccm2.visible_clubs,
                ccm2.user_id AS "member_id"
            FROM
                chat_channel_member ccm1
            INNER JOIN chat_channel_member ccm2
                ON ccm1.chat_channel_id = ccm2.chat_channel_id
                AND ccm1.user_id = $1
        ),
        -- Logged in user
        logged_in_user AS (
          SELECT gender, tier FROM "user"
          WHERE id = $1
        )


        SELECT
            c.id AS id,
            array_to_json(
                array_agg(
                  DISTINCT jsonb_build_object(
                        'user_id', other.id,
                        'isFriend',
                        CASE
                            WHEN uaf.id IS NOT NULL THEN true
                            ELSE false
                        END,
                        'isMyTGGroupMember',
                        CASE
                            WHEN tg_group.chat_channel_id IS NOT NULL THEN true
                            ELSE false
                        END
                    )
                )
            ) AS user_array,
            count(DISTINCT other.id) AS user_id_count
        FROM
            "user" other
        JOIN user_club oc ON oc.user_id = other.id AND NOT oc.muted
        JOIN club_subset c ON c.id = oc.club_id
        LEFT JOIN favorite_club_subset fcs ON fcs.user_id = other.id
        LEFT JOIN user_all_friends uaf ON uaf.sender_id = other.id OR uaf.receiver_id = other.id
        LEFT JOIN mytg_group_member tg_group ON tg_group.member_id = other.id AND c.id = ANY(tg_group.visible_clubs)
        LEFT JOIN logged_in_user liu ON 1=1
        WHERE
            -- Other user be an active user
            NOT other.muted
            AND other.account_activated
            AND NOT other.deactivated
            AND other.declined IS NULL
            -- Checking gender preferences
            AND
            (
                uaf.id IS NOT NULL --Both user are friend
                    OR
                (
                    oc.gender_preference = '${constantOptions.GENDER.BOTH}'
                        OR
                    (oc.gender_preference = LOWER(liu.gender))
                )   
            )

            AND (
                uaf.id IS NOT NULL -- Both users are friends
                OR tg_group.chat_channel_id IS NOT NULL
                OR (
                    other."visibleToPublic"
                    AND (
                      (
                          oc.favorite_restricted = true
                          AND (
                          (SELECT array_agg(club_id) FROM user_club
                          WHERE user_id = $1)
                          && ARRAY(SELECT DISTINCT unnest(fcs.fav_clubs))
                        )      
                      )
                        OR (NOT oc.favorite_restricted OR oc.favorite_restricted IS NULL)
                    )
                    AND (
                        (
                            SELECT tier
                            FROM "user"
                            WHERE id = $1
                        ) <= c.lowest_visible_tier
                        OR (
                            oc.is_visibile_to_lower_tiers
                            AND (
                                SELECT tier
                                FROM "user"
                                WHERE id = $1
                            ) <= oc.visible_to_tiers
                        )
                    )
                )
            )
        GROUP BY
            c.id;
      
        `,
    [userId, clubId],
  );

  return clubData?.length ? clubData[0]?.user_array?.length : 0;
};
