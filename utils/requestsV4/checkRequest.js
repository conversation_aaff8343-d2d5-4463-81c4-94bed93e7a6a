import pgPoolQuery from '../db/pgQuery';

/**
 * function for check request exist or not and get request info
 * @param {*} param
 * @returns
 */
const checkRequest = async (requestId, userId) => {
  try {
    const [isRequest] = await pgPoolQuery(
      `
                SELECT r.id, 
                r.user_id, 
                r.number_of_players,
                r.start_date,
                r.end_date,
                r.message,
                r.deleted_by_users,
                g."hostCompleted", 
                g."requestorCompleted",
                c."guestTimeRestrictions",
                c."closurePeriods",
                g.game_id,
                r.club_id,
                g.host_id
                FROM request r
                LEFT JOIN game g ON r.id = g.request_id
                LEFT JOIN courses c ON c.id = r.club_id
                WHERE r.id = $1 AND r.user_id = $2
            `,
      [requestId, userId],
    );

    return isRequest;
  } catch (error) {
    return false;
  }
};

export default checkRequest;
