import pgPoolQuery from '../db/pgQuery';

/**
 * function for check host request exist or not and host declined is part of that request
 * @param {*} param
 * @returns
 */
const checkHostRequest = async ({ requestId, hostId }) => {
  try {
    const [isRequest] = await pgPoolQuery(
      `
                            SELECT 
                                r.id, 
                                r.user_id, 
                                r.hosts_declined, 
                                r.hosts_sent,
                                r.status,
                                r.game_id AS request_game_id,
                                game.game_id,
                                game.host_id,
                                game."hostCompleted", 
                                game."requestorCompleted",
                                game.status AS game_status,
                                rc.has_messages,
                                r.club_id
                            FROM "request" r
                            LEFT JOIN game ON r.id = game.request_id
                            LEFT JOIN request_chat rc ON r.id = rc.request_id 
                                AND rc.club_member_id = $3
                            WHERE r.id = $1 AND r.hosts_sent @> $2::jsonb
                        `,
      [requestId, JSON.stringify(hostId), hostId],
    );

    return isRequest;
  } catch (error) {
    return false;
  }
};

export default checkHostRequest;
