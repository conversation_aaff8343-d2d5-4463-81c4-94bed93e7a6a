/**
 * This function is used for fetching hosts of a request as a requester
 * @param {*} req
 * @param {*} res
 * @returns
 */

import pgPoolQuery from '../../db/pgQuery'
import { reportError } from '../../rollbar'

const getHostOfMultipleRequests = async (requesterId, requestIds = []) => {
    try {
        if (!requesterId) throw new Error('Requester Id is mandatory')
        if (!requestIds?.length) throw new Error('Request Id is mandatory')

        const result = await pgPoolQuery(
            `
                SELECT
            request_chat.request_id,
            json_agg(
                jsonb_build_object(
                    'sendbird_channel_id', request_chat.sendbird_channel_id,
                    'stream_channel_id', request_chat.stream_channel_id,
                    'has_messages', request_chat.has_messages,
                    'id', "user".id,
                    'username', 
                        CASE 
                            WHEN ("user".deleted_at IS NULL) THEN "user".username
                            ELS<PERSON> ("user".old_data->>'username'::text)
                        END,
                    'deleted_at', "user".deleted_at,
                    'pace', "user".pace,
                    'handicap', "user".handicap,
                    'englishFluency', "user"."englishFluency",
                    'facebook', "user".facebook,
                    'linkedin', "user".linkedin,
                    'socialPublic', "user"."socialPublic",
                    'name', 
                        CASE
                            WHEN ("user".deleted_at IS NULL) THEN concat("user".first_name, ' ', "user".last_name)
                            ELSE concat("user".old_data->>'first_name'::text, ' ',"user".old_data->>'last_name'::text)
                        END,
                    'email', "user".email,
                    'phone', "user".phone,
                    'created_at', "user".created_at,
                    'tier', "user".tier,
                    'visibleToPublic', "user"."visibleToPublic",
                    'profilePhoto', "user"."profilePhoto"
                )
            ) AS hosts
            FROM
                request_chat
            JOIN "user" ON "user".id = request_chat.club_member_id
            LEFT JOIN friends f ON (
                (f.sender_id = $1 AND f.receiver_id = "user".id) OR 
                (f.receiver_id = $1 AND f.sender_id = "user".id)
            )
            WHERE 
                request_chat.has_messages = TRUE 
                AND request_chat.request_id IN (${requestIds
                .map((id) => `'${id}'`)
                .join(', ')})
            GROUP BY 
                request_chat.request_id;

        `,
            [requesterId]
        )

        return result
    } catch (error) {
        reportError(error)
        return Promise.reject(error)
    }
}

export default getHostOfMultipleRequests
