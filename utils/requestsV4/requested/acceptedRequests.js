import pgPoolQuery from '../../db/pgQuery';
import { reportError } from '../../rollbar';

/**
 * This function is used for fetching requested accepted requests of the user
 * @param {*} req
 * @param {*} res
 * @returns
 */
const acceptedRequests = async (userId) => {
  try {
    if (!userId) throw new Error('User Id is manadatory');

    const result = await pgPoolQuery(
      `
        SELECT
            request.id AS request_id,
            request.game_id,
            courses.name AS club_name,
            to_char(request.start_date, 'YYYY-MM-DD') AS start_date,
            to_char(request.end_date, 'YYYY-MM-DD') AS end_date,
            game.host_id AS game_host_user_id,
            request.message,
            (request.criteria->'accompanied_only'::text) AS accompanied_only,
            request.number_of_players,
            request.hosts_declined,
            request.created_at,
            request.updated_at,
            game."hostCompleted" AS host_completed,
            game.date AS game_date,
            concat(accepted_host_detail.first_name, ' ', accepted_host_detail.last_name) AS game_host_full_name,
            accepted_host_detail.username AS game_host_user_name,
            request.deleted_by_users,
            request.offer_details,
            request.offer_id,
            request.club_id,
            $1 AS requestor_user_id,
            request.request_for_all,
            request.is_first_request,
            uc.review_disabled AS is_game_review_disabled
        FROM 
            request
        LEFT JOIN courses ON courses.id = request.club_id
        JOIN game ON request.game_id = game.game_id
        LEFT JOIN "user" accepted_host_detail ON accepted_host_detail.id = game.host_id
        LEFT JOIN user_club uc on uc.user_id = game.host_id and uc.club_id = request.club_id
        LEFT JOIN accepted_request ON request.id = accepted_request.request_id
        WHERE (
            request.user_id = $1 -- the requester is supposed to be logged in user
            AND request.status <> 'cancelled'::text -- request's status should not be cancelled
            AND (request.status <> 'deleted'::text) -- request's status should not be deleted
            AND (game.game_id IS NOT NULL) -- game id should exist
            AND (game.status <> 'declined'::text) -- game's status should not be declined
            AND (game."requestorCompleted" = FALSE) -- requester must not have completed the game
            AND (accepted_request.request_id IS NOT NULL) -- there must be a row in accepted_request
            AND (accepted_request.status <> 'declined'::text) -- the status of accepted request must not be declined
            AND (request.hosts_sent <> request.hosts_declined) -- the hosts sent must not be same as hosts declined
            AND (jsonb_array_length(request.hosts_sent) <> jsonb_array_length(request.hosts_declined)) -- the length of hosts sent must not be same as hosts declined
        )
        ORDER BY request.game_id DESC;
        `,
      [userId],
    );

    return result;
  } catch (error) {
    reportError(error);
    return Promise.reject(error);
  }
};

export default acceptedRequests;
