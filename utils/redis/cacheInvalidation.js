import redis from './redisClient';

/**
 * Cache invalidation utilities for the clubs API
 */

/**
 * Invalidate user-related cache when user data changes
 * @param {string} userId - The user ID whose cache needs to be invalidated
 */
export const invalidateUserCache = async (userId) => {
  const keys = [
    `user_details:${userId}`,
    `offers:${userId}`,
    `fetch_clubs_final:${userId}`,
    // Also invalidate club list cache patterns for this user
    `clubs_list:${userId}:*`,
  ];

  try {
    // Delete specific keys
    for (const key of keys.slice(0, -1)) {
      await redis.del(key);
    }

    // Delete pattern-based keys (clubs_list with any club combination)
    const clubsListKeys = await redis.keys(`clubs_list:${userId}:*`);
    if (clubsListKeys.length > 0) {
      await redis.del(...clubsListKeys);
    }

    console.log(`Cache invalidated for user: ${userId}`);
  } catch (error) {
    console.error(`Cache invalidation error for user ${userId}:`, error);
  }
};

/**
 * Invalidate offer-related cache when offers change
 * @param {string[]} affectedUserIds - Array of user IDs affected by offer changes
 */
export const invalidateOfferCache = async (affectedUserIds = []) => {
  try {
    const keys = [];

    for (const userId of affectedUserIds) {
      keys.push(`offers:${userId}`, `fetch_clubs_final:${userId}`);
    }

    if (keys.length > 0) {
      await redis.del(...keys);
      console.log(
        `Offer cache invalidated for ${affectedUserIds.length} users`,
      );
    }
  } catch (error) {
    console.error('Offer cache invalidation error:', error);
  }
};

/**
 * Invalidate club-related cache when club data changes
 * @param {number} clubId - The club ID whose cache needs to be invalidated
 */
export const invalidateClubCache = async (clubId) => {
  try {
    // This is more complex as we need to find all cache entries that might contain this club
    // For now, we'll invalidate broader patterns
    const keys = await redis.keys('clubs_list:*');
    const finalKeys = await redis.keys('fetch_clubs_final:*');

    const allKeys = [...keys, ...finalKeys];

    if (allKeys.length > 0) {
      await redis.del(...allKeys);
      console.log(
        `Club cache invalidated for club: ${clubId} (${allKeys.length} keys cleared)`,
      );
    }
  } catch (error) {
    console.error(`Club cache invalidation error for club ${clubId}:`, error);
  }
};

/**
 * Invalidate cache when user joins/leaves a club
 * @param {string} userId - The user ID
 * @param {number} clubId - The club ID
 */
export const invalidateUserClubCache = async (userId, clubId) => {
  try {
    // Invalidate user-specific caches
    await invalidateUserCache(userId);

    // Also invalidate other users' caches that might be affected by this club change
    // This is important because club membership affects visibility for other users
    await invalidateClubCache(clubId);

    console.log(
      `User-club cache invalidated for user: ${userId}, club: ${clubId}`,
    );
  } catch (error) {
    console.error(`User-club cache invalidation error:`, error);
  }
};

/**
 * Clear all clubs API related cache (use sparingly, for maintenance)
 */
export const clearAllClubsCache = async () => {
  try {
    const patterns = [
      'user_details:*',
      'offers:*',
      'clubs_list:*',
      'fetch_clubs_final:*',
    ];

    let totalDeleted = 0;
    for (const pattern of patterns) {
      const keys = await redis.keys(pattern);
      if (keys.length > 0) {
        await redis.del(...keys);
        totalDeleted += keys.length;
      }
    }

    console.log(`All clubs cache cleared: ${totalDeleted} keys deleted`);
  } catch (error) {
    console.error('Clear all cache error:', error);
  }
};

/**
 * Get cache statistics for monitoring
 */
export const getCacheStats = async () => {
  try {
    const patterns = [
      'user_details:*',
      'offers:*',
      'clubs_list:*',
      'fetch_clubs_final:*',
    ];

    const stats = {};
    for (const pattern of patterns) {
      const keys = await redis.keys(pattern);
      stats[pattern] = keys.length;
    }

    return stats;
  } catch (error) {
    console.error('Cache stats error:', error);
    return {};
  }
};
