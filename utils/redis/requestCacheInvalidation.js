import redis from './redisClient';

/**
 * Cache invalidation utilities for request-related caches
 */

/**
 * Invalidate request cache for a specific request and user
 * @param {string} requestId - The request ID
 * @param {string} userId - The user ID
 */
export const invalidateRequestCache = async (requestId, userId) => {
  try {
    const keys = [
      `request_tab:${requestId}:${userId}`,
      `request_data:${requestId}:${userId}`,
    ];

    for (const key of keys) {
      await redis.del(key);
    }
  } catch (error) {
    console.error('Error invalidating request cache:', error);
  }
};

/**
 * Invalidate all caches for a specific request
 * This should be used when a request's status changes
 * @param {string} requestId - The request ID
 */
export const invalidateAllRequestCache = async (requestId) => {
  try {
    // Get all keys matching the request patterns
    const tabKeys = await redis.keys(`request_tab:${requestId}:*`);
    const dataKeys = await redis.keys(`request_data:${requestId}:*`);

    const allKeys = [...tabKeys, ...dataKeys];

    if (allKeys.length > 0) {
      await redis.del(...allKeys);
    }
  } catch (error) {
    console.error('Error invalidating all request cache:', error);
  }
};

/**
 * Invalidate all request caches for a specific user
 * This should be used when a user's data changes that might affect their requests
 * @param {string} userId - The user ID
 */
export const invalidateUserRequestCache = async (userId) => {
  try {
    // Get all keys matching the user patterns
    const tabKeys = await redis.keys(`request_tab:*:${userId}`);
    const dataKeys = await redis.keys(`request_data:*:${userId}`);

    const allKeys = [...tabKeys, ...dataKeys];

    if (allKeys.length > 0) {
      await redis.del(...allKeys);
    }
  } catch (error) {
    console.error('Error invalidating user request cache:', error);
  }
};
