import redis from './redisClient';

/**
 * Invalidate teal dot status cache for a specific user
 * @param {string} userId - The user ID
 */
export const invalidateTealDotCache = async (userId) => {
  try {
    const cacheKey = `teal_dot_status:${userId}`;
    await redis.del(cacheKey);
  } catch (error) {
    console.error('Error invalidating teal dot cache:', error);
  }
};

/**
 * Invalidate teal dot status cache for multiple users
 * @param {string[]} userIds - Array of user IDs
 */
export const invalidateTealDotCacheMultiple = async (userIds) => {
  try {
    if (!Array.isArray(userIds) || userIds.length === 0) return;

    const keys = userIds.map((userId) => `teal_dot_status:${userId}`);
    await redis.del(...keys);
  } catch (error) {
    console.error('Error invalidating multiple teal dot caches:', error);
  }
};
