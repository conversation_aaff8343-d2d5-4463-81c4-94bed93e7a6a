import moment from 'moment';
import adminClient from '../../graphql/adminClient';
import { CREATE_NOTIFICATION } from '../../graphql/mutations/notification';
import { UPDATE_REQUEST } from '../../graphql/mutations/request';
import { GET_USER_EMAIL } from '../../graphql/queries/user';
import getClubMembersForUser from '../clubs/getClubMembersForUser';
import createDynamicLink from '../notifications/createDynamicLink';
import { getTier } from '../tiers';
import sleep from '../helper/sleep';
import rollbar from '../rollbar';
import { sendFailureMail } from '../../pages/api/cron/dailyCron';
import { invalidateRequestCache } from '../redis/requestCacheInvalidation';

const filters = {
  pace: ['fast', 'average', 'leisure'],
  gender: 'both',
  all_ages: true,
  max_age: null,
  min_age: null,
  handicap: ['< 5', '5-10', '> 10'],
  playAsCouple: false,
  englishFluency: ['native', 'fluent', 'understandable', 'basic'],
  sendToPrivateNetwork: null,
};

export default async function sendRequestToOthers() {
  var filteredRequests = [],
    notifications = [];
  let rollbarResponse = [];

  try {
    await sleep();

    const to = moment().add(-1, 'day').format('YYYY-MM-DD');
    const from = moment().add(-2, 'day').format('YYYY-MM-DD');

    const { request: requests } = await adminClient.request(`
    {
        request(where: {status: {_nin: ["cancelled", "deleted"]}, createPublicRequest: {_eq: true}, created_at: {_gte: "${from}", _lt: "${to}"}}) {
            id
            game_id
            hosts_sent
            hosts_declined
            offer_id
            offer_details
            club_id
            offer {
                offer_id
            }
            game {
                game_id
            }
            chats {
                club_member_id
            }
            user {
                id
                tier
                private_network {
                    private_network_id
                }
                visibleToPublic
                first_name
                last_name
                clubs {
                    club_id
                    club {
                        name
                    }
                }
            }
        }
    }                     
    `);

    filteredRequests = requests.filter((request) => {
      if (!request.offer_id) {
        return false;
      }

      if (
        (request.offer_id && request?.game.length) ||
        request?.chats.length ||
        request.hosts_sent.length === request.hosts_declined.length
      ) {
        return false;
      }

      return true;
    });

    await Promise.all(
      filteredRequests.map(async (request) => {
        // Creating short link for this request to send in text and email
        const { shortLink } = await createDynamicLink({
          webLink: `dashboard/play`,
          appParams: `type=requests&id=${request?.id}`,
        });
        const shortLinkMessage = shortLink
          ? `Click here to check details: ${shortLink}`
          : '';

        const requestor = {
          clubs: request.user.clubs.reduce((clubNames, club, index) => {
            if (index === 0) {
              return club.club.name;
            } else if (index === 1) {
              if (request.user.clubs.length === 2) {
                return `${clubNames} and ${club.club.name}`;
              } else {
                return `${clubNames}, ${club.club.name}`;
              }
            } else {
              if (index === request.user.clubs.length - 1) {
                return `${clubNames}, and ${club.club.name}`;
              } else {
                return `${clubNames}, ${club.club.name}`;
              }
            }
          }, ''),
          tier: getTier(request.user.tier),
        };

        const { view_club_search } = await adminClient.request(`{
                view_club_search(where: {id: {_eq: ${request.club_id}}}) {
                    user_array
                    name
                }
            }`);

        if (view_club_search && view_club_search.length) {
          let members = getClubMembersForUser({
            user_data: {
              tier: request?.user?.tier,
              private_network: request?.user.private_network.length
                ? request?.user.private_network[0]
                : {},
              visibleToPublic: request?.user?.visibleToPublic,
              clubs: request?.user?.clubs,
            },
            filters,
            user_array: view_club_search[0].user_array,
            club_id: request.club_id,
            clubTier: view_club_search[0]?.tier,
          });

          let message = `You have received a request #${request?.game_id} from ${request.user.first_name} ${request.user.last_name}, a ${requestor.tier} tier member belonging to ${requestor.clubs}. This request was originally created against an offer ${request?.offer?.offer_id} created by one of your fellow members. You can see the request under the Received/Open Requests Section on the App or the website`;

          const allMembers = Array.from(
            new Set([
              ...request.hosts_sent,
              ...members.map((member) => member.user_id),
            ]),
          );
          if (members.length) {
            await adminClient.request(UPDATE_REQUEST, {
              id: request.id,
              request: {
                hosts_sent: allMembers,
              },
            });

            // Invalidate cache for this request and user
            await invalidateRequestCache(request.id, request.user_id);

            const notificationTo = allMembers.filter((mem) => {
              if (!request.hosts_sent.includes(mem)) {
                return true;
              }
              return false;
            });

            await Promise.all(
              notificationTo.map(async (to) => {
                const { user_by_pk } = await adminClient.request(
                  GET_USER_EMAIL,
                  {
                    id: to,
                  },
                );

                rollbarResponse.push(to);

                notifications.push({
                  user_id: to,
                  type: 'host-request',
                  message,
                  text_message: `${message}. ${shortLinkMessage}`,
                  email_template_name: 'Request Against Offer',
                  data: {
                    request_id: request?.id,
                    template_content: [
                      {
                        name: 'first_name',
                        content: user_by_pk?.first_name,
                      },
                      {
                        name: 'requestor_club_name',
                        content: `${requestor.clubs}`,
                      },
                      {
                        name: 'requestor_tier',
                        content: `${requestor.tier}`,
                      },
                      {
                        name: 'host_club_name',
                        content: `${view_club_search[0].name}`,
                      },
                      {
                        name: 'request_id',
                        content: request.game_id,
                      },
                      {
                        name: 'requestor_name',
                        content: `${request.user.first_name} ${request.user.last_name}`,
                      },
                      {
                        name: 'offer_id',
                        content: request?.offer?.offer_id,
                      },
                      {
                        name: 'dynamic_link',
                        content: shortLinkMessage,
                      },
                    ],
                    email: user_by_pk.email,
                    subject: `New Request (#${request.game_id}) against Offer #${request.offer.offer_id}`,
                  },
                });
              }),
            );
          }
        }
      }),
    )
      .then((res) => console.log({ res }))
      .catch((err) => {});

    if (notifications.length) {
      adminClient.request(CREATE_NOTIFICATION, {
        notifications,
      });
    }

    return notifications;
  } catch (error) {
    const errorType = {
      functionName: sendRequestToOthers.name,
      errorMsg: error.toString(),
    };

    rollbar.error(`Cron:`, errorType);
    await sendFailureMail(errorType);

    return errorType;
  } finally {
    rollbar.info('Cron: -- Ran Send Request To Others --', {
      rollbarResponse,
    });
    console.log('-- Ran Send Request To Others --');
  }
}
