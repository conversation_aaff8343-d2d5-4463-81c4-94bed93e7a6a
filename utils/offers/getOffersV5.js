import pgPoolQuery from '../db/pgQuery';
import {
  CTE_ALL_FRIENDS,
  CTE_DISTINCT_CHAT_CHANNELS,
  CTE_MY_TG_GROUP_MEMBERS,
  MANDATORY_FROM_TABLES_TO_INCLUDE,
  WHERE_CLAUSE,
} from './DB_QUERIES';
import rollbar from '../rollbar';
import adminClient from '../../graphql/adminClient';

const getOffers = async (userId) => {
  try {
    // Get offers with optimized single query
    const offers = await pgPoolQuery(
      `
        WITH
            ${CTE_ALL_FRIENDS},
            ${CTE_DISTINCT_CHAT_CHANNELS},
            ${CTE_MY_TG_GROUP_MEMBERS}

        SELECT DISTINCT ON (
                CASE
                    WHEN "offer".offer_user_id = $1 THEN 0
                    ELSE 1
                END,
                offer.offer_start_date, offer.offer_id
            )
          offer.visible_to_all AS visible_to_all,
          offer.offer_user_id AS user_id,
          offer.offer_id AS id,
          offer.offer_created_at AS created_at,
          offer.offer_club_id AS club_id,
          offer.offer_start_date AS start_date,
          offer.offer_end_date AS end_date,
          offer.offer_details AS details,
          offer.offer_custom_id AS offer_id,
          offer.offer_status AS status,
          offer.offer_tier_visibility AS "tierVisibility",
          offer.my_tg_group_id,
          dcc.chat_channels AS "chatChannels",
              (
                  CASE
                      WHEN (uaf.id IS NOT NULL OR offer.offer_user_id = $1) THEN CONCAT(creator.first_name , ' ', creator.last_name)
                      ELSE creator.username
                  END
              ) AS "creatorName",
              offer."forFriends",
              offer.is_single_group_offer AS "isSingleGroupOffer",
                (
                    CASE
                        WHEN uaf.id IS NOT NULL THEN TRUE
                        ELSE FALSE
                    END
                ) AS "creatorIsFriend"
            ${MANDATORY_FROM_TABLES_TO_INCLUDE}
            LEFT JOIN DistinctChatChannels dcc ON offer.offer_id = dcc.offer_id
            LEFT JOIN "user" creator ON creator.id = offer.offer_user_id

            WHERE
                ${WHERE_CLAUSE}
            ORDER BY
                CASE
                    WHEN "offer".offer_user_id = $1 THEN 0
                    ELSE 1
                END,
                offer.offer_start_date, offer.offer_id
        `,
      [userId],
    );

    if (!offers || offers.length === 0) {
      return [];
    }

    // Separate user's own offers from others
    const userOffers = [];
    const otherOffers = [];

    for (const offer of offers) {
      if (offer.user_id === userId) {
        userOffers.push({
          ...offer,
          requested: null,
        });
      } else {
        otherOffers.push(offer);
      }
    }

    // If no other offers, return early
    if (otherOffers.length === 0) {
      return userOffers;
    }

    // Get all offer IDs for batch processing
    const offerIds = otherOffers.map((offer) => offer.id);

    // Single GraphQL query to get all requests for all offers
    const { request: allRequests } = await adminClient.request(
      `
      query getOfferRequests($offerIds: [uuid!]!, $userId: uuid!) {
        request(where: {offer_id: {_in: $offerIds}, user_id: {_eq: $userId}}) {
          id
          offer_id
        }
      }
    `,
      {
        offerIds,
        userId,
      },
    );

    // Group requests by offer_id for efficient lookup
    const requestsByOfferId = {};
    for (const req of allRequests) {
      if (!requestsByOfferId[req.offer_id]) {
        requestsByOfferId[req.offer_id] = [];
      }
      requestsByOfferId[req.offer_id].push(req.id);
    }

    // Get detailed request status for all requests in one query
    let closedRequestIds = new Set();

    if (allRequests.length > 0) {
      const allRequestIds = allRequests.map((req) => req.id);

      const requestStatusData = await adminClient.request(
        `
        query getRequestStatus($userId: uuid!, $requestIds: [uuid!]!) {
          view_requests_history_requested(where: {requestor_user_id: {_eq: $userId}, request_id: {_in: $requestIds}}) {
            request_id
          }
          view_requests_requested_accepted(where: {requestor_user_id: {_eq: $userId}, host_completed: {_eq: true}, request_id: {_in: $requestIds}}) {
            request_id
          }
        }
      `,
        {
          userId,
          requestIds: allRequestIds,
        },
      );

      // Collect all closed request IDs
      requestStatusData.view_requests_history_requested.forEach((req) => {
        closedRequestIds.add(req.request_id);
      });
      requestStatusData.view_requests_requested_accepted.forEach((req) => {
        closedRequestIds.add(req.request_id);
      });
    }

    // Process other offers with batch data
    const processedOtherOffers = otherOffers.map((offer) => {
      const offerRequestIds = requestsByOfferId[offer.id] || [];

      if (offerRequestIds.length === 0) {
        return {
          ...offer,
          requested: false,
        };
      }

      // Check if all requests for this offer are closed
      const activeRequestIds = offerRequestIds.filter(
        (reqId) => !closedRequestIds.has(reqId),
      );
      const hasActiveRequests = activeRequestIds.length > 0;

      return {
        ...offer,
        requested: hasActiveRequests,
      };
    });

    return [...userOffers, ...processedOtherOffers];
  } catch (error) {
    rollbar.error(error);
    throw error;
  }
};

export default getOffers;
