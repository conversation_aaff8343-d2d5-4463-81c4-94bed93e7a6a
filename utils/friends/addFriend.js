import EMAILS from '../../constants/emails';
import adminClient from '../../graphql/adminClient';
import { CREATE_NOTIFICATION } from '../../graphql/mutations/notification';
import { GET_USER_EMAIL } from '../../graphql/queries/user';
import createOneToOneChannel from '../chat-v2/createOneToOneChannel';
import pgPoolQuery from '../db/pgQuery';
import mutualFriendsDetails from './mutualFriendsDetails';
import UserInfo from './userInfo';

/**
 * Add two users as friends
 * @param {*} param0
 * @returns
 */
const addFriend = async ({
  host = '',
  requester = '',
  isMigrated = false,
  isSystemAdded = false,
  sendNotification = true,
}) => {
  try {
    //Find if there is friend request exist between the users
    let requestData = [];
    requestData = await pgPoolQuery(
      `SELECT *
            FROM friend_requests
            WHERE (receiver_id = $2 AND sender_id = $1 ) 
            OR (receiver_id = $1 AND sender_id = $2) `,
      [host, requester],
    );

    if (requestData && !requestData.length) {
      //Get user details to add in friends in table.
      const usersData = await pgPoolQuery(
        `SELECT
                u.id,
                u.first_name,
                u.last_name,
                u.phone, u.email,
                u."profilePhoto",
                pnu.private_network_id,
                    array_agg(c.name) AS "userClubs"
                FROM "user" u
                LEFT JOIN user_club AS uc ON uc.user_id = u.id
                LEFT JOIN courses c ON c.id = uc.club_id
                LEFT JOIN private_network_user pnu ON pnu.user_id = u.id
                WHERE u.id IN ('${host}', '${requester}')
                    AND u.account_activated = true
                    AND u.activate_later = false
                    AND u.deactivated = false
                GROUP BY uc.user_id, u.id, pnu.private_network_id`,
        [host, requester],
      );

      if (usersData && usersData.length !== 2) {
        return 'One or both users are inactive.';
      }

      let requesterData = {},
        hostData = {};
      if (usersData[0].id === host) {
        hostData = usersData[0];
        requesterData = usersData[1];
      } else {
        hostData = usersData[1];
        requesterData = usersData[0];
      }

      let sender_info = new UserInfo(requesterData),
        receiver_info = new UserInfo(hostData);

      requestData.push({
        sender_id: requesterData?.id,
        receiver_id: hostData?.id,
        request_notes: '',
        sender_info,
        receiver_info,
      });
    }

    const {
      sender_id,
      receiver_id,
      request_notes,
      sender_info,
      receiver_info,
    } = requestData[0];

    const { streamChannelId } = await createOneToOneChannel(
      sender_id,
      receiver_id,
    );

    //Insert in friends table
    await pgPoolQuery(
      `INSERT 
            INTO friends 
            (
                sender_id,
                receiver_id,
                request_notes,
                sender_info,
                receiver_info,
                stream_channel_id,
                is_migrated,
                is_system_added
            )
            VALUES($1, $2, $3, $4, $5, $6, $7, $8)`,
      [
        sender_id,
        receiver_id,
        request_notes,
        sender_info,
        receiver_info,
        streamChannelId,
        isMigrated,
        isSystemAdded,
      ],
    );

    //Delete friend request if it exist and send a notification
    if (requestData && requestData.length) {
      await pgPoolQuery(`DELETE FROM friend_requests WHERE id = $1`, [
        requestData[0]?.id,
      ]);

      let notification = {
        user_id: sender_id,
        type: 'friend-request-accepted',
        data: {
          requestId: requestData[0]?.id,
          userName: receiver_info?.name,
          userPhoto: receiver_info?.profilePhoto,
        },
        message: `${receiver_info.name} has accepted your friend request 🏌️‍♂️`,
        html_message: `<b>${receiver_info.name}</b> has accepted your friend request 🏌️‍♂️`,
      };

      if (sendNotification) {
        await adminClient.request(CREATE_NOTIFICATION, {
          notifications: [notification],
        });
      }
    }

    /******** Send a push notification to their mutual friends  ********/
    const [mutualFriendsList, senderInfo, receiverInfo] = await Promise.all([
      mutualFriendsDetails(sender_id, receiver_id),
      adminClient.request(GET_USER_EMAIL, {
        id: sender_id,
      }),
      adminClient.request(GET_USER_EMAIL, {
        id: receiver_id,
      }),
    ]);

    for (let mutualFriend of mutualFriendsList) {
      if (mutualFriend?.fcm_token) {
        let notification = {
          token: mutualFriend?.fcm_token,
          data: {
            user1: sender_id,
            user2: receiver_id,
          },
          type: EMAILS.MUTUAL_FRIENDS_CONNECTED.PUSH.type,
          html_message: EMAILS.MUTUAL_FRIENDS_CONNECTED.PUSH.html_message(
            senderInfo?.user_by_pk?.first_name,
            receiverInfo?.user_by_pk?.first_name,
          ),
          message: EMAILS.MUTUAL_FRIENDS_CONNECTED.PUSH.message(
            senderInfo?.user_by_pk?.first_name,
            receiverInfo?.user_by_pk?.first_name,
          ),
          is_push_required: false,
        };
        adminClient.request(CREATE_NOTIFICATION, {
          notifications: [notification],
        });
      }
    }

    return {
      message: 'Host and Requester are now friends!!',
    };
  } catch (error) {
    console.log(error);
    return error;
  }
};

export default addFriend;
