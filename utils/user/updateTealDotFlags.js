import pgPoolQuery from '../db/pgQuery';
import constantOptions from '../../constants/constantOptions';
import { invalidateTealDotCacheMultiple } from '../redis/tealDotCacheInvalidation';

const { TEAL_DOT_FLAG_KEYS } = constantOptions;

/**
 * Updates teal_dot_flags for one or multiple users
 * @param {Object} params - Parameters object
 * @param {string} params.flagKey - The key to update within teal_dot_flags (e.g., 'offer', 'message')
 * @param {string|string[]} params.userIds - Single user ID or array of user IDs to update
 * @param {boolean|number|string|Object} [params.value=true] - The value to set for the flag (defaults to true)
 * @returns {Promise<Object>} Result object with success status and error if any
 */
const updateTealDotFlags = async ({ flagKey, userIds, value = true }) => {
  if (!flagKey) {
    return { success: false, error: 'Flag key is required' };
  }

  // Validate if the flag key is one of the predefined keys
  const validKeys = Object.values(TEAL_DOT_FLAG_KEYS);
  if (!validKeys.includes(flagKey)) {
    console.warn(
      `Warning: Using non-standard teal dot flag key: "${flagKey}". Valid keys are: ${validKeys.join(
        ', ',
      )}`,
    );
  }

  if (!userIds || (Array.isArray(userIds) && userIds.length === 0)) {
    return { success: false, error: 'At least one user ID is required' };
  }

  // Convert single user ID to array for consistent processing
  const userIdArray = Array.isArray(userIds) ? userIds : [userIds];

  try {
    let batchSize = 100;
    // Process users in batches to avoid overwhelming the database
    for (let i = 0; i < userIdArray.length; i += batchSize) {
      const batch = userIdArray.slice(i, i + batchSize).filter(Boolean);

      if (batch.length === 0) continue;

      // Convert value to JSON string for PostgreSQL
      const valueStr = JSON.stringify(value);

      // Use a single query that handles both update and insert in one transaction
      await pgPoolQuery(
        `
                -- Update existing records
                UPDATE user_informations
                SET teal_dot_flags = jsonb_set(
                    COALESCE(teal_dot_flags, '{}'::jsonb),
                    '{${flagKey}}',
                    '${valueStr}'::jsonb,
                    true
                )
                WHERE user_id = ANY($1::uuid[]);
                
                -- Insert records for users that don't have one yet
                INSERT INTO user_informations (user_id, teal_dot_flags)
                SELECT u, jsonb_build_object('${flagKey}', '${valueStr}'::jsonb)
                FROM unnest($1::uuid[]) AS u
                WHERE NOT EXISTS (
                SELECT 1 FROM user_informations WHERE user_id = u
                );
                `,
        [batch],
      );

      // Invalidate cache for this batch of users
      await invalidateTealDotCacheMultiple(batch);
    }

    return { success: true };
  } catch (error) {
    console.error(`Error updating teal_dot_flags (${flagKey}):`, error);
    return {
      success: false,
      error: error.message || 'Unknown error occurred',
    };
  }
};

export default updateTealDotFlags;
