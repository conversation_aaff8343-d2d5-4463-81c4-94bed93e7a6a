import END_POINTS from '../../constants/endpoints.json';

/**
 * Fetches game reviews for a specific club
 * 
 * @param {Object} params - Parameters for fetching game reviews
 * @param {string} params.token - Authentication token
 * @param {string} params.userId - ID of the current user
 * @param {string} params.clubId - ID of the club to fetch reviews for
 * @param {number} params.page - Page number for pagination (default: 0)
 * @param {number} params.limit - Number of reviews to fetch per page (default: 3)
 * @returns {Promise<Object>} - Promise resolving to game reviews data
 */
const fetchUserGameReviews = async ({ token, userId, golferId, page = 0, limit = 3 }) => {
  try {
    const response = await fetch(END_POINTS.GET_GAME_REVIEWS_FOR_USER, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ['Authorization']: `Bearer ${token}`,
      },
      body: JSON.stringify({
        userId,
        golferId,
        page,
        limit
      }),
    });
    
    const data = await response.json();

    return {
      reviews: data?.data?.gameReviews || [],
      reviewsCount: data?.data?.pagination?.total || 0,
      hasMore: data?.data?.pagination?.hasNextPage || false,
    };
  } catch (error) {
    console.log('Error fetching user game reviews:', error);
    return { reviews: [], reviewsCount: 0 };
  }
};

export default fetchUserGameReviews;