import { Pool } from 'pg';
import rollbar from '../rollbar';

if (!global.db) {
  global.db = { pool: null };
}

export function connectionPool() {
  try {
    if (!global.db.pool) {
      global.db.pool = new Pool({
        connectionString: process.env.DATABASE_URL,
        ssl: {
          rejectUnauthorized: false,
        },
        max: 10,
      });
    }

    return global.db;
  } catch (error) {
    rollbar.info('connectionPool -->', error, process.env.DATABASE_URL);
  }
}
