import moment from 'moment'
import adminClient from '../../graphql/adminClient'
import { CREATE_NOTIFICATION } from '../../graphql/mutations/notification'
import rollbar from '../rollbar'
import sleepForLoop from '../helper/sleepForLoop'
import pgPoolQuery from '../db/pgQuery'

/**
 * Delete existing game reminder notifications for a specific game and user
 *
 * @param {string} user_id - The ID of the user
 * @param {number} game_id - The ID of the game
 * @returns {Promise<Object>} - Result of the delete operation
 */
const deleteExistingGameReminders = async (user_id, game_id) => {
    try {
        const result = await pgPoolQuery(
            `
            DELETE FROM notification
            WHERE user_id = $1
            AND type = 'game-reminder'
            AND data->>'game_id' = $2
            AND type = 'game-reminder'
            RETURNING id;
        `,
            [user_id, game_id.toString()]
        )
        return result
    } catch (error) {
        rollbar.error('Error deleting existing game reminders:', error)
        return []
    }
}

/**
 * Utility function to send daily reminders for upcoming games
 * Sends notifications for games 7 days to 1 day away
 *
 * @returns {Object} Results of the operation
 */
const dailyGameReminders = async () => {
    try {
        const results = {
            hostsNotified: 0,
            requestersNotified: 0,
            errors: 0,
            totalGames: 0,
            gamesToday: 0,
            oldRemindersDeleted: 0,
        }

        const games = await pgPoolQuery(`
            SELECT g.game_id,
            g.date::TEXT AS date,
            g.status,
            g.host_id,
            r.user_id,
            c.name,
            r.id as request_id
            FROM game g
            LEFT JOIN request r ON r.game_id = g.game_id
            LEFT JOIN courses c ON c.id = r.club_id
            WHERE g."hostCompleted" = false 
            AND g."requestorCompleted" = false 
            AND g.date BETWEEN CURRENT_DATE AND CURRENT_DATE + INTERVAL '7 days'
            AND g.status <> 'declined'
            AND r.status <> 'cancelled';`)

        let i = 0,
            notifications = []

        results.totalGames = games?.length
        for (const game of games) {
            try {
                const { game_id, date, host_id, user_id, name, request_id } =
                    game

                // Add a small delay between notifications to avoid rate limits
                await sleepForLoop(undefined, i++)

                let daysToGame = moment(date)
                    .startOf('day')
                    .diff(moment().startOf('day'), 'days')

                if (daysToGame === 0) {
                    results.gamesToday++
                    continue
                }

                // Delete existing game reminders for both users
                const deletedRequesterReminders =
                    await deleteExistingGameReminders(user_id, game_id)
                const deletedHostReminders = await deleteExistingGameReminders(
                    host_id,
                    game_id
                )

                // Count deleted reminders
                results.oldRemindersDeleted +=
                    deletedRequesterReminders.length +
                    deletedHostReminders.length

                let message = ''
                if (daysToGame === 7) {
                    message = `You have an upcoming game at ${name} in 7 days.`
                } else if (daysToGame === 1) {
                    message = `You have an upcoming game at ${name} in ${daysToGame} day.`
                } else {
                    message = `You have an upcoming game at ${name} in ${daysToGame} days.`
                }

                //Notification for requester
                notifications.push({
                    user_id: user_id,
                    type: 'game-reminder',
                    message,
                    data: {
                        game_id,
                        request_id,
                    },
                })

                //Notification for host
                notifications.push({
                    user_id: host_id,
                    type: 'game-reminder',
                    message,
                    data: {
                        game_id,
                        request_id,
                    },
                })

                results.hostsNotified++
                results.requestersNotified++
            } catch (error) {
                console.log('error :', error)
                results.errors++
            }
        }

        if (notifications.length > 0) {
            await adminClient.request(CREATE_NOTIFICATION, {
                notifications,
            })
        }

        return {
            results,
        }
    } catch (error) {
        console.error('Error in daily game reminders:', error)
        rollbar.error('Error in daily game reminders:', error)

        return {
            status: 0,
            error: error.toString(),
        }
    } finally {
        rollbar.info('-- Ran Daily Game Reminders --')
    }
}

export default dailyGameReminders
