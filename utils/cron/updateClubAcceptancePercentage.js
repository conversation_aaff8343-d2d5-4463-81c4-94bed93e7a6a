import pgPoolQuery from '../db/pgQuery';
import rollbar from '../rollbar';
import { sendFailureMail } from '../../pages/api/cron/dailyCron';

/**
 * Cron function to calculate and update acceptance percentage for all clubs
 * Formula: (Total Games Created / Total Requests) * 100 for each club
 * @returns {Object} - Result object with success/error status
 */
const updateClubAcceptancePercentage = async () => {
  let processedClubs = 0;
  let updatedClubs = 0;
  let errors = [];

  try {
    // Process clubs in batches of 100 for better performance
    const batchSize = 100;
    let offset = 0;
    let hasMoreClubs = true;

    while (hasMoreClubs) {
      // Get batch of active clubs
      const clubs = await pgPoolQuery(
        `
                SELECT id, name 
                FROM courses 
                WHERE deactivated = false
                AND club_type = 0
                ORDER BY id 
                LIMIT $1 OFFSET $2
            `,
        [batchSize, offset],
      );

      if (!clubs || clubs.length === 0) {
        hasMoreClubs = false;
        break;
      }

      // Calculate acceptance percentage for each club in batch
      const clubIds = clubs.map((club) => club.id);

      const acceptanceData = await pgPoolQuery(
        `
                WITH club_stats AS (
                    SELECT
                    r.club_id,
                    COUNT(r.id) AS total_requests,
                    COUNT(g.game_id) AS accepted_requests
                    FROM
                    request r
                    LEFT JOIN
                    game g ON r.id = g.request_id
                    GROUP BY
                    r.club_id
                )
                SELECT 
                    club_id,
                    total_requests,
                    accepted_requests,
                    ROUND(accepted_requests::numeric / total_requests::numeric * 100, 2) AS acceptance_percentage
                FROM club_stats
                WHERE club_id = ANY($1)
            `,
        [clubIds],
      );

      // Update acceptance percentage for each club
      for (const clubData of acceptanceData) {
        try {
          await pgPoolQuery(
            `
                        UPDATE courses 
                        SET acceptance_percentage = $1,
                            updated_at = NOW()
                        WHERE id = $2
                    `,
            [clubData.acceptance_percentage, clubData.club_id],
          );

          updatedClubs++;
        } catch (updateError) {
          errors.push({
            clubId: clubData.club_id,
            error: updateError.message,
          });
        }
      }

      processedClubs += clubs.length;
      offset += batchSize;

      // Add small delay between batches to avoid overwhelming the database
      await new Promise((resolve) => setTimeout(resolve, 100));
    }

    const result = {
      success: true,
      processedClubs,
      updatedClubs,
      errors: errors.length > 0 ? errors : null,
      timestamp: new Date().toISOString(),
    };

    rollbar.info('Cron: Club Acceptance Percentage Update Completed', result);
    console.log('-- Club Acceptance Percentage Update Completed --');
    return result;
  } catch (error) {
    const errorType = {
      functionName: updateClubAcceptancePercentage.name,
      errorMsg: error.toString(),
      processedClubs,
      updatedClubs,
      errors,
    };

    rollbar.error('Cron: Club Acceptance Percentage Update Failed', errorType);
    await sendFailureMail(errorType);

    return {
      success: false,
      ...errorType,
    };
  }
};

export default updateClubAcceptancePercentage;
