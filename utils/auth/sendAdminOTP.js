const { sendMailTemplate } = require('../mailchimp');
import EMAILS from '../../constants/emails';
const { default: constantOptions } = require('../../constants/constantOptions');

/**
 * Sends an OTP email to an admin user
 * @param {string} email - Admin's email address
 * @param {string} otp - Generated OTP
 * @returns {Promise<Object>} - Result of email sending operation
 */
async function sendAdminOTP(email, otp) {
  try {
    const result = await sendMailTemplate({
      email,
      template_name: EMAILS.ADMIN_2FA.template,
      subject: EMAILS.ADMIN_2FA.subject,
      template_content: [
        {
          name: 'otp',
          content: otp,
        },
        {
          name: 'expires_in',
          content: `${constantOptions.ADMIN_OTP_EXPIRY_MINUTES} minutes`,
        },
      ],
    });

    return result;
  } catch (error) {
    console.error('Error sending admin OTP email:', error);
    throw error;
  }
}

module.exports = sendAdminOTP;
