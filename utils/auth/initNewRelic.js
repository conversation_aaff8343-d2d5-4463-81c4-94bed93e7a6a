let newrelicInstance = null;

export async function initNewRelic() {
    if (typeof window === 'undefined') {
        return null; // Prevent execution on server
    }

    try {
        // Fix: if window.NREUM was corrupted to false
        if (typeof window.NREUM !== 'object' || window.NREUM === null) {
            window.NREUM = {};
        }

        if (!newrelicInstance) {
            const { BrowserAgent } = await import('@newrelic/browser-agent/loaders/browser-agent');

            const newrelicOptions = {
                init: {
                    distributed_tracing: { enabled: true },
                    privacy: { cookies_enabled: true },
                    ajax: { deny_list: [process.env.NEXT_PUBLIC_NEW_RELIC_BEACON] },
                },
                info: {
                    accountID: process.env.NEXT_PUBLIC_NEW_RELIC_ACCOUNT_ID,
                    trustKey: process.env.NEXT_PUBLIC_NEW_RELIC_ACCOUNT_ID,
                    agentID: process.env.NEXT_PUBLIC_NEW_RELIC_AGENT_ID,
                    licenseKey: process.env.NEXT_PUBLIC_NEW_RELIC_LICENSE_KEY,
                    applicationID: process.env.NEXT_PUBLIC_NEW_RELIC_AGENT_ID,
                    beacon: process.env.NEXT_PUBLIC_NEW_RELIC_BEACON,
                    errorBeacon: process.env.NEXT_PUBLIC_NEW_RELIC_BEACON,
                },
                loader_config: {
                    accountID: process.env.NEXT_PUBLIC_NEW_RELIC_ACCOUNT_ID,
                    trustKey: process.env.NEXT_PUBLIC_NEW_RELIC_ACCOUNT_ID,
                    agentID: process.env.NEXT_PUBLIC_NEW_RELIC_AGENT_ID,
                    licenseKey: process.env.NEXT_PUBLIC_NEW_RELIC_LICENSE_KEY,
                    applicationID: process.env.NEXT_PUBLIC_NEW_RELIC_AGENT_ID,
                    sa: 1,
                },
            };

            newrelicInstance = new BrowserAgent(newrelicOptions);
        }

        return newrelicInstance;
    } catch (error) {
        console.error("Failed to initialize New Relic:", error);
        return null;
    }
}


