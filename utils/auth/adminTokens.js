const crypto = require('crypto');
const { default: pgPoolQuery } = require('../db/pgQuery');

// Constants
const TOKEN_EXPIRY_DAYS = 30;
const OTP_EXPIRY_MINUTES = 30;

/**
 * Hash sensitive data like IP and user agent
 */
function hashData(data) {
  return crypto.createHash('sha256').update(data).digest('hex');
}

/**
 * Get device token by user agent and IP
 */
async function getDeviceTokenByIdentifiers(adminId, deviceId, ipAddress) {
  const ipHash = hashData(ipAddress);

  const [token] = await pgPoolQuery(
    `SELECT device_token, expires_at 
     FROM admin_device_tokens 
     WHERE admin_id = $1 
     AND device_id = $2 
     AND ip_hash = $3 

     AND expires_at > NOW() 
     AND NOT is_revoked
     ORDER BY created_at DESC
     LIMIT 1`,
    [adminId, deviceId, ipHash],
  );

  if (token) {
    return {
      deviceToken: token.device_token,
      expiresAt: token.expires_at,
    };
  }

  return null;
}

/**
 * Generate a 6-digit OTP
 */
async function generateOTP(adminId, adminEmail, deviceId) {
  const otp = crypto.randomInt(100000, 999999).toString();
  const expiresAt = new Date();
  expiresAt.setMinutes(expiresAt.getMinutes() + OTP_EXPIRY_MINUTES);

  await pgPoolQuery(
    `INSERT INTO admin_2fa_tokens 
     (admin_id, otp, expires_at, admin_email, device_id) 
     VALUES ($1, $2, $3, $4, $5)
     ON CONFLICT (device_id) 
     DO UPDATE SET 
       otp = EXCLUDED.otp,
       expires_at = EXCLUDED.expires_at,
       created_at = NOW()`,
    [adminId, otp, expiresAt, adminEmail, deviceId],
  );

  return otp;
}

/**
 * Verify OTP and create device token if valid
 */
async function verifyOTPAndCreateToken(
  adminId,
  otp,
  userAgent,
  ipAddress,
  rememberDevice,
  deviceId,
) {
  // Get and verify OTP
  const [otpRecord] = await pgPoolQuery(
    `SELECT * FROM admin_2fa_tokens 
     WHERE admin_id = $1 
     AND otp = $2 
     AND device_id = $3
     AND expires_at > NOW()`,
    [adminId, otp, deviceId],
  );

  if (!otpRecord) {
    return { isValid: false };
  }

  // Hash device identifiers
  // const userAgentHash = hashData(userAgent);
  const ipHash = hashData(ipAddress);

  // Create new device token
  const deviceToken = crypto.randomBytes(32).toString('hex');
  const tokenExpiresAt = new Date();
  tokenExpiresAt.setDate(tokenExpiresAt.getDate() + TOKEN_EXPIRY_DAYS);

  if (rememberDevice) {
    await pgPoolQuery(
      `INSERT INTO admin_device_tokens 
       (admin_id, device_token, user_agent, ip_hash, expires_at, device_id) 
       VALUES ($1, $2, $3, $4, $5, $6)`,
      [adminId, deviceToken, userAgent, ipHash, tokenExpiresAt, deviceId],
    );
  }

  // Delete used OTP
  await pgPoolQuery('DELETE FROM admin_2fa_tokens WHERE admin_id = $1', [
    adminId,
  ]);

  return {
    isValid: true,
    deviceToken,
    expiresAt: tokenExpiresAt,
  };
}

/**
 * Check if a valid device token exists
 */
async function checkDeviceToken(adminId, deviceToken, userAgent, ipAddress) {
  if (!deviceToken) return null;

  const ipHash = hashData(ipAddress);

  const [token] = await pgPoolQuery(
    `SELECT * FROM admin_device_tokens 
     WHERE admin_id = $1 
     AND device_token = $2
     AND ip_hash = $3 
     AND expires_at > NOW() 
     AND NOT is_revoked`,
    [adminId, deviceToken, ipHash],
  );

  return token || null;
}

/**
 * Revoke a device token
 */
async function revokeDeviceToken(adminId, deviceToken) {
  await pgPoolQuery(
    `UPDATE admin_device_tokens 
     SET is_revoked = true, updated_at = NOW() 
     WHERE admin_id = $1 AND device_token = $2`,
    [adminId, deviceToken],
  );
}

module.exports = {
  generateOTP,
  verifyOTPAndCreateToken,
  checkDeviceToken,
  revokeDeviceToken,
  getDeviceTokenByIdentifiers,
  TOKEN_EXPIRY_DAYS,
};
