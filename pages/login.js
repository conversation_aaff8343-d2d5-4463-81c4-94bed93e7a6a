import { useState, useEffect } from 'react'
import firebase from 'firebase/compat/app'
import 'firebase/compat/auth'
import * as Yup from 'yup'
import { Formik, Form as FormikForm, Field } from 'formik'
import { useRouter } from 'next/router'
import Loader, { ThreeDots } from 'react-loader-spinner'
import Link from 'next/link'
import Logo from '../components/layout/Logo'
import END_POINTS from '../constants/endpoints.json'
import MESSAGES from '../constants/messages'
import Verify2FaModal from '../components/common/signup2/Verify2FaModal'
import uuidGenerator from '../utils/helper/uuidGenerator'

function Login() {
    const router = useRouter()
    const [error, setError] = useState()
    const [loading, setLoading] = useState(false)
    const [showOtpModal, setShowOtpModal] = useState(false)
    const [email, setEmail] = useState('')
    const [password, setPassword] = useState('')

    const handleSubmit = (values) => {
        // if (values.email === process.env.NEXT_PUBLIC_ADMIN_EMAIL) {
        //     setEmail(values?.email)
        //     setPassword(values?.password)
        //     if (!localStorage.getItem('device_id')) {
        //         localStorage.setItem('device_id', uuidGenerator())
        //     }
        //     checkAdmin(values)
        // } else {
            loginUser(values)
        // }
    }

    const checkAdmin = async (values) => {
        setLoading(true)
        const response = await fetch(END_POINTS.ADMIN_TOKEN_CHECK, {
            method: 'POST',
            credentials: 'same-origin',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ email: values?.email, deviceId: localStorage.getItem('device_id') }),
        })
            .then((data) => data.json())
            .then((data) => {
                if (data?.requires2FA) {
                    setShowOtpModal(true)
                } else {
                    loginUser(values)
                }
                setLoading(false)
            })
            .catch(console.log)
    }

    async function loginUser(values) {
        setLoading(true)
        const response = await fetch(END_POINTS.GET_EMAIL_BY_USERNAME, {
            method: 'POST',
            credentials: 'same-origin',
            body: JSON.stringify({ username: values.email }),
        })
            .then((data) => data.json())
            .catch(console.log)

        if (response?.deactivated) {

            setError(MESSAGES?.login?.deactivated)
            setLoading(false)
        } else if (response?.email || values?.email === '<EMAIL>') {

            firebase
                .auth()
                .signInWithEmailAndPassword(
                    response.email ? response.email : '<EMAIL>',
                    values.password
                )
                .then((user) => {
                    setLoading(false)
                })
                .catch((e) => {
                    if (e?.code === "auth/too-many-requests") {

                        setError(e?.message)
                    } else {

                        setError(MESSAGES?.login?.incorrect)
                    }
                    setLoading(false)
                })
        } else {
            setError(MESSAGES?.login?.incorrect)
            setLoading(false)
        }
    }

    const validationSchema = Yup.object().shape({
        email: Yup.string().required(),
        password: Yup.string().required(),
    })
    return (
        <div
            className="w-screen h-screen flex-center flex-col font-ubuntu py-xl px-lg"
            style={{
                backgroundImage: `linear-gradient(180deg, rgba(0, 0, 0, 0.8) 2.34%, rgba(0, 0, 0, 0.16) 100%), url("/images/LoginBackground.svg")`,
                backgroundSize: 'cover',
                backgroundPosition: 'center',
            }}>
            <Logo white height={75} />
            <img
                className="my-46"
                src="/images/clubs.svg"
                style={{ width: 20, height: 30 }}
            />
            <div className="text-2xl font-normal text-white">
                Login to your account
            </div>
            <Formik
                validateOnBlur={false}
                validateOnChange={false}
                initialValues={{ email: '', password: '' }}
                onSubmit={handleSubmit}
                validationSchema={validationSchema}>
                {({ values, errors, setFieldValue }) => {
                    return (
                        <FormikForm className="flex flex-1 justify-items-center flex-col pt-xxl">
                            <div className='border-b border-white '>
                                <input
                                    className="px-sm bg-transparent self-center text-white text-16 font-normal"
                                    placeholder="Email/Username"
                                    value={values.email}
                                    onChange={({ target: { value } }) =>
                                        setFieldValue('email', value.trim())
                                    }
                                    style={{
                                        marginBottom: errors.email ? 0 : 5,
                                        width: 330,
                                        height: 40,
                                    }}
                                />
                            </div>

                            {errors.email && (
                                <div
                                    className="flex-center"
                                    style={{
                                        height: 40,
                                    }}>
                                    <div className="px-md rounded shadow text-12 bg-red text-white font-normal py-1">
                                        {errors.email}
                                    </div>
                                </div>
                            )}
                            <div className='border-b border-white mt-md'>
                                <input
                                    className="px-sm bg-transparent self-center text-white text-16 font-normal"
                                    placeholder="Password"
                                    type="password"
                                    autoComplete='off'
                                    value={values.password}
                                    onChange={({ target: { value } }) =>
                                        setFieldValue('password', value)
                                    }
                                    style={{
                                        marginBottom: 5,
                                        width: 330,
                                        height: 40,
                                    }}
                                />
                            </div>
                            {errors.password && (
                                <div
                                    className="flex-center"
                                    style={{
                                        height: 40,
                                    }}>
                                    <div className="px-md rounded shadow text-12 bg-red text-white font-normal py-1">
                                        {errors.password}
                                    </div>
                                </div>
                            )}
                            {error && (
                                <div
                                    className="flex-center"
                                    style={{
                                        height: 40,
                                    }}>
                                    <div className="px-md rounded shadow text-12 bg-red text-white font-normal py-1">
                                        {error}
                                    </div>
                                </div>
                            )}
                            <button
                                type="submit"
                                className="text-16 bg-darkteal self-center text-white rounded-lg p-md flex-center mt-md font-ubuntu"
                                style={{
                                    width: 220,
                                    height: 54,
                                }}>
                                {loading ? (
                                    <ThreeDots
                                        visible={true}
                                        height="25"
                                        width="25"
                                        color="#FFFFFF"
                                        radius="9"
                                        ariaLabel="three-dots-loading"
                                        wrapperStyle={{}}
                                        wrapperClass=""
                                    />
                                ) : (
                                    'Login'
                                )}
                            </button>
                            <Link href="/reset-password">
                                <div className="ml-sm text-center font-bold cursor-pointer hover:underline text-white mt-lg text-14">
                                    Reset Password
                                </div>
                            </Link>
                            <div className="font-thin flex-center text-white text-14 flex mt-md">
                                Don't have an account?{' '}
                                <Link href="/membership/welcome">
                                    <div className="ml-sm font-bold cursor-pointer hover:underline">
                                        Create One
                                    </div>
                                </Link>
                            </div>
                        </FormikForm>
                    )
                }}
            </Formik>
            {
                showOtpModal &&
                <Verify2FaModal
                    email={email}
                    password={password}
                    setModalVisible={setShowOtpModal}
                    checkAdmin={checkAdmin}
                />
            }
        </div>
    )
}

export default Login
