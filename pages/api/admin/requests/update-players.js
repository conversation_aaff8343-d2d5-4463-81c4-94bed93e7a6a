import Joi from 'joi';
import MESSAGES from '../../../../constants/messages';
import rollbar from '../../../../utils/rollbar';
import pgPoolQuery from '../../../../utils/db/pgQuery';
import adminGuard from '../../../../utils/auth/adminGuard';

const schema = Joi.object().keys({
  requestId: Joi.string().trim().required(),
  players: Joi.number().max(4).min(1),
});

/**
 * Admin API to update number of players in a request
 * @param {*} req
 * @param {*} res
 * @returns
 */
const updatePlayers = async (req, res) => {
  try {
    // Validate request body
    let validate = schema.validate(req.body);

    if (validate.error) {
      let error = validate.error.details[0].message;
      return res.status(400).send({
        status: 0,
        message: MESSAGES[400],
        error,
      });
    }

    const { requestId, players } = validate.value;

    const request = await pgPoolQuery(
      `
            UPDATE request
            SET number_of_players = $1
            WHERE id = $2
            RETURNING id;
        `,
      [players, requestId],
    );

    if (!request?.length) {
      return res.status(404).send({
        status: 0,
        message: MESSAGES[404],
      });
    }

    return res.status(200).send({
      status: 1,
      message: MESSAGES[200],
    });
  } catch (error) {
    rollbar.error(error, req);
    return res.status(500).send({
      message: `${MESSAGES['500']}: ${error}`,
      status: 0,
    });
  }
};

export default adminGuard(updatePlayers);
