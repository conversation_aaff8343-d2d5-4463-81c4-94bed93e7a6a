import Joi from 'joi';
import MESSAGES from '../../../../constants/messages';
import pgPoolQuery from '../../../../utils/db/pgQuery';
import constantOptions from '../../../../constants/constantOptions';
import adminGuard from '../../../../utils/auth/adminGuard';
import rollbar from '../../../../utils/rollbar';

const schema = Joi.object().keys({
  search: Joi.string().required().trim(),
});

/**
 * API to search host to complete the request by admin
 * @param {*} req
 * @param {*} res
 * @returns
 */

const searchHosts = async (req, res) => {
  try {
    const { search, requestId } = req?.body;
    const hostsData = await pgPoolQuery(
      `
            SELECT hosts_sent
            FROM request
            WHERE id = '${requestId}'
            `,
    );

    const { hosts_sent } = hostsData[0];

    let hostIds = hosts_sent.map((id) => {
      return `'${id}'`;
    });
    let finalId = hostIds.join(', ');

    const hostData = await pgPoolQuery(
      `
        SELECT username, first_name, last_name, email, id
        FROM "user"
        WHERE id IN (${finalId}) 
        AND deleted_at IS NULL 
        AND (username ILIKE $2
        OR first_name ILIKE $2
        OR last_name LIKE $2);
        `,
      [finalId, search],
    );
    console.log('hostData :', hostData);

    return res.send({
      status: 1,
      message: MESSAGES[200],
      hostData,
    });
  } catch (error) {
    rollbar.error(error, req);
    return res.status(500).send({
      message: `${MESSAGES['500']}: ${error}`,
      status: 0,
    });
  }
};

export default adminGuard(searchHosts);
