import {
  generateOTP,
  getDeviceTokenByIdentifiers,
  TOKEN_EXPIRY_DAYS,
} from '../../../utils/auth/adminTokens';
import sendAdminOTP from '../../../utils/auth/sendAdminOTP';
import rollbar from '../../../utils/rollbar';
import MESSAGES from '../../../constants/messages';
import Joi from 'joi';

const schema = Joi.object({
  email: Joi.string().email().required(),
  deviceId: Joi.string().required(),
});

/**
 * API endpoint to check if 2FA is required for admin login
 *
 * Request body:
 * - email: string - Admin email
 *
 * Request headers:
 * - X-Device-Token: string (optional) - Previously issued device token
 *
 * Response:
 * - status: number - 1 for success, 0 for failure
 * - requires2FA: boolean - Whether 2FA is required
 * - message: string - Message indicating the result
 * - deviceToken: string (optional) - If device is recognized
 * - expiresAt: string (optional) - When the device token expires
 * - error: string (optional) - Error details if any
 */
export default async function handler(req, res) {
  try {
    // Validate request body
    let validate = schema.validate(req.body);

    if (validate.error) {
      let error = validate.error.details[0].message;
      return res.status(400).json({
        status: 0,
        message: MESSAGES[400] || 'Bad Request',
        error,
      });
    }

    const { email, deviceId } = validate.value;

    // Check admin role
    if (email !== process.env.NEXT_PUBLIC_ADMIN_EMAIL) {
      return res.status(200).json({
        status: 1,
        requires2FA: false,
        message: 'Not an admin email',
      });
    }

    // Get client identifiers
    const userAgent = req.headers['user-agent'];
    const ipAddress =
      req.headers['x-forwarded-for'] || req.socket.remoteAddress;

    const adminId = process.env.NEXT_PUBLIC_ADMIN_ID;

    // If no valid token provided, try to find one by device identifiers
    const existingToken = await getDeviceTokenByIdentifiers(
      adminId,
      deviceId,
      ipAddress,
    );
    if (existingToken) {
      return res.status(200).json({
        status: 1,
        requires2FA: false,
        message: 'Device recognized',
        deviceToken: existingToken.deviceToken,
        expiresAt: existingToken.expiresAt,
      });
    }

    // No valid device token found, generate and send OTP
    const otp = await generateOTP(adminId, email, deviceId);

    // Send OTP via email
    await sendAdminOTP(email, otp);

    return res.status(200).json({
      status: 1,
      requires2FA: true,
      message: 'OTP sent to email',
      tokenValidity: TOKEN_EXPIRY_DAYS,
    });
  } catch (error) {
    console.error('Admin token check error:', error);
    rollbar.error(error, req);
    return res.status(500).json({
      status: 0,
      message: MESSAGES[500] || 'Internal server error',
      error: error.message,
    });
  }
}
