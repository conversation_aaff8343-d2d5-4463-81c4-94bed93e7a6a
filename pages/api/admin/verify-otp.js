import { verifyOTPAndCreateToken } from '../../../utils/auth/adminTokens';
import rollbar from '../../../utils/rollbar';
import <PERSON><PERSON> from 'joi';
import MESSAGES from '../../../constants/messages';

const schema = Joi.object().keys({
  email: Joi.string().email().required(),
  otp: Joi.string().trim().required(),
  rememberDevice: Joi.boolean().optional().default(false),
  deviceId: Joi.string().required(),
});

/**
 * API endpoint to verify admin OTP and create device token
 *
 * Request body:
 * - email: string - Admin email
 * - otp: string - The OTP to verify
 * - rememberDevice: boolean - Whether to remember the device
 *
 * Response:
 * - status: number - 1 for success, 0 for failure
 * - message: string - Success/error message
 * - deviceToken: string - The device token (if rememberDevice is true)
 * - expiresAt: string - When the device token expires (if rememberDevice is true)
 * - error: string (optional) - Error details if any
 */
export default async function handler(req, res) {
  try {
    // Validate request body
    let validate = schema.validate(req.body);

    if (validate.error) {
      let error = validate.error.details[0].message;
      return res.status(400).json({
        status: 0,
        message: MESSAGES[400] || 'Bad Request',
        error,
      });
    }

    const { email, otp, rememberDevice, deviceId } = validate.value;

    const adminId = process.env.NEXT_PUBLIC_ADMIN_ID;

    if (email !== process.env.NEXT_PUBLIC_ADMIN_EMAIL) {
      return res.status(400).json({
        status: 0,
        message: MESSAGES[400] || 'Bad Request',
        error: 'Invalid email',
      });
    }

    // Get client identifiers
    const userAgent = req.headers['user-agent'];
    const ipAddress =
      req.headers['x-forwarded-for'] || req.socket.remoteAddress;

    // Verify OTP and create device token
    const result = await verifyOTPAndCreateToken(
      adminId,
      otp,
      userAgent,
      ipAddress,
      rememberDevice,
      deviceId,
    );

    if (!result.isValid) {
      return res.status(400).json({
        status: 0,
        message: 'Invalid or expired OTP',
        error: 'OTP verification failed',
      });
    }

    // Return success response with device token if rememberDevice is true
    return res.status(200).json({
      status: 1,
      message: 'OTP verified successfully',
      ...(rememberDevice && {
        deviceToken: result.deviceToken,
        expiresAt: result.expiresAt,
      }),
    });
  } catch (error) {
    console.error('OTP verification error:', error);
    rollbar.error(error, req);
    return res.status(500).json({
      status: 0,
      message: MESSAGES[500] || 'Internal server error',
      error: error.message,
    });
  }
}
