import <PERSON><PERSON> from 'joi';
import ME<PERSON><PERSON>ES from '../../../constants/messages';
import adminClient from '../../../graphql/adminClient';
import { sendMailTemplate } from '../../../utils/mailchimp';
import rollbar from '../../../utils/rollbar';
import adminGuard from '../../../utils/auth/adminGuard';

// Validation schema for request body
const schema = Joi.object().keys({
  subject: Joi.string().trim().min(1).required(),
  body: Joi.string().trim().min(1).required(),
});

// Configuration for batch processing
const BATCH_SIZE = 100; // Process 100 users at a time
const BATCH_DELAY_MS = 2000; // Wait 2 seconds between batches

// Query to get all active users
const GET_ACTIVE_USERS = `
{
  user(where: {
    deleted_at: {_is_null: true}, 
    account_activated: {_eq: true},
    deactivated: {_eq: false}
  }
    ) {
    id
    email
    first_name
    last_name
    username
  }
}`;

/**
 * API to send broadcast email to all active TG users
 * Independent of mute settings, email preferences, or mailchimp subscription status
 * @param {*} req
 * @param {*} res
 * @returns
 */
const broadcastEmail = async (req, res) => {
  try {
    // Validate request body
    const validate = schema.validate(req.body);

    if (validate.error) {
      const error = validate.error.details[0].message;
      return res.status(400).send({
        status: 0,
        message: MESSAGES[400],
        error,
      });
    }

    const { subject, body } = validate.value;

    // Get all active users
    const { user: allUsers } = await adminClient.request(GET_ACTIVE_USERS);

    if (!allUsers || allUsers.length === 0) {
      return res.status(404).send({
        status: 0,
        message: 'No active users found',
      });
    }

    // Send immediate response to frontend
    res.status(200).send({
      status: 1,
      message: 'Email broadcast initiated successfully',
      data: {
        totalUsers: allUsers.length,
        estimatedBatches: Math.ceil(allUsers.length / BATCH_SIZE),
        estimatedTimeMinutes: Math.ceil(
          ((allUsers.length / BATCH_SIZE) * (BATCH_DELAY_MS / 1000)) / 60,
        ),
      },
    });

    // Process email sending in background
    processBroadcastEmail(allUsers, subject, body);
  } catch (error) {
    rollbar.error('Broadcast email error:', error);
    return res.status(500).send({
      message: `${MESSAGES['500']}: ${error}`,
      status: 0,
    });
  }
};

/**
 * Background function to process email sending in batches
 * @param {Array} users - Array of user objects
 * @param {string} subject - Email subject
 * @param {string} body - Email body content
 */
const processBroadcastEmail = async (users, subject, body) => {
  let successCount = 0;
  let failureCount = 0;
  const failedEmails = [];
  const startTime = new Date();

  try {
    // Process users in batches
    for (let i = 0; i < users.length; i += BATCH_SIZE) {
      const batch = users.slice(i, i + BATCH_SIZE);

      rollbar.info(
        `Processing email batch ${Math.floor(i / BATCH_SIZE) + 1}/${Math.ceil(users.length / BATCH_SIZE)}`,
        {
          batchSize: batch.length,
          totalProcessed: i + batch.length,
          totalUsers: users.length,
        },
      );

      // Prepare batch emails
      const batchEmails = batch.map((user) => ({
        email: user.email,
        name: user.first_name || user.username,
        type: 'to',
      }));

      try {
        // Send batch email using Mailchimp
        const result = await sendMailTemplate({
          email: batchEmails,
          subject: subject,
          template_name: 'TG Admin Notification',
          template_content: [
            {
              name: 'dynamic_content',
              content: body.replace(/\n/g, '<br>'),
            },
          ],
          multiple: true,
        });

        if (result.success) {
          successCount += batch.length;
          rollbar.info(`Batch email sent successfully`, {
            batchNumber: Math.floor(i / BATCH_SIZE) + 1,
            batchSize: batch.length,
            successCount,
          });
        } else {
          failureCount += batch.length;
          batch.forEach((user) => failedEmails.push(user.email));
          rollbar.error(`Batch email failed`, {
            batchNumber: Math.floor(i / BATCH_SIZE) + 1,
            batchSize: batch.length,
            error: result.message,
          });
        }
      } catch (batchError) {
        failureCount += batch.length;
        batch.forEach((user) => failedEmails.push(user.email));
        rollbar.error(`Batch email error`, {
          batchNumber: Math.floor(i / BATCH_SIZE) + 1,
          error: batchError.message,
        });
      }

      // Add delay between batches to avoid overwhelming the email service
      if (i + BATCH_SIZE < users.length) {
        await new Promise((resolve) => setTimeout(resolve, BATCH_DELAY_MS));
      }
    }

    const endTime = new Date();
    const processingTimeMinutes = Math.round((endTime - startTime) / 1000 / 60);

    // Send completion notification to admin
    await sendAdminNotification({
      subject,
      body,
      totalUsers: users.length,
      successCount,
      failureCount,
      failedEmails,
      processingTimeMinutes,
      startTime,
      endTime,
    });
  } catch (error) {
    rollbar.error('Broadcast email processing error:', error);

    // Send error notification to admin
    await sendAdminNotification({
      subject,
      body,
      totalUsers: users.length,
      successCount,
      failureCount,
      failedEmails,
      error: error.message,
      startTime,
      endTime: new Date(),
    });
  }
};

/**
 * Send completion notification to admin
 * @param {Object} data - Processing results data
 */
const sendAdminNotification = async (data) => {
  try {
    const adminEmail = process.env.NEXT_PUBLIC_ADMIN_EMAIL;

    const notificationSubject = data.error
      ? 'TG Broadcast Email - Processing Failed'
      : 'TG Broadcast Email - Processing Complete';

    const notificationBody = `
      <h2>Broadcast Email Processing ${data.error ? 'Failed' : 'Complete'}</h2>
      
      <h3>Email Details:</h3>
      <p><strong>Subject:</strong> ${data.subject}</p>
      <p><strong>Body Preview:</strong> ${data.body.substring(0, 200)}${data.body.length > 200 ? '...' : ''}</p>
      
      <h3>Processing Results:</h3>
      <p><strong>Total Users:</strong> ${data.totalUsers}</p>
      <p><strong>Successfully Sent:</strong> ${data.successCount}</p>
      <p><strong>Failed:</strong> ${data.failureCount}</p>
      <p><strong>Processing Time:</strong> ${data.processingTimeMinutes || 0} minutes</p>
      
      ${data.error ? `<p><strong>Error:</strong> ${data.error}</p>` : ''}
      
      ${
        data.failedEmails.length > 0
          ? `
        <h3>Failed Email Addresses (${data.failedEmails.length}):</h3>
        <p>${data.failedEmails.join(', ')}</p>
      `
          : ''
      }
      
      <hr>
      <p><em>This is an automated notification from the TG Admin Broadcast Email system.</em></p>
    `;

    await sendMailTemplate({
      email: adminEmail,
      subject: notificationSubject,
      template_name: 'TG Admin Notification',
      template_content: [
        {
          name: 'dynamic_content',
          content: notificationBody,
        },
      ],
    });
  } catch (notificationError) {
    rollbar.error('Failed to send admin notification:', notificationError);
  }
};

export default adminGuard(broadcastEmail);
