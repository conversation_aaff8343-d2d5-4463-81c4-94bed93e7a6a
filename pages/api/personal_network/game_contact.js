import pgPoolQuery from '../../../utils/db/pgQuery';
import checkFriend from '../../../utils/friends/checkFriend';
import getBlockStatus from '../../../utils/chat-v2/isBlocked';
import addFriend from '../../../utils/friends/addFriend';

// event trigger:
// https://thousandgreens-hasura.herokuapp.com/console/events/manage/triggers/create_game_add_personal_network_contact/processed

// As on 31 Jan 2022 running this event on UPDATE operation instead on INSERT.

export default async function gameContact(req, res) {
  const {
    event: {
      data: {
        old: {
          hostCompleted: oldHostCompleted,
          requestorCompleted: oldRequestorCompleted,
        },
        new: { game_id, requestorCompleted, hostCompleted },
      },
    },
  } = req.body;

  // If the game has been marked as completed by either requester or host, then only we have to add a personal contact row
  if (
    (!oldHostCompleted && hostCompleted && !requestorCompleted) ||
    (!oldRequestorCompleted && requestorCompleted && !hostCompleted)
  ) {
    //Get the host id and requester id
    const gameData = await pgPoolQuery(
      `
                SELECT host_id AS "hostId", r.user_id AS "requesterId"
                FROM game g
                LEFT JOIN request r ON r.game_id = g.game_id
                WHERE g.game_id = $1
            `,
      [game_id],
    );

    if (gameData && !gameData.length) {
      return res.send({
        status: 0,
        message: 'Unable to get game and request data.',
      });
    }

    let { hostId, requesterId } = gameData[0];

    let [{ count }] = await checkFriend({
      senderId: requesterId,
      receiverId: hostId,
    });

    // If the RnH are already friends
    if (!!parseInt(count)) {
      return res.send({
        status: 1,
        message: 'Both users are friends already.',
      });
    }

    //Check if RnH have not blocked each other
    const { isBlocked } = await getBlockStatus(requesterId, hostId);

    if (isBlocked) {
      return res.send({ status: 1, message: 'Users have blocked each other.' });
    }

    //Add both users in friends table
    const addFriendResponse = await addFriend({
      host: hostId,
      requester: requesterId,
      isSystemAdded: true,
      sendNotification: false,
    });

    return res.send({ status: 1, message: addFriendResponse });
  } else {
    res.send({
      status: 1,
      message: 'Nothing impacted.',
    });
  }
}
