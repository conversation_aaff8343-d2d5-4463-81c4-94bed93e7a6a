import { verifyIdToken } from '../../../utils/auth/firebaseAdmin';
import { FETCH_USER } from '../../../graphql/queries/user';
import { GraphQLClient } from 'graphql-request';
import rollbar from '../../../utils/rollbar';

export default async function fetchUser(req, res) {
  try {
    const { role } = req.query;
    const authorizationToken = req?.headers?.authorization || '';
    const token = authorizationToken?.split(' ')?.[1];
    let isValid = null;
    try {
      isValid = await verifyIdToken(token);
    } catch (error) {
      return res.status(401).send({ message: 'Token Expired' });
    }
    if (isValid) {
      const client = new GraphQLClient(process.env.CONFIG.graphQLEndpoint, {
        headers: {
          ['Authorization']: `Bearer ${token}`,
        },
      });

      if (role === 'user') {
        const {
          user: [user],
        } = await client.request(FETCH_USER, {
          user_id: isValid.uid,
        });

        const userClubs = user?.clubs?.map(({ club, ...profile }) => {
          return { ...profile, ...club };
        });

        return res.send({
          ...user,
          private_network:
            user.private_network.length === 1
              ? user.private_network[0].private_network
              : null,
          clubs: userClubs,
        });
      }

      if (['network-admin'].includes(role)) {
        return res.send({});
      }

      if (role === 'admin') {
        return res.send();
      }

      return res.send();
    } else {
      return res.send({ message: 'Error' });
    }
  } catch (error) {
    console.log('error :', error);
    rollbar.error(error, req);
    return res.status(500).send({ message: 'Error' });
  }
}
