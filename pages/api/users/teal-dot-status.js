import userGuardCheck from '../../../utils/auth/userGuardCheck';
import <PERSON><PERSON> from 'joi';
import MESSAGES from '../../../constants/messages';
import pgPoolQuery from '../../../utils/db/pgQuery';
import constantOptions from '../../../constants/constantOptions';
import rollbar from '../../../utils/rollbar';

const { TEAL_DOT_FLAG_KEYS } = constantOptions;

const schema = Joi.object().keys({
  userId: Joi.string().trim().required(),
});

/**
 * API to fetch the teal dot status for a user
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} Response with teal dot status
 */
const getTealDotStatus = async (req, res) => {
  try {
    res.setHeader(
      'Cache-Control',
      'public, s-maxage=1000000, stale-while-revalidate=59',
    );

    // Validate request body
    const validate = schema.validate(req.body);

    if (validate.error) {
      const error = validate.error.details[0].message;
      return res.status(400).send({
        status: 0,
        message: MESSAGES[400],
        error,
      });
    }

    const { userId } = validate.value;

    const [maintenanceStatus, userInfoResult] = await Promise.all([
      pgPoolQuery(`
        SELECT jsonb_object_agg(module_name, is_under_maintenance) AS "maintenanceStatus" 
        FROM app_maintenance_configs;
        `),
      pgPoolQuery(
        `
            SELECT teal_dot_flags
            FROM user_informations
            WHERE user_id = $1
            `,
        [userId],
      ),
    ]);

    // Initialize default teal dot status with all flags set to false
    const defaultTealDotStatus = Object.values(TEAL_DOT_FLAG_KEYS).reduce(
      (acc, key) => {
        acc[key] = false;
        return acc;
      },
      {},
    );

    // Get the actual teal dot status from the query result or use the default
    const tealDotStatus =
      userInfoResult?.length > 0 && userInfoResult[0]?.teal_dot_flags
        ? { ...defaultTealDotStatus, ...userInfoResult[0].teal_dot_flags }
        : defaultTealDotStatus;

    return res.status(200).send({
      status: 1,
      message: MESSAGES[200],
      data: {
        tealDotStatus,
        maintenanceStatus: { ...maintenanceStatus?.[0]?.maintenanceStatus },
      },
    });
  } catch (error) {
    rollbar.error('Error in getTealDotStatus:', error, req);
    return res.status(500).send({
      status: 0,
      message: `${MESSAGES['500']}: ${error.message || 'Unknown error occurred'}`,
      error: error.message || 'Unknown error occurred',
    });
  }
};

export default getTealDotStatus;
