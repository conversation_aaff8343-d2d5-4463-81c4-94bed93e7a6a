import constantOptions from '../../../../constants/constantOptions';
import MESSAGES from '../../../../constants/messages';
import userGuardCheck from '../../../../utils/auth/userGuardCheck';
import {
  filterMembers,
  getClubsList,
  getEligibleOffers,
  getUserDetail,
} from '../../../../utils/map-v3/common';
import rollbar from '../../../../utils/rollbar';
import redis from '../../../../utils/redis/redisClient';

const _ = require('lodash');

// Cache TTL values in seconds
const CACHE_TTL = {
  USER_DETAILS: 60 * 60, // 1 hour - user details don't change frequently
  OFFERS: 60 * 60, // 1 hour - offers can change but not too frequently
  CLUBS_LIST: 60 * 60, // 1 hour - clubs data is more dynamic
  FINAL_RESULT: 60 * 60, // 1 hour - final processed result, this is the most dynamic result
};

// Cache key generators
const getCacheKey = {
  userDetails: (userId) => `user_details:${userId}`,
  offers: (userId) => `offers:${userId}`,
  clubsList: (userId, userClubs) =>
    `clubs_list:${userId}:${(userClubs || []).sort().join(',')}`,
  finalResult: (userId) => `fetch_clubs_final:${userId}`,
};

// Helper function to get data with caching
const getCachedData = async (cacheKey, fetchFunction, ttl) => {
  try {
    // Try to get from cache first
    const cached = await redis.get(cacheKey);
    if (cached) {
      console.log(`Cache HIT for key: ${cacheKey}`);
      return JSON.parse(cached);
    }

    console.log(`Cache MISS for key: ${cacheKey}`);
    // If not in cache, fetch from database
    const data = await fetchFunction();

    // Store in cache with TTL
    if (data !== null && data !== undefined) {
      await redis.set(cacheKey, JSON.stringify(data), 'EX', ttl);
    }

    return data;
  } catch (error) {
    console.error(`Cache error for key ${cacheKey}:`, error);
    // If cache fails, fall back to direct database call
    return await fetchFunction();
  }
};

const getColorInfo = ({ club, offers, user }) => {
  const hasCommonGroupTGMember =
    club.has_common_tg_group_member?.find((item) => item) || false;

  const canMakeRequestForTGMember = club?.members?.some(
    (item) => item.has_common_tg_group_member && item.is_active_user,
  );

  // If the club has a friend or a common TG group member, then we can create request on that club
  const hasSomeFriends = club?.has_friend.some((friend) => friend);
  const canMakeRequestAsPerMyTG = hasSomeFriends || canMakeRequestForTGMember;

  //If the club has contact, friends or common tg group member, then club is My TG Club.
  const isMyTgClub =
    club?.has_contacts.some((contact) => contact) ||
    hasCommonGroupTGMember ||
    hasSomeFriends;

  const assumptionClubHasMembers = club?.active_members?.some(
    (member) => member,
  );

  const canMakeRequestAsPerLTV = filterMembers(
    club?.members || [],
    user,
    club?.lowest_visible_tier,
  )?.length;

  const isCanMakeRequest = canMakeRequestAsPerLTV || canMakeRequestAsPerMyTG;

  if (user.clubs?.includes(club.id)) {
    // If it is a user's club
    if (isMyTgClub) {
      return 'teal_contact';
    } else {
      return 'teal';
    }
  } else if (
    (!assumptionClubHasMembers && isMyTgClub) || // this means the club does not have any member and has a contact, show teal color
    (!isCanMakeRequest && club?.has_contacts.some((contact) => contact))
  ) {
    // if the user is not of All TG, and the club only has contacts and no req can be created on it
    return 'teal_contact';
  }

  // Return blue color if there is an offer for that user and current user doesn't belong to that club
  else if (offers?.includes(club.id) && !user.clubs?.includes(club.id)) {
    if (isMyTgClub) {
      return 'blue_contact';
    } else {
      return 'blue';
    }
  } else if (isCanMakeRequest) {
    if (isMyTgClub) {
      return 'green_contact';
    }
    return 'green';
  } else if (canMakeRequestAsPerLTV) {
    if (isMyTgClub) {
      // If the club type is virtual and the club is my tg, we need to show teal color
      if (club?.club_type === constantOptions.CLUB_TYPES.VIRTUAL) {
        return 'teal_contact';
      } else {
        return 'green_contact';
      }
    } else {
      // If the club type is virtual and the club is my tg, we need to show teal color
      if (club?.club_type === constantOptions.CLUB_TYPES.VIRTUAL) {
        return 'teal';
      }
      return 'green';
    }
  } else {
    if (!user?.visibleToPublic && isMyTgClub) {
      return 'teal_contact';
    } else if (user?.visibleToPublic && isMyTgClub) {
      // If the user is of fern tier and the color is grey contact, we will convert it into teal_contact as fern users are
      // not supposed to see grey clubs
      if (user?.tier === constantOptions.CLUB_TIERS.FERN) {
        return 'teal_contact';
      }
      return 'grey_contact';
    }
    return 'grey';
  }
};

/**
 * API to return clubs which are visible for a specific user Id.
 * @param {*} req
 * @param {*} res
 */
const fetchClubsForMap = async (req, res) => {
  try {
    res.setHeader(
      'Cache-Control',
      'public, s-maxage=1000000, stale-while-revalidate=59',
    );

    const { userId } = req.body;

    // Check final result cache first
    const finalCacheKey = getCacheKey.finalResult(userId);
    try {
      const cachedFinalResult = await redis.get(finalCacheKey);
      if (cachedFinalResult) {
        console.log(`Final result cache HIT for userId: ${userId}`);
        return res.send(JSON.parse(cachedFinalResult));
      }
    } catch (cacheError) {
      console.error('Final cache check error:', cacheError);
      // Continue with normal flow if cache fails
    }

    // Fetch user details with caching
    const user = await getCachedData(
      getCacheKey.userDetails(userId),
      () => getUserDetail(userId),
      CACHE_TTL.USER_DETAILS,
    );

    if (!user) {
      const emptyResult = { clubs: [] };
      // Cache empty result for a short time to avoid repeated DB calls for invalid users
      try {
        await redis.set(finalCacheKey, JSON.stringify(emptyResult), 'EX', 30);
      } catch (cacheError) {
        console.error('Cache set error for empty result:', cacheError);
      }
      return res.send(emptyResult);
    }

    // Fetch offers and clubs list in parallel with caching
    const [offers, clubs] = await Promise.all([
      getCachedData(
        getCacheKey.offers(userId),
        () => getEligibleOffers(userId),
        CACHE_TTL.OFFERS,
      ),
      getCachedData(
        getCacheKey.clubsList(userId, user?.clubs),
        () => getClubsList(userId, user?.clubs),
        CACHE_TTL.CLUBS_LIST,
      ),
    ]);

    let allClubs = [];

    /**
     * Here we will get the correct icon for that club
     */
    clubs.map((club) => {
      let color = getColorInfo({ club, offers, user });

      club.played = false;
      club.favClub = false;
      if (user?.playedClubs?.length) {
        club.played = user?.playedClubs?.includes(club.id);
      }
      if (user?.fClubs?.length) {
        club.favClub = user?.fClubs?.some((item) => item === club.id);
      }

      if (
        user?.tier === constantOptions.CLUB_TIERS.FERN &&
        color?.includes('grey')
      ) {
        // If the user is of Fern tier then no need to show them grey clubs
        return false;
      } else {
        let clubMembers = club.members || [];
        allClubs.push({
          type: 'Feature',
          geometry: {
            type: 'Point',
            coordinates: [parseFloat(club.lng), parseFloat(club.lat)],
          },
          id: club.id,
          properties: {
            id: club.id,
            ct: club.club_type,
            name: club.name,
            addr: club.address,
            tier: club.lowest_visible_tier,
            isFriend: club.has_friend?.find((item) => item) || false,
            isContact: club.has_contacts[0] == null ? false : true,
            isMyTgGroupMember: club.has_common_tg_group_member?.find(
              (item) => item,
            )
              ? true
              : false,
            myTgGroupIds:
              club?.has_common_tg_group_member?.filter((item) => item) || [],
            hasPlayAsCoupleMember:
              club?.has_play_as_couple?.find((item) => item) || false,
            color,
            hasFemalMember: club?.has_female?.find((item) => item) || false,
            played: club.played,
            favClub: club.favClub,
            clubDemandType: club.club_demand_type,
            clubMembers: new Set(clubMembers.map((item) => item.userId)).size,
            // If the club has contacts, then we will not show the acceptance rate
            acceptanceRate:
              club.has_contacts[0] == null
                ? parseInt(club?.acceptance_percentage || 0)
                : 0,
          },
        });
        return true;
      }
    });

    const finalResult = { clubs: allClubs };

    // Cache the final result
    try {
      await redis.set(
        finalCacheKey,
        JSON.stringify(finalResult),
        'EX',
        CACHE_TTL.FINAL_RESULT,
      );
      console.log(`Final result cached for userId: ${userId}`);
    } catch (cacheError) {
      console.error('Final cache set error:', cacheError);
    }

    res.send(finalResult);
  } catch (error) {
    console.log('error :', error);
    rollbar.error(error, req);
    res.status(500).send({
      status: 0,
      message: `${MESSAGES[500]}: ${error}`,
    });
  }
};

export default fetchClubsForMap;
