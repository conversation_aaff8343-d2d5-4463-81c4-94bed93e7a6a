import constantOptions from '../../../../constants/constantOptions';
import MESSAGES from '../../../../constants/messages';
import userGuardCheck from '../../../../utils/auth/userGuardCheck';
import {
  filterMembers,
  getClubsList,
  getEligibleOffers,
  getUserDetail,
} from '../../../../utils/map-v3/common';
import rollbar from '../../../../utils/rollbar';

const _ = require('lodash');

const getColorInfo = ({ club, offers, user }) => {
  const hasCommonGroupTGMember =
    club.has_common_tg_group_member?.find((item) => item) || false;

  const canMakeRequestForTGMember = club?.members?.some(
    (item) => item.has_common_tg_group_member && item.is_active_user,
  );

  // If the club has a friend or a common TG group member, then we can create request on that club
  const hasSomeFriends = club?.has_friend.some((friend) => friend);
  const canMakeRequestAsPerMyTG = hasSomeFriends || canMakeRequestForTGMember;

  //If the club has contact, friends or common tg group member, then club is My TG Club.
  const isMyTgClub =
    club?.has_contacts.some((contact) => contact) ||
    hasCommonGroupTGMember ||
    hasSomeFriends;

  const assumptionClubHasMembers = club?.active_members?.some(
    (member) => member,
  );

  const canMakeRequestAsPerLTV = filterMembers(
    club?.members || [],
    user,
    club?.lowest_visible_tier,
  )?.length;

  const isCanMakeRequest = canMakeRequestAsPerLTV || canMakeRequestAsPerMyTG;

  if (user.clubs?.includes(club.id)) {
    // If it is a user's club
    if (isMyTgClub) {
      return 'teal_contact';
    } else {
      return 'teal';
    }
  } else if (
    (!assumptionClubHasMembers && isMyTgClub) || // this means the club does not have any member and has a contact, show teal color
    (!isCanMakeRequest && club?.has_contacts.some((contact) => contact))
  ) {
    // if the user is not of All TG, and the club only has contacts and no req can be created on it
    return 'teal_contact';
  }

  // Return blue color if there is an offer for that user and current user doesn't belong to that club
  else if (offers?.includes(club.id) && !user.clubs?.includes(club.id)) {
    if (isMyTgClub) {
      return 'blue_contact';
    } else {
      return 'blue';
    }
  } else if (isCanMakeRequest) {
    if (isMyTgClub) {
      return 'green_contact';
    }
    return 'green';
  } else if (canMakeRequestAsPerLTV) {
    if (isMyTgClub) {
      // If the club type is virtual and the club is my tg, we need to show teal color
      if (club?.club_type === constantOptions.CLUB_TYPES.VIRTUAL) {
        return 'teal_contact';
      } else {
        return 'green_contact';
      }
    } else {
      // If the club type is virtual and the club is my tg, we need to show teal color
      if (club?.club_type === constantOptions.CLUB_TYPES.VIRTUAL) {
        return 'teal';
      }
      return 'green';
    }
  } else {
    if (!user?.visibleToPublic && isMyTgClub) {
      return 'teal_contact';
    } else if (user?.visibleToPublic && isMyTgClub) {
      // If the user is of fern tier and the color is grey contact, we will convert it into teal_contact as fern users are
      // not supposed to see grey clubs
      if (user?.tier === constantOptions.CLUB_TIERS.FERN) {
        return 'teal_contact';
      }
      return 'grey_contact';
    }
    return 'grey';
  }
};

/**
 * API to return clubs which are visible for a specific user Id.
 * @param {*} req
 * @param {*} res
 */
const fetchClubsForMap = async (req, res) => {
  try {
    res.setHeader(
      'Cache-Control',
      'public, s-maxage=1000000, stale-while-revalidate=59',
    );

    const { userId } = req.body;

    let [user, offers] = await Promise.all([
      getUserDetail(userId),
      getEligibleOffers(userId),
    ]);

    if (!user) {
      return res.send({ clubs: [] });
    }

    let clubs = await getClubsList(userId, user?.clubs);
    let allClubs = [];

    /**
     * Here we will get the correct icon for that club
     */
    clubs.map((club) => {
      let color = getColorInfo({ club, offers, user });

      club.played = false;
      club.favClub = false;
      if (user?.playedClubs?.length) {
        club.played = user?.playedClubs?.includes(club.id);
      }
      if (user?.fClubs?.length) {
        club.favClub = user?.fClubs?.some((item) => item === club.id);
      }

      if (
        user?.tier === constantOptions.CLUB_TIERS.FERN &&
        color?.includes('grey')
      ) {
        // If the user is of Fern tier then no need to show them grey clubs
        return false;
      } else {
        let clubMembers = club.members || [];
        allClubs.push({
          type: 'Feature',
          geometry: {
            type: 'Point',
            coordinates: [parseFloat(club.lng), parseFloat(club.lat)],
          },
          id: club.id,
          properties: {
            id: club.id,
            ct: club.club_type,
            name: club.name,
            addr: club.address,
            tier: club.lowest_visible_tier,
            isFriend: club.has_friend?.find((item) => item) || false,
            isContact: club.has_contacts[0] == null ? false : true,
            isMyTgGroupMember: club.has_common_tg_group_member?.find(
              (item) => item,
            )
              ? true
              : false,
            myTgGroupIds:
              club?.has_common_tg_group_member?.filter((item) => item) || [],
            hasPlayAsCoupleMember:
              club?.has_play_as_couple?.find((item) => item) || false,
            color,
            hasFemalMember: club?.has_female?.find((item) => item) || false,
            played: club.played,
            favClub: club.favClub,
            clubDemandType: club.club_demand_type,
            clubMembers: new Set(clubMembers.map((item) => item.userId)).size,
            // If the club has contacts, then we will not show the acceptance rate
            acceptanceRate:
              club.has_contacts[0] == null
                ? parseInt(club?.acceptance_percentage || 0)
                : 0,
          },
        });
        return true;
      }
    });

    res.send({ clubs: allClubs });
  } catch (error) {
    console.log('error :', error);
    rollbar.error(error, req);
    res.status(500).send({
      status: 0,
      message: `${MESSAGES[500]}: ${error}`,
    });
  }
};

export default fetchClubsForMap;
