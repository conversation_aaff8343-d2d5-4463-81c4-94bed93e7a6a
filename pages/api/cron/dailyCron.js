import adminClient from '../../../graphql/adminClient'
import { CREATE_NOTIFICATION } from '../../../graphql/mutations/notification'
import Moment from 'moment'
import removePastEvents from '../../../utils/cron/removePastEvents'
import removePastOffers from '../../../utils/cron/removePastOffers'
import cancelPastRequests from '../../../utils/cron/cancelPastRequests'
import remindRequestHosts from '../../../utils/cron/remindRequestHosts'
import sendRequestToOthers from '../../../utils/offers/sendRequestToOthers'
import createDynamicLink from '../../../utils/notifications/createDynamicLink'
import removeIncompleteProfileUsers from '../../../utils/cron/removeIncompleteProfileUsers'
import removeDeclinedUsers from '../../../utils/cron/removeDeclinedUsers'
import sendActivateLaterReminder from '../../../utils/cron/sendActivateLaterReminder'
import removeActivateLaterUsers from '../../../utils/cron/removeActivateLaterUsers'
import removePastClosurePeriods from './removePastClosurePeriods'
import remindHostsWithOpenChat from '../../../utils/cron/remindHostsWithOpenChat'
import remindUsersToCompleteProfile from '../../../utils/cron/remindUsersToCompleteProfile'
import deductPayment from '../../../utils/membership/cron/deductPayment'
import gameNotMarkedCompletedNotification from '../../../utils/cron/gameNotMarkedCompletedNotification'
import remindHostsAfterEndDate from '../../../utils/cron/remindHostsAfterEndDate'
import EMAILS from '../../../constants/emails'
import { mandrill_client, sendMailTemplate } from '../../../utils/mailchimp'
import sleep from '../../../utils/helper/sleep'
import unmuteUsers from '../../../utils/cron/unmuteUsers'
import unmuteUserClubs from '../../../utils/cron/unmuteUserClubs'
import paymentFailureSecondReminder from '../../../utils/cron/paymentFailureSecondReminder'
import paymentFailureThirdReminder from '../../../utils/cron/paymentFailureThirdReminder'
import deleteUserWithInactiveMembership from '../../../utils/cron/deleteUserWithInactiveMembership'
import membershipRenewalReminder from '../../../utils/cron/membershipRenewalReminder'
import removePastFtt from '../../../utils/cron/removePastFtt'
import removeExpiredTemporaryTiers from '../../../utils/cron/removeExpiredTemporaryTiers'
import deleteOldDbEventLogs from '../../../utils/cron/deleteOldDbEventLogs'
import removePastBenefits from '../../../utils/cron/removePastBenefits'
import rollbar, { reportError } from '../../../utils/rollbar'

async function gameNotMarkedCompleted() {
    var notifications = []
    let gamesResponse = [];

    // finding games in which the date was yesterday and not marked completed by either the requester or host
    try {
        await sleep() //Cooldown period before excuting the next function.

        const yesterday = Moment().add(-1, 'days').format('YYYY-MM-DD');
    const response = await adminClient.request(
        `
        query getGamesNotMarkedCompleted($yesterday: date) {
            game(where: {date: {_eq: $yesterday}, _or: [{hostCompleted: {_eq: false}}, {requestorCompleted: {_eq: false}}], status: {_neq: "declined"}, request: {status: {_nin: ["deleted", "cancelled"]}}}) {
                date
                request {
                    id
                    game {
                        host {
                            email
                            id
                            first_name
                        }
                    }
                    user {
                        email
                        first_name
                        id
                    }
                }
                requestorCompleted
                hostCompleted
                game_id
            }
        }      
        `,
        {
            yesterday
        }
    )

    if (response?.game?.length) {
        response.game.map(async (game) => {
            const { template, subject, message, text_message } = EMAILS.GAME_COMPLETION_REMINDER
            let sendNotifToUsers = [];

            if (!game.hostCompleted) {
                const host = {...game.request.game[0].host}
                sendNotifToUsers = [host];
            }

            if (!game.requestorCompleted) {
                const requester = {...game.request.user}
                sendNotifToUsers = [...sendNotifToUsers, requester];
            }

            gamesResponse.push(game?.game_id);

            const { shortLink } = await createDynamicLink({
                webLink: `dashboard/play`,
                appParams: `type=requests&id=${game?.request?.id}`
            });

            const shortLinkMessage = shortLink ? `Click here to check details: ${shortLink}` : ""
            
            notifications = sendNotifToUsers.map(user => {
                return {
                    user_id: user.id,
                    type: 'game',
                    message: message(game.game_id),
                    text_message: text_message({ gameId: game.game_id, shortLinkMessage }),
                    email_template_name: template,
                    data: {
                        request_id: game?.request?.id,
                        template_content: [
                            {
                                name: 'first_name',
                                content: user.first_name,
                            },
                            {
                                name: 'request_game_id',
                                content: game.game_id,
                            },
                            {
                                name: 'dynamic_link',
                                content: shortLinkMessage
                            }
                        ],
                        email: user.email,
                        subject,
                    },
                }
            })

            await adminClient.request(CREATE_NOTIFICATION, {
                notifications,
            })
        })
    }

        return notifications;
    } catch(error) {
        const errorType = {
            functionName: gameNotMarkedCompleted.name,
            errorMsg: error.toString(),
        }

        rollbar.error(`Cron:`, errorType)
        await sendFailureMail(errorType)

        return errorType
    } finally {
        rollbar.info('Cron: -- Ran Game Not Marked Completed --', {
            gamesResponse
        })
        console.log('-- Ran Game Not Marked Completed --');
    }
}

async function deleteOlderNotifications () {
    var delete_notification;
    try {
        const date = Moment().add(-15, 'd').format('YYYY-MM-DD');
        var delete_notification = await adminClient.request(`
        mutation deleteNotifs {
            delete_notification(where: {created_at: {_lt: "${date}"}, type: {_neq: "my-tg-offer"}}) {
              affected_rows
            }
        }`)
        await sleep();
        return delete_notification;
    } catch(error) {
        const errorType = {
            functionName: deleteOlderNotifications.name,
            errorMsg: error.toString(),
        }

        rollbar.error(`Cron:`, errorType)
        await sendFailureMail(errorType)

        return errorType
    } finally {
        rollbar.info('Cron: -- Ran Delete Older Notifications --')
        console.log('-- Ran Delete Older Notifications --');
    }
}

const cronFunctions = async () => {
    try {
        let response = {};

        response['deleteUserWithInactiveMembership'] = await deleteUserWithInactiveMembership()
        response['unmuteUsers'] = await unmuteUsers()
        response['unmuteUserClubs'] = await unmuteUserClubs()
        response['removePastFtt'] = await removePastFtt();
        response['removeExpiredTemporaryTiers'] = await removeExpiredTemporaryTiers()
        response['cancelPastRequests'] = await cancelPastRequests()
        response['gameNotMarkedCompleted'] = await gameNotMarkedCompleted()
        response['removePastOffers'] = await removePastOffers()
        response['remindRequestHosts'] = await remindRequestHosts()
        response['sendRequestToOthers'] = await sendRequestToOthers()

        response['deductPayment'] = await deductPayment();

        response['removePastEvents'] = await removePastEvents()
        response['deleteOlderNotifications'] = await deleteOlderNotifications()
        response['removeIncompleteProfileUsers'] = await removeIncompleteProfileUsers()
        response['removeDeclinedUsers'] = await removeDeclinedUsers();
        response['sendActivateLaterReminder'] = await sendActivateLaterReminder();
        response['removeActivateLaterUsers'] = await removeActivateLaterUsers();
        response['removePastClosurePeriods'] = await removePastClosurePeriods();
        response['remindHostsWithOpenChat'] = await remindHostsWithOpenChat();
        response['remindUsersToCompleteProfile'] = await remindUsersToCompleteProfile();
        response['gameNotMarkedCompletedNotification'] = await gameNotMarkedCompletedNotification()
        response['remindHostsAfterEndDate'] = await remindHostsAfterEndDate();
        response['paymentFailureSecondReminder'] = await paymentFailureSecondReminder();
        response['paymentFailureThirdReminder'] = await paymentFailureThirdReminder();
        response['membershipRenewalReminder'] = await membershipRenewalReminder()
        response['removePastBenefits'] = await removePastBenefits()
        response['deleteOldDbEventLogs'] = deleteOldDbEventLogs();
        
        await sendCronReport(response);
        return true;
    } catch (error) {
        await sendFailureMail(error);
        
        rollbar.error('Cron Daily Report:', error)
        return ({
            error: `Error: ${error?.errorMsg}`
        })
    }
}

export default async function dailyCron(req, res) {
    try {
        cronFunctions();
        return res.send('In progress')
    } catch (error) {
        await sendFailureMail(error);
        return false;
    }
}

export const sendFailureMail = async (error) => {
    try {
        reportError(error)

        await sendMailTemplate({
            email: process.env.CONFIG.DAILY_CRON_MAIL,
            multiple: true,
            template_name: `Daily Cron Fail Alert`,
            template_content: [
                {
                    name: 'date',
                    content: Moment().format('lll')
                },
                {
                    name: 'function_name',
                    content: error?.functionName
                },
                {
                    name: 'error',
                    content: error?.errorMsg || error?.message
                },
                {
                    name: 'environment',
                    content: process.env.ENVIRONMENT
                }
            ],
            subject: `${process.env.ENVIRONMENT}: Daily cron fail Alert`,
        }) 
    } catch (error) {
        rollbar.error('An error occurred in sendFailureMail in dailyCron', error)
    }
}

export const sendCronReport = async (response) => {
    try {
        await mandrill_client.messages.send(
            { 
                message: {
                    to: process.env.CONFIG.DAILY_CRON_MAIL,
                    from_email: process.env.CONFIG.mailchimp.outboundEmail,
                    from_name: 'Thousand Greens',
                    subject: `${process.env.ENVIRONMENT} Daily Cron Report from Thousand Greens: ${Moment().format('YYYY-MM-DD')}`,
                    html: `
                        <html>
                            <body>
                                <h1>Daily Cron Report from Thousand Greens</h1>
                                <table>
                                    <thead>
                                        <tr>
                                            <td><strong>Name</strong></td>
                                            <td><strong>Response</strong></td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${Object.entries(response).map(entry => {
                                            return (
                                                `<tr>
                                                    <td>${entry?.[0]}</td>
                                                    <td>
                                                        <code>
                                                            ${JSON.stringify(entry?.[1])}
                                                        </code>
                                                    </td>
                                                </tr>`
                                            )
                                        })}
                                        
                                    </tbody>
                                </table>
                            </body>
                        </html>`
                }, 
                async: true 
            },
            (res) => {
              console.log('res- --->', res);
            },
            (e) => {
              console.error('e-->', e);
            }
        )
    } catch (error) {
        await mandrill_client.messages.send(
            { 
                message: {
                    to: process.env.CONFIG.DAILY_CRON_MAIL,
                    from_email: process.env.CONFIG.mailchimp.outboundEmail,
                    from_name: 'Thousand Greens',
                    subject: `${process.env.ENVIRONMENT} Daily Cron Report from Thousand Greens: ${Moment().format('YYYY-MM-DD')}`,
                    html: `
                        <html>
                            <body>
                                Sending mail failed, but everything looks Ok as code has reached the end of it's execution
                            </body>
                        </html>`
                }, 
                async: true 
            },
            (res) => {
              console.log('res- --->', res);
            },
            (e) => {
              console.error('e-->', e);
            }
        )
    }
}