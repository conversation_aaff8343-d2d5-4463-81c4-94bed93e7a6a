import rollbar from '../../../utils/rollbar';
import { sendCronReport, sendFailureMail } from './dailyCron';
import updateDashboardStats from '../../../utils/cron/updateDashboardStats';
import updateClubAcceptancePercentage from '../../../utils/cron/updateClubAcceptancePercentage';

const cronFunctions = async () => {
  try {
    let response = {};
    response['dashboardStats'] = await updateDashboardStats();
    response['updateClubAcceptancePercentage'] =
      await updateClubAcceptancePercentage();

    await sendCronReport(response);
    return true;
  } catch (error) {
    await sendFailureMail(error);

    rollbar.error('Cron Daily Report:', error);
    return {
      error: `Error: ${error?.errorMsg}`,
    };
  }
};

// run this cron at 12:50 am HST
export default async function dashboardStatsDailyCron(req, res) {
  try {
    cronFunctions();
    return res.send('In progress');
  } catch (error) {
    await sendFailureMail(error);
    return false;
  }
}
