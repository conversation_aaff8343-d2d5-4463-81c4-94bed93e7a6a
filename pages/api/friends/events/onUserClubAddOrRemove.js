import MESSAGES from '../../../../constants/messages';
import adminClient from '../../../../graphql/adminClient';
import { CREATE_NOTIFICATION } from '../../../../graphql/mutations/notification';
import { GET_CLUB_USERS } from '../../../../graphql/queries/club';
import pgPoolQuery from '../../../../utils/db/pgQuery';
import rollbar from '../../../../utils/rollbar';

const GET_USER_DETAILS = `
    query getUser($userId: uuid!) {
        user(where: {id: {_eq: $userId}}) {
        username
        }
    }`;

/**
 * Event API to add club name in receiver_info and sender_info when a user is associate/dissociate with a club.
 * @param {*} req
 * @param {*} res
 * @returns
 */
const onUserClubAddOrRemove = async (req, res) => {
  try {
    const {
      event: {
        op,
        data: { old: oldData, new: newData },
      },
    } = req.body;

    let updatedIds = [];

    // If the operation is INSERT then,
    if (op === 'INSERT') {
      // Get the club name from courses table and user_id from newData
      let { club_id, user_id } = newData;

      const clubDetails = await pgPoolQuery(
        `SELECT name, id FROM courses WHERE id = $1`,
        [club_id],
      );

      const clubName = clubDetails.length > 0 ? clubDetails[0].name : '';

      // Now find the user in friends and friend_requests table and
      // update its clubs array in sender_info and receiver_info
      const [friendsData, friendRequestData] = await Promise.all([
        pgPoolQuery(
          `
                        UPDATE friend_requests
                        SET sender_info = CASE
                            WHEN sender_id = $1 THEN jsonb_set(
                                sender_info::jsonb, 
                                '{clubs}', 
                                (
                                    SELECT jsonb_agg(elem) 
                                    FROM jsonb_array_elements(COALESCE(sender_info->'clubs', '[]')) elem 
                                    WHERE elem != $2::jsonb
                                ) || $2::jsonb
                            )
                            ELSE sender_info
                        END,
                        receiver_info = CASE
                            WHEN receiver_id = $1 THEN jsonb_set(
                                receiver_info::jsonb, 
                                '{clubs}', 
                                (
                                    SELECT jsonb_agg(elem) 
                                    FROM jsonb_array_elements(COALESCE(receiver_info->'clubs', '[]')) elem 
                                    WHERE elem != $2::jsonb
                                ) || $2::jsonb
                            )
                            ELSE receiver_info
                        END
                        WHERE receiver_id = $1 OR sender_id = $1
                        RETURNING sender_info, receiver_info, id
                    `,
          [user_id, JSON.stringify([clubName])],
        ),
        pgPoolQuery(
          `
                        UPDATE friends
                        SET sender_info = CASE
                            WHEN sender_id = $1 THEN jsonb_set(
                                sender_info::jsonb, 
                                '{clubs}', 
                                (
                                    SELECT jsonb_agg(elem) 
                                    FROM jsonb_array_elements(COALESCE(sender_info->'clubs', '[]')) elem 
                                    WHERE elem != $2::jsonb
                                ) || $2::jsonb
                            )
                            ELSE sender_info
                        END,
                        receiver_info = CASE
                            WHEN receiver_id = $1 THEN jsonb_set(
                                receiver_info::jsonb, 
                                '{clubs}', 
                                (
                                    SELECT jsonb_agg(elem) 
                                    FROM jsonb_array_elements(COALESCE(receiver_info->'clubs', '[]')) elem 
                                    WHERE elem != $2::jsonb
                                ) || $2::jsonb
                            )
                            ELSE receiver_info
                        END
                        WHERE receiver_id = $1 OR sender_id = $1
                        RETURNING sender_info, receiver_info, id
                    `,
          [user_id, JSON.stringify([clubName])],
        ),

        // When a new club is added, add that as a visible club to all the my tg chat channels of the user
        pgPoolQuery(
          `
                    UPDATE chat_channel_member
                    SET visible_clubs = CASE
                        WHEN NOT ($2 = ANY (visible_clubs)) THEN array_append(visible_clubs, $2)
                        ELSE visible_clubs
                    END
                    WHERE chat_channel_member.user_id = $1;
                `,
          [user_id, club_id],
        ),

        //Update user's my_tg_club_visibility to add the club id
        pgPoolQuery(
          `
                    UPDATE "user"
                    SET my_tg_club_visibility = CASE
                        WHEN NOT ($2 = ANY (my_tg_club_visibility)) THEN array_append(my_tg_club_visibility, $2)
                        ELSE my_tg_club_visibility
                    END
                    WHERE id = $1
                `,
          [user_id, club_id],
        ),
      ]);

      if (
        (friendsData && friendsData.length) ||
        (friendRequestData && friendRequestData.length)
      ) {
        updatedIds.push(user_id);
      }

      //Send notification to all users in the same club
      const { user } = await adminClient.request(GET_USER_DETAILS, {
        userId: user_id,
      });

      //Get user's of same club
      const { user_club: userClub } = await adminClient.request(
        GET_CLUB_USERS,
        {
          clubIds: [club_id],
        },
      );
      let notifications = [];
      for (const club of userClub) {
        if (club?.user_id === user_id) {
          continue;
        }

        notifications.push({
          user_id: club.user_id,
          type: 'new-club-user',
          message: `A member ${user?.[0]?.username} from your club ${clubDetails[0].name} has just joined the network`,
          data: {
            clubId: club?.club?.id,
          },
          is_push_required: false,
        });
      }

      if (notifications.length) {
        await adminClient.request(CREATE_NOTIFICATION, {
          notifications,
        });
      }

      //if the user is dissasociated from a club, then remove tha club name from sender_info and receiver_info
    } else if (op === 'DELETE') {
      // Get the club name from courses table and user_id from oldData
      let { club_id, user_id } = oldData;

      const clubDetails = await pgPoolQuery(
        `SELECT name, id FROM courses WHERE id = $1`,
        [club_id],
      );

      const clubName = clubDetails.length > 0 ? clubDetails[0].name : '';

      // Remove clubs from sender_info and receiver_info
      const [friendsData, friendRequestData] = await Promise.all([
        pgPoolQuery(
          `
                        UPDATE friend_requests
                        SET sender_info = CASE
                            WHEN sender_id = $1 THEN jsonb_set(
                                sender_info::jsonb, 
                                '{clubs}', 
                                (
                                    SELECT jsonb_agg(elem) 
                                    FROM jsonb_array_elements(COALESCE(sender_info->'clubs', '[]')) elem 
                                    WHERE elem != $2::jsonb
                                )
                            )
                            ELSE sender_info
                        END,
                        receiver_info = CASE
                            WHEN receiver_id = $1 THEN jsonb_set(
                                receiver_info::jsonb, 
                                '{clubs}', 
                                (
                                    SELECT jsonb_agg(elem) 
                                    FROM jsonb_array_elements(COALESCE(receiver_info->'clubs', '[]')) elem 
                                    WHERE elem != $2::jsonb
                                )
                            )
                            ELSE receiver_info
                        END
                        WHERE receiver_id = $1 OR sender_id = $1
                        RETURNING sender_info, receiver_info, id
                    `,
          [user_id, JSON.stringify([clubName])],
        ),
        pgPoolQuery(
          `
                        UPDATE friends
                        SET sender_info = CASE
                            WHEN sender_id = $1 THEN jsonb_set(
                                sender_info::jsonb, 
                                '{clubs}', 
                                (
                                    SELECT jsonb_agg(elem) 
                                    FROM jsonb_array_elements(COALESCE(sender_info->'clubs', '[]')) elem 
                                    WHERE elem != $2::jsonb
                                )
                            )
                            ELSE sender_info
                        END,
                        receiver_info = CASE
                            WHEN receiver_id = $1 THEN jsonb_set(
                                receiver_info::jsonb, 
                                '{clubs}', 
                                (
                                    SELECT jsonb_agg(elem) 
                                    FROM jsonb_array_elements(COALESCE(receiver_info->'clubs', '[]')) elem 
                                    WHERE elem != $2::jsonb
                                )
                            )
                            ELSE receiver_info
                        END
                        WHERE receiver_id = $1 OR sender_id = $1
                        RETURNING sender_info, receiver_info, id
                    `,
          [user_id, JSON.stringify([clubName])],
        ),

        pgPoolQuery(
          `
                    UPDATE chat_channel_member
                    SET visible_clubs = array_remove(visible_clubs, $2)
                    WHERE chat_channel_member.user_id = $1;
                `,
          [user_id, club_id],
        ),

        //Remove club from user's my_tg_club_visibility
        pgPoolQuery(
          `
                    BEGIN;
                    UPDATE "user"
                    SET my_tg_club_visibility = array_remove(my_tg_club_visibility, $2)
                    WHERE id = $1;

                    WITH matching_requests AS (
                        SELECT r.id
                        FROM request r
                        LEFT JOIN game g ON g.request_id = r.id
                        WHERE r.status NOT IN ('cancelled', 'deleted')
                        AND hosts_sent ?& ARRAY[$1::text]
                        AND NOT hosts_declined ?& ARRAY[$1::text]
                        AND user_id <> $1
                        AND g.game_id IS NOT NULL
                        AND g."hostCompleted" = false
                        AND g."requestorCompleted" = false
                        AND r.club_id = $2
                    )
                    UPDATE request
                    SET hosts_declined = COALESCE(hosts_declined, '[]'::jsonb) || $3::jsonb
                    FROM matching_requests
                    WHERE request.id = matching_requests.id
                    RETURNING request.id, request.hosts_declined;


                    COMMIT;
                `,
          [user_id, club_id, JSON.stringify([user_id])],
        ),
      ]);

      if (
        (friendsData && friendsData.length) ||
        (friendRequestData && friendRequestData.length)
      ) {
        updatedIds.push(user_id);
      }
    }

    return res.send({
      op,
      updatedIds,
      message: MESSAGES['200'],
    });
  } catch (error) {
    rollbar.error(error, req);
    return res.send({
      message: `${MESSAGES['500']}: ${error}`,
      status: 0,
    });
  }
};

export default onUserClubAddOrRemove;
