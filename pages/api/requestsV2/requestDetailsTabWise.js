import validateUser from '../../../utils/auth/validateUser';
import determineRequestTab from '../../../utils/requests/determineRequestTab';
import redis from '../../../utils/redis/redisClient';
import rollbar from '../../../utils/rollbar';

// Cache TTL values in seconds
const CACHE_TTL = {
  TAB: 60 * 60, // 1 hour for tab determination
  DATA: 60 * 60, // 1 hour for request data
};

/**
 * Get data from cache
 * @param {string} key - Cache key
 * @returns {Promise<any>} - Cached data or null
 */
const getCachedData = async (key) => {
  try {
    const cachedData = await redis.get(key);
    return cachedData ? JSON.parse(cachedData) : null;
  } catch (error) {
    console.error('Redis cache error:', error);
    return null;
  }
};

/**
 * Set data in cache
 * @param {string} key - Cache key
 * @param {any} data - Data to cache
 * @param {number} ttl - Time to live in seconds
 */
const setCachedData = async (key, data, ttl) => {
  try {
    await redis.set(key, JSON.stringify(data), 'EX', ttl);
    console.log(`Data cached for key: ${key}`);
  } catch (error) {
    console.error('Redis cache error:', error);
  }
};

/**
 * API - uses determineRequestTab, to send request data (optionally) and tab as per the request_id and the user_id
 * @param {*} req
 * @param {*} res
 */
export default async function requestDetailsTabWise(req, res) {
  const { user_id, request_id, data = false } = req.body;

  try {
    const validUser = await validateUser(user_id);

    if (!validUser) {
      return res.status(404).send({
        status: 0,
        message: 'No resource found.',
      });
    }

    // Generate cache keys
    const tabCacheKey = `request_tab:${request_id}:${user_id}`;
    const dataCacheKey = data ? `request_data:${request_id}:${user_id}` : null;

    // Try to get from cache first
    const cachedTab = await getCachedData(tabCacheKey);
    const cachedData = data ? await getCachedData(dataCacheKey) : null;

    // If we have both cached tab and data (when requested), return them
    if (cachedTab && (!data || cachedData)) {
      console.log(`Returning cached data for key: ${tabCacheKey}`);
      return res.status(cachedTab.code || 200).send({
        ...cachedTab,
        data: cachedData?.data,
        code: cachedTab.code || 200,
      });
    }

    // If not in cache, get fresh data
    const response = await determineRequestTab({ request_id, user_id, data });

    // Cache the response
    if (response.status === 1) {
      // Cache tab information
      const tabData = {
        status: response.status,
        tab: response.tab,
        code: response.code,
      };
      await setCachedData(tabCacheKey, tabData, CACHE_TTL.TAB);

      // Cache request data if it was requested
      if (data && response.data) {
        const requestData = {
          data: response.data,
        };
        await setCachedData(dataCacheKey, requestData, CACHE_TTL.DATA);
      }
    }

    res.status(response?.code || 200).send({
      ...response,
      code: response?.code || 200,
    });
  } catch (error) {
    rollbar.error(error, req);
    res.status(500).send({
      status: 0,
      code: 500,
      message: 'An error occurred while processing your request.',
      error,
    });
  }
}
