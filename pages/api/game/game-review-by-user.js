import Joi from 'joi';
import MESSAGES from '../../../constants/messages';
import pgPoolQuery from '../../../utils/db/pgQuery';
import rollbar from '../../../utils/rollbar';
import userGuardCheck from '../../../utils/auth/userGuardCheck';

// Validation schema for request body
const schema = Joi.object().keys({
  userId: Joi.string().trim().required(),
  golferId: Joi.string().trim().required(),
  page: Joi.number().integer().min(0).default(0),
  limit: Joi.number().integer().min(1).max(100).default(10),
});

/**
 * API to get game reviews posted by a specific user
 * @param {*} req
 * @param {*} res
 * @returns
 */
const getGameReviewsByUser = async (req, res) => {
  res.setHeader(
    'Cache-Control',
    'public, s-maxage=300, stale-while-revalidate=59',
  );

  try {
    // Validate request body
    const validate = schema.validate(req.body);

    if (validate.error) {
      const error = validate.error.details[0].message;
      return res.status(400).send({
        status: 0,
        message: MESSAGES[400],
        error,
      });
    }

    const { userId, golferId, page, limit } = validate.value;
    console.log('golferId :', golferId);

    // Check if reviewer exists and is not deactivated
    const reviewerCheck = await pgPoolQuery(
      `SELECT id, username, first_name, last_name, "profilePhoto", "visibleToPublic", deactivated 
      FROM "user" WHERE id = $1 AND deactivated = false`,
      [golferId],
    );
    console.log('reviewerCheck :', reviewerCheck);

    if (!reviewerCheck || reviewerCheck.length === 0) {
      return res.status(404).send({
        status: 0,
        message: 'User not found',
      });
    }

    const reviewer = reviewerCheck[0];

    // Check relationship between logged-in user and reviewer
    const relationshipCheck = await pgPoolQuery(
      `
      SELECT 
        -- Check if they are friends
        (SELECT id FROM friends 
         WHERE (sender_id = $1 AND receiver_id = $2) 
            OR (receiver_id = $1 AND sender_id = $2)
        ) AS friend_id,
        -- Check if they have common TG groups
        (SELECT COUNT(*) > 0 FROM chat_channel_member ccm1
         INNER JOIN chat_channel_member ccm2 
           ON ccm1.chat_channel_id = ccm2.chat_channel_id
         WHERE ccm1.user_id = $1 AND ccm2.user_id = $2
        ) AS has_common_groups
      `,
      [userId, golferId],
    );

    const relationship = relationshipCheck[0];
    const isFriend = relationship.friend_id !== null;
    const hasCommonGroups = relationship.has_common_groups;
    const isOwnProfile = userId === golferId;

    // Check if user can view reviews based on privacy settings
    const canViewReviews =
      isOwnProfile || reviewer.visibleToPublic || isFriend || hasCommonGroups;

    if (!canViewReviews) {
      return res.status(403).send({
        status: 0,
        message: "You do not have permission to view this user's reviews",
      });
    }

    // Get total count of game reviews by this user
    const [totalCount] = await pgPoolQuery(
      `
      SELECT COUNT(*) as total
      FROM game_review gr
      INNER JOIN game g ON g.game_id = gr.game_id
      WHERE gr.user_id = $1
      `,
      [golferId],
    );

    // Get game reviews posted by the specified user
    const gameReviews = await pgPoolQuery(
      `
      SELECT
                    gr.game_id,
                    gr.review,
                    gr.user_id,
                    gr.photo,
                    gr.created_at,
                    c.name AS club_name,
                    g.date AS game_date
                FROM game_review gr
                INNER JOIN game g ON g.game_id = gr.game_id
                INNER JOIN request r ON r.game_id = g.game_id
                INNER JOIN courses c ON c.id = r.club_id
                WHERE gr.user_id = $1
                ORDER BY gr.created_at DESC
                LIMIT $2 OFFSET $3
      `,
      [golferId, limit, page * limit],
    );

    // Calculate pagination information
    const total = parseInt(totalCount?.total || 0);
    const totalPages = Math.ceil(total / limit);
    const hasNextPage = page < totalPages - 1;
    const hasPrevPage = page > 0;

    return res.send({
      status: 1,
      message: MESSAGES[200],
      data: {
        gameReviews: gameReviews,
        pagination: {
          total,
          totalPages,
          currentPage: page,
          hasNextPage,
          hasPrevPage,
          limit,
        },
      },
    });
  } catch (error) {
    rollbar.error(error, req);
    return res.status(500).send({
      message: `${MESSAGES['500']}: ${error}`,
      status: 0,
    });
  }
};

export default userGuardCheck(getGameReviewsByUser);
