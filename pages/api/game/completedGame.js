import adminClient from '../../../graphql/adminClient';
import { CREATE_NOTIFICATION } from '../../../graphql/mutations/notification';
import { UPDATE_USER } from '../../../graphql/mutations/user';
import Moment from 'moment';
import { sendMailTemplate } from '../../../utils/mailchimp';
import checkRequestDeletedByUser from '../../../utils/requests/checkRequestDeletedByUser';
import checkHostCompletedGameCount from '../../../utils/requests/checkHostCompletedGameCount';
import checkRequesterCompletedGameCount from '../../../utils/requests/checkRequesterCompletedGameCount';
import createDynamicLink from '../../../utils/notifications/createDynamicLink';
import ngvCalculator from '../../../utils/membership/ngvCalculator';
import createLog from '../../../utils/membership/createLog';
import constantOptions from '../../../constants/constantOptions';
import {
  determineTempOrFutureTier,
  getOneLevelHigherTier,
  isClubOverRequested,
  isClubRestricted,
} from '../../../utils/temporary-tier/helper';
import addHostToSystemTGGroup from '../../../utils/chat-v2/myTgGroups/addHostToSystemTGGroup';
import updateUserFTR from '../../../utils/user/updateUserFTR';
import updateTealDotFlags from '../../../utils/user/updateTealDotFlags';
import sendRequestSystemMessage from '../../../utils/requests/sendReqeuestSystemMessage';
import rollbar from '../../../utils/rollbar';
import calculateAndUpdateUserTier from '../../../utils/tiers/calculateAndUpdateUserTier';

const GET_HOST_AND_REQUESTOR = `
query getHostAndRequestor($host_id: uuid!, $request_id: uuid!) {
    request_by_pk(id: $request_id) {
      user {
        id
        first_name
        last_name
        email
        tokens
        playedClubs
      }
      club_id
      deleted_by_users
      is_first_request
      number_of_players
      is_played_offline
      chats(where: {club_member_id: {_eq: $host_id}}) {
      stream_channel_id
      sendbird_channel_id
    }
    }
    user_by_pk(id: $host_id) {
      id
      first_name
      last_name
      email
      tokens
    }
}
`;

const GET_CLUB_NAME = `
query getRequestClub($request_id: uuid!) {
    request_by_pk(id: $request_id) {
      club {
        name
        lowest_visible_tier
      }
    }
}  
`;

const CREATE_PENDING_COMPLETED_GAME = `
mutation insertPendingCompletedGame($game_id: Int!) {
    insert_pending_completed_game_one(object: {game_id: $game_id}) {
      created_at
    }
}
`;

const DELETE_PENDING_COMPLETED_GAME = `
mutation deletePendingCompletedGame($game_id: Int!) {
    delete_pending_completed_game_by_pk(game_id: $game_id) {
      created_at
    }
}
`;

export default async function completedGame(req, res) {
  try {
    const { old: oldGame, new: newGame } = req.body.event.data;
    let requestor,
      host,
      club_id,
      ngvUpgradeResponse,
      requesterLog,
      hostLog,
      clubRestricted,
      fttOrTT,
      overRequestedClubTT,
      addedHostToSystemResponse,
      isFirstRequest,
      noOfPlayers,
      isOfflineGame;
    const notifications = [],
      pushNotifs = [];

    // Only to run this when the game is not declined by admin
    if (!newGame?.declined_by_admin) {
      const gameCompleted =
        newGame.hostCompleted &&
        newGame.requestorCompleted &&
        (oldGame.hostCompleted || oldGame.requestorCompleted);

      const oneUserCompleted =
        !oldGame.hostCompleted &&
        !oldGame.requestorCompleted &&
        (newGame.hostCompleted || newGame.requestorCompleted);

      let request_by_pk, user_by_pk;
      try {
        const result = await adminClient.request(GET_HOST_AND_REQUESTOR, {
          host_id: newGame.host_id,
          request_id: newGame.request_id,
        });
        request_by_pk = result.request_by_pk;
        user_by_pk = result.user_by_pk;
        requestor = request_by_pk.user;
        club_id = request_by_pk.club_id;
        host = user_by_pk;
        isFirstRequest = request_by_pk.is_first_request;
        noOfPlayers = request_by_pk.number_of_players;
        isOfflineGame = request_by_pk.is_played_offline;
      } catch (error) {
        rollbar.error('Failed to fetch host and requestor data', {
          error: error.message,
          gameId: newGame.game_id,
          hostId: newGame.host_id,
          requestId: newGame.request_id,
        });
        throw error;
      }

      let club_name, lowest_visible_tier;
      try {
        const {
          request_by_pk: {
            club: { name: clubNameResult, lowest_visible_tier: tierResult },
          },
        } = await adminClient.request(GET_CLUB_NAME, {
          request_id: newGame.request_id,
        });
        club_name = clubNameResult;
        lowest_visible_tier = tierResult;
      } catch (error) {
        rollbar.error('Failed to fetch club data', {
          error: error.message,
          gameId: newGame.game_id,
          requestId: newGame.request_id,
        });
        throw error;
      }

      if (
        !oldGame.hostCompleted &&
        newGame.hostCompleted &&
        newGame?.host_experience_reason
      ) {
        let reason = `${newGame.host_experience_reason}`;

        sendUserMail({
          user_type: 'Host',
          requester: requestor,
          host: host,
          game_id: newGame.game_id,
          experience_reason: reason,
        });
      }
      if (
        !oldGame.requestorCompleted &&
        newGame.requestorCompleted &&
        newGame?.requestor_experience_reason
      ) {
        let reason = `${newGame.requestor_experience_reason}`;

        sendUserMail({
          user_type: 'Requester',
          requester: requestor,
          host: host,
          game_id: newGame.game_id,
          experience_reason: reason,
        });
      }

      const { shortLink } = await createDynamicLink({
        webLink: `dashboard/play`,
        appParams: `type=requests&id=${newGame?.request_id}`,
      });
      const shortLinkMessage = shortLink
        ? `Click here to check details: ${shortLink}`
        : '';

      /*
       * Update the R's played clubs
       * Check if the R doesn't already have the club_id of this request
       */

      if (
        (oneUserCompleted || gameCompleted) &&
        !requestor?.playedClubs.includes(club_id)
      ) {
        try {
          await adminClient.request(UPDATE_USER, {
            user_id: requestor?.id,
            user: {
              playedClubs: [...requestor?.playedClubs, club_id],
            },
          });
        } catch (error) {
          rollbar.error('Failed to update requestor played clubs', {
            error: error.message,
            gameId: newGame.game_id,
            requestorId: requestor?.id,
            clubId: club_id,
          });
          throw error;
        }
      }

      //If requester has completed the game, then send a system message
      if (
        !oldGame.requestorCompleted &&
        newGame.requestorCompleted &&
        !isOfflineGame
      ) {
        sendRequestSystemMessage({
          stream_channel_id: request_by_pk?.chats[0]?.stream_channel_id,
          systemChatMessage: `The Requester has marked the game as completed on ${Moment().format(constantOptions.DATE_FORMAT_Do_MM_YYYY)}.`,
        });
      }

      //If host has completed the game, then send a system message
      if (!oldGame.hostCompleted && newGame.hostCompleted && !isOfflineGame) {
        sendRequestSystemMessage({
          stream_channel_id: request_by_pk?.chats[0]?.stream_channel_id,
          systemChatMessage: `The Host has marked the game as completed on ${Moment().format(constantOptions.DATE_FORMAT_Do_MM_YYYY)}.`,
        });
      }

      // One user Game Completed
      if (oneUserCompleted) {
        const userTypeThatCompleted = newGame.hostCompleted
          ? 'host'
          : 'requester';
        await adminClient.request(CREATE_PENDING_COMPLETED_GAME, {
          game_id: newGame.game_id,
        });

        let notificationToOtherParty = `The ${userTypeThatCompleted} or the system has marked the game related to request #${newGame.game_id} as completed. Please mark it as completed at your end as well.`;

        addedHostToSystemResponse = await addHostToSystemTGGroup(host.id);

        //function is used for host & get count of mark completed game if more than 5 than update the "is_tg_ambassador" & "ambassador_visibility"
        await checkHostCompletedGameCount(host.id);

        //function is used for requester & get count of mark completed game if more than 5 than update the "is_tg_ambassador" & "ambassador_visibility"
        await checkRequesterCompletedGameCount(requestor.id);

        //Update user's tier
        await calculateAndUpdateUserTier(host.id);

        //If the request is first time request, then increase ftr_count of the host
        if (isFirstRequest) {
          updateUserFTR({ userId: host.id });
        }

        //Send email to the user who completed the game
        notifications.push({
          user_id: userTypeThatCompleted === 'host' ? host.id : requestor.id,
          type: 'game-completed',
          message: `You’ve Marked Game Request #${newGame.game_id} as Completed`,
          email_template_name: 'Game Complete 3',
          data: {
            request_id: newGame?.request_id,
            template_content: [
              {
                name: 'first_name',
                content:
                  userTypeThatCompleted === 'host'
                    ? host.first_name
                    : requestor.first_name,
              },
              {
                name: 'request_game_id',
                content: newGame.game_id,
              },
              {
                name: 'dynamic_link',
                content: shortLink,
              },
            ],
            email:
              userTypeThatCompleted === 'host' ? host.email : requestor.email,
            subject: `You’ve Marked Game Request #${newGame.game_id} as Completed`,
            is_push_required: false,
            web_panel: false,
            mobile_panel: false,
          },
        });

        if (userTypeThatCompleted === 'requester') {
          const requestDeletedByHost = await checkRequestDeletedByUser({
            user_id: host.id,
            game_id: newGame.game_id,
          });

          if (!requestDeletedByHost) {
            await updateTealDotFlags({
              flagKey: constantOptions.TEAL_DOT_FLAG_KEYS.REQUEST,
              userIds: [host.id],
            });

            // Send host the notification to mark the game as complete
            notifications.push({
              user_id: host.id,
              type: 'requester-game-completed',
              message: notificationToOtherParty,
              email_template_name: 'Game Completion Mail to Other User',
              data: {
                request_id: newGame?.request_id,
                template_content: [
                  {
                    name: 'first_name',
                    content: host.first_name,
                  },
                  {
                    name: 'request_game_id',
                    content: newGame.game_id,
                  },
                  {
                    name: 'dynamic_link',
                    content: shortLink,
                  },
                  {
                    name: 'game_completed_by',
                    content: 'Requester',
                  },
                ],
                email: host.email,
                subject: `Game Request #${newGame.game_id} Marked as Completed`,
                is_push_required: false,
                web_panel: false,
                mobile_panel: false,
              },
            });
          }
        } else if (userTypeThatCompleted === 'host') {
          const requestDeletedByRequester = await checkRequestDeletedByUser({
            user_id: requestor.id,
            game_id: newGame.game_id,
          });

          if (!requestDeletedByRequester) {
            await updateTealDotFlags({
              flagKey: constantOptions.TEAL_DOT_FLAG_KEYS.REQUEST,
              userIds: [requestor.id],
            });

            // Send requester the notification to mark the game as complete
            notifications.push({
              user_id: requestor.id,
              type: 'host-game-completed',
              message: notificationToOtherParty,
              email_template_name: 'Game Completion Mail to Other User',
              data: {
                request_id: newGame?.request_id,
                template_content: [
                  {
                    name: 'first_name',
                    content: requestor.first_name,
                  },
                  {
                    name: 'request_game_id',
                    content: newGame.game_id,
                  },
                  {
                    name: 'dynamic_link',
                    content: shortLink,
                  },
                  {
                    name: 'game_completed_by',
                    content: 'Host',
                  },
                ],
                email: requestor.email,
                subject: `Game Request #${newGame.game_id} Marked as Completed`,
                is_push_required: false,
                web_panel: false,
                mobile_panel: false,
              },
            });
          }
        }

        if (!isOfflineGame) {
          try {
            await adminClient.request(CREATE_NOTIFICATION, {
              notifications,
            });
          } catch (error) {
            rollbar.error('Failed to send one user completed notifications', {
              error: error.message,
              gameId: newGame.game_id,
              notificationCount: notifications.length,
            });
            throw error;
          }
        } else {
          rollbar.info('Skipped notifications for offline game', {
            gameId: newGame.game_id,
            isOfflineGame,
          });
        }

        // ** Update host's tiers conditionally **
        try {
          clubRestricted = await isClubRestricted(club_id, host?.id);

          if (clubRestricted) {
            fttOrTT = await determineTempOrFutureTier(
              host?.id,
              lowest_visible_tier,
              undefined,
              club_id,
            );
          }

          //Check if the club is over-requested, then give an additional tier bump
          const overRequestedClub = await isClubOverRequested(club_id);

          if (
            overRequestedClub &&
            lowest_visible_tier != constantOptions.CLUB_TIERS.FERN
          ) {
            const {
              system_setting:
                time_duration_tier_bump_due_to_over_requested_clubs,
            } = await adminClient.request(`
                    {
                        system_setting(where: {name: {_eq: "time_duration_tier_bump_due_to_over_requested_clubs"}}) {
                            value
                        }
                    }
                    `);
            let newTierExpiry = Moment()
              .add(
                time_duration_tier_bump_due_to_over_requested_clubs?.[0]?.value
                  ?.value,
                'M',
              )
              .format('YYYY-MM-DD');

            overRequestedClubTT = await determineTempOrFutureTier(
              host?.id,
              getOneLevelHigherTier(lowest_visible_tier),
              newTierExpiry,
              club_id,
            );
          }
        } catch (error) {
          rollbar.error('Error in tier update process', {
            error: error.message,
            gameId: newGame.game_id,
            hostId: host?.id,
            clubId: club_id,
          });
          fttOrTT = error?.message;
        }

        // ** Update both the user's NGV and also create a log **
        try {
          ngvUpgradeResponse = await ngvCalculator(newGame?.request_id, true);
        } catch (error) {
          rollbar.error('Failed to calculate NGV upgrade', {
            error: error.message,
            gameId: newGame.game_id,
            requestId: newGame?.request_id,
          });
          throw error;
        }

        // A general log for the host and the requester
        var logData = {
          moduleId: `${newGame.game_id}`,
          moduleType: constantOptions.MEMBERSHIP_LOGS.MODULE_TYPES.REQUEST,
          action: constantOptions?.MEMBERSHIP_LOGS?.MODULE_ACTIONS?.COMPLETED,
          actionTime: Moment(),
          numberofPlayers: noOfPlayers,
        };

        try {
          requesterLog = await createLog({
            ...logData,
            userId: requestor.id,
            extraInfo: `Host: ${host?.first_name} ${host?.last_name}, Club: ${club_name}`,
            ngvChange: ngvUpgradeResponse,
            isRequested: true,
          });

          hostLog = await createLog({
            ...logData,
            userId: host.id,
            extraInfo: `Requester: ${requestor?.first_name} ${requestor?.last_name}, Club: ${club_name}`,
            ngvChange: ngvUpgradeResponse * -1,
            isRequested: false,
          });
        } catch (error) {
          rollbar.error('Failed to create membership logs', {
            error: error.message,
            gameId: newGame.game_id,
            requestorId: requestor?.id,
            hostId: host?.id,
          });
          throw error;
        }

        return res.send({
          status: 'One user Game Completed',
          notifications,
          ngvUpgradeResponse,
          requesterLog,
          hostLog,
          fttOrTT,
          overRequestedClubTT,
          addedHostToSystemResponse,
        });

        // Game completed
      } else if (gameCompleted) {
        try {
          await adminClient.request(DELETE_PENDING_COMPLETED_GAME, {
            game_id: newGame.game_id,
          });
        } catch (error) {
          rollbar.error('Failed to delete pending completed game record', {
            error: error.message,
            gameId: newGame.game_id,
          });
          throw error;
        }

        res.send({
          notifications,
          pushNotifs,
        });
        return;

        // Neither user completed the game
      } else {
        rollbar.info('Game not completed - neither user completed', {
          gameId: newGame?.game_id,
          hostCompleted: newGame?.hostCompleted,
          requestorCompleted: newGame?.requestorCompleted,
        });
        res.send('Game not completed');
      }
    } else {
      return res.send('Game marked declined by the admin');
    }
  } catch (error) {
    console.log('error :', error);
    rollbar.error('Critical error in completedGame function', {
      error: error.message,
      stack: error.stack,
      gameId: req.body?.event?.data?.new?.game_id,
      requestId: req.body?.event?.data?.new?.request_id,
      hostId: req.body?.event?.data?.new?.host_id,
      requestBody: req.body,
    });

    return res.status(500).send({
      error: 'Internal server error during game completion processing',
      message: error.message,
    });
  }
}

async function sendUserMail({
  user_type,
  game_id,
  experience_reason,
  host,
  requester,
}) {
  try {
    await sendMailTemplate({
      email: process.env.TG_ADMIN_EMAIL,
      subject: 'Game Dissatisfactory Experience',
      template_name: 'Game Experience to Admin - Requester/Host',
      template_content: [
        {
          name: 'user_type',
          content: user_type,
        },
        {
          name: 'request_game_id',
          content: game_id,
        },
        {
          name: 'experience_reason',
          content: experience_reason,
        },
        {
          name: 'host_name',
          content: `${host.first_name} ${host.last_name}`,
        },
        {
          name: 'host_email',
          content: host.email,
        },
        {
          name: 'requester_name',
          content: `${requester.first_name} ${requester.last_name}`,
        },
        {
          name: 'requester_email',
          content: requester.email,
        },
      ],
    });
  } catch (error) {
    console.log('error :', error);
    rollbar.error('Failed to send dissatisfactory experience email', {
      userType: user_type,
      gameId: game_id,
      hostId: host?.id,
      requesterId: requester?.id,
      adminEmail: process.env.TG_ADMIN_EMAIL,
    });
    // Don't throw error here as this is not critical to game completion flow
  }
}
