import adminClient from '../../../graphql/adminClient'
import { CREATE_NOTIFICATION } from '../../../graphql/mutations/notification'
import { CLUB_REQUEST_MEMBERS } from '../../../graphql/queries/user';
import checkMutedUser from '../../../utils/user/checkMutedUser';

export default async function RequesterConfirmedRequest(req, res) {
    const {request, host} = req.body;

    let notifications = [], notificationsToOtherMembers = [], notificationsToAcceptedHost = [];
    let notificationsToAcceptedHostMessage = `The requester has confirmed your acceptance of request #${request.game_id}. The request has been moved to your Accepted Section. Please return to the Web or App and mark the game as completed after you have played the game.`;
    let notificationsToOtherMembersMessage = `Request #${request.game_id} has been accepted by another member. No further action is needed on this request.`;

    /* 
        * Filtering hosts to whom the request was sent initially except the one requestor is confirming and
        * the hosts who have declined the request
    */
    const otherHosts = request.hosts_sent.filter(host_sent => 
        host_sent !== host.id
        && !request.hosts_declined.includes(host_sent)
    )

    // ** Finding the details of the club members to send notification - req accepted by Requestor **
    const clubMembers = await adminClient
    .request(CLUB_REQUEST_MEMBERS, {
        club_id: request.club_id,
    })
    .then(({ user_club }) => {
        return user_club.filter(club => !club.muted);
    })
    .then((allHosts) => {            
        notificationsToOtherMembers = allHosts.filter(({ user }) => otherHosts.includes(user.id)).map(({user}) => {
            return {
                user_id: user.id,
                type: 'requestor-confirmed-request',
                message: notificationsToOtherMembersMessage,
                text_message: notificationsToOtherMembersMessage,
                email_template_name: 'Requester Confirmed Request - to other members',
                data: {
                        request_id: request.id,
                        template_content: [
                            {
                                name: 'first_name',
                                content: user.first_name,
                            },
                            {
                                name: 'request_game_id',
                                content: request.game_id,
                            },
                        ],
                        email: user.email,
                        subject: `Request accepted by another member`,
                    },
            }  
        });

        const { user } =  allHosts
        .find(({ user }) => user.id === host.id)

        return Promise.all([
            checkMutedUser(user.id, request.club_id),
            user,
        ]);
    }).then(([muted, user]) => {
        if (!muted) {
            return {
                user_id: user.id,
                type: 'requestor-confirmed-request',
                message: notificationsToAcceptedHostMessage,
                text_message: notificationsToAcceptedHostMessage,
                email_template_name: 'Requester Confirmed Request - to main host',
                data: {
                    request_id: request.id,
                    template_content: [
                        {
                            name: 'request_game_id',
                            content: request.game_id,
                        },
                        {
                            name: 'first_name',
                            content: user.first_name
                        }
                    ],
                    email: user.email,
                    subject:
                        "Request accepted by the requestor",
                },
            } 
        } else {
            return {}
        }
    }).then((notificationsToAcceptedHost) => {
        notifications = [...notificationsToOtherMembers, {...notificationsToAcceptedHost}];
        
        // ** Sending Notifications **
        return adminClient.request(CREATE_NOTIFICATION, {
            notifications,
        })

    }).then((insert_notification) => res.send(insert_notification))
    .catch(e => Promise.reject(e));
}