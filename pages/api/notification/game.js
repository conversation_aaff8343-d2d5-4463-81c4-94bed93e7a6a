import adminClient from '../../../graphql/adminClient'
import { CREATE_NOTIFICATION } from '../../../graphql/mutations/notification'
import Moment from 'moment'
import { CHAT_REQUEST } from '../../../graphql/queries/request'
import { GET_USER_EMAIL } from '../../../graphql/queries/user'
import checkMutedUserRequestInHistory from '../../../utils/requests/checkMutedUserRequestInHistory'
import createDynamicLink from '../../../utils/notifications/createDynamicLink'
import constantOptions from '../../../constants/constantOptions'
import getStreamInstance from '../../../utils/chat-v2/getStreamInstance'
import updateTealDotFlags from '../../../utils/user/updateTealDotFlags'

const {
    STREAM_CHANNEL_TYPES: { REQUEST_CHAT_GROUP },
} = constantOptions

const UNACCEPTED_REQUEST_CHATS = `
query getUnacceptedRequestChats($request_id: uuid) {
    request_chat(where: {request_id: {_eq: $request_id}}) {
      sendbird_channel_id
      stream_channel_id
      club_member_id
      request {
        game_id
      }
    }
}  
`

const GET_CLUB_DATA = `
query fetchClubData ($club_id: Int!, $user_id: uuid!) {
    user_club(where: {club_id: {_eq: $club_id}, user_id: {_eq: $user_id}}) {
        paymentMethod
        otherInstructions
        club {
            guestFee
            caddieFee
            caddieRequired
            dressCode
        }
    }
}
`

export default async function gameNotification(req, res) {
    const {
        event: {
            data: {
                new: { request_id, host_id, date, game_id },
            },
        },
    } = req.body

    const { shortLink } = await createDynamicLink({
        webLink: `dashboard/play`,
        appParams: `type=requests&id=${request_id}`,
    })
    const shortLinkMessage = shortLink
        ? `Click here to check details: ${shortLink}`
        : ''

    let notifications = [],
        chatMessages = []

    const { request_by_pk: requestData } = await adminClient.request(
        CHAT_REQUEST,
        {
            request_id,
        }
    )
    const notificationsToOtherMembersMessage = `Request #${game_id} at ${requestData.club.name} has been accepted by another member and has now been moved to your History section. No further action is needed on this request.`

    // If the game is played offline, we do not need to send notifications
    if (requestData.is_played_offline) {
        return res.send({ message: 'Game is played offline' })
    }

    // ** Get Club Data for sending system message b/w R and H **
    const { user_club } = await adminClient.request(GET_CLUB_DATA, {
        club_id: requestData.club_id,
        user_id: host_id,
    })

    let notificationToRequester = `Request #${game_id} to play ${requestData.club.name} has been accepted for ${Moment.utc(date).format(constantOptions?.DATE_FORMAT_Do_MM_YYYY)}. The request's status has been updated to "Accepted". Please mark the game as completed after playing.`
    let systemMessageRH1 = ''
    let gameInformation
    let systemMessageRH = `The request has been accepted and the game is set for ${Moment.utc(date).format(constantOptions?.DATE_FORMAT_Do_MM_YYYY)}`

    if (user_club && user_club.length) {
        const userClub = user_club[0]
        systemMessageRH1 = `Guest Fee: ${userClub.club.guestFee}, Caddie Required: ${userClub.club.caddieRequired && userClub.club.caddieFee ? userClub.club.caddieFee : 'No'}, Method of Fee Settlement: ${userClub?.paymentMethod?.type || 'NA'}, Dress Code: ${userClub.club.dressCode}, Other Information: ${userClub.otherInstructions || 'None'}`
        gameInformation = {
            guestFee: userClub.club.guestFee,
            caddieRequired:
                userClub.club.caddieRequired && userClub.club.caddieFee
                    ? userClub.club.caddieFee
                    : 'No',
            methodOfFeeSettlement: userClub?.paymentMethod?.type || 'NA',
            dressCode: userClub.club.dressCode,
            otherInformation: userClub.otherInstructions || 'None',
        }
    }

    await updateTealDotFlags({
        flagKey: constantOptions.TEAL_DOT_FLAG_KEYS.REQUEST,
        userIds: [requestData.user.id],
    })

    // ** Send Notification to the requester **

    notifications.push({
        user_id: requestData.user.id,
        type: 'confirmed-request',
        message: notificationToRequester,
        text_message: `${notificationToRequester} ${shortLinkMessage}`,
        email_template_name: 'Request Accepted by Host V2',
        data: {
            request_id,
            template_content: [
                {
                    name: 'request_game_id',
                    content: game_id,
                },
                {
                    name: 'first_name',
                    content: requestData.user.first_name,
                },
                {
                    name: 'club_name',
                    content: requestData.club.name,
                },
                {
                    name: 'game_date',
                    content: Moment.utc(date).format(
                        constantOptions?.DATE_FORMAT_Do_MM_YYYY
                    ),
                },
                {
                    name: 'dynamic_link',
                    content: shortLinkMessage,
                },
            ],
            email: requestData.user.email,
            subject: 'Request Accepted by Host',
        },
    })

    // ** Send system message to the hosts who did not decline/delete the request and to the accepted host **
    const { request_chat: requestChats } = await adminClient.request(
        UNACCEPTED_REQUEST_CHATS,
        {
            request_id,
        }
    )

    if (requestChats.length > 0) {
        chatMessages = await Promise.all(
            requestChats.map(
                async ({
                    sendbird_channel_id,
                    stream_channel_id,
                    club_member_id,
                    request: { game_id },
                }) => {
                    const clubMutedAndInHistory =
                        await checkMutedUserRequestInHistory({
                            game_id,
                            user_id: club_member_id,
                        })
                    if (
                        (!requestData.hosts_declined ||
                            (requestData.hosts_declined &&
                                !requestData.hosts_declined.includes(
                                    club_member_id
                                ))) && // Request should not be declined by the host
                        (!requestData.deleted_by_users ||
                            (requestData.deleted_by_users &&
                                !requestData.deleted_by_users.includes(
                                    club_member_id
                                ))) && // Request should not be deleted by the host
                        !clubMutedAndInHistory
                    ) {
                        // ** Send notification the hosts who did not decline the request
                        if (club_member_id !== host_id) {
                            // ** Sending chat message **
                            await sendSystemMessage({
                                sendbird_channel_id,
                                stream_channel_id,
                                systemChatMessage: `This request has been accepted by another host and is now closed`,
                                skipPush: true,
                                silent: true,
                            })

                            const {
                                user_by_pk: {
                                    email: host_email,
                                    first_name: host_name,
                                },
                            } = await adminClient.request(GET_USER_EMAIL, {
                                id: club_member_id,
                            })

                            notifications.push({
                                user_id: club_member_id,
                                type: 'confirmed-request',
                                message: notificationsToOtherMembersMessage,
                                text_message: `${notificationsToOtherMembersMessage} ${shortLinkMessage}`,
                                email_template_name:
                                    'Requester Confirmed Request - to other members',
                                data: {
                                    request_id,
                                    template_content: [
                                        {
                                            name: 'first_name',
                                            content: host_name,
                                        },
                                        {
                                            name: 'request_game_id',
                                            content: game_id,
                                        },
                                        {
                                            name: 'dynamic_link',
                                            content: shortLinkMessage,
                                        },
                                    ],
                                    email: host_email,
                                    subject: `Request accepted by another member`,
                                },
                            })
                        } else {
                            // ** Sending chat message **
                            await sendSystemMessage({
                                sendbird_channel_id,
                                stream_channel_id,
                                systemChatMessage: systemMessageRH,
                            })

                            // ** Sending other club details to the chat thread **
                            if (systemMessageRH1) {
                                await sendSystemMessage({
                                    sendbird_channel_id,
                                    stream_channel_id,
                                    systemChatMessage: systemMessageRH1,
                                    gameInformation,
                                })
                            }

                            let guidelinesMessage = `The following guidelines will lead to a better game experience:\n\nBEFORE THE GAME\n\n1. Don't flake or ghost\n2. Clarify expectations and cultural norms. Each club is unique At the course\n3. As a host, be explicit with on-course expectations, give the guest an amazing experience, and be clear on what you want the guest to cover monetarily\n4. As a guest, follow the host's lead, leave a light footprint, fully pay and don't overstay`
                            await sendSystemMessage({
                                sendbird_channel_id,
                                stream_channel_id,
                                systemChatMessage: guidelinesMessage,
                            })
                        }
                    }
                }
            )
        )
    }

    if (notifications.length) {
        const {
            insert_notification: { affected_rows },
        } = await adminClient.request(CREATE_NOTIFICATION, {
            notifications,
        })
    }

    res.send({ notifications, chatMessages })
}

async function sendSystemMessage({
    sendbird_channel_id,
    stream_channel_id,
    systemChatMessage,
    gameInformation,
    skipPush = false,
    silent = false,
}) {
    if (stream_channel_id) {
        const serverClient = getStreamInstance()
        const channel = serverClient.channel(
            REQUEST_CHAT_GROUP,
            stream_channel_id
        )

        const message = {
            text: systemChatMessage,
            type: 'system',
            user: { id: 'system' },
            silent: silent,
        }

        if (gameInformation) {
            message.text = ''
            message.game_information = gameInformation
        }

        await channel.sendMessage(message, { skip_push: skipPush })
        return channel
    } else {
        const chatMessage = await fetch(
            `https://api-${process.env.SENDBIRD_APPLICATION_ID}.sendbird.com/v3/group_channels/${sendbird_channel_id}/messages`,
            {
                method: 'POST',
                headers: {
                    ['Content-Type']: 'application/json',
                    ['Api-Token']: process.env.SENDBIRD_API_TOKEN,
                },
                body: JSON.stringify({
                    message_type: 'ADMM',
                    message: systemChatMessage,
                }),
            }
        )
            .then((data) => data.json())
            .catch((e) => {
                console.log({ e })
            })
        return chatMessage
    }
}
