import constantOptions from '../../../constants/constantOptions';
import MESSAGES from '../../../constants/messages';
import validateUser from '../../../utils/auth/validateUser';
import pgPoolQuery from '../../../utils/db/pgQuery';
import rollbar from '../../../utils/rollbar';

const { NOTIFICATION_LIMIT } = constantOptions;

// Define priority notification types (same as v3/all-notification)
const PRIORITY_NOTIFICATION_TYPES = constantOptions.PRIORITY_NOTIFICATION_TYPES;

/**
 * NotificationV3 - Same request/response format as V2 but with priority notifications on top
 * Uses the same pagination logic as V2 (row_id based) but prioritizes certain notification types
 * @param {*} req
 * @param {*} res
 * @returns
 */
export default async function allNotificationsV3(req, res) {
  try {
    // Keep exact same request format as V2
    const {
      user_id,
      last_id = 0,
      type = 'next',
      limit = NOTIFICATION_LIMIT,
    } = req.body;

    const isValidUser = await validateUser(user_id);
    if (!isValidUser) {
      return res.status(400).send('User does not exist.');
    }

    // Create priority types string for SQL IN clause
    const priorityTypesString = PRIORITY_NOTIFICATION_TYPES.map(
      (type) => `'${type}'`,
    ).join(', ');

    // Step 1: Get ALL priority notifications (no limit)
    const priorityNotifications = await fetchPriorityNotifications(
      user_id,
      priorityTypesString,
    );

    // Step 2: Calculate how many regular notifications we need
    const priorityCount = priorityNotifications.length;

    // Step 3: Get regular notifications with pagination, accounting for priority notifications
    let regularNotifications = [];
    let regularNotificationsNeeded = 0;

    if (last_id < priorityCount) {
      // We're still in the priority notifications range
      const remainingPriorityCount = priorityCount - last_id;
      regularNotificationsNeeded = Math.max(limit - remainingPriorityCount, 0);

      if (regularNotificationsNeeded > 0) {
        regularNotifications = await fetchRegularNotifications(
          user_id,
          priorityTypesString,
          0, // Start from beginning of regular notifications
          type,
          regularNotificationsNeeded,
        );
      }
    } else {
      // We're past priority notifications, get regular notifications with proper pagination
      const adjustedLastId = last_id - priorityCount;
      regularNotifications = await fetchRegularNotifications(
        user_id,
        priorityTypesString,
        adjustedLastId,
        type,
        limit,
      );
    }

    // Step 4: Combine and assign row_ids
    const allNotifications = [];
    let currentRowId = last_id + 1;

    // Add priority notifications first (if we're still in their range)
    if (last_id < priorityCount) {
      const prioritySlice = priorityNotifications.slice(
        last_id,
        Math.min(last_id + limit, priorityCount),
      );
      prioritySlice.forEach((notification) => {
        allNotifications.push({
          ...notification,
          row_id: currentRowId++,
        });
      });
    }

    // Add regular notifications
    regularNotifications.forEach((notification) => {
      allNotifications.push({
        ...notification,
        row_id: currentRowId++,
      });
    });

    // Return exact same response format as V2
    res.send(allNotifications);
  } catch (error) {
    rollbar.error(error, req);
    return res.status(500).send({
      message: `${MESSAGES['500']}: ${error}`,
      status: 0,
    });
  }
}

/**
 * Fetch ALL priority notifications for a user
 * @param {string} user_id - The user ID
 * @param {string} priorityTypesString - SQL IN clause string for priority types
 * @returns {Promise<Array>} - Array of priority notifications
 */
async function fetchPriorityNotifications(user_id, priorityTypesString) {
  const query = `
    SELECT
      notification.id,
      notification.user_id,
      notification.created_at,
      notification.type,
      notification.message,
      notification.data,
      notification.read,
      notification.html_message
    FROM notification
    WHERE notification.user_id = $1
      AND notification.web_panel = true
      AND notification.delete_from_web_panel = false
      AND read = false
      AND notification.type IN (${priorityTypesString})

    UNION ALL

    SELECT
      system_message.id,
      system_message.user_id,
      system_message.created_at,
      system_message.type,
      system_message.message,
      system_message.data,
      true AS read,
      system_message.html_message
    FROM system_message
    WHERE system_message.user_id = $1
      AND system_message.delete_from_web_panel = false
      AND read = false
      AND system_message.type IN (${priorityTypesString})

    ORDER BY created_at DESC;
  `;

  return pgPoolQuery(query, [user_id]);
}

/**
 * Fetch regular (non-priority) notifications with pagination
 * @param {string} user_id - The user ID
 * @param {string} priorityTypesString - SQL IN clause string for priority types
 * @param {number} last_id - Last row ID for pagination
 * @param {string} type - Pagination direction
 * @param {number} limit - Number of notifications to fetch
 * @returns {Promise<Array>} - Array of regular notifications
 */
async function fetchRegularNotifications(
  user_id,
  priorityTypesString,
  last_id,
  type,
  limit,
) {
  let WHERE_QUERY = ``;

  if (last_id > 0) {
    if (type === 'next') {
      WHERE_QUERY = `AND row_id > ${last_id}`;
    } else {
      WHERE_QUERY = `AND row_id < ${last_id}`;
    }
  }

  const query = `
    SELECT *
    FROM (
      SELECT
        row_number() OVER (ORDER BY user_notifications.created_at DESC) AS row_id,
        user_notifications.id,
        user_notifications.user_id,
        user_notifications.created_at,
        user_notifications.type,
        user_notifications.message,
        user_notifications.data,
        user_notifications.read,
        user_notifications.html_message
      FROM (
        SELECT
          notification.id,
          notification.user_id,
          notification.created_at,
          notification.type,
          notification.message,
          notification.data,
          notification.read,
          notification.html_message
        FROM notification
        WHERE notification.user_id = $1
          AND notification.web_panel = true
          AND notification.delete_from_web_panel = false
          AND read = false
          AND notification.type NOT IN (${priorityTypesString})

        UNION ALL

        SELECT
          system_message.id,
          system_message.user_id,
          system_message.created_at,
          system_message.type,
          system_message.message,
          system_message.data,
          true AS read,
          system_message.html_message
        FROM system_message
        WHERE system_message.user_id = $1
          AND system_message.delete_from_web_panel = false
          AND read = false
          AND system_message.type NOT IN (${priorityTypesString})
      ) user_notifications
    ) AS subquery
    WHERE 1=1 ${WHERE_QUERY}
    ORDER BY created_at DESC
    LIMIT $2;
  `;

  return pgPoolQuery(query, [user_id, limit]);
}
