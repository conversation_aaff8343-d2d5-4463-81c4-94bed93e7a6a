import Joi from 'joi';
import MESSAGES from '../../../../constants/messages';
import pgPoolQuery from '../../../../utils/db/pgQuery';
import isAnyDateOutsideClosurePeriod from '../../../../utils/requests/helper/validateDateRange';
import checkRequest from '../../../../utils/requestsV4/checkRequest';
import userGuardCheck from '../../../../utils/auth/userGuardCheck';
import constantOptions from '../../../../constants/constantOptions';
import rollbar from '../../../../utils/rollbar';
import moment from 'moment';
import sendRequestToOtherMembers from '../../../../utils/requests/helper/sendRequestToOtherMembers';
import { invalidateRequestCache } from '../../../../utils/redis/requestCacheInvalidation';

const schema = Joi.object().keys({
  userId: Joi.string().trim().required(),
  requestId: Joi.string().trim().required(),
  players: Joi.number().max(4).min(1),
  startDate: Joi.date(),
  endDate: Joi.date(),
  message: Joi.string().trim().max(constantOptions.REQUEST_NOTES_MAX_LENGTH),
});

/**
 * API to edit request data
 * @param {*} req
 * @param {*} res
 * @returns
 */
const editRequest = async (req, res) => {
  try {
    // Validate request body
    let validate = schema.validate(req.body);

    if (validate.error) {
      let error = validate.error.details[0].message;
      return res.status(400).send({
        status: 0,
        message: MESSAGES[400],
        error,
      });
    }

    const { userId, requestId, players, startDate, endDate, message } =
      validate.value;

    const requestData = await checkRequest(requestId, userId);

    if (!requestData) {
      return res.status(404).send({
        status: 0,
        message: MESSAGES.REQUEST.REQUEST_NOT_FOUND,
      });
    }

    const {
      game_id,
      number_of_players,
      start_date,
      end_date,
      message: messageOld,
      guestTimeRestrictions,
      closurePeriods,
    } = requestData;

    //If the request is accepted by any host, then request is not allowed to edit.
    if (game_id) {
      return res.status(400).send({
        status: 0,
        message: MESSAGES.REQUEST.EDIT_REQUEST_NOT_ALLOWED,
      });
    }

    let dataToUpdate = {
      players: players || number_of_players,
      startDate: startDate || start_date,
      endDate: endDate || end_date,
      message: message || messageOld,
    };

    //Check if there is a date available
    if (
      dataToUpdate?.startDate !== start_date ||
      dataToUpdate?.endDate !== end_date
    ) {
      if (
        !isAnyDateOutsideClosurePeriod(
          dataToUpdate?.startDate,
          dataToUpdate?.endDate,
          closurePeriods,
          guestTimeRestrictions,
        )
      ) {
        return res.status(400).send({
          status: 0,
          message: MESSAGES.REQUEST.DATES_UNAVAILBLE,
        });
      }
    }

    //Update the request data
    await pgPoolQuery(
      `
            UPDATE request
            SET number_of_players = $1,
            start_date = $2,
            end_date = $3,
            message = $4
            WHERE id = $5
        `,
      [
        dataToUpdate?.players,
        dataToUpdate?.startDate,
        dataToUpdate?.endDate,
        dataToUpdate?.message,
        requestId,
      ],
    );

    // Invalidate cache for this request
    await invalidateRequestCache(requestId, userId);

    //If the start date of a request is changed and it is in upcoming four days, then send this request
    // to the remaining members as well
    if (
      moment(startDate, 'YYYY-MM-DD').isSameOrBefore(
        moment().add(4, 'days'),
        'days',
      )
    ) {
      await sendRequestToOtherMembers({ requestId: requestId });
    }

    return res.send({ status: 1, message: MESSAGES[200] });
  } catch (error) {
    rollbar.error(error, req);
    return res.status(500).send({
      message: `${MESSAGES['500']}: ${error}`,
      status: 0,
    });
  }
};

export default userGuardCheck(editRequest);
