import Joi from 'joi';
import constantOptions from '../../../../constants/constantOptions';
import MESSAGES from '../../../../constants/messages';
import userGuardCheck from '../../../../utils/auth/userGuardCheck';
import pgPoolQuery from '../../../../utils/db/pgQuery';
import rollbar from '../../../../utils/rollbar';
import { invalidateAllRequestCache } from '../../../../utils/redis/requestCacheInvalidation';
import sendGameDeclineNotification from '../../../../utils/notification/sendGameDeclineNotification';
import checkRequest from '../../../../utils/requestsV4/checkRequest';

const schema = Joi.object().keys({
  userId: Joi.string().trim().required(),
  requestId: Joi.string().trim().required(),
  deleteReason: Joi.string().trim().allow(''),
});
/**
 * Cancel a request
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} Response object
 */
const cancelRequest = async (req, res) => {
  try {
    // Validate request body
    let validate = schema.validate(req.body);

    if (validate.error) {
      let error = validate.error.details[0].message;
      return res.status(400).send({
        message: error,
        status: 0,
      });
    }

    const { userId, requestId, deleteReason } = validate.value;

    const isRequest = await checkRequest(requestId, userId);

    const cancelledBy = constantOptions?.REQUEST_CANCELLED_BY?.REQUESTER;

    // if hostCompleted and requestorCompleted is false/null then we can cancel request else not
    if (isRequest) {
      if (!isRequest?.hostCompleted && !isRequest?.requestorCompleted) {
        await pgPoolQuery(
          `
                        UPDATE "request"
                        SET status = 'cancelled', "deleteReason" = $2, cancelled_by = $3
                        WHERE id = $1
                `,
          [requestId, deleteReason, cancelledBy],
        );

        // If there's a game associated with the request, track cancellation and send admin notification if needed
        if (isRequest.game_id) {
          await sendGameDeclineNotification(
            requestId,
            userId,
            'requester',
            deleteReason || '',
          );
        }

        // Invalidate cache for this request
        await invalidateAllRequestCache(requestId);

        return res.send({
          message: MESSAGES[200],
          status: 1,
          data: {},
        });
      } else {
        return res.status(400).send({
          message: `${MESSAGES.REQUEST.REQUEST_ALREADY_MARK_COMPLETED}`,
          status: 0,
        });
      }
    } else {
      return res.status(400).send({
        message: `${MESSAGES.REQUEST.REQUEST_NOT_VALID}`,
        status: 0,
      });
    }
  } catch (error) {
    rollbar.error(error, req);
    return res.status(500).send({
      message: `${MESSAGES['500']}: ${error}`,
      status: 0,
    });
  }
};

export default userGuardCheck(cancelRequest);
