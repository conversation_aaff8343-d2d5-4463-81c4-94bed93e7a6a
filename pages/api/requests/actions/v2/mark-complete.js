import Joi from 'joi';
import MESSAGES from '../../../../../constants/messages';
import checkRequest from '../../../../../utils/requestsV4/checkRequest';
import checkHostRequest from '../../../../../utils/requestsV4/checkHostRequest';
import pgPoolQuery from '../../../../../utils/db/pgQuery';
import userGuardCheck from '../../../../../utils/auth/userGuardCheck';
import rollbar from '../../../../../utils/rollbar';
import adminClient from '../../../../../graphql/adminClient';

// Modified schema to make reason required when satisfied is false
const schema = Joi.object().keys({
  userId: Joi.string().trim().required(),
  requestId: Joi.string().trim().required(),
  satisfied: Joi.boolean().required(),
  reason: Joi.when('satisfied', {
    is: false,
    then: Joi.string().trim().required().messages({
      'any.required':
        'Reason is required when the experience is not satisfactory',
      'string.empty':
        'Reason cannot be empty when the experience is not satisfactory',
    }),
    otherwise: Joi.string().allow('', null),
  }),
  review: Joi.string().allow('', null), // only for Requester
  photo: Joi.string().allow('', null), // only for Requester
});

/**
 * API v2 to mark the request as complete (either by host or requester)
 * Requires reason when satisfied is false
 * @param {*} req
 * @param {*} res
 * @returns
 */
const markComplete = async (req, res) => {
  try {
    // Validate request body
    let validate = schema.validate(req.body);

    if (validate.error) {
      let error = validate.error.details[0].message;
      return res.status(400).send({
        status: 0,
        message: MESSAGES[400],
        error,
      });
    }

    const { userId, requestId, satisfied, review, reason, photo } =
      validate.value;

    let requestData,
      userType = 'R';

    // If the user is a requester
    requestData = await checkRequest(requestId, userId);

    if (!requestData) {
      // If the user is host
      requestData = await checkHostRequest({ requestId, hostId: userId });
      userType = 'H';
    }

    if (!requestData) {
      return res.status(404).send({
        status: 0,
        message: MESSAGES.REQUEST.REQUEST_NOT_FOUND,
      });
    }

    //Check if host has disabled review
    const { user_club } = await adminClient.request(
      `
          query GetUserClub($userId: uuid!, $clubId: Int!) {
              user_club(where: {club_id: {_eq: $clubId}, user_id: {_eq: $userId}}) {
                  review_disabled
                  }
                  }
                  `,
      {
        userId: requestData.host_id,
        clubId: requestData.club_id,
      },
    );

    if (userType === 'R' && !review && !user_club?.[0]?.review_disabled) {
      return res.status(400).send({
        status: 0,
        message: MESSAGES.REQUEST.GAME_REVIEW_REQUIRED,
      });
    }

    const [additionalRequestData] = await pgPoolQuery(
      `
            SELECT
                r.status,
                g.status AS "gameStatus",
                r.hosts_declined,
                g.host_id,
                g.game_id as "gameId",
                g."hostCompleted",
                g."requestorCompleted"
            FROM
                request r
            LEFT JOIN game g ON r.game_id = g.game_id
            WHERE r.id = $1`,
      [requestId],
    );

    if (
      additionalRequestData?.status !== 'cancelled' &&
      additionalRequestData.gameStatus !== 'declined' &&
      additionalRequestData?.host_id
    ) {
      // The host who has accepted the game
      if (
        additionalRequestData?.host_id === userId &&
        userType === 'H' &&
        !additionalRequestData?.hosts_declined?.includes(userId) &&
        !additionalRequestData?.hostCompleted
      ) {
        await pgPoolQuery(
          `
                    UPDATE game
                    SET 
                        "hostCompleted" = TRUE,
                        "hostSatisfied" = $2,
                        host_experience_reason = $3
                    WHERE request_id = $1;
                `,
          [requestId, satisfied, !satisfied ? reason : null],
        );

        return res.send({
          message: MESSAGES[200],
          status: 1,
          data: {},
        });
      } else if (
        userType === 'R' &&
        !additionalRequestData?.requestorCompleted
      ) {
        await pgPoolQuery(
          `
                    BEGIN;

                    UPDATE game
                    SET 
                        "requestorCompleted" = TRUE,
                        "requestorSatisfied" = $7,
                        "requestorReview" = $3,
                        requestor_experience_reason = $6
                    WHERE request_id = $1;

                    ${
                      review
                        ? `INSERT INTO game_review (user_id, review, game_id, photo)
                    VALUES ($2, $3, $4, $5);`
                        : ''
                    }

                    COMMIT;
                `,
          [
            requestId,
            userId,
            review,
            additionalRequestData?.gameId,
            photo,
            !satisfied ? reason : null,
            satisfied,
          ],
        );

        return res.send({
          message: MESSAGES[200],
          status: 1,
          data: {},
        });
      }
    }

    return res.status(400).send({
      message: MESSAGES.REQUEST.REQUEST_CANNOT_BE_MARKED_COMPLETED,
      status: 0,
    });
  } catch (error) {
    rollbar.error(error, req);
    return res.status(500).send({
      message: `${MESSAGES['500']}: ${error}`,
      status: 0,
    });
  }
};

export default userGuardCheck(markComplete);
