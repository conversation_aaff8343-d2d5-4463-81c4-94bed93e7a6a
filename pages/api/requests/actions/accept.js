import pgPoolQuery from '../../../../utils/db/pgQuery';
import MESSAGES from '../../../../constants/messages';
import userGuardCheck from '../../../../utils/auth/userGuardCheck';
import Joi from 'joi';
import constantOptions from '../../../../constants/constantOptions';
import checkHostRequest from '../../../../utils/requestsV4/checkHostRequest';
import moment from 'moment';
import adminClient from '../../../../graphql/adminClient';
import rollbar from '../../../../utils/rollbar';
import { invalidateRequestCache } from '../../../../utils/redis/requestCacheInvalidation';

const {
  REQUEST: {
    STATUS: { CANCELLED },
    DATE_FORMATE: { YYYY_MM_DD },
  },
} = constantOptions;

const schema = Joi.object().keys({
  userId: Joi.string().trim().required(),
  requestId: Joi.string().trim().required(),
  gameDate: Joi.date().required().min(moment().subtract(1, 'day')).messages({
    'date.min': 'Game date must be greater than or equal to the current date.',
  }),
});

/**
 * API to accept a request
 * @param {*} req
 * @param {*} res
 * @returns
 */
const acceptRequest = async (req, res) => {
  try {
    // Validate request body
    let validate = schema.validate(req.body);

    if (validate.error) {
      let error = validate.error.details[0].message;
      return res.status(400).send({
        status: 0,
        message: MESSAGES[400],
        error,
      });
    }

    const { userId, requestId, gameDate } = validate.value;

    const isRequest = await checkHostRequest({ requestId, hostId: userId });

    if (!isRequest) {
      return res.status(400).send({
        status: 0,
        message: MESSAGES.REQUEST.REQUEST_NOT_ALLOWED_TO_ACCEPT,
      });
    }

    const { has_messages, status, game_id, request_game_id } = isRequest;

    let formattedGameDate = moment(gameDate).format(YYYY_MM_DD);

    //If request is cancelled or the request is already accepted by a host.
    if (status === CANCELLED || game_id) {
      return res.status(400).send({
        status: 0,
        message: MESSAGES.REQUEST.REQUEST_ACCEPTED_OR_CANCELLED,
      });
    }

    //Checking if the chat is initiated or not
    if (!has_messages) {
      return res.status(400).send({
        status: 0,
        message: MESSAGES.REQUEST.INITIATE_CHAT_FOR_REQUEST,
      });
    }

    // Insert in accepted_request table
    await pgPoolQuery(
      `
            INSERT into accepted_request (host_id, request_id, date)
            VALUES ($1, $2, $3)
            ON CONFLICT (host_id, request_id) DO NOTHING
         `,
      [userId, requestId, formattedGameDate],
    );

    // Inserting a game row here, as the event of creating game row takes time, especially on production env
    await adminClient.request(`
         mutation {
            insert_game(on_conflict: {constraint: game_pkey, update_columns: updated_at}, objects: {game_id: ${request_game_id}, host_id: "${userId}", request_id: "${requestId}", date: "${formattedGameDate}"}) {
                affected_rows
            }
        }
        `);

    // Invalidate cache for this request and user
    await invalidateRequestCache(requestId, userId);

    return res.send({
      status: 1,
      message: MESSAGES['200'],
    });
  } catch (error) {
    rollbar.error(error, req);
    return res.status(500).send({
      message: `${MESSAGES['500']}: ${error}`,
      status: 0,
    });
  }
};

export default userGuardCheck(acceptRequest);
