import adminClient from '../../../graphql/adminClient'
import { CREATE_NOTIFICATION } from '../../../graphql/mutations/notification'
import {
    UPDATE_REQUEST_HOSTS,
    UPDATE_REQUEST_PN,
} from '../../../graphql/mutations/request'
import getTier from '../../../utils/tiers/getTier'
import Moment from 'moment'
import { sendMailTemplate } from '../../../utils/mailchimp'
import { GET_USER_EMAIL } from '../../../graphql/queries/user'
import createDynamicLink from '../../../utils/notifications/createDynamicLink'
import requestAcceptanceCalculator from '../../../utils/helper/requestAcceptanceCalculator'
import requestsCompletedByUser from '../../../utils/requests/requestsCompletedByUser'
import EMAILS from '../../../constants/emails'
import constantOptions from '../../../constants/constantOptions'
import { createCustomTokenWithExpiration } from '../../../utils/auth/firebaseAdmin'
import pgPoolQuery from '../../../utils/db/pgQuery'
import getStreamInstance from '../../../utils/chat-v2/getStreamInstance'
import checkFriend from '../../../utils/friends/checkFriend'
import sendNewRequestAdminNotification from '../../../utils/notifications/sendNewRequestAdminNotification'
import handleOfflineGame from '../../../utils/requests/helper/handleOfflineGame'
import updateTealDotFlags from '../../../utils/user/updateTealDotFlags'
import dateFormatter from '../../../utils/helper/dateFormatter'

const {
    subject: hostSubject,
    template: hostTemplate,
    message: hostMessage,
    text_message: hostTextMessage,
    html_message: hostHtmlMessage,
} = EMAILS.SEND_REQUEST.HOSTS
const {
    STREAM_CHANNEL_TYPES: { REQUEST_CHAT_GROUP },
} = constantOptions

const GET_REQUESTOR = `
query getRequestor($id: uuid!) {
    user_by_pk(id: $id) {
      tier
      first_name
      full_name
      email
      about_yourself
      age
      gender
      handicap
      visibleToPublic
      private_network_user {
        private_network_id
      }
      pace
      clubs {
        club {
          name
        }
      }
      playedClubs
    }
    system_setting_by_pk(name: "open_requests_validity_length") {
      value
    }
  }
`

const GET_CLUB_NAME = `
query getRequestClub($request_id: uuid!) {
    request_by_pk(id: $request_id) {
      club {
        name
        stats
        requests {
          accepted_requests_aggregate {
            aggregate {
              count
            }
          }
        }
      }
    }
  }
`

const GET_REQUEST_HOSTS = `
query clubRequestMembers($club_id: Int, $user_ids: [uuid!]) {
    user_club(where: {club_id: {_eq: $club_id}, muted: {_eq: false}, user: {muted: {_eq: false}, account_activated: {_eq: true}}, user_id: {_in: $user_ids}}) {
        user {
            id
            age
            gender
            visibleToPublic
            handicap
            pace
            englishFluency
            private_network_user {
                private_network_id
            }
            email
            first_name
            last_name
            muted
            playAsCouple
        }
        muted
        }
    }
`

const UNACCEPTED_REQUEST_CHATS = `
query getUnacceptedRequestChats($request_id: uuid) {
    request_chat(where: {request_id: {_eq: $request_id}, has_messages: {_eq: true}}) {
        sendbird_channel_id
        stream_channel_id
        club_member_id
        request {
            game_id
        }
    }
}  
`

const OFFER = `
query getOffer($id: uuid!) {
    offer_by_pk(id: $id) {
        club_id
        created_at
        details
        end_date
        id
        offer_id
        private_network_id
        start_date
        status
        tierVisibility
        updated_at
        user_id
    }
}
`

const GET_GAME = `
query getGame($gameId: Int!) {
  game(where: {game_id: {_eq: $gameId}}) {
    request_id
    requestorCompleted
  }
}
`

export default async function sendRequest(req, res) {
    const { op, data } = req.body.event
    const { old: oldRequest, new: newRequest } = data

    if (newRequest.is_played_offline) {
        // Handle offline game logic
        if (op === 'INSERT') {
            const result = await handleOfflineGame(newRequest)
            return res.send({
                message: 'Offline game processed',
                result,
            })
        } else {
            return res.send({
                message: 'Offline game processed',
            })
        }
    }

    if (op === 'INSERT') {
        if (newRequest.hosts_sent.length === 0) {
            await adminClient.request(`
                mutation {
                    delete_request_by_pk(id: "${newRequest.id}") {
                        id
                    }
                }
            `)

            const { user_by_pk } = await adminClient.request(GET_USER_EMAIL, {
                id: newRequest.user_id,
            })

            if (!user_by_pk?.deleted_at) {
                const mailResult = await sendMailTemplate({
                    email: user_by_pk?.email,
                    subject: 'Notice | Request Deleted',
                    template_name: 'Request Deletion Notice',
                })
            }

            res.send(
                `Request ${newRequest.id} created by ${newRequest.user_id} has been deleted because there were no hosts : ${newRequest.hosts_sent}`
            )
        } else {
            const {
                request_by_pk: {
                    club: {
                        name: club_name,
                        stats: club_stats,
                        requests: allClubRequests,
                    },
                },
            } = await adminClient.request(GET_CLUB_NAME, {
                request_id: newRequest.id,
            })

            const {
                user_by_pk: {
                    first_name: requestor_name,
                    email: requestor_email,
                    clubs: requestor_clubs,
                    tier: requestor_tier,
                    ...requestor_user
                },
                system_setting_by_pk: {
                    value: { value: daysRequestValid },
                },
            } = await adminClient.request(GET_REQUESTOR, {
                id: newRequest.user_id,
            })

            // Send admin notification about the new play request
            await sendNewRequestAdminNotification(newRequest, {
                first_name: requestor_name,
                email: requestor_email,
                ...requestor_user,
            })

            const requestor = {
                clubs: requestor_clubs.reduce((clubNames, club, index) => {
                    if (index === 0) {
                        return club.club.name
                    } else if (index === 1) {
                        if (requestor_clubs.length === 2) {
                            return `${clubNames} and ${club.club.name}`
                        } else {
                            return `${clubNames}, ${club.club.name}`
                        }
                    } else {
                        if (index === requestor_clubs.length - 1) {
                            return `${clubNames}, and ${club.club.name}`
                        } else {
                            return `${clubNames}, ${club.club.name}`
                        }
                    }
                }, ''),
                tier: getTier(requestor_tier),
            }

            // Creating short link for this request to send in text and email
            const { shortLink } = await createDynamicLink({
                webLink: `dashboard/play`,
                appParams: `type=requests&id=${newRequest?.id}`,
            })
            const shortLinkMessage = shortLink
                ? `Click here to check details: ${shortLink}`
                : ''

            // ** For Request Against Offers only **
            if (newRequest?.offer_id) {
                const { user_id, club_id } = newRequest
                const { offer_by_pk } = await adminClient.request(OFFER, {
                    id: newRequest.offer_id,
                })

                if (offer_by_pk?.private_network_id) {
                    await adminClient.request(UPDATE_REQUEST_PN, {
                        request_id: newRequest.id,
                        private_network_id: offer_by_pk?.private_network_id,
                    })
                }

                const updatedRequest = await adminClient.request(
                    UPDATE_REQUEST_HOSTS,
                    {
                        request_id: newRequest.id,
                        hosts: [offer_by_pk?.user_id],
                        offer_details: offer_by_pk,
                    }
                )

                const { user_by_pk } = await adminClient.request(
                    GET_USER_EMAIL,
                    {
                        id: newRequest.hosts_sent[0],
                    }
                )

                const token = await createCustomTokenWithExpiration(
                    newRequest.hosts_sent[0],
                    newRequest.id
                )
                let {
                    hosted: hostedByRequester,
                    requested: requestedByRequester,
                } = await requestsCompletedByUser(user_id)
               
                const totalRequestedGames = await pgPoolQuery(
                    `
                     SELECT
            COUNT(*) AS requested
        FROM 
            request
        WHERE 
            request.user_id = $1
            AND request.status != 'cancelled';
                    `,
                    [user_id]
                )
                const customDeclineLink = `${process.env.CONFIG.baseURL}/sign-in-with-token/request-decline?requestId=${newRequest?.id}&tokenId=${token.id}`
                let htmlMessage = `New request #${newRequest.game_id} was created for ${club_name} against your offer.`
                let textMessage = `New request #${newRequest.game_id} was created for ${club_name} against your offer`
                const notification = {
                    user_id: newRequest.hosts_sent[0],
                    type: 'host-request',
                    message: textMessage,
                    text_message: `${textMessage} ${shortLinkMessage}`,
                    html_message: `${htmlMessage}`,
                    email_template_name: 'New Request Against My Offer V2',
                    data: {
                        request_id: newRequest?.id,
                        template_content: [
                            {
                                name: 'first_name',
                                content: user_by_pk?.first_name,
                            },
                            {
                                name: 'is_first_time_requester',
                                content: newRequest?.is_first_request
                                    ? '(First Time Requester) '
                                    : '',
                            },
                            {
                                name: 'requestor_club_name',
                                content: `${requestor.clubs}`,
                            },
                            {
                                name: 'requestor_tier',
                                content: `${requestor.tier}`,
                            },
                            {
                                name: 'host_club_name',
                                content: `${club_name}`,
                            },
                            {
                                name: 'request_id',
                                content: newRequest.game_id,
                            },
                            {
                                name: 'offer_id',
                                content: offer_by_pk?.offer_id,
                            },
                            {
                                name: 'request_dates',
                                content: `${dateFormatter(
                                    Moment.utc(newRequest.start_date),
                                    Moment.utc(newRequest.end_date)
                                )}`,
                            },
                            {
                                name: 'max_players',
                                content: newRequest.number_of_players,
                            },
                            {
                                name: 'request_notes',
                                content: newRequest.message,
                            },
                            {
                                name: 'request_age',
                                content: requestor_user.age,
                            },
                            {
                                name: 'request_handicap',
                                content: requestor_user.handicap,
                            },
                            {
                                name: 'request_pace',
                                content: requestor_user.pace,
                            },
                            {
                                name: 'request_gender',
                                content: requestor_user.gender,
                            },
                            {
                                name: 'requestor_name',
                                content: requestor_name,
                            },
                            {
                                name: 'dynamic_link',
                                content: shortLinkMessage,
                            },
                            {
                                name: 'games_hosted',
                                content: hostedByRequester,
                            },
                            {
                                name: 'games_requested',
                                content: requestedByRequester,
                            },
                            {
                                name: 'total_games_requested',
                                content:
                                    totalRequestedGames[0]?.requested,
                            },
                            {
                                name: 'decline_link',
                                content: customDeclineLink,
                            },
                            {
                                name: 'reply_link',
                                content: shortLink,
                            },
                            {
                                name: 'host_count',
                                content: newRequest?.eligible_hosts?.length,
                            },
                        ],
                        email: user_by_pk?.email,
                        subject: `New Request (#${newRequest.game_id}) against Offer #${offer_by_pk?.offer_id}`,
                    },
                }

                await adminClient.request(CREATE_NOTIFICATION, {
                    notifications: [notification],
                })

                res.send({ updatedRequest })
                return
            } else {
                const { user_id, club_id } = newRequest

                // ** Add private_network_id if the request is created for PN as on 27th Oct 2021 **
                if (requestor_user?.private_network_user?.private_network_id) {
                    await adminClient.request(UPDATE_REQUEST_PN, {
                        request_id: newRequest.id,
                        private_network_id:
                            requestor_user.private_network_user
                                .private_network_id,
                    })
                }
                // ** End of private_network_id if the request is created for PN **

                // *** Finding requests completed by user as a requester and host, added as on 26th April 2023 ****
                let {
                    hosted: hostedByRequester,
                    requested: requestedByRequester,
                } = await requestsCompletedByUser(user_id)
                const { user_club } = await adminClient.request(
                    GET_REQUEST_HOSTS,
                    {
                        club_id,
                        user_ids: newRequest.hosts_sent,
                    }
                )

                const hostNotifications = []
                const totalRequestedGames = await pgPoolQuery(
                    `
                     SELECT
            COUNT(*) AS requested
        FROM 
            request
        WHERE 
            request.user_id = $1
            AND request.status != 'cancelled';
                    `,
                    [user_id]
                )

                for (const { user: host } of user_club) {
                    try {
                        const [{ count }] = await checkFriend({
                            senderId: user_id,
                            receiverId: host.id,
                        })

                        const mutualGroups = await pgPoolQuery(
                            `
                            SELECT 
                cc.id,
                cc.name
            FROM chat_channel cc
            JOIN chat_channel_member ccm1 ON ccm1.chat_channel_id = cc.id AND ccm1.user_id = $2
            JOIN chat_channel_member ccm2 ON ccm2.chat_channel_id = cc.id AND ccm2.user_id = $1
            WHERE cc.channel_type = 'my_tg_group'
            AND cc.is_active = TRUE
                            `,
                            [host.id, user_id]
                        )

                        const dynamicMessage =
                            //Only Friend
                            `${count !== '0' && mutualGroups?.length === 0
                                ? `One of your TG friends from ${requestor?.clubs}`
                                : //Only 1 common group
                                count === '0' && mutualGroups?.length > 0
                                    ? `A member from one of your common groups ${mutualGroups[0].name} from ${requestor?.clubs}`
                                    : //Friend & common group
                                    count !== '0' && mutualGroups.length > 0
                                        ? `One of your TG friends from ${requestor?.clubs}`
                                        : //None of above
                                        `A member from ${requestor?.clubs}`
                            }`

                        const token = await createCustomTokenWithExpiration(
                            host.id,
                            newRequest.id
                        )
                        const customDeclineLink = `${process.env.CONFIG.baseURL}/sign-in-with-token/request-decline?requestId=${newRequest?.id}&tokenId=${token.id}`

                        // If the requester has no accepted requests yet, then we will send an extra line in the mail to the hosts
                        let countOfAcceptedRequestsOfRequester =
                            await pgPoolQuery(
                                `
                            SELECT
                            COUNT(game.game_id):: INT
                            FROM
                                request r
                            JOIN
                                game ON game.request_id = r.id
                            WHERE r.user_id = $1`,
                                [newRequest?.user_id]
                            )

                        countOfAcceptedRequestsOfRequester =
                            countOfAcceptedRequestsOfRequester?.[0]?.count

                        await updateTealDotFlags({
                            flagKey: constantOptions.TEAL_DOT_FLAG_KEYS.REQUEST,
                            userIds: [host.id],
                        })

                        // Push the result to the hostNotifications array
                        hostNotifications.push({
                            user_id: host.id,
                            type: 'host-request',
                            message: hostMessage({
                                gameId: newRequest.game_id,
                                clubName: club_name,
                            }),
                            html_message: hostHtmlMessage({
                                gameId: newRequest.game_id,
                                clubName: club_name,
                            }),
                            text_message: hostTextMessage({
                                gameId: newRequest.game_id,
                                clubs: requestor.clubs,
                                tier: requestor.tier,
                                clubName: club_name,
                                shortLinkMessage,
                            }),
                            email_template_name: hostTemplate,
                            data: {
                                request_id: newRequest?.id,
                                template_content: [
                                    {
                                        name: 'first_name',
                                        content: host.first_name,
                                    },
                                    {
                                        name: 'is_first_time_requester',
                                        content: newRequest?.is_first_request
                                            ? '(First Time Requester) '
                                            : '',
                                    },
                                    {
                                        name: 'requestor_club_name',
                                        content: `${requestor.clubs} `,
                                    },
                                    {
                                        name: 'requestor_tier',
                                        content: `${requestor.tier} `,
                                    },
                                    {
                                        name: 'host_club_name',
                                        content: `${club_name} `,
                                    },
                                    {
                                        name: 'request_id',
                                        content: newRequest.game_id,
                                    },
                                    {
                                        name: 'request_dates',
                                        content: `${dateFormatter(
                                            Moment.utc(newRequest.start_date),
                                            Moment.utc(newRequest.end_date)
                                        )}`,
                                    },
                                    {
                                        name: 'max_players',
                                        content: newRequest.number_of_players,
                                    },
                                    {
                                        name: 'request_notes',
                                        content: newRequest.message,
                                    },
                                    {
                                        name: 'request_age',
                                        content: requestor_user.age,
                                    },
                                    {
                                        name: 'request_handicap',
                                        content: requestor_user.handicap,
                                    },
                                    {
                                        name: 'request_pace',
                                        content: requestor_user.pace,
                                    },
                                    {
                                        name: 'request_gender',
                                        content: requestor_user.gender,
                                    },
                                    {
                                        name: 'dynamic_link',
                                        content: shortLinkMessage,
                                    },
                                    {
                                        name: 'games_hosted',
                                        content: hostedByRequester,
                                    },
                                    {
                                        name: 'games_requested',
                                        content: requestedByRequester,
                                    },
                                    {
                                        name: 'total_games_requested',
                                        content:
                                            totalRequestedGames[0]?.requested,
                                    },
                                    {
                                        name: 'decline_link',
                                        content: customDeclineLink,
                                    },
                                    {
                                        name: 'reply_link',
                                        content: shortLink,
                                    },
                                    {
                                        name: 'dynamic_message',
                                        content: dynamicMessage,
                                    },
                                ],
                                email: host.email,
                                subject: hostSubject(club_name),
                            },
                        })
                    } catch (error) {
                        console.error(
                            'Error processing host notification:',
                            error
                        )
                        continue
                    }
                }
                const { percentage: requestAcceptancePercentage } =
                    await requestAcceptanceCalculator()

                const userNotification = {
                    user_id,
                    type: 'my-host-request',
                    message: `Your request(#${newRequest.game_id}) was successfully sent to ${newRequest?.eligible_hosts?.length} users`,
                    text_message: `Your request(#${newRequest.game_id}) was successfully sent to ${newRequest?.eligible_hosts?.length} members in Thousand Greens.${shortLinkMessage} `,
                    email_template_name: 'Sent My Request V3',
                    data: {
                        request_id: newRequest?.id,
                        template_content: [
                            {
                                name: 'first_name',
                                content: requestor_name,
                            },
                            {
                                name: 'club_name',
                                content: club_name,
                            },
                            {
                                name: 'acceptance_percentage',
                                content: parseFloat(
                                    requestAcceptancePercentage
                                ).toFixed(2),
                            },
                            {
                                name: 'open_requests_validity_length',
                                content: `${daysRequestValid} days`,
                            },
                            {
                                name: 'dynamic_link',
                                content: shortLinkMessage,
                            },
                            {
                                name: 'host_count',
                                content: newRequest?.eligible_hosts?.length,
                            },
                        ],
                        email: requestor_email,
                        subject: `Your request(#${newRequest.game_id}) was successfully sent to ${newRequest?.eligible_hosts?.length} users`,
                    },
                }

                if (hostNotifications.length > 0) {
                    await adminClient.request(CREATE_NOTIFICATION, {
                        notifications: [...hostNotifications, userNotification],
                    })
                }

                res.send({ hostNotifications, userNotification })
                return
            }
        }
    } else {
        //Check if a game is created then we do not need to send notifications
        const { game } = await adminClient.request(GET_GAME, {
            gameId: newRequest.game_id,
        })

        if (game?.length) {
            return res.send('No update after game creation.')
        }

        const dateChanged =
            oldRequest.start_date !== newRequest.start_date ||
            oldRequest.end_date !== newRequest.end_date
        const playersChanged =
            oldRequest.number_of_players !== newRequest.number_of_players
        const messageChanged = oldRequest.message !== newRequest.message

        let notifications = []
        let dynamicMessage = ''
        if (dateChanged && playersChanged && messageChanged) {
            dynamicMessage = 'dates, notes and number of participants'
        } else if (!dateChanged && playersChanged && messageChanged) {
            dynamicMessage = 'notes and number of participants'
        } else if (dateChanged && playersChanged && !messageChanged) {
            dynamicMessage = 'dates and number of participants'
        } else if (dateChanged && !playersChanged && messageChanged) {
            dynamicMessage = 'dates and notes'
        } else if (!dateChanged && !playersChanged && messageChanged) {
            dynamicMessage = 'notes'
        } else if (!dateChanged && playersChanged && !messageChanged) {
            dynamicMessage = 'number of participants'
        } else if (dateChanged && !playersChanged && !messageChanged) {
            dynamicMessage = 'dates'
        }

        let message = `The ${dynamicMessage} for game #${newRequest.game_id} have been changed.`
        let text_message = `The ${dynamicMessage} for game #${newRequest.game_id} have been changed.`

        if (dateChanged || playersChanged || messageChanged) {
            const hosts = newRequest.hosts_sent

            hosts.map((id) => {
                if (
                    (!newRequest.hosts_declined ||
                        (newRequest.hosts_declined &&
                            !newRequest.hosts_declined.includes(id))) && // Request should not be declined by the host
                    (!newRequest.deleted_by_users ||
                        (newRequest.deleted_by_users &&
                            !newRequest.deleted_by_users.includes(id))) // Request should not be deleted by the host
                ) {
                    notifications.push({
                        user_id: id,
                        type: 'host-request',
                        message,
                        text_message,
                        data: {
                            request_id: newRequest?.id,
                        },
                    })
                }
            })

            await updateTealDotFlags({
                flagKey: constantOptions.TEAL_DOT_FLAG_KEYS.REQUEST,
                userIds: hosts,
            })

            await adminClient.request(CREATE_NOTIFICATION, {
                notifications,
            })

            // ** Send system message to the hosts who did not decline/delete the request **
            const { request_chat: requestChats } = await adminClient.request(
                UNACCEPTED_REQUEST_CHATS,
                {
                    request_id: newRequest.id,
                }
            )

            if (requestChats.length > 0) {
                await Promise.all(
                    requestChats.map(
                        async ({
                            sendbird_channel_id,
                            stream_channel_id,
                            club_member_id,
                            request: { game_id },
                        }) => {
                            if (
                                (!newRequest.hosts_declined ||
                                    (newRequest.hosts_declined &&
                                        !newRequest.hosts_declined.includes(
                                            club_member_id
                                        ))) && // Request should not be declined by the host
                                (!newRequest.deleted_by_users ||
                                    (newRequest.deleted_by_users &&
                                        !newRequest.deleted_by_users.includes(
                                            club_member_id
                                        ))) // Request should not be deleted by the host
                            ) {
                                if (stream_channel_id) {
                                    const serverClient = getStreamInstance()
                                    const channel = serverClient.channel(
                                        REQUEST_CHAT_GROUP,
                                        stream_channel_id
                                    )

                                    const message = {
                                        text: `The details of this request have been changed by the requester.`,
                                        type: 'system', // Specify the type as 'system'
                                        user: { id: 'system' }, // A user ID for system messages
                                    }

                                    // Send the message
                                    await channel.sendMessage(message)
                                }
                            }
                        }
                    )
                )
            }

            res.send({ notifications })
            return
        } else {
            res.send('Dates and number of players were not updated')
            return
        }
    }
}
