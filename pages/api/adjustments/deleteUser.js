import adminClient from '../../../graphql/adminClient';
import { deleteUser } from '../../../utils/auth/firebaseAdmin';
import md5 from 'md5';
import { deleteFromMailChimp } from '../../../utils/mailchimp/deleteFromMailChimp';
import { sendMailTemplate } from '../../../utils/mailchimp';
import moment from 'moment';
import EMAILS from '../../../constants/emails';
import { CHECK_PN_EXISTS } from '../../../graphql/queries/private_networks';
import deleteUserFromStream from '../../../utils/chat-v2/deleteUserFromStream';
import rollbar from '../../../utils/rollbar';
import { invalidateRequestCache } from '../../../utils/redis/requestCacheInvalidation';

const FETCH_USER_REQUESTS = `
query fetchRequests($user_id: jsonb) {
    request(where: {hosts_sent: {_contains: $user_id}}) {
        id
        hosts_sent
        hosts_declined
        deleted_by_users
    }
}
`;
const UPDATE_REQUEST = `
mutation updateRequest($request_id: uuid!, $user_id: jsonb, $hosts_sent: jsonb, $hosts_declined: jsonb, $deleted_by_users: jsonb) {
    update_request(where: {id: {_eq: $request_id}, hosts_sent: {_contains: $user_id}}, _set: {hosts_sent: $hosts_sent, hosts_declined: $hosts_declined, deleted_by_users: $deleted_by_users}) {
        affected_rows
    }
}
`;

async function deleteUserFromMailChimp(email) {
  const subscriber_hash = md5(email.toLowerCase());
  const response = await deleteFromMailChimp({ subscriber_hash });
}

async function sendUserMail(email) {
  await sendMailTemplate({
    email: email,
    subject: 'Account Deleted in Thousand Greens',
    template_name: 'Delete User V2',
    template_content: [],
  });
}

async function sendUserIncompleteRegistrationMail(email, first_name) {
  await sendMailTemplate({
    email: email,
    template_name: 'User Deletion - Incomplete Registration V2',
    template_content: [
      {
        name: 'first_name',
        content: first_name,
      },
    ],
    subject: 'Deletion of account due to Incomplete Registration',
  });
}

async function sendUserPostDeclinationMail(email, first_name) {
  await sendMailTemplate({
    email: email,
    template_name: 'User Deletion - Post User Declination V2',
    template_content: [
      {
        name: 'first_name',
        content: first_name,
      },
    ],
    subject: 'Deletion of account due to Declination',
  });
}

async function sendUserPostActivateLaterMail(email, first_name) {
  await sendMailTemplate({
    email: email,
    template_name: 'User Deletion - Post Activate Later V2',
    template_content: [
      {
        name: 'first_name',
        content: first_name,
      },
    ],
    subject: 'Deletion of account due to Failure of Account Verification',
  });
}

async function sendLegacyUserDeletionMail(email, first_name) {
  await sendMailTemplate({
    email,
    template_name: EMAILS.LEGACY_USERS.DELETION.template,
    template_content: [
      {
        name: 'first_name',
        content: first_name,
      },
    ],
    subject: EMAILS.LEGACY_USERS.DELETION.subject,
  });
}

const sendUserPNRemovalDeletionMail = async (email, first_name, pn) => {
  const { private_networks_aggregate } = await adminClient.request(
    CHECK_PN_EXISTS,
    {
      id: parseInt(pn),
    },
  );
  let pn_name = '';
  if (private_networks_aggregate?.nodes.length) {
    pn_name = private_networks_aggregate?.nodes[0]?.name;
  }
  await sendMailTemplate({
    email,
    template_name: EMAILS.DELETION.PN_REMOVAL.template,
    template_content: [
      {
        name: 'first_name',
        content: first_name,
      },
      {
        name: 'pn_name',
        content: pn_name,
      },
    ],
    subject: EMAILS.DELETION.PN_REMOVAL.subject,
  });
};

export default async function deleteUserFromFirebase(req, res) {
  try {
    const {
      event: {
        data: {
          old: {
            id: user_id,
            email,
            registration_incomplete_deletion,
            first_name,
            declination_date,
            declined,
            activate_later,
            activate_later_date,
            deleted_at,
            is_legacy_user,
            legacy_password_reset,
            legacy_user_reminder_date,
            pn_removal_deletion,
          },
        },
      },
    } = req.body;

    // Delete the user from requests
    const { request: requests } = await adminClient.request(
      FETCH_USER_REQUESTS,
      {
        user_id,
      },
    );

    let impacted_requests = [],
      errorFirebase,
      errorSendbird,
      errorMailchimp,
      otherErrors;

    if (requests.length) {
      for (let request of requests) {
        const updated_hosts_sent = request.hosts_sent.filter(
          (host) => host !== user_id,
        );
        const updated_hosts_declined = request.hosts_declined.filter(
          (host) => host !== user_id,
        );
        let updated_deleted_by_users = request.deleted_by_users;
        if (request.deleted_by_users) {
          updated_deleted_by_users = updated_deleted_by_users.filter(
            (host) => host !== user_id,
          );
        }

        impacted_requests.push(request.id);

        await adminClient.request(UPDATE_REQUEST, {
          request_id: request.id,
          user_id,
          hosts_sent: updated_hosts_sent,
          hosts_declined: updated_hosts_declined,
          deleted_by_users: updated_deleted_by_users,
        });

        // Invalidate cache for this request and user
        await invalidateRequestCache(request.id, user_id);
      }
    }

    // If the user getting deleted is a creator of any pegboard, then we must delete all the pegboards created by him
    await adminClient.request(
      `
        mutation deleteUserData($user_id: uuid!) {
            delete_pegboard(where: {creator_id: {_eq: $user_id}}) {
                affected_rows
            }
        }
        `,
      {
        user_id,
      },
    );

    // Delete from mailchimp
    try {
      await deleteUserFromMailChimp(email);
    } catch (error) {
      errorMailchimp = `${error} error occurred`;
    }

    // Delete user from firebase
    try {
      await deleteUser(user_id);
    } catch (error) {
      errorFirebase = `${error} error occurred`;
    }

    if (!deleted_at) {
      if (registration_incomplete_deletion) {
        try {
          await sendUserIncompleteRegistrationMail(email, first_name);
        } catch (error) {
          otherErrors = `Error in sendUserIncompleteRegistrationMail: ${error}`;
        }
      } else if (declination_date && declined) {
        try {
          await sendUserPostDeclinationMail(email, first_name);
        } catch (error) {
          otherErrors = `Error in sendUserIncompleteRegistrationMail: ${error}`;
        }
      } else if (activate_later && activate_later_date) {
        try {
          await sendUserPostActivateLaterMail(email, first_name);
        } catch (error) {
          otherErrors = `Error in sendUserPostActivateLaterMail: ${error}`;
        }
      } else if (
        is_legacy_user &&
        !legacy_password_reset &&
        moment(legacy_user_reminder_date)
          .add('+15', 'd')
          .format('YYYY-MM-DD') <= moment().format('YYYY-MM-DD')
      ) {
        try {
          // We need to send the legacy users email who have not reset their password and it's been already 2 weeks days since then
          await sendLegacyUserDeletionMail(email, first_name);
        } catch (error) {
          otherErrors = `Error in sendLegacyUserDeletionMail: ${error}`;
        }
      } else if (pn_removal_deletion) {
        try {
          await sendUserPNRemovalDeletionMail(
            email,
            first_name,
            pn_removal_deletion,
          );
        } catch (error) {
          otherErrors = `Error in sendUserPNRemovalDeletionMail: ${error}`;
        }
      } else {
        try {
          await sendUserMail(email, first_name);
        } catch (error) {
          otherErrors = `Error in sendUserMail: ${error}`;
        }
      }
    }

    //Delete User from Stream
    const deleteUserFromStreamResponse = await deleteUserFromStream(user_id);

    res.send({
      message: `${email} successfully deleted from firebase, mailchimp and stream`,
      impacted_requests,
      errorFirebase,
    });
  } catch (error) {
    rollbar.error(error, req);
    res.send({
      error,
      message: 'An error occured',
    });
  }
}
