import adminClient from '../../../graphql/adminClient';
import { UNFAVORITE_CLUB } from '../../../graphql/mutations/club';
import { DELETE_MULTIPLE_OFFERS } from '../../../graphql/mutations/offer';
import { REMOVE_OFFER_ID_FROM_REQUESTS } from '../../../graphql/mutations/request';
import { UPDATE_USER } from '../../../graphql/mutations/user';
import {
  FIND_CLUB_RELATED_OFFERS,
  USER_PRIVATE_CLUBS_ID,
} from '../../../graphql/queries/user';
import { invalidateUserCache } from '../../../utils/redis/cacheInvalidation';
import rollbar from '../../../utils/rollbar';
import calculateAndUpdateUserTier from '../../../utils/tiers/calculateAndUpdateUserTier';

export default async function updatedUserClubs(req, res) {
  try {
    const {
      event: {
        op,
        data: { old, new: newData },
      },
    } = req.body;
    let tgVisibility,
      delete_personal_network_contact,
      offerIds,
      requestsRelatedToOffers,
      offersRemoved;

    if (op === 'INSERT') {
      //If users clubs are changed then we need to invalidate users' cache
      await invalidateUserCache(newData?.user_id);

      /**
       * Here we are checking if the new club of the user is already a favorite club, if yes then we remove it as the fav club.
       */
      let response = false;

      const { favorite_club_by_pk } = await adminClient.request(`
            {
                favorite_club_by_pk(club_id: ${newData?.club_id}, user_id: "${newData?.user_id}") {
                    user_id
                }
            }
            `);

      if (favorite_club_by_pk?.user_id) {
        await adminClient.request(UNFAVORITE_CLUB, {
          user_id: newData?.user_id,
          club_id: newData?.club_id,
        });
        response = true;
      }

      const tierCalc = await calculateAndUpdateUserTier(newData?.user_id);

      res.send({
        response,
        user_id: newData?.user_id,
        club_id: newData?.club_id,
        favorite_club_by_pk,
        tierCalc,
      });
      return;
    } else if (op === 'DELETE') {
      //If users clubs are changed then we need to invalidate users' cache
      await invalidateUserCache(old?.user_id);
      /**
       * If the user's club is disassociated from him, then we need to remove all the
       * system added contacts with tg_contact_id = user.id and course_id = course.id
       */
      const response = await adminClient.request(`
            mutation {
                delete_personal_network_contact(where: {course_id: {_eq: ${old?.club_id}}, tg_contact_id: {_eq: "${old?.user_id}"}}) {
                    returning {
                        id
                        user_id
                    }
                    affected_rows
                }
            }`);
      delete_personal_network_contact =
        response?.delete_personal_network_contact;

      // If the user has no private clubs left then he must have his TG off - visibleToPublic = false
      const { user_club } = await adminClient.request(USER_PRIVATE_CLUBS_ID, {
        user_id: old?.user_id,
      });

      if (!user_club?.length) {
        tgVisibility = await adminClient.request(UPDATE_USER, {
          user_id: old?.user_id,
          user: {
            visibleToPublic: false,
          },
        });
      }

      /*
       * If there are any offers made by the user for this club then they should be removed
       * Keeping in mind that the requests associated with that offer_id should be made as null
       */
      const { offer: offers } = await adminClient.request(
        FIND_CLUB_RELATED_OFFERS,
        {
          user_id: old?.user_id,
          club_id: old?.club_id,
        },
      );
      offerIds = offers.map((offer) => offer?.id);
      requestsRelatedToOffers = await adminClient.request(
        REMOVE_OFFER_ID_FROM_REQUESTS,
        {
          offer_id: offerIds,
        },
      );

      offersRemoved = await adminClient.request(DELETE_MULTIPLE_OFFERS, {
        offer_id: offerIds,
      });

      const tierCalc = await calculateAndUpdateUserTier(old?.user_id);

      return res.send({
        delete_personal_network_contact,
        tgVisibility,
        offerIds,
        requestsRelatedToOffers,
        offersRemoved,
        tierCalc,
      });
    } else {
      res.send({
        message: 'No changes!',
        op,
      });
    }
  } catch (error) {
    rollbar.error(error, req);
    res.status(500).send({
      error,
      status: 0,
    });
    return;
  }
}
