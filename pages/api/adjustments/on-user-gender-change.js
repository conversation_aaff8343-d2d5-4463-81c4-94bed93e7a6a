import constantOptions from '../../../constants/constantOptions';
import adminClient from '../../../graphql/adminClient';
import { invalidateUserCache } from '../../../utils/redis/cacheInvalidation';
import { invalidateUserRequestCache } from '../../../utils/redis/requestCacheInvalidation';

/**
 * This will get triggered with the event: on_user_gender_change
 * @param {*} req
 * @param {*} res
 */
const onUserGenderChange = async (req, res) => {
  try {
    const {
      event: {
        data: { old, new: newData },
      },
    } = req.body;

    //If user played clubs is changed then we need to invalidate users' cache
    if (old?.playedClubs !== newData?.playedClubs) {
      await invalidateUserCache(old?.id);
    }

    if (
      old?.gender &&
      newData?.gender?.toLowerCase() !== old?.gender?.toLowerCase()
    ) {
      const response = await adminClient.request(
        `
        mutation updateUserClubRestrictions($userId: uuid!, $oldGender: String!, $toSetGenderPref: String!) {
            update_user_club(where: {user_id: {_eq: $userId}, gender_preference: {_eq: $oldGender}}, _set: {gender_preference: $toSetGenderPref}) {
              returning {
                club_id
                gender_preference
              }
            }
        }`,
        {
          userId: old?.id,
          oldGender: old?.gender?.toLowerCase(),
          toSetGenderPref: constantOptions.GENDER.BOTH,
        },
      );

      res.send({
        status: 1,
        response,
      });
    }

    res.send({
      status: 1,
      response: 'No changes',
    });
  } catch (error) {
    console.error('Error in onUserGenderChange:', error);
    res.status(500).send({
      status: 0,
      response: 'Internal server error',
    });
  }
};

export default onUserGenderChange;
