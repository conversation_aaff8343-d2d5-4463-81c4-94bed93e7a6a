import constantOptions from '../../../constants/constantOptions';
import adminClient from '../../../graphql/adminClient';
import { CREATE_NOTIFICATION } from '../../../graphql/mutations/notification';
import { UPDATE_USER } from '../../../graphql/mutations/user';
import { FETCH_MIN_CLUBS_REQUIRED_FOR_FAV_VISIBILITY } from '../../../graphql/queries/system-settings';
import pgPoolQuery from '../../../utils/db/pgQuery';
import { invalidateUserCache } from '../../../utils/redis/cacheInvalidation';
import rollbar from '../../../utils/rollbar';

const MARK_ALL_CLUBS = `
mutation markUsersClubs($user_id: uuid!) {
    update_user_club(where: {user_id: {_eq: $user_id}}, _set: {favorite_restricted: false}) {
        returning {
            user_id
            club_id
            favorite_restricted
        }
    }
}  
`;

/**
 * If the fav clubs of a user are getting deleted and the count of his fav clubs is less than the min required
 * , then we have to mark the visibilty of user to only fav clubs as false.
 * @param {*} req
 * @param {*} res
 */
export default async function updatedFavoriteClubs(req, res) {
  try {
    const {
      event: {
        op,
        data: { old: oldData, new: newData },
      },
    } = req.body;

    //If users favorite clubs are changed then we need to invalidate users' cache
    await invalidateUserCache(oldData?.user_id);

    if (op === 'DELETE') {
      const { user_id } = oldData;
      const { system_setting } = await adminClient.request(
        FETCH_MIN_CLUBS_REQUIRED_FOR_FAV_VISIBILITY,
      );
      let minFavClubsRequired = system_setting.length
        ? system_setting[0]?.value?.value
        : 0;

      fetch(
        `${process.env.CONFIG.apiEndPoint}/api/favoriteClubs/getUserFavoriteClubs`,
        {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            user_id,
            data: false,
          }),
        },
      )
        .then((data) => data.json())
        .then(async (data) => {
          if (minFavClubsRequired > data.count) {
            await adminClient.request(UPDATE_USER, {
              user_id,
              user: {
                visible_to_favorite_clubs: false,
              },
            });

            adminClient
              .request(MARK_ALL_CLUBS, {
                user_id,
              })
              .then(() => {
                res.send(
                  'User fav clubs visibility settings have been turned off',
                );
                return;
              });

            res.send(
              'User fav clubs visibility settings have been turned off, Request complete',
            );
            return;
          }

          res.send('No changes, all Ok!');
          return;
        })
        .catch((error) => {
          res.status(500).send(error);
          return;
        });
    } else if (op === 'INSERT') {
      const { user_id } = newData;
      //Find all friends of the user
      const friendsData = await pgPoolQuery(
        `
                SELECT (
                    CASE WHEN (sender_id = $1) THEN receiver_id ELSE sender_id END
                ) AS friend_id,
                stream_channel_id
                FROM friends
                WHERE (
                    sender_id = $1
                    OR receiver_id = $1
                )
                AND status = ${constantOptions.FRIEND_STATUS.COMPLETED}
            `,
        [user_id],
      );

      //Get the user details
      const [{ user }, { courses }] = await Promise.all([
        adminClient.request(
          `query getUser($userId: uuid!) {
                        user(where: {id: {_eq: $userId}}) {
                            full_name
                        }
                    }`,
          {
            userId: user_id,
          },
        ),

        adminClient.request(
          `query getClub($clubId: Int!) {
                        courses(where: {id: {_eq: $clubId}}) {
                            name
                        }
                    }`,
          {
            clubId: newData.club_id,
          },
        ),
      ]);

      let notificationData = [];
      for (const friend of friendsData) {
        const { friend_id, stream_channel_id } = friend;

        //Send notification to the friend
        notificationData.push({
          user_id: friend_id,
          type: 'friend-favorite-club',
          message: `Your friend ${user?.[0]?.full_name} has marked ${courses?.[0]?.name} as a favorite course.`,
          data: {
            friend_id,
            club_id: newData.club_id,
            streamChannelId: stream_channel_id,
          },
          is_push_required: false,
          web_panel: false,
        });
      }

      if (notificationData.length) {
        await adminClient.request(CREATE_NOTIFICATION, {
          notifications: notificationData,
        });
      }

      res.send('Notification sent successfully');
    }
  } catch (error) {
    console.log('error :', error);
    rollbar.error(error, req);
    res.status(500).send(error);
  }
}
