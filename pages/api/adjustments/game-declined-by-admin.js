import moment from 'moment';
import EMAILS from '../../../constants/emails';
import MESSAGES from '../../../constants/messages';
import adminClient from '../../../graphql/adminClient';
import { UPDATE_REQUEST } from '../../../graphql/mutations/request';
import { UPDATE_USER } from '../../../graphql/mutations/user';
import { INSERT_LOGS } from '../../../graphql/queries/logs';
import { sendMailTemplate } from '../../../utils/mailchimp';
import pgPoolQuery from '../../../utils/db/pgQuery';
import updateUserFTR from '../../../utils/user/updateUserFTR';
import { CREATE_NOTIFICATION } from '../../../graphql/mutations/notification';
import createDynamicLink from '../../../utils/dynamic-links/createDynamicLink';
import updateTealDotFlags from '../../../utils/user/updateTealDotFlags';
import constantOptions from '../../../constants/constantOptions';
import sendRequestSystemMessage from '../../../utils/requests/sendReqeuestSystemMessage';
import { invalidateRequestCache } from '../../../utils/redis/requestCacheInvalidation';

const UPDATE_GAME = `
mutation updateGame($game_id: Int!) {
    update_game_by_pk(pk_columns: {game_id: $game_id}, _set: {status: "declined", requestorCompleted: false, hostCompleted: false, declined_by_admin: true}) {
        host_id
        request_id
        request {
            hosts_declined
        }
    }
    delete_game_review(where: {game_id: {_eq: $game_id}}){
      affected_rows
    }
}`;

const GET_LOGS_OF_GAME = `
query getGameLog($game_id: String!) {
    user_membership_logs(where: {module_id: {_eq: $game_id}, module_type: {_eq: "request"}}) {
        user {
            net_games_value
            id
            first_name
            email
        }
        extra_info
        ngv_change
        action
    }
}`;

const GET_REQUEST_INFO = `
    query getRequestInfo($game_id: Int!) {
    request(where: {game_id: {_eq: $game_id}}) {
    is_first_request
    id
    club {
      name
    }
    user_id
    game {
      host_id
      host {
        email
        first_name
      }
    }
    user {
      email
      first_name
        }
    }
}
`;

const GET_REQUEST_CHAT = `
query getRequestChat($request_id: uuid!, $host_id: uuid!) {
  request_chat(where: {request_id: {_eq: $request_id}, club_member_id: {_eq: $host_id}}) {
    stream_channel_id
    club_member_id
  }
}
`;

/**
 * This API is for declining the game, resetting the user's NGV and creating a revert log for the user.
 * @param {*} req
 * @param {*} res
 */
const gameDeclinedByAdmin = async (req, res) => {
  try {
    const { game_id } = req.body;

    // Finding out the info of declined game and the logs of the game
    const [declinedGame, logsOfGame, requestInfo] = await Promise.all([
      adminClient.request(UPDATE_GAME, {
        game_id,
      }),
      adminClient.request(GET_LOGS_OF_GAME, {
        game_id: game_id + '',
      }),
      adminClient.request(GET_REQUEST_INFO, {
        game_id: game_id + '',
      }),
    ]);

    //If the game is is_first_re
    if (requestInfo?.request?.[0]?.is_first_request) {
      updateUserFTR({
        userId: declinedGame?.update_game_by_pk?.host_id,
        increase: false,
      });
    }

    const { user_membership_logs } = logsOfGame;
    let logs = [],
      notifications = [];

    const { shortLink } = await createDynamicLink({
      webLink: `dashboard/play`,
      appParams: `type=requests&id=${requestInfo?.request?.[0]?.id}`,
    });

    let request = requestInfo?.request?.[0];
    // Creating notifications for the game's Requester and Host

    await updateTealDotFlags({
      flagKey: constantOptions.TEAL_DOT_FLAG_KEYS.REQUEST,
      userIds: [request?.user_id, request?.game?.[0]?.host_id],
    });

    notifications.push({
      user_id: request?.user_id,
      type: EMAILS.ADMIN_UPDATED_GAME_STATUS.type,
      message: EMAILS.ADMIN_UPDATED_GAME_STATUS.message(
        game_id,
        request?.club?.name,
      ),
      text_message: EMAILS.ADMIN_UPDATED_GAME_STATUS.text_message(
        game_id,
        request?.club?.name,
      ),
      email_template_name: EMAILS.ADMIN_UPDATED_GAME_STATUS.template,
      data: {
        game_id: game_id,
        request_id: request?.id,
        template_content: [
          {
            name: 'first_name',
            content: request?.user?.first_name,
          },
          {
            name: 'game_id',
            content: game_id,
          },
          {
            name: 'club_name',
            content: request?.club?.name,
          },
          {
            name: 'link',
            content: `<a href="${shortLink}">here</a>`,
          },
        ],
        email: request?.user?.email,
        subject: EMAILS.ADMIN_UPDATED_GAME_STATUS.subject,
      },
    });

    //Notification for the host
    notifications.push({
      user_id: request?.game?.[0]?.host_id,
      type: EMAILS.ADMIN_UPDATED_GAME_STATUS.type,
      message: EMAILS.ADMIN_UPDATED_GAME_STATUS.message(
        game_id,
        request?.club?.name,
      ),
      text_message: EMAILS.ADMIN_UPDATED_GAME_STATUS.text_message(
        game_id,
        request?.club?.name,
      ),
      email_template_name: EMAILS.ADMIN_UPDATED_GAME_STATUS.template,
      data: {
        game_id: game_id,
        request_id: request?.id,
        template_content: [
          {
            name: 'first_name',
            content: request?.game?.[0]?.host?.first_name,
          },
          {
            name: 'game_id',
            content: game_id,
          },
          {
            name: 'club_name',
            content: request?.club?.name,
          },
          {
            name: 'link',
            content: `<a href="${shortLink}">here</a>`,
          },
        ],
        email: request?.game?.[0]?.host?.email,
        subject: EMAILS.ADMIN_UPDATED_GAME_STATUS.subject,
      },
    });

    // Creating reverted logs for the game's Requester and Host
    for (let log of user_membership_logs) {
      // Updating the NGV of the users
      let newUserNgv =
        parseInt(log?.user?.net_games_value) + parseInt(log?.ngv_change) * -1;

      // If the user's NGV comes out to be 0, then we have to check if he is eligible for null
      if (!newUserNgv) {
        const requestsOfUser = await getTotalCountOfCompletedRequests(
          log?.user?.id,
        );
        if (!requestsOfUser) {
          newUserNgv = 0;
        }
      }

      await adminClient.request(UPDATE_USER, {
        user_id: log?.user?.id,
        user: {
          net_games_value: newUserNgv,
        },
      });

      // Returning the new log that needs to be created as a reverted one
      logs.push({
        user_id: log?.user?.id,
        module_id: game_id + '',
        module_type: 'request',
        extra_info: null,
        admin_reason: `Reverted as per declination of #${game_id}`,
        admin_added: true,
        ngv_change: parseInt(log?.ngv_change) * -1,
        action: 'admin',
        action_time: moment(),
        is_requested: false,
      });

      // Send users mail
      await sendMail({
        email: log?.user?.email,
        first_name: log?.user?.first_name,
        game_id,
      });
    }

    await adminClient.request(UPDATE_REQUEST, {
      id: declinedGame?.update_game_by_pk?.request_id,
      request: {
        hosts_declined: [
          ...declinedGame?.update_game_by_pk?.request.hosts_declined,
          declinedGame?.update_game_by_pk?.host_id,
        ],
      },
    });

    // Invalidate cache for this request and user
    await invalidateRequestCache(request?.id, request?.user_id);

    await adminClient.request(INSERT_LOGS, {
      logs,
    });

    await adminClient.request(CREATE_NOTIFICATION, {
      notifications,
    });

    const { request_chat } = await adminClient.request(GET_REQUEST_CHAT, {
      request_id: request?.id,
      host_id: request?.game?.[0]?.host_id,
    });

    await sendRequestSystemMessage({
      stream_channel_id: request_chat?.[0]?.stream_channel_id,
      systemChatMessage: 'Admin has cancelled this game.',
    });

    return res.status(200).send({
      status: 1,
      message: MESSAGES['200'],
      logs,
    });
  } catch (error) {
    return res.status(500).send({
      message: `${MESSAGES['500']}: ${error}`,
      status: 0,
    });
  }
};

export default gameDeclinedByAdmin;

const sendMail = async ({ email, first_name, game_id }) => {
  await sendMailTemplate({
    email,
    subject: EMAILS.GAME_DECLINED_BY_ADMIN.subject,
    template_name: EMAILS.GAME_DECLINED_BY_ADMIN.template,
    template_content: [
      {
        name: 'first_name',
        content: first_name,
      },
      {
        name: 'game_id',
        content: game_id,
      },
    ],
  });
};

const getTotalCountOfCompletedRequests = async (user_id) => {
  let count = await pgPoolQuery(
    `
    SELECT 
    (
        COALESCE(
            (
                SELECT COUNT(game.request_id)
                FROM "user" u
                LEFT JOIN request ON request.user_id = u.id
                LEFT JOIN game ON game.request_id = request.id
                WHERE (game."hostCompleted" = TRUE OR game."requestorCompleted" = TRUE)
                    AND u.id = $1
                    AND JSONB_ARRAY_LENGTH(request.hosts_sent) <> JSONB_ARRAY_LENGTH(request.hosts_declined)
            ), 0
        ) +
        COALESCE(
            (
                SELECT COUNT(game.request_id)
                FROM "user" u
                LEFT JOIN game ON game.host_id = u.id
                LEFT JOIN request ON request.game_id = game.game_id
                WHERE (game."hostCompleted" = TRUE OR game."requestorCompleted" = TRUE)
                    AND u.id = $1
                    AND JSONB_ARRAY_LENGTH(request.hosts_sent) <> JSONB_ARRAY_LENGTH(request.hosts_declined)
            ), 0
        )
    ) AS requests
    `,
    [user_id],
  );
  return count?.requests;
};
