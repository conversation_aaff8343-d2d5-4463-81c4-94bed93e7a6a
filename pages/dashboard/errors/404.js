import React from 'react'
import Link from 'next/link'
import background from '../../../public/images/background.png'

function PageNotFound() {

    return (
        <div className="flex flex-col items-center h-auto">
            <div
                className="flex flex-col items-center justify-center"
                style={{
                    backgroundImage: `url("${background.src}")`,
                    backgroundPosition: 'center',
                    backgroundSize: 'cover',
                    backgroundRepeat: 'no-repeat',
                    height: '100vh',
                    width: '100%',
                }}>
                <div className="items-center text-2xl text-white">
                    The resource you were trying to find does not exist or has
                    been deleted.
                </div>
                <Link href="/dashboard/map">
                    <button
                        className="text-sm text-white uppercase border border-white rounded p-md flex-center font-regular font-ubuntu mb-md mt-xl"
                        style={{ width: 220, height: 53 }}>
                        Back To Home
                    </button>
                </Link>
            </div>
        </div>
    )
}

export default PageNotFound
