import React, { useContext, useState } from 'react'
import CustomButton from '../../components/buttons/CustomButton'
import { ModalContext } from '../../context/ModalContext'
import { UserContext } from '../_app'
import useCheckDeviceScreen from '../../hooks/useCheckDeviceScreen'
import ENDPOINTS from "../../constants/endpoints.json"
import { useRouter } from 'next/router'
import constantOptions from '../../constants/constantOptions'

const alternativeSubtext = "Before proceeding with deleting your account, we want to make sure you are aware of the following alternatives that may address any concerns you have:"
const confirmationSubtext = "Deleting your account will permanently remove your profile and all associated data from our app. This action is irreversible, and the following consequences will occur:"
const confirmationData = [
    { title: "1. Connections and interactions:", description: ["All your TG connections with other users will be severed.", "Messages, conversations, and any shared content with other users or on Feed will be lost."] },
    { title: "2. Groups you have created:", description: ["All TG groups you have created will be deleted where you are the sole creator and members of these groups will no longer have access to them.", "Any content, discussions, or shared resources within these groups will be lost."] },
    { title: "3. Account information and settings:", description: ["Your personal information, including your username, email address, and profile details, will be removed."] },
    { title: "4. Termination of Membership:", description: ["Deleting your account will result in the automatic termination of your membership. Please note that no refunds will be provided in such cases."] },
]

const alternativeData = [
    { title: "1. Super Flexibility in Game Requests", description: "Our network operates on a non-obligatory basis, allowing you the freedom to disregard any Game Requests that do not interest you. Similar to LinkedIn, you have the autonomy to choose which requests to engage with. We hope that occasionally you may come across intriguing requests that pique your curiosity." },
    { title: "2. Temporarily Mute Clubs:", description: 'If you need a break or wish to limit your interactions, you can utilize the "Mute" feature in your account settings. This allows you to temporarily mute yourself or specific clubs for a defined period. ' },
    { title: "3. Customizing Notification Preferences:", description: "You have the ability to manage your notification preferences according to your preferences. In your account settings, you can choose to disable email, text, or push notifications as desired." },
    { title: "4. Favourites Clubs for Request Viewing:", description: "To streamline your experience, you can select a smaller set of clubs that you are willing to receive play requests from. By adding clubs to your favourites, you will primarily see requests from these preferred clubs." }
]

const DeleteAccount = () => {

    const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false)
    const { setModal } = useContext(ModalContext)
    const [loading, setLoading] = useState(false)
    const { user, token, clevertap } = useContext(UserContext)
    const { isMobile } = useCheckDeviceScreen()
    const router = useRouter()

    const handleDelete = async () => {
        try {
            setLoading(true)
            await fetch(`..${ENDPOINTS.CAN_DELETE_ACCOUNT}`, {
                method: 'POST',
                credentials: 'same-origin',
                headers: {
                    ['Content-Type']: 'application/json',
                    ['Authorization']: `Bearer ${token}`,
                },
                body: JSON.stringify({
                    user_id: user?.id,
                }),
            })
                .then((data) => data.json())
                .then((response) => {
                    if (response?.canDelete === false) {
                        setModal({
                            type: 'cannot-delete-user',
                            width: 670,
                            user: user?.role
                        })
                    } else {
                        setShowDeleteConfirmation(true)
                    }
                })
            setLoading(false)
        } catch (error) {
            console.error(error)
        }
        setLoading(false)
    }

    return (
        <div className='flex-col flex-center bg-mediumgray'>
            <div className='text-24 md:text-32 text-center my-md'>{!showDeleteConfirmation ? 'Explore Alternatives before Deleting Your Account' : 'Delete Account'}</div>
            <div className={`text-16 py-md px-md md:px-55 text-center my-lg font-medium ${showDeleteConfirmation ? 'text-red' : ''}`} style={{ width: "80%", backgroundColor: !showDeleteConfirmation ? "rgba(9, 128, 137, 0.04)" : "rgba(224, 94, 82, 0.1)" }}>{!showDeleteConfirmation ? alternativeSubtext : confirmationSubtext}</div>
            <div className={`flex flex-wrap flex-center`} style={{ maxWidth: "1000px" }}>
                {!showDeleteConfirmation ? (<>
                    {alternativeData?.map((t, i) => {
                        return (
                            <div key={i} className='p-lg m-sm bg-white' style={{ width: !isMobile ? 450 : 343, height: 244 }}>
                                <div className='text-20 font-medium text-darkteal'>{t?.title}</div>
                                <div className='pt-md'>{t?.description}</div>
                            </div>
                        )
                    })}
                </>) : (<>
                    {confirmationData?.map((t, i) => {
                        return (
                            <div key={i} className='p-lg m-sm bg-white' style={{ width: !isMobile ? 450 : 343, height: 244 }}>
                                <div className='text-20 font-medium'>{t?.title}</div>
                                <div className='pt-md flex-col'>{t?.description?.map((des) => <div
                                    key={t?.title}
                                    className='flex pl-lg'>
                                    <div className='rounded-full bg-black mt-sm mr-sm'
                                        style={{ height: 5, minWidth: 5, }}>
                                    </div>{des}
                                </div>)}
                                </div>
                            </div>
                        )
                    })}
                </>)}
            </div>
            {!showDeleteConfirmation ? (
                <div className='text-center my-md px-md md:px-55 text-16' style={{ width: "75%" }}>
                    We encourage you to explore these alternatives before making the irreversible decision to delete your account. <span className='text-darkteal'>Do you still wish to continue ?</span>
                </div>
            ) : (
                <div className='text-center my-md text-16'>Please consider the impact of this decision before proceeding.</div>
            )}

            <div className='flex justify-between py-sm '>
                <CustomButton
                    text={!showDeleteConfirmation ? 'Dismiss' : 'Cancel'}
                    color='lightestgray'
                    textColor='black'
                    width={!isMobile ? 330 : 163}
                    height={45}
                    onClick={() => {
                        router.push({
                            pathname: "/profile/account-settings"
                        })
                    }}
                    loading={loading}
                    textSize={16}
                />
                <CustomButton
                    loading={loading}
                    text={!showDeleteConfirmation ? 'Continue' : 'Delete Account'}
                    color={!showDeleteConfirmation ? 'darkteal' : 'red'}
                    textColor='white'
                    width={!isMobile ? 330 : 163}
                    height={45}
                    onClick={() => {
                        if (!showDeleteConfirmation) {
                            handleDelete()
                            clevertap.event.push(constantOptions?.CLEVERTAP_EVENTS.ACCOUNT_SETTINGS_DELETE_ACCOUNT_1);
                        } else {
                            setModal({ type: 'delete-account-confirmation' })
                            clevertap.event.push(constantOptions?.CLEVERTAP_EVENTS.ACCOUNT_SETTINGS_DELETE_ACCOUNT_2);
                        }
                    }}
                    textSize={16}
                />
            </div>
        </div>
    )
}

export default DeleteAccount