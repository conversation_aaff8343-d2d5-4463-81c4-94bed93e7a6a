import React, { useEffect, useState, useContext } from 'react'
import { UserContext } from '../_app'
import ENDPOINT from '../../constants/endpoints.json'
import moment from 'moment'
import useClient from '../../graphql/useClient'
import Link from 'next/link'
import { ThreeDots } from 'react-loader-spinner'
import { ToolTip } from '../../components/common'
import CustomButton from '../../components/buttons/CustomButton'
import useCheckDeviceScreen from '../../hooks/useCheckDeviceScreen'
import { ModalContext } from '../../context/ModalContext'

const logsHeaders = [
  { name: 'Game ID', width: 'w-1/12' },
  { name: 'Club Name', width: 'w-1/12' },
  { name: 'Date', width: 'w-1/12' },
  { name: '# of People', width: 'w-1/12' },
  { name: 'Requester / Host Name', width: 'w-1/12' },
  { name: 'NGV', width: 'w-1/12' },
]

const MembershipLogs = () => {

  const { user, fetchUser, token } = useContext(UserContext)
  const [page, setPage] = useState(0);
  const [logs, setLogs] = useState([]);
  const [nextExists, setNextExists] = useState(false);
  const [loading, setLoading] = useState(false)
  const [lifetimeNgv, setLifetimeNgv] = useState(null)
  const client = useClient()
  const { isMobile, isTablet, isDesktop } = useCheckDeviceScreen()
  const { setModal } = useContext(ModalContext)

  useEffect(() => {
    setLoading(true)
    fetchNgvLogs()
  }, [page])

  useEffect(() => {
    fetchLifetimeNgv()
  }, [client])

  const fetchLifetimeNgv = async () => {
    await fetch(`../..${ENDPOINT.LIFETIME_NGV}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ['Authorization']: `Bearer ${token}`,
      },
      body: JSON.stringify({
        user_id: user.id
      }),
    })
      .then((data) => data.json())
      .then((data) => {
        setLifetimeNgv(data.ngv)
      })
      .catch(err => console.log('Error', err));
  }

  const fetchNgvLogs = async (defaultPage = page) => {
    await fetch(`../..${ENDPOINT.FETCH_NGV_LOGS}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ['Authorization']: `Bearer ${token}`,
      },
      body: JSON.stringify({
        user_id: user.id,
        lastId: logs[49]?.id
      }),
    })
      .then((data) => data.json())
      .then((data) => {
        setLogs(data.data)
        setNextExists(data?.nextExists)
      })
      .catch(err => console.log('Error', err));
    setLoading(false)
  }

  const transit = val => {
    if (val === 'next') {
      setPage(page + 1)
    } else {
      setPage((page - 1) < 0 ? 0 : page - 1)
    }
  }

  return (
    <div className='font-ubuntu'>
      <div className='lg:my-md px-sm md:px-xxl' style={{ display: 'grid', gridAutoFlow: 'column', gridAutoColumns: 'minmax(0, 1fr)' }}>
        <div className='flex flex-center justify-self-start'>
          <Link href={'/profile/membership'}>
            <div className="text-lg text-gray mb-md mt-4 flex items-center cursor-pointer">
              <img
                className="cursor-pointer"
                style={{
                  transform: 'rotate(90deg)',
                  marginRight: 8,
                }}
                src="/svg/arrow-down.svg"
                width={15}
                height={15}
              />
              Back
            </div>
          </Link>

        </div>
        {!isMobile && (
          <div className='flex flex-center'>
            <div className='text-24 px-md'>NGV Assessment Logs</div>
          </div>
        )}
        <div className='flex md:flex-col lg:flex-row items-center justify-end'>
          {!isMobile &&
            <>
              <div className='text-18 flex mx-sm'>Lifetime NGV:<div className='text-darkteal ml-sm'>{(lifetimeNgv !== null || user?.net_games_value !== null) ? (lifetimeNgv || 0) : "NA"}</div></div>
              <div className='text-18 flex mx-sm py-sm lg:py-0'>Current NGV:<div className='text-darkteal ml-sm'>{user?.net_games_value !== null ? user?.net_games_value : "NA"}</div></div>
            </>}
        </div>
      </div>
      {isMobile && (
        <div className='flex flex-center'>
          <div className='text-24 font-medium px-md'>NGV Assessment Logs</div>
        </div>
      )}
      {isMobile && (
        <div className='flex justify-evenly py-sm'>
          <div className='text-16 flex mx-sm'>Lifetime NGV:<div className='text-darkteal ml-sm'>{(lifetimeNgv !== null || user?.net_games_value !== null) ? (lifetimeNgv || 0) : "NA"}</div></div>
          <div className='text-16 flex mx-sm'>Current NGV:<div className='text-darkteal ml-sm'>{user?.net_games_value !== null ? user?.net_games_value : "NA"}</div></div>
        </div>
      )}
      {!loading && !logs?.length ? (
        <div className='flex-center bg-white mx-xxl py-xxl shadow-lg text-20' style={{ height: 700, boxShadow: "0px 0 30px rgba(0, 0, 0, 0.1)", }}>No NGV logs found</div>
      ) : (
        <>
          {loading ? <div className="flex flex-center flex-1" style={{ height: 640 }}>
            <ThreeDots
              visible={true}
              height="50"
              width="50"
              color={"#098089"}
              radius="9"
              ariaLabel="three-dots-loading"
              wrapperStyle={{}}
              wrapperClass=""
            />
          </div> : (
            <>
              {isMobile ? (
                <div className='px-md'>
                  {logs?.map((log) => {
                    return (
                      <div key={log?.name} className={`flex flex-col p-sm  mb-md ${log?.admin_added ? 'bg-tealTierBg' : 'bg-white'}`} style={{ zIndex: 100000 }}>
                        <div className='flex justify-between mb-xs text-12'>
                          <div className='text-darkteal font-medium'>Game ID</div>
                          <div>{log?.module_id || 'NA'}</div>
                        </div>
                        <div className='flex justify-between mb-xs text-12'>
                          <div className='text-darkteal font-medium'>Club Name</div>
                          {log?.extra_info ? (
                            <>
                              <div>{(log?.extra_info?.split(',')[1].slice(6) || 'NA')}</div>
                            </>
                          ) : (
                            <div className='flex'>
                              <div>Admin Adjusted</div>
                              <div className="ml-xs relative inline-block">
                                <ToolTip
                                  style={{
                                    width: 280,
                                    top: -21,
                                    height: 74,
                                    left: 20
                                  }}
                                  tip={`This is the Admin adjusted NGV value. <NAME_EMAIL> for any queries`} />
                              </div>
                            </div>
                          )}
                        </div>
                        <div className='flex justify-between mb-xs text-12'>
                          <div className='text-darkteal font-medium'>Date</div>
                          <div>{moment(log?.date_to_display).format('MM/DD/YYYY')}</div>
                        </div>
                        <div className='flex justify-between mb-xs text-12'>
                          <div className='text-darkteal font-medium'># Of People</div>
                          <div>{(log?.extra_info && log?.players) ? log?.players : ''}</div>
                        </div>
                        <div className='flex justify-between mb-xs text-12'>
                          <div className='text-darkteal font-medium'>Reason</div>
                          <div className='max-w-sm'>{log?.extra_info ? (log?.extra_info?.split(',')[0].split(":").pop().trim() !== '' ? log?.extra_info?.split(',')[0].split(":").pop() : 'Unidentified') : log?.admin_reason}</div>
                        </div>
                        <div className='flex justify-between mb-xs text-12'>
                          <div className='text-darkteal font-medium'>NGV</div>
                          <div>{log?.ngv_change}</div>
                        </div>
                      </div>
                    )
                  })}
                </div>
              ) : (
                <div className='membership-logs bg-white mx-xxl shadow-lg' style={{ minHeight: 650, boxShadow: "0px 0 30px rgba(0, 0, 0, 0.1)" }}>
                  <table className='w-full table-fixed'>
                    <thead className='border-b border-lightgray'>
                      <tr className=''>
                        {logsHeaders.map((t) => (
                          <th
                            key={t.name}
                            className={`p-md text-16 font-normal text-darkteal text-left ${t.width}`}>
                            {t.name}
                          </th>
                        ))}
                      </tr>
                    </thead>
                    <tbody className=''>
                      {logs?.map((log) => {
                        return (
                          <tr
                            key={log?.id}
                            className=''
                            style={{
                              borderBottom: "1px solid rgba(196, 196, 196, 0.34)",
                              background: `${log?.admin_added ? 'rgba(9, 128, 137, 0.08)' : ''}`,
                            }}>
                            <td className={`px-md text-14 py-md w-1/12 truncate`}>{log?.module_id || 'NA'}</td>
                            {log?.extra_info ? (
                              <td className='text-14 px-md py-md w-1/12 truncate'>{log?.extra_info?.split(',')[1].slice(6) || 'NA'}</td>
                            ) : (
                              <td className='text-14 px-md py-md w-1/12'>Admin Adjusted
                                <div className="ml-xs relative inline-block">
                                  <ToolTip
                                    style={{
                                      width: 280,
                                      top: -21,
                                      height: 74,
                                      left: 20
                                    }}
                                    tip={`This is the Admin adjusted NGV value. <NAME_EMAIL> for any queries`} />
                                </div>
                              </td>
                            )}
                            <td className='text-14 px-md py-md w-1/12'>{moment(log?.date_to_display).format('MM/DD/YYYY')}</td>
                            <td className='text-14 px-md py-md w-1/12'>{(log?.extra_info && log?.players) ? log?.players : ''}</td>
                            <td className='text-14 px-md py-md w-1/12' style={{ maxWidth: 290 }}>
                              {log?.extra_info ? (log?.extra_info?.split(',')[0].split(":").pop().trim() !== '' ? log?.extra_info?.split(',')[0].split(":").pop() : 'Unidentified') : log?.admin_reason}
                            </td>
                            <td className='text-14 px-md py-md w-1/12'><span style={log?.ngv_change < 0 ? { position: "relative", left: -1 } : {}}>{log?.ngv_change}</span></td>
                          </tr>
                        )
                      })}
                    </tbody>
                  </table>
                </div>
              )}
            </>
          )}
        </>
      )}
      <div className="flex flex-row justify-evenly w-full pt-lm mb-xl">
        <div
          role="button"
          className={`px-sm py-xs  flex  ${!page
            ? 'cursor-not-allowed text-gray'
            : 'text-darkgray'
            }`}
          onClick={() => (!page ? null : transit("prev"))}>
          Back
        </div>
        <div
          className={`px-sm py-xs flex  ${!nextExists
            ? 'cursor-not-allowed text-gray'
            : 'text-darkteal cursor-pointer'
            }`}
          onClick={() => (!nextExists ? null : transit("next"))}>
          Next
        </div>
      </div>
      <div className='' style={{ zIndex: 1 }}>
        <div className='absolute bottom-0 left-0'>
          <img src='/images/logs_single_golf_club.png' />
        </div>
        <div className='absolute bottom-0 right-0'>
          <img src='/images/logs_multiple_golf_club.png' />
        </div>
      </div>
    </div>

  )
}

export default MembershipLogs
