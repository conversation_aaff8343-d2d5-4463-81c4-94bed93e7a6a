import React, { useContext } from 'react'
import Moment from 'moment'
import { ModalContext } from '../../../context/ModalContext'
import OptionsButton from '../../../components/buttons/OptionsButton'
import XIcon from '../../../components/icons/XIcon'
import CheckIcon from '../../../components/icons/CheckIcon'
import { UPDATE_EVENT, DECLINE_EVENT } from '../../../graphql/mutations/event'
import { UserContext } from '../../_app'
import NameInitials from '../../../components/common/NameInitials'
import constantOptions from '../../../constants/constantOptions'
import analyticsEventLog from '../../../utils/firebase/analytics'
import { DrawerContext } from '../../../context/DrawerContext'
import { RichTextDisplay } from '../../../components/common'
import dateFormatter from '../../../utils/helper/dateFormatter'
import useThumbnail from '../../../hooks/useThumbnail'
import { ImageViewer } from '../../../components/common/ImageViewer'


export default function EventCard({
    event,
    refresh,
    isAdmin,
    expanded,
    client
}) {
    const { EVENT_DETAILS } = constantOptions?.DRAWER_TYPE

    const { modal, setModal } = useContext(ModalContext)
    const { drawer, setDrawer } = useContext(DrawerContext)
    const { user } = useContext(UserContext)
    const { thumbnailUrl } = useThumbnail(event?.userdetails?.profilePhoto, 1280)


    async function approveEvent() {
        await client.request(UPDATE_EVENT, {
            id: event.id,
            event: { approved: true },
        })
        await refresh()
    }

    async function declineEvent() {
        await client.request(DECLINE_EVENT, {
            id: event.id,
        })
        await refresh()
    }

    return (
        <div id='card'
            onClick={(e) => {
                if (e.target.id !== "link") {
                    setDrawer({
                        type: EVENT_DETAILS,
                        global: true,
                        event_id: event?.event_id
                    })
                    analyticsEventLog(constantOptions.USER_ACTIVITY.EVENT.event, {
                        id: Math.random(),
                        email: user?.email,
                        event_name: event?.title,
                        date: Moment().format('MMMM Do YYYY, h:mm:ss a'),
                        from: constantOptions.USER_ACTIVITY.EVENT.from,
                    })
                }
            }
            }
            className={`transition duration-300 hover:scale-105 fade-in relative w-full ${expanded ? '' : 'sm:w-1/2 lg:w-1/3 px-lg pt-lg sm:pt-xl'
                }`}>
            {event?.isEdited &&
                <div style={{ right: 40, top: 50, zIndex: 49 }} className='h-[22px] w-[47px] rounded-lg flex-center bg-white absolute'>Edited</div>
            }
            <div className="relative flex flex-col h-full overflow-hidden bg-white rounded-lg shadow hover:bg-hover cursor-pointer">
                <div className="relative flex flex-col h-full overflow-hidden cursor-pointer">
                    {!expanded && (
                        <>
                            <ImageViewer event={event} isAdmin={isAdmin} />
                        </>
                    )}
                    <div className="flex flex-col flex-1 px-md pt-md">
                        <div className="flex items-center justify-between mb-sm">
                            <div className="flex items-center">
                                {event?.userdetails?.profilePhoto ? (
                                    <div
                                        className="rounded-lg mr-sm"
                                        style={{
                                            width: 45,
                                            height: 45,
                                            backgroundImage: `url("${thumbnailUrl || '/images/avatar.png'}")`,
                                            backgroundPosition: 'center',
                                            backgroundSize: 'cover',
                                            borderColor: 'rgba(0,0,0,0)',
                                        }}></div>
                                ) : (
                                    <div
                                        className="rounded-lg mr-sm"
                                        style={{
                                            width: 45,
                                            height: 45,
                                        }}>
                                        <NameInitials
                                            height={45}
                                            width={45}
                                            user={{
                                                first_name: event?.user_id === constantOptions?.ADMIN_ID ? "Thousand Greens Admin" : event?.userdetails?.name,
                                            }}
                                            fontSize={25}
                                        />
                                    </div>
                                )}

                                <div style={{ fontSize: 18 }}>
                                    {event?.user_id === constantOptions?.ADMIN_ID ? "Thousand Greens Admin" : event?.userdetails?.name}
                                </div>
                            </div>
                            {(isAdmin || event.user_id === user.id) && (
                                <div className="text-sm">
                                    <OptionsButton
                                        rotate={'90deg'}
                                        options={{
                                            edit: () => {
                                                setModal({
                                                    title: 'Edit Event',
                                                    img: {
                                                        src:
                                                            '/svg/profile-play-active.svg',
                                                        style: {
                                                            height: 80,
                                                            marginBottom: 30,
                                                        },
                                                    },
                                                    width: 787,
                                                    type: 'event',
                                                    event,
                                                    isAdmin,
                                                    refresh: refresh,
                                                    width: "878px"
                                                })
                                            },
                                            delete: () => {
                                                setModal({
                                                    title: 'Delete Event',
                                                    img: {
                                                        src:
                                                            '/svg/profile-play-active.svg',
                                                        style: {
                                                            height: 80,
                                                            marginBottom: 30,
                                                        },
                                                    },
                                                    type: 'delete-event',
                                                    event,
                                                    fetchEvents: refresh,
                                                    width: 475,
                                                })
                                            },
                                        }}
                                    />
                                </div>
                            )}
                        </div>
                        <div
                            className="flex items-center justify-between cursor-pointer"
                            style={{ fontSize: 18 }}>
                            {event.title}
                        </div>
                        <div className={`event-details text-gray font-thin text-sm overflow-hidden cursor-pointer ${expanded ? 'mb-md' : ''}`}>
                            <RichTextDisplay content={event?.details?.length < 170 ? event?.details : event?.details?.slice(0, 170) + " ..."} />
                        </div>
                    </div>
                </div>
                <div className="flex items-end justify-between flex-1 mt-1 text-sm px-md">
                    <div className="flex items-center mb-2">
                        <img
                            className="mr-sm"
                            src="/svg/calendar-teal.svg"
                            style={{ height: 15, width: 15 }}
                        />
                        {dateFormatter(event?.start_date, event?.end_date)}
                        {isAdmin && (
                            <div className="flex items-center ml-lg">
                                <img
                                    className="mr-sm"
                                    src="/svg/clicks.svg"
                                    style={{ height: 15, width: 15 }}
                                />
                                {event.views} Click
                                {event.views === 1 ? '' : 's'}
                            </div>
                        )}
                    </div>

                    {isAdmin && !event.approved && (
                        <div className="flex">
                            <div onClick={approveEvent} className="mr-sm">
                                <CheckIcon />
                            </div>
                            <XIcon
                                onClick={declineEvent}
                                alternateStyle
                                eventStyle
                            />
                        </div>
                    )}
                </div>

                {!isAdmin &&
                    !Moment().isSameOrAfter(Moment.utc(event?.registration_start_date), 'd') && (
                        <div className="font-thin text-gray mb-2 text-sm mx-md">
                            Registration begins on{' '}
                            {Moment.utc(
                                event.registration_start_date
                            ).format(constantOptions?.DATE_FORMAT_Do_MM_YYYY)}
                        </div>
                    )}
                {!isAdmin && (
                    <div className="flex justify-end px-sm pb-md">
                        {(event.request_email || event.redirect_url) && (
                            <div
                                id='link'
                                className="text-darkteal hover:underline"
                                onClick={(e) => {
                                    if (e.target.id === 'link') {
                                        analyticsEventLog(
                                            constantOptions.USER_ACTIVITY
                                                .EVENT_REQUEST_INFO.event,
                                            {
                                                id: Math.random(),
                                                email: user?.email,
                                                event_name: event?.title,
                                                date: Moment().format(
                                                    'MMMM Do YYYY, h:mm:ss a'
                                                ),
                                                from:
                                                    constantOptions
                                                        .USER_ACTIVITY
                                                        .EVENT_REQUEST_INFO
                                                        .from,
                                            }
                                        )
                                        setModal({
                                            type: 'disclaimer',
                                            event,
                                        })
                                    }
                                }}>
                                Request More Information
                            </div>
                        )}
                    </div>
                )}
            </div>
        </div>
    )
}