import React, { useState, useEffect, useContext } from 'react'
import { UserContext } from '../_app'
import EditClubForm from '../../components/profile/forms/EditClubForm'
import { ModalContext } from '../../context/ModalContext'
import AddNewClub from '../../components/common/AddNewClub'
import constantOptions from '../../constants/constantOptions'
import { ClubInformationCard } from '../../components/common/ClubInformationCard'
import { GET_USER_CLUBS } from '../../graphql/queries/club'
import adminClient from '../../graphql/adminClient'
import useCheckDeviceScreen from '../../hooks/useCheckDeviceScreen'
import Link from 'next/link'
import CustomButton from '../../components/buttons/CustomButton'
const { MY_FAV_CLUB } = constantOptions?.DRAWER_TYPE
import { DrawerContext } from '../../context/DrawerContext'
import ENDPOINTS from "../../constants/endpoints.json"

export default function GolfClubs({ setShowDetails }) {
  const { drawer, setDrawer } = useContext(DrawerContext)
  const { user, token, fetchUser } = useContext(UserContext)
  const [clubs, setClubs] = useState([])
  const [editing, setEditing] = useState()
  const [adding, setAdding] = useState()
  const [replacing, setReplacing] = useState()
  const [allClubsMuted, setAllClubsMuted] = useState(false);
  const { modal, setModal } = useContext(ModalContext)
  const [userClubs, setUserClubs] = useState([])
  const { isMobile } = useCheckDeviceScreen()
  const [markingFavorite, setMarkingFavorite] = useState(false)

  useEffect(() => {
    getUserClubs()
  }, [adminClient])

  useEffect(() => {
    if (user) {
      const userClubs = user?.clubs?.filter(club => club?.club_type === constantOptions?.CLUB_TYPES?.PRIVATE)
      setClubs(userClubs)
    }
  }, [user, editing])

  useEffect(() => {
    setEditing(false)

    if (clubs.length) {
      let mutedClubs = clubs.filter(club => club.muted).length === clubs.length;
      setAllClubsMuted(mutedClubs);
    }
  }, [])

  useEffect(() => {
    setEditing(false)

    if (clubs.length) {
      let mutedClubs = clubs.filter(club => club.muted).length === clubs.length;
      setAllClubsMuted(mutedClubs);
    }
  }, [])

  //API - Mark club as favourite restricted
  const markClubFavouriteRestricted = (clubId, isRestricted) => {
    try {
      setMarkingFavorite(true)
      fetch(`..${ENDPOINTS?.MARK_CLUB_FAVOURITE_RESTRICTED}`, {
        method: 'POST',
        credentials: 'same-origin',
        headers: {
          ['Content-Type']: 'application/json',
          ['Authorization']: `Bearer ${token}`,
        },
        body: JSON.stringify({
          userId: user.id,
          clubId,
          isRestricted
        }),
      })
        .then((data) => data.json())
        .then(async (data) => {
          if (data.status) {
            await getUserClubs()
          }
          setMarkingFavorite(false)
        })
    } catch (error) {
      console.error(error)
      setMarkingFavorite(false)
    }
  }

  function updateClub(updatedClub) {
    setClubs(
      clubs.map((club) =>
        club.id === updatedClub.id ? updatedClub : club
      )
    )
  }

  const getUserClubs = async () => {
    await adminClient.request(GET_USER_CLUBS, {
      user_id: user?.id
    }).then((res) => {
      setUserClubs(res?.user_club)
    })
  }

  function updateClub(updatedClub) {
    setClubs(
      clubs.map((club) =>
        club.id === updatedClub.id ? updatedClub : club
      )
    )
  }

  function removeClub(id) {
    setClubs(clubs.filter((club) => club.id !== id))
  }


  return (
    <>
      {adding ? <AddNewClub setAdding={setAdding} /> :
        replacing ? (<AddNewClub
          replacingClub={replacing}
          setAdding={setAdding}
          setReplacing={setReplacing}
        />) : (
          <div className="flex flex-col mb-xl w-full">
            {!editing &&
              <>
                {isMobile ? (
                  <div className='flex justify-between pb-md pt-md px-sm'>
                    <img onClick={() => { setShowDetails(false) }} src='/svg/BackArrow.svg' />
                    <div className='text-18 font-medium'>Golf Clubs</div>
                    <img onClick={() => setAdding(true)} src='/svg/AddClubBlack.svg' />
                  </div>
                ) : (
                  <div className='flex w-full mb-md pt-md'>
                    <div className="text-black flex flex-1 text-26 font-medium">
                      Golf Clubs
                    </div>
                    <CustomButton
                      marginX='md'
                      onClick={() => {
                        setDrawer({
                          type: MY_FAV_CLUB,
                          global: true,
                          markClubFavouriteRestricted
                        })
                      }}
                      imageMarginBottom='0'
                      textSize={'14'}
                      width={174}
                      height={40}
                      color='white'
                      borderColor='black'
                      textColor='black'
                      text='My Favourite Clubs'
                      buttonImage='/svg/FavClubMarkButton.svg' />

                    {clubs?.length === 1 && !user?.private_network ? (
                      <div className='cursor-pointer flex flex-center bg-darkteal py-sm px-md rounded-lg text-white mr-md' style={{ width: 138, height: 42 }}
                        onClick={() => setModal({
                          type: 'replace-club-confirmation',
                          setReplacing: setReplacing
                        })}
                      >
                        Replace Club
                      </div>
                    ) : null}

                    <div className='cursor-pointer flex flex-center bg-darkteal py-sm px-md mr-lg rounded-lg text-white' style={{ width: 108, height: 40 }}
                      onClick={() => setAdding(true)}
                    >
                      + Add Club
                    </div>
                  </div>
                )}
              </>

            }
            {editing ? (
              <EditClubForm
                club={editing}
                user={user}
                isOnlyClub={clubs?.length === 1}
                removeClub={removeClub}
                updateClub={(club) => updateClub(club)}
                cancel={() => {
                  setEditing()
                  fetchUser()
                }}
              />
            ) : (
              (clubs && typeof (clubs) === "object" && clubs.length) ?
                <div className="flex flex-col">
                  {console.log('clubs', clubs)}
                  {(clubs && typeof (clubs) === "object") && clubs.map((club) => {
                    return <ClubInformationCard
                      key={club?.id}
                      user={user}
                      token={token}
                      club={club}
                      setEditing={setEditing}
                      setModal={setModal}
                      getUserClubs={getUserClubs}
                      userClub={userClubs?.filter((userClub) => userClub?.club_id === club?.id)[0]}
                      markingFavorite={markingFavorite}
                      setMarkingFavorite={setMarkingFavorite}
                      markClubFavouriteRestricted={markClubFavouriteRestricted}
                    />
                  })}
                </div>
                : (
                  <div
                    style={{ height: 400 }}
                    className="flex-col bg-white rounded flex-center shadow py-md flex flex-wrap">
                    <div>
                      <img src="/svg/no-golf-clubs.svg" />
                    </div>
                    <div className="text-18 mt-4">
                      No Club Added
                    </div>
                    <div
                      className="font-light text-14 mt-3 leading-6 text-gray">
                      You don't have any golf club attached to your profile yet.
                      <br />
                      Add one by clicking on "Add Club" button on the screen
                    </div>
                  </div>
                )
            )}
            {(isMobile && (clubs?.length === 1 && !user?.private_network)) ? (
              <CustomButton
                text='Replace Club'
                width={"100%"}
                height={40}
                marginX='0'
                color='lightestgray'
                textColor='black'
                borderColor={'grayLight'}
                onClick={() => setModal({
                  type: 'replace-club-confirmation',
                  setReplacing: setReplacing
                })}
              />
            ) : null}
          </div>
        )}
    </>
  )
}