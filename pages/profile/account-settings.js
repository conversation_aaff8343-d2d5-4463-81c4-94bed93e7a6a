import React, { useState, useEffect, useContext } from 'react'
import useCheckDeviceScreen from '../../hooks/useCheckDeviceScreen'
import { UserContext } from '../_app'
import useClient from '../../graphql/useClient'
import { ModalContext } from '../../context/ModalContext'
import Checkbox from '../../components/chat-v2/CheckBox'
import ProfileQuestion from '../../components/common/ProfileQuestion'
import ENDPOINTS from "../../constants/endpoints.json"
import CustomButton from '../../components/buttons/CustomButton'
import { UPDATE_USER } from '../../graphql/mutations/user'
import constantOptions from '../../constants/constantOptions'
import toastNotification from '../../utils/notifications/toastNotification'
import { ThreeDots } from 'react-loader-spinner'
import getMyClubsVisibilitySettings from '../../utils/groups/getMyClubsVisibilitySettings'
import { DrawerContext } from '../../context/DrawerContext'
import IntermediateCheckbox from '../../components/common/IntermediateCheckbox'
import AccountSettingsHowItWorks from '../../components/settings/AccountSettingsHowItWorks'
import TutorialCard from '../../components/common/TutorialCard'

const { CLUB_VISIBILITY_DETAILS } = constantOptions?.DRAWER_TYPE

const AccountSettings = ({ setShowDetails }) => {
  const { setModal } = useContext(ModalContext)
  const { setDrawer } = useContext(DrawerContext)
  const { user, fetchUser, token, clevertap } = useContext(UserContext)
  const client = useClient()
  const { isMobile } = useCheckDeviceScreen()
  const [visibleToFavouriteClubs, setVisibleToFavouriteClubs] = useState(user?.visible_to_favorite_clubs)
  const [checked, setChecked] = useState()
  const [checkedClub, setCheckedClub] = useState()
  const [muted, setMuted] = useState(user?.muted)
  const [updatingVisibility, setUpdatingVisibility] = useState(false)
  const [loading, setLoading] = useState(false)
  const [clubs, setClubs] = useState([])
  const [refresh, setRefresh] = useState(0)
  const [isTgAmbassador, setIsTgAmbassador] = useState(user?.is_tg_ambassador)
  const [ambassadorTag, setAmbassadorTag] = useState(user?.tg_ambassador_visibility)
  const [showTutorial, setShowTutorial] = useState(user?.role === 'user' && !user?.is_web_tutorial_viewed)
  const [transition, setTransition] = useState(false)
  const { ACCOUNT_SETTINGS } = constantOptions?.TUTORIAL
  const [currentTutorial, setCurrentTutorial] = useState(ACCOUNT_SETTINGS[0])

  let isMounted = 1
  let abortController = new AbortController()

  useEffect(() => {
    setIsTgAmbassador(user?.is_tg_ambassador)
    setAmbassadorTag(user?.tg_ambassador_visibility)
  }, [user?.is_tg_ambassador, user?.tg_ambassador_visibility])


  useEffect(() => {
    getMyClubsVisibilitySettings(user.id, token, 0, setClubs, setLoading);
  }, [refresh])

  const unmuteAccount = async () => {
    if (client) {
      await client.request(UPDATE_USER, {
        user_id: user.id,
        user: {
          muted: false,
          mute_until: null,
        },
      })
    }
  }

  useEffect(() => {
    if (!muted) {
      unmuteAccount()
    }
  }, [muted])

  useEffect(() => {
    const timer = setTimeout(() => {
      if (user) {
        fetchUser()
      }
    }, 1000);
    return () => {
      isMounted = 0
      clearTimeout(timer)
    };

  }, [])

  useEffect(() => {
    // We need to update the user's visited_account_settings property to true if he has visited this page from a non mobile device
    if (client && user?.visited_account_settings === false && !window.matchMedia("only screen and (max-width: 480px)").matches) {
      updateVisitedAccSettings()
    }
    return () => {
      isMounted = 0
      abortController.abort()
    }
  }, [user?.visited_account_settings, client])

  useEffect(() => {
    if (user && (visibleToFavouriteClubs !== user?.visible_to_favorite_clubs)) {
      setLoading(true)
      limitVisibility()
    }
    return () => {
      isMounted = 0
      abortController.abort()
    }
  }, [visibleToFavouriteClubs])

  useEffect(() => {
    if (checkedClub !== undefined && checked !== undefined) {
      setLoading(true)
      limitVisibility()
    }
    return () => {
      isMounted = 0
      abortController.abort()
    }
  }, [checkedClub, checked])

  const updateVisibility = async () => {
    try {
      await fetch(ENDPOINTS.UPDATE_AMBASSADOR_VISIBILITY, {
        method: 'POST',
        credentials: 'same-origin',
        headers: {
          ['Content-Type']: 'application/json',
          ['Authorization']: `Bearer ${token}`,
        },
        body: JSON.stringify({
          userId: user?.id,
          status: !ambassadorTag
        }),
      })
        .then((data) => data.json())
        .then(async () => {
          await fetchUser()
          if (isMounted) {
            setLoading(false)
          }
        })
    } catch (error) {
      console.error(error)
    }
  }

  const limitVisibility = async () => {
    try {
      await fetch(`..${ENDPOINTS.RESTRICT_CLUBS}`, {
        method: 'POST',
        credentials: 'same-origin',
        headers: {
          ['Content-Type']: 'application/json',
        },
        body: JSON.stringify({
          user_id: user?.id,
          visible_to_favorite_clubs: visibleToFavouriteClubs,
          club_id: visibleToFavouriteClubs !== user?.visible_to_favorite_clubs ? null : checkedClub,
          club_checked: visibleToFavouriteClubs !== user?.visible_to_favorite_clubs ? null : checked
        }),
      })
        .then((data) => data.json())
        .then(async (clubs) => {
          await fetchUser()
          if (isMounted) {
            setLoading(false)
          }
          setChecked()
          setCheckedClub()
        })
    } catch (error) {
      console.error(error)
    }
    fetchUser()
  }

  const updateVisitedAccSettings = async () => {
    if (user && user?.id) {
      await client.request(UPDATE_USER, {
        user_id: user?.id,
        user: {
          visited_account_settings: true
        }
      })
      await fetchUser()
    }
  }

  async function updateVisibilitySettings() {
    try {
      setUpdatingVisibility(true)
      await fetch(ENDPOINTS?.UPDATE_VISIBILITY_SETTINGS_V1, {
        method: "POST",
        headers: {
          'Content-Type': 'application/json',
          ['Authorization']: `Bearer ${token}`,
        },
        body: JSON.stringify({
          userId: user?.id,
          isVisibleToPublic: !user?.visibleToPublic
        })
      })
        .then((response) => response.json())
        .then(async (data) => {
          if (!data?.status) {
            toastNotification({
              type: constantOptions.TOAST_TYPE.ERROR,
              message: data?.message,
            })
          }
          else {
            await fetchUser()
          }
          setUpdatingVisibility(false)
        })
    } catch (error) {
      setUpdatingVisibility(false)
      console.log("Update Visibility Settings------->", error);
    }
  }

  const renderHowItWorks = () => (
    <div
      className='text-14 cursor-pointer text-darkteal underline text-end'
      onClick={() => setModal({
        type: "how-it-works",
        subtext: "Learn how these TG club checkboxes works",
        renderChildren: () => <AccountSettingsHowItWorks />,
        width: 550
      })}
    >
      How it works?
    </div>
  )

  const markTutorialAsViewed = async () => {
    setLoading(true)
    try {
      await fetch(ENDPOINTS.MARK_WEB_TUTORIAL_VIEWED, {
        method: 'POST',
        credentials: 'same-origin',
        headers: {
          ['Content-Type']: 'application/json',
          ['Authorization']: `Bearer ${token}`,
        },
        body: JSON.stringify({
          userId: user?.id
        }),
      })
        .then((data) => data.json())
        .then(async () => {
          setShowTutorial(false)
          await fetchUser()
        })
      setLoading(false)
    } catch (error) {
      console.error(error)
    }
  }

  useEffect(() => {
    if (showTutorial) {
      // Small delay to ensure transition works
      setTimeout(() => {
        setTransition(true);
      }, 50);
    } else {
      setTransition(false);
    }
  }, [showTutorial]);

  return (
    <>
      {showTutorial && (
        <div className="fixed inset-0 flex items-center justify-center"
          style={{
            backgroundColor: 'rgba(0,0,0,0.5)',
            boxSizing: 'border-box',
            minHeight: '100vh',
            overflowY: 'scroll',
            opacity: transition ? 1 : 0,
            transition: 'opacity 0.2s ease-in-out',
            backdropFilter: 'blur(2px)'
          }}>
        </div>
      )}
      <div className="flex flex-col mb-xl w-full">
        <div className='flex w-full mb-md'>
          {isMobile ? (
            <div className={`w-full flex-center relative pt-md`}>
              <div className='absolute' style={{ left: 10 }} onClick={() => setShowDetails(false)}><img src='/svg/BackArrow.svg' /></div>
              <div className='text-18 font-medium'>Account Settings</div>
            </div>
          ) : (
            <>
              <div className="flex items-end flex-1 text-26 font-medium py-md">
                Account Settings
              </div>
            </>
          )}
        </div>
        <div className="flex flex-col">
          <div className="w-full mb-lg">
            <div className="bg-white rounded shadow flex-col p-lg">
              <div className={`flex ${isMobile && 'flex-col'} ${!showTutorial && 'border-b border-lightestgray'} pb-md ${(showTutorial && currentTutorial?.id === 0) ? 'relative bg-white rounded-lg px-md pt-sm outline outline-2 outline-offwhite4 outline-offset-[-2px]' : ''}`}>
                {(showTutorial && currentTutorial?.id === 0) &&
                  <div className='absolute bottom-[-40px] left-[0px] lg:left-[-120px]'>
                    <TutorialCard loading={loading} currentTutorial={currentTutorial} setCurrentTutorial={setCurrentTutorial} markTutorialAsViewed={markTutorialAsViewed} />
                  </div>
                }

                <div className={`flex-col lg:w-10/24 lg:pr-md`}>
                  <div className={`${isMobile ? 'text-16' : 'text-18'}`}>TG Account Visibility</div>
                  <div className='text-grayLight'>This setting allows you to control who can send you a Game request on the TG platform.</div>
                </div>

                <div className={`${isMobile && 'mt-md'} lg:w-15/24`}>
                  {/* My TG community only */}
                  <div>
                    <Checkbox
                      customClasses={'text-14'}
                      label="My TG community only"
                      isDisabled={true}
                      value={true}
                    />
                    <div className='text-12 text-grayLight pl-lg'>This is your mandatory visibility in the application. Your "My TG Community" (Friends and group members as per Group Settings) will always be able to send you game requests.</div>
                  </div>

                  {/* All Thousand Greens Members */}
                  <div className='mt-sm'>
                    {updatingVisibility
                      ? <div className='flex items-center h-28 pl-8'>
                        <ThreeDots
                          visible={true}
                          height="50"
                          width="50"
                          color={"#098089"}
                          radius="9"
                          ariaLabel="three-dots-loading"
                          wrapperStyle={{}}
                          wrapperClass=""
                        />
                      </div>
                      : <Checkbox
                        customClasses={'text-14'}
                        label="All Thousand Greens Members"
                        isDisabled={updatingVisibility}
                        value={user?.visibleToPublic}
                        onClick={() => {
                          updateVisibilitySettings()
                        }}
                      />}
                    <div className='text-12 text-grayLight pl-lg'>
                      If you uncheck this option, any TG member who is not part of your "My TG Community" will not be able to create game requests to you, You will however, have an option to create game requests to any eligible TG member.
                    </div>
                  </div>
                </div>
              </div>

              <div className='flex flex-col border-b border-lightestgray mt-2'>
                {
                  !isMobile && renderHowItWorks()
                }

                <div className={`flex ${isMobile && 'flex-col'}`}>
                  <div className={`flex-col pb-md lg:w-10/24 lg:pr-md`}>
                    <div className={`${isMobile ? 'text-16' : 'text-18'}`}>Manage My Clubs Visibility</div>
                    <div className='text-grayLight'>All checked clubs will be visible to all TG users belonging to same group as you irrespective of their tier and club's defined LTV settings.</div>
                  </div>

                  <div className='flex justify-between flex-col md:flex-row lg:w-15/24'>
                    <div className={`${isMobile && 'mt-md'} py-md`}>
                      {clubs?.map((club) => {
                        return (
                          <div key={club?.id}>
                            <div className='flex' key={club?.id}>
                              <IntermediateCheckbox
                                isDisabled={true}
                                customClasses={'pointer-events-none'}
                                label={club?.name}
                                value={club?.isIntermediate || club?.isVisible}
                                image={club?.isIntermediate ? "/svg/IntermediateCheck.svg" : club?.isVisible ? "/svg/Check.svg" : ''}
                                update={(value) => {
                                }} />
                              <div className='pl-md pt-xs text-12 text-darkteal min-w-[85px] cursor-pointer underline'
                                onClick={() => {
                                  setDrawer({
                                    type: CLUB_VISIBILITY_DETAILS,
                                    global: true,
                                    clubId: club?.id,
                                    clubName: club?.name,
                                    setRefresh
                                  })
                                }}
                              >View Groups</div>
                            </div>
                          </div>
                        )
                      })}
                    </div>
                    <div className='flex flex-row md:flex-col justify-between items-center md:justify-end py-md'>
                      {
                        isMobile && renderHowItWorks()
                      }
                      <CustomButton
                        marginX='0'
                        onClick={() => setModal({
                          type: "edit-club-visibility",
                          width: 451,
                          clubs,
                          getMyClubsVisibilitySettings,
                          fetchUser,
                          setRefresh
                        })} imageMarginBottom='0' textSize={'14'} width={119} height={30} color='lightestgray'
                        borderColor='black' textColor='black' text='Edit Visibility' buttonImage='/svg/EditIconBlack.svg' />
                    </div>
                  </div>
                </div>
              </div>

              <div className={`flex ${isMobile && 'flex-col'} ${!showTutorial && 'border-b border-lightestgray'} pb-md ${(showTutorial && currentTutorial?.id === 1) ? 'relative bg-white rounded-lg px-md pt-sm outline outline-2 outline-offwhite4 outline-offset-[-2px]' : ''}`}>
                <div className='flex-col lg:w-10/24 lg:pr-md'>
                  <div className={`${isMobile ? 'text-16' : 'text-18'}`}>Mute Account</div>
                  <div className='text-grayLight'>You will not be able to create or receive any Game Request while your account is muted</div>
                </div>
                {(showTutorial && currentTutorial?.id === 1) && (
                  <>
                    {window.innerHeight < 920 ?
                      <div className='absolute top-[-250px] left-[0px]'>
                        <TutorialCard loading={loading} flipTriangle={true} currentTutorial={currentTutorial} setCurrentTutorial={setCurrentTutorial} markTutorialAsViewed={markTutorialAsViewed} />
                      </div> :
                      <div className='absolute bottom-[-40px] left-[0px]'>
                        <TutorialCard loading={loading} currentTutorial={currentTutorial} setCurrentTutorial={setCurrentTutorial} markTutorialAsViewed={markTutorialAsViewed} />
                      </div>
                    }
                  </>
                )}
                <div className={`${!isMobile ? '' : ''} lg:w-[37%] flex-col flex float-start`}>
                  <ProfileQuestion
                    togglePosition={!isMobile ? '' : 'justify-end'}
                    value={muted}
                    cssClasses="mt-sm"
                    update={(val) => {
                      if (val) {
                        setModal({
                          type: 'mute-user',
                          title: 'Mute Your Account',
                          setMuted,
                        })
                      } else {
                        setMuted(val)
                      }
                    }}
                  />
                </div>
              </div>

              {isTgAmbassador &&
                <div className='flex py-md'>
                  <div className='flex'>
                    <div className='flex-col lg:pr-md'>
                      <div className={`${isMobile ? 'text-16' : 'text-18'}`}>TG Ambassador Visibility</div>
                      <div className='text-grayLight'>New users near you will be connected to you as friends if this toggle is On </div>
                    </div>
                    {!isMobile &&
                      <div className={`${!isMobile ? '' : ''} lg:w-[37%] flex-col flex float-start`}>
                        <ProfileQuestion
                          togglePosition={!isMobile ? '' : 'justify-end'}
                          value={ambassadorTag}
                          cssClasses="mt-sm "
                          update={(val) => {
                            setAmbassadorTag(val)
                            updateVisibility(val)
                          }}
                        />
                      </div>
                    }
                  </div>
                  {ambassadorTag &&
                    <div className='flex flex-col md:flex-row justify-end items-end flex-1'>
                      {isMobile &&
                        <ProfileQuestion
                          togglePosition={!isMobile ? '' : 'justify-end'}
                          value={ambassadorTag}
                          cssClasses="mt-sm "
                          update={(val) => {
                            setAmbassadorTag(val)
                            updateVisibility(val)
                          }}
                        />
                      }
                      <CustomButton
                        marginX='0'
                        onClick={() => setModal({
                          type: "edit-default-message",
                          width: 530,
                        })} imageMarginBottom='0' textSize={'14'} width={119} height={30} color='lightestgray'
                        borderColor='black' textColor='black' text='Edit Message' buttonImage='/svg/EditIconBlack.svg' />
                    </div>
                  }
                </div>
              }
              <div className={` ${isMobile && 'justify-evenly'} flex py-md`}>
                <CustomButton
                  text='Change Password'
                  height={45}
                  width={138}
                  onClick={() => {
                    setModal({
                      type: 'change-password',
                      user
                    })
                  }}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

export default AccountSettings