import React, { useContext, useEffect, useState } from 'react'
import useCheckDeviceScreen from '../../hooks/useCheckDeviceScreen'
import { UserContext } from '../_app'
import constantOptions from '../../constants/constantOptions'
import useClient from '../../graphql/useClient'
import moment from 'moment'
import Link from 'next/link'
import CustomButton from '../../components/buttons/CustomButton'
import { GET_MY_PLAN } from '../../graphql/queries/membership'
import { useRouter } from 'next/router'
import { ModalContext } from '../../context/ModalContext'

const Membership = ({ setShowDetails }) => {
  const { isMobile, isDesktop, isWideScreen } = useCheckDeviceScreen()
  const { user, fetchUser, token, clevertap } = useContext(UserContext)
  const client = useClient()
  const [plan, setPlan] = useState()
  const router = useRouter()
  const { setModal } = useContext(ModalContext)



  useEffect(() => {
    const fetchMembershipPlan = () => {
      if (client) {
        client.request(GET_MY_PLAN, {
          id: user?.membership_plan_id
        })
          .then(data => {
            setPlan(data?.membership_plans[0]?.name);
          });
      }
    }
    fetchMembershipPlan()
  }, [user?.membership_plan_id, client])



  return (
    <div className="flex flex-col mb-xl w-full">
      <div className='flex w-full'>
        {isMobile ? (
          <div className={`w-full flex-center py-sm px-sm relative`}>
            <div className='absolute' style={{ left: 10 }} onClick={() => setShowDetails(false)}>
              <img src='/svg/BackArrow.svg' alt="Back" />
            </div>
            <div className='text-18 font-medium'>Membership</div>
          </div>
        ) : (
          <>
            <div className="flex items-end flex-1 text-24 font-medium pt-md">
              Membership
            </div>
          </>
        )}
      </div>
      <div className="flex flex-col">
        <div className="w-full mb-lg">
          {user?.membership_plan_id !== constantOptions?.PLAN_KEYS?.FCM ? (
            <div className={`bg-white p-lg rounded-lg flex-col mt-8`}>
              <div className={`${isMobile ? 'text-16' : 'text-18'}`}>Membership Details</div>
              <div className='flex py-md'>
                <div className={` ${isMobile && 'flex-col'} flex justify-between`}
                  style={{ width: "60%" }}
                >
                  {user?.payment_method_details && (
                    <div className='text-14'>
                      <div className='text-grayLight'>Payment Method</div>
                      <div className='mt-md text-16'>**** **** **** {user?.payment_method_details?.last4}</div>
                    </div>
                  )}
                  <div className={`${isMobile && 'py-md'}`}>
                    <div className='text-grayLight'>My Level</div>
                    <div className='text-16 mt-md'>{plan}</div>
                  </div>
                  <div>
                    <div className='text-grayLight'>Next Renewal Date</div>
                    <div className='text-16 mt-md'>{moment(user?.membership_expires_on).format('MM/DD/YYYY')}</div>
                  </div>
                </div>
              </div>
              <div className={`flex items-center ${isMobile ? 'justify-between' : 'justify-end'}`}>
                {user?.payment_method_details ? (
                  <Link href={'/dashboard/payment-history'}>
                    <div className={`flex-center ${isMobile ? 'pr-md' : 'px-md'} text-14 text-darkteal cursor-pointer underline`}>View Payment History</div>
                  </Link>
                ) : null}
                {
                  user?.payment_method_details?.id ?
                    <CustomButton
                      imageRightMargin={5}
                      buttonImage='/svg/EditBlack.svg'
                      marginX='0'
                      color='lightestgray'
                      textColor='black'
                      borderColor={'grayLight'}
                      height={30}
                      width={161}
                      text='Edit Payment Details'
                      onClick={() => {
                        setModal({
                          type: 'edit-payment',
                          width: 600,
                          setModal
                        })
                      }}
                    />
                    : ""
                }
              </div>
            </div>
          ) : null}
        </div>
        {user?.membership_plan_id !== constantOptions?.PLAN_KEYS?.FCM &&
          <div className='flex justify-between items-center bg-white rounded-lg p-lg mb-lg'>
            <div className='flex items-center flex-1'>
              <div className='h-[42px] w-[42px] rounded-full flex-center bg-tealTierBg'>
                <img src='/svg/CreditCard.svg' />
              </div>
              <div className='text-16 pl-sm'>Check available membership plans & pricing of Thousand Greens</div>
            </div>
            <a className='h-[30px] px-md rounded-lg bg-darkteal flex-center text-14 text-white' href="/pricing/renewal" target='_blank'>
              View Pricing Plan
            </a>
          </div>
        }
      </div>
      {!isMobile ? (
        <div className='bg-white flex items-center py-32 px-md rounded-lg'>
          <div className='h-[56px] w-[56px] rounded-full flex-center bg-tealTierBg'>
            <img src='/icons/ngv-icon.svg' />
          </div>
          <div className='pl-lg flex-1'>
            <div className='text-18'>Net Game Value and First Time Requests</div>
            <div className='text-grayLight'>NGV reflects guest games played vs games hosted. FTR represents the count of first-time requesters you have hosted. Renewal pricing is based on a member's NGV and FTR over the past 12 months.
            </div>
          </div>
          <div className='flex-1 flex justify-end'>
            <div className='flex-col'>
              <div className='flex justify-end mb-md'>
                <Link href={'/dashboard/membership-logs'}><div className='text-14 text-darkteal cursor-pointer underline'>View History</div></Link>
              </div>
              <div className='flex bg-tealTierBg rounded-lg px-md py-sm'>
                <div className='rounded-l-lg px-sm border-r-2 border-white'>
                  <span className='text-16 font-medium'>Current NGV: </span><span className='text-16 text-darkteal'>{user?.net_games_value !== null ? user?.net_games_value : "NA"}</span>
                </div>
                <div className='rounded-r-lg px-sm'>
                  <span className='text-16 font-medium'>Current FTR: </span><span className='text-16 text-darkteal'>{user?.ftr_count !== null ? user?.ftr_count : "NA"}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className='bg-white flex-col py-32 px-md'>
          <div className='flex'>
            <div className='h-[56px] w-[56px] rounded-full flex-center bg-tealTierBg'>
              <img src='/icons/ngv-icon.svg' />
            </div>
            <div className='pl-lg flex-1'>
              <div className='flex justify-between'>
                <div className='text-18 w-[60%] md:w-[70%]'>Net Game Value and First Time Requests</div>
                <Link href={'/dashboard/membership-logs'}><div className='text-14 text-darkteal cursor-pointer underline'>View History</div></Link>
              </div>
              <div className='text-grayLight'>NGV reflects guest games played vs games hosted. FTR represents the count of first-time requesters you have hosted. Renewal pricing is based on a member's NGV and FTR over the past 12 months.
              </div>
            </div>
          </div>
          <div className='flex bg-tealTierBg rounded-lg px-md py-[10px] flex-center mt-md'>
            <div className='rounded-l-lg px-sm border-r-2 border-white flex-center flex-1'>
              <span className='text-16'>Current NGV: </span><span className='text-16 text-darkteal pl-sm'>{user?.net_games_value !== null ? user?.net_games_value : "NA"}</span>
            </div>
            <div className='rounded-r-lg px-sm flex-center flex-1'>
              <span className='text-16'>Current FTR: </span><span className='text-16 text-darkteal pl-sm'>{user?.ftr_count !== null ? user?.ftr_count : "NA"}</span>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export { Membership as default } 