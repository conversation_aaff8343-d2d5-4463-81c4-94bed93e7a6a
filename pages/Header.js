import React, { useState, useEffect, useContext, useMemo, useCallback } from 'react'
import Link from 'next/link'
import firebase from 'firebase/compat/app'
import 'firebase/compat/auth'
import { useRouter } from 'next/router'
import MobileMenu from '../components/header/MobileMenu'
import { UserContext } from './_app'
import Logo from '../components/layout/Logo'
import { ModalContext } from '../context/ModalContext'
import NameInitials from '../components/common/NameInitials'
import { DrawerContext } from '../context/DrawerContext'
import constantOptions from '../constants/constantOptions'
import toPascalCaseWithSpaces from '../utils/helper/toPascalCaseWithSpaces'
import useThumbnail from '../hooks/useThumbnail'
import streamOptions from '../constants/streamOptions'

import ENDPOINTS from "../constants/endpoints.json";
import useMaintenanceStatus from '../hooks/useMaintenanceStatus'
const { NOTIFICATION } = constantOptions?.DRAWER_TYPE

const initialLinks = {
  admin: [
    'feed',
    'users',
    'clubs',
    'associations',
    'events',
    'networks',
    'pegboards',
    'games',
    'chats',
    'forum',
    'FAQ',
    'benefits',
  ],
  user: [
    'tg chat',
    'groups',
    'friends',
    'map',
    'requests',
    'offers',
    'pegboards',
    'events',
    'benefits',
    'help',
  ],
  demo: [
    'feed',
    'chat',
    'play',
    'requests',
    'events',
    'local',
    'network',
    'marketplace',
  ],
  none: [],
  ['network-admin']: ['users', 'games'],
}

export default function Header({ notificationsActive, setNotificationsActive }) {
  const { user, clevertap, streamChatClient, token, fetchUser } = useContext(UserContext)
  const router = useRouter()
  const [role] = useState(user.role)
  const [links] = useState(initialLinks)
  const { modal, setModal } = useContext(ModalContext)
  const { setDrawer } = useContext(DrawerContext)
  const [dropdownVisible, setDropdownVisible] = useState(false)
  const { thumbnailUrl } = useThumbnail(user?.profilePhoto, 128)
  const [hasUnread, setHasUnread] = useState(false);
  const [hasUnreadRequest, setHasUnreadRequest] = useState(false);
  // const [tealDotStatus, setTealDotStatus] = useState({});
  const { tealDotStatus, refetchStatus } = useMaintenanceStatus()
  const [refreshTealDotStatus, setRefreshTealDotStatus] = useState(0)
  const adminSignedInAsUser = role !== 'network-admin' && window.localStorage.getItem('tg-admin-id')
  const notificationsImage = useMemo(() => {
    if (!user?.unread?.aggregate?.count) return '/svg/notifications.svg'
    return notificationsActive ? '/svg/notifications-active-new.svg' : '/svg/notifications-new.svg'
  }, [user?.all?.aggregate?.count, user?.unread?.aggregate?.count, notificationsActive])

  useEffect(() => {
    if (['offers', 'my-friends', 'pegboards', 'groups'].includes(router?.query?.type)) {
      updateLinkStatus(getLinkStatusKey(router?.query?.type === 'my-friends' ? 'friends' : router?.query?.type))
    }
  }, [router.query])

  useEffect(() => {
    if (streamChatClient && user?.role === 'user') {
      getUnreadCount()
      getUnreadRequestCount()
    }
  }, [streamChatClient?.user, user?.role])

  useEffect(() => {
    refetchStatus()
  }, [user?.id, user?.role, token, refreshTealDotStatus]);

  useEffect(() => {
    if (user?.role === 'user') {
      /**
       * This event is for listening to the new messages
       * Here we will check that the message must not be from the user himself
       */
      const newMessageEventListener = async (event) => {
        if (event?.message?.user?.id !== user?.id) {
          if (event?.channel_type === streamOptions.CHANNEL.TYPES.REQUEST_CHAT_GROUP) {
            setHasUnreadRequest(!!event?.unread_channels)
          } else {
            setHasUnread(!!event?.unread_channels)
          }
        }
      }

      /**
       * When we read the channel we have to update our UI
       * @param {*} event
       */
      const messageReadNotification = async (event) => {
        let unreadChannels = []
        let unreadRequestChannels = []
        if (event?.unread_channels) {
          unreadChannels = await streamChatClient.queryChannels({
            members: { $in: [user?.id] },
            has_unread: true,
            type: {
              $in: [
                streamOptions.CHANNEL.TYPES.USER_CREATED_GROUP,
                streamOptions.CHANNEL.TYPES.SYSTEM_THOUSAND_GREENS_PUBLIC,
                streamOptions.CHANNEL.TYPES.ADMIN_CREATED_GROUP,
                streamOptions.CHANNEL.TYPES.MY_TG_GROUP,
                streamOptions.CHANNEL.TYPES.ONE_TO_ONE,
                streamOptions.CHANNEL.TYPES.SYSTEM,
              ],
            },
          })
          unreadRequestChannels = await streamChatClient.queryChannels({
            members: { $in: [user?.id] },
            has_unread: true,
            type: {
              $in: [
                streamOptions.CHANNEL.TYPES.REQUEST_CHAT_GROUP,
              ],
            },
          })
        }
        if (event?.channel_type === streamOptions.CHANNEL.TYPES.REQUEST_CHAT_GROUP && !unreadRequestChannels?.length) {
          setHasUnreadRequest(false)
        }
        setHasUnread(!!event?.unread_channels && !!unreadChannels?.length)
      }

      const notificationNewMessage = streamChatClient.on(
        'message.new',
        newMessageEventListener
      )
      const notificationMessageRead = streamChatClient.on(
        'notification.mark_read',
        messageReadNotification
      )

      return () => {
        notificationNewMessage?.unsubscribe()
        notificationMessageRead?.unsubscribe()
      }
    }
  }, [streamChatClient, user?.role])

  const getUnreadRequestCount = async () => {
    let unreadChannels = []
    if (streamChatClient?.user?.unread_channels) {
      unreadChannels = await streamChatClient.queryChannels({
        members: { $in: [user?.id] },
        has_unread: true,
        type: {
          $in: [
            streamOptions.CHANNEL.TYPES.REQUEST_CHAT_GROUP,
          ],
        },
      })
    }
    setHasUnreadRequest(!!streamChatClient?.user?.unread_channels && !!unreadChannels?.length)
  }

  const getUnreadCount = async () => {
    let unreadChannels = []
    if (streamChatClient?.user?.unread_channels) {
      unreadChannels = await streamChatClient.queryChannels({
        members: { $in: [user?.id] },
        has_unread: true,
        type: {
          $in: [
            streamOptions.CHANNEL.TYPES.USER_CREATED_GROUP,
            streamOptions.CHANNEL.TYPES
              .SYSTEM_THOUSAND_GREENS_PUBLIC,
            streamOptions.CHANNEL.TYPES.ADMIN_CREATED_GROUP,
            streamOptions.CHANNEL.TYPES.MY_TG_GROUP,
            streamOptions.CHANNEL.TYPES.ONE_TO_ONE,
            streamOptions.CHANNEL.TYPES.SYSTEM,
          ],
        },
      })
    }
    setHasUnread(!!streamChatClient?.user?.unread_channels && !!unreadChannels?.length)
  }


  const clearSessionStorage = useCallback(() => {
    window?.sessionStorage?.removeItem("MAP_LIST_TYPE")
    window?.sessionStorage?.removeItem("MAP_INFO_RETURN_DATA")
    window?.sessionStorage?.removeItem("MY_TG_GROUP_DETAILS_BACK_HANDLER")
    window?.sessionStorage.removeItem('MAP_FILTERS')
    setDrawer()
  }, [setDrawer])

  const updateLinkStatus = useCallback((linkStatusKey) => {
    try {
      fetch(ENDPOINTS?.UPDATE_TEAL_DOT_STATUS, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ['Authorization']: `Bearer ${token}`,
        },
        body: JSON.stringify({
          userId: user?.id,
          flagKey: linkStatusKey,
          value: false
        }),
      })
        .then((data) => data.json())
        .then((data) => {
          setRefreshTealDotStatus(prev => prev + 1)
          fetchUser()
        })
    } catch (error) {
      console.log("Update Link Status------->", error);
    }
  }, [token, user?.id])

  const handleLinkClick = useCallback((link) => {
    clearSessionStorage()
    clevertap?.event.push(toPascalCaseWithSpaces(link))
    if (['offers', 'friends', 'pegboards', 'events', 'benefits', 'groups','requests'].includes(link) && tealDotStatus?.[getLinkStatusKey(link)]) {
      updateLinkStatus(getLinkStatusKey(link))
    }

    router.push({
      pathname: getPathname(link, role),
      search: getSearchParams(link)
    })
    setRefreshTealDotStatus(prev => prev + 1)
  }, [clearSessionStorage, clevertap, router, role])

  const handleProfileClick = useCallback(() => {
    if (role === 'user') {
      clearSessionStorage()
      router.push({ pathname: '/profile/profile-settings' })
      clevertap?.event.push("Profile Settings Clicked")
    }
  }, [clearSessionStorage, router, role, clevertap])

  const handleLogout = useCallback(() => {
    setModal({
      type: 'user-logout',
      width: 400,
      setDropdownVisible,
    })
  }, [setModal])

  useEffect(() => {
    if (dropdownVisible) {
      window.addEventListener('click', () => setDropdownVisible(false))
    }
    return () => window.removeEventListener('click', () => setDropdownVisible(false))
  }, [dropdownVisible])

  const backToAdmin = useCallback(async () => {
    try {
      const { customToken } = await fetch('/api/admin/signInAsUser', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ id: adminSignedInAsUser }),
      }).then(data => data.json())

      localStorage.removeItem('tg-admin-id')
      await firebase.auth().signInWithCustomToken(customToken)
    } catch (error) {
      console.error('Error signing in as admin:', error)
    }
  }, [adminSignedInAsUser])

  const getPathname = (link, role) => {
    if (link === 'system' && role === 'user') return '/profile/account-settings'

    if (role === 'admin') {
      const adminRoutes = {
        'feed': '/admin/feed',
        'users': '/admin/users',
        'clubs': '/admin/clubs',
        'associations': '/admin/associations',
        'networks': '/admin/networks',
        'pegboards': '/admin/pegboards',
        'games': '/admin/games',
        'chats': '/admin/chats',
        'forum': '/admin/forum',
        'faq': '/admin/faq',
        'benefits': '/admin/benefits'
      }
      return adminRoutes[link.toLowerCase()] || `/${role}/${link.toLowerCase()}`
    }

    const basePath = role === 'user' || role === 'demo' ? 'dashboard' : role
    const subPath = ['events', 'benefits', 'help', 'map', 'offers'].includes(link)
      ? link.toLowerCase()
      : ['requests', 'pegboards'].includes(link)
        ? 'play'
        : 'my-tg'

    return `/${basePath}/${subPath}`
  }

  const getSearchParams = (link) => {
    const searchParams = {
      'requests': 'type=requests&tab=received&subtype=open',
      'pegboards': link === 'pegboards' && role === 'admin' ? '?t=0' : 'type=pegboards',
      'friends': 'type=my-friends',
      'tg chat': 'type=chat',
      'groups': 'type=groups'
    }
    return searchParams[link] || ''
  }

  const isLinkActive = (router, link) => {
    if (router.pathname.toLowerCase().includes(link.toLowerCase())) return true

    const typeMappings = {
      'tg chat': 'chat',
      'friends': 'my-friends',
      'map': 'clubs'
    }

    const queryType = typeMappings[link] || link.toLowerCase()
    return router?.query?.type === queryType
  }

  const getLinkStatusKey = (link) => {
    switch (link) {
      case 'offers': return 'offer';
      case 'groups': return 'group';
      case 'benefits': return 'benefit';
      case 'events': return 'event';
      case 'friends': return 'friend';
      case 'pegboards': return 'pegboard';
      case 'requests': return 'request';
      default: return null;
    }
  }

  return (
    <>
      {adminSignedInAsUser && role !== 'admin' && (
        <div className="w-full text-white bg-darkteal flex-center py-sm">
          <div className="container flex justify-between text-sm font-thin">
            Signed in as {user.email}
            <div
              onClick={backToAdmin}
              id="back-to-admin-portal"
              className="cursor-pointer hover:underline">
              Back to Admin Portal
            </div>
          </div>
        </div>
      )}

      <div
        id="main-nav-bar"
        className="container relative flex items-center justify-center md:justify-between font-ubuntu pl-md md:px-md mx-md"
        style={{ height: 70, }}>
        <div className="hidden md:block">
          <Logo height={46} key="logo46" />
        </div>
        <div className="md:hidden">
          <Logo height={26} key="logo26" />
        </div>

        <div className="flex-1 lg:hidden" />
        <div className={`hidden ${role === 'admin' ? 'w-full' : 'w-[60%]'} lg:flex ${role !== 'network-admin' ? 'justify-around' : ''} mx-12`}>
          {(role ? links[role] : []).map((link, _i) => (
            <a
              key={`${link}_${_i}`}
              onClick={() => handleLinkClick(link)}
              className={`
                ${isLinkActive(router, link) ? 'text-darkteal' : 'hover:underline'} 
                cursor-pointer relative py-md capitalize text-black text-14 
                ${role === 'network-admin' ? 'mx-lg' : ''}
              `}
            >
              {link === 'tg chat' ? 'TG Chat' : link}
              {(link === 'tg chat' && hasUnread || (link === 'requests' && hasUnreadRequest) ||
                ['groups', 'friends', 'pegboards', 'events', 'benefits', 'offers'].includes(link) &&
                tealDotStatus?.[getLinkStatusKey(link)]) &&
                <div
                  className='bg-darkteal rounded-full absolute'
                  style={{ minHeight: 7, minWidth: 7, top: 13, right: -5 }}
                />
              }
            </a>
          ))}
        </div>

        <div className="relative flex items-center">
          {role === 'admin' && (
            <>
              <Link href="/admin/analytics">
                <img
                  src={`/svg/${router.pathname === '/admin/analytics' ? 'analytics-active' : 'analytics'}.svg`}
                  style={{ height: 30, width: 30 }}
                  className="cursor-pointer mr-md"
                  alt="Analytics"
                />
              </Link>
              <Link href="/admin/system-settings">
                <img
                  className="cursor-pointer"
                  src={`/svg/${router.pathname === '/admin/system-settings' ? 'settings-active' : 'settings'}.svg`}
                  style={{ height: 23, width: 30 }}
                  alt="Settings"
                />
              </Link>
            </>
          )}

          {role === 'user' && (
            <img
              onClick={() => setDrawer({ type: NOTIFICATION, global: true })}
              src={notificationsImage}
              height={20}
              width={20}
              className="cursor-pointer"
              alt="Notifications"
            />
          )}

          {user?.profilePhoto ? (
            <>
              <div
                className={`cursor-pointer hidden md:block ${router.pathname.includes('/profile') ? 'border border-darkteal' : ''}`}
                onClick={handleProfileClick}
                style={{
                  marginLeft: 25,
                  marginRight: 8,
                  borderWidth: router.pathname.includes('/profile') ? 2 : 0,
                  height: 40,
                  width: 40,
                  borderRadius: 10,
                  backgroundImage: `url("${thumbnailUrl}")`,
                  backgroundSize: 'cover',
                  backgroundPosition: 'center',
                }}
              />
              <div
                className={`cursor-pointer md:hidden ${router.pathname.includes('/profile') ? 'border border-darkteal' : ''}`}
                onClick={handleProfileClick}
                style={{
                  marginLeft: 18,
                  marginRight: 6,
                  borderWidth: router.pathname.includes('/profile') ? 2 : 0,
                  height: 28,
                  width: 28,
                  borderRadius: 4,
                  backgroundImage: `url("${thumbnailUrl}")`,
                  backgroundSize: 'cover',
                  backgroundPosition: 'center',
                }}
              />
            </>
          ) : (
            <>
              <div
                className='cursor-pointer hidden md:block'
                onClick={handleProfileClick}
                style={{
                  marginLeft: 25,
                  marginRight: 8,
                  height: 40,
                  width: 40,
                  borderRadius: 10,
                }}>
                <NameInitials
                  key="ni1"
                  user={user}
                  height={40}
                  width={40}
                  fontSize={25}
                  role={role}
                />
              </div>
              <div
                className='cursor-pointer md:hidden'
                onClick={handleProfileClick}
                style={{
                  marginLeft: 18,
                  marginRight: 6,
                  height: 28,
                  width: 28,
                  borderRadius: 10,
                }}>
                <NameInitials
                  key="ni2"
                  user={user}
                  height={40}
                  width={40}
                  fontSize={25}
                  role={role}
                />
              </div>
            </>
          )}

          <img
            onClick={(e) => {
              e.stopPropagation()
              setDropdownVisible(true)
            }}
            className="cursor-pointer"
            src="/svg/arrow-down-black.svg"
            width={10}
            height={10}
            style={{ marginLeft: 15 }}
            alt="Dropdown"
          />

          {dropdownVisible && (
            <div
              onClick={(e) => e.stopPropagation()}
              className="absolute bg-white border rounded shadow p-md border-lightgray"
              style={{
                top: 'calc(100% + 10px)',
                right: -10,
                zIndex: 1000,
              }}>
              <div
                onClick={handleLogout}
                className="text-sm uppercase cursor-pointer text-gray hover:underline">
                Log Out
              </div>
            </div>
          )}
        </div>

        <div style={{ zIndex: 1000 }}>
          <MobileMenu
            links={links[role]}
            prefix={role === 'user' ? '/dashboard/' : '/admin/'}
            role={role}
          />
        </div>
      </div>
    </>
  )
}
