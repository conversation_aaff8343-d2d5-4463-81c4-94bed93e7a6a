const GET_GAME = `
query getGame($game_id: Int!) {
    game_by_pk(game_id: $game_id) {
      host {
        first_name
        last_name
      }
      request_id
      request {
        user {
            id
            first_name
            last_name
        }
        club_id
        club {
          name
        }
      }
      date
    }
}  
`;

const GAME_REVIEW_EXISTS = `
query fetchGameReview($user_id: uuid!, $game_id: Int!) {
  game_review_aggregate(where: {user_id: {_eq: $user_id}, game_id: {_eq: $game_id}}) {
    aggregate {
      count
    }
  }
}
`;

const GET_REQUEST_CHAT = `
query getRequestChat($requestId: uuid!, $clubMemberId: uuid!) {
  request_chat(where: {request_id: {_eq: $requestId}, club_member_id: {_eq: $clubMemberId}}) {
    sendbird_channel_id
    stream_channel_id
    club_member_id
  }
}

`;

export { GET_GAME, GAME_REVIEW_EXISTS, GET_REQUEST_CHAT };
