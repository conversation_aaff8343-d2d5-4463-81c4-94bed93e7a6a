import constantOptions from '../../constants/constantOptions';

const FETCH_USER = `
query getUser($user_id: uuid) {
  user(where: {id: {_eq: $user_id}}) {
    signup_current_step
    is_tutorial_viewed
    is_super_host
    is_super_guest
    is_web_tutorial_viewed
    deleted_at
    about_yourself
    association_id
    user_association_number
    id
    created_at
    username
    birthYear
    email
    englishFluency
    facebook
    gender
    linkedin
    pace
    handicap
    first_name
    last_name
    playAsCouple
    phone
    tier
    profilePhoto
    playedClubs
    tokens
    premium
    declined
    deactivated
    last_active_session
    profile_complete
    registration_complete
    referral
    is_legacy_user
    legacy_password_reset
    account_activated
    visibleInNetwork
    visibleToPublic
    tierVisibility
    last_muted_prompt
    visibleToLowerTiers
    verificationMethod
    verified
    notificationSettings
    net_games_value
    hasSendbirdAccount
    created_at
    annual_confirmation
    muted
    mute_until
    opt_in_email
    verified_on
    mute_other_notifications
    phone_number_details
    is_tg_founder
    membership_expires_on
    membership_active
    stripe_customer_id
    membership_plan_id
    stripe_customer_info
    payment_method_details
    ftr_count
    visited_account_settings
    yearly_club_validation
    show_visibility_popup
    show_visibility_popup_time
    favorite_clubs {
      club_id
    }
    network_admin {
      id
    }
    clubs(where: {club: {club_type: {_eq: ${constantOptions?.CLUB_TYPES?.PRIVATE}}}}) {
      proximity
      paymentMethod
      is_yearly_validated
      is_replaced
      muted
      restricted_membership
      otherInstructions
      visible_to_non_friends
      favorite_restricted
      visibleInNetwork
      club {
        id
        name
        hasGuestRestrictions
        has_membership_privileges
        guest_time_restrictions
        guestFee
        dressCode
        caddieRequired
        caddieFee
        notes
        new
        lowest_visible_tier
        closurePeriods
        club_type
        club_demand_type
      }
    }
    unread: notifications_aggregate(where: {read: {_eq: false}, web_panel: {_eq: true}}) {
      aggregate {
        count
      }
    }
    all: notifications_aggregate {
      aggregate {
        count
      }
    }
    private_network {
      private_network {
        name
        id
        pin
        createChatGroup
      }
    }
    personal_network_contacts {
      first_name
      last_name
      id
      tg_contact_id
      course {
        id
      }
    }
    association {
      name
    }
    user_association_number
    socialPublic
    additional_settings
    visible_to_favorite_clubs
    is_tg_ambassador
    tg_ambassador_visibility
    activated_date
    show_visibility_popup
  }
}
`;

const GET_USER_PROFILE = `
query getUserProfile($id: uuid!) {
  user_by_pk(id: $id) {
    email
    id
    birthYear
    facebook
    first_name
    gender
    englishFluency
    handicap
    last_name
    linkedin
    pace
    phone
    playAsCouple
    username
    about_yourself
    phone_number_details
    deleted_at
    private_network_user {
      private_network {
        id
        name
      }
    }
  }
}
`;

const GET_GOLFER_PROFILE = `
query getGolferProfile($user_id: uuid!) {
  user_by_pk(id: $user_id) {
    id
    first_name
    last_name
    profilePhoto
    phone
    email
    linkedin
    facebook
  }
}
`;

const FIND_USERNAME_DUPLICATE = `
query findUsername($username: String) {
  user_aggregate(where: {username: {_eq: $username}}) {
    aggregate {
      count
    }
  }
}
`;

const FIND_EMAIL_DUPLICATE = `
query findEmail($email: String) {
  user_aggregate(where: {email: {_ilike: $email}}) {
    aggregate {
      count
    }
    nodes {
      first_name
      phone
    }
  }
}
`;

const CLUB_REQUEST_MEMBERS = `
query clubRequestMembers($club_id: Int) {
  user_club(where: {club_id: {_eq: $club_id}, muted: {_eq: false}, user: {muted: {_eq: false}, account_activated: {_eq: true}}}) {
    user {
      id
      age
      gender
      visibleToPublic
      handicap
      pace
      englishFluency
      private_network_user {
        private_network_id
      }
      email
      first_name
      last_name
      muted
      playAsCouple
    }
    club {
      name
    }
    muted
  }
}
`;

const MEMBERS_IN_PRIVATE_NETWORK_CLUB = `
query isClubInPN($private_network_id: Int, $club_id: Int) {
	private_network_user(where: {private_network_id: {_eq: $private_network_id}, user: {clubs: {club_id: {_eq: $club_id}}}}) {
		user_id
  }
}
`;

const GET_USER_PRIVATE_NETWORK = `
query getUserPrivateNetwork($user_id: uuid) {
  private_network_user(where: {user: {id: {_eq: $user_id}}}) {
    private_network_id
  }
}
`;

const GET_USER_EMAIL = `
query getUserEmail($id: uuid!) {
    user_by_pk(id: $id) {
      email
      first_name
      deleted_at
    }
  }
`;
const FB_URL =
  '(?:(?:http|https):\/\/)?(?:www.)?facebook.com\/(?:(?:\w)*#!\/)?(?:pages\/)?(?:[?\w\-]*\/)?(?:profile.php\?id=(?=\d.*))?([\w\-]*)?';
const LINKEDIN_URL =
  '^https?://((www|\w\w)\.)?linkedin.com/((in/[^/]+/?)|(pub/[^/]+/((\w|\d)+/?){3}))$';
const USER_PLAYED_CLUBS = `
query geyPlayedClubs ($user_id: uuid!) {
    user_by_pk(id: $user_id) {
        playedClubs
    }
}  
`;

const ABOUT_YOURSELF_MAX_LENGTH = 300;
const ADMIN_CUSTOM_MESSAGE_MAX_LENGTH = 500;

const GET_USER_VERIFIED_ON = `
query getVerifiedOn ($user_id: uuid!) {
  user_by_pk(id: $user_id) {
    verified_on
  }
}
`;

const VALIDATE_PHONE = `
query getVerifiedOn($phone: String) {
  user_aggregate(where: {phone: {_like: $phone}}) {
    aggregate {
      count
    }
  }
}`;

const TOTAL_USER_COUNT = `
query getTotalUserCount {
  user_aggregate(where: {registration_complete: {_eq: true}}) {
    aggregate {
      count
    }
  }
}`;

const USER_CLUBS = `
query findUser($user_id: uuid!) {
  user_by_pk(id: $user_id) {
    id
    declined
    clubs {
      club_id
      club {
        new
      }
    }
  }
}
`;
const TOTAL_CLUB_COUNT = `
query clubCount {
  courses_aggregate {
    aggregate {
      count
    }
  }
}`;

const ALL_USERS = `
query getUsers($except_user: uuid!) {
    user(where: {deleted_at: {_is_null: true}, declined: {_is_null: true}, profile_complete: {_eq: true}, activate_later: {_eq: false}, account_activated: {_eq: true}, deactivated: {_eq: false}, muted: {_eq: false}, id: {_neq: $except_user}}) {
        id
        email
        first_name
        notificationSettings
        visibleToPublic
        mute_other_notifications
    }
}     
`;

const US_ONLY_USERS = `
query getUsers($except_user: uuid!) {
    user(where: {deleted_at: {_is_null: true}, declined: {_is_null: true}, profile_complete: {_eq: true}, activate_later: {_eq: false}, account_activated: {_eq: true}, deactivated: {_eq: false}, muted: {_eq: false}, id: {_neq: $except_user}, _or: [{phone_number_details: {_contains: {countryCode: "us"}}}, {phone_number_details: {_contains: {countryCode: "US"}}}]}) {
      id
      email
      first_name
      notificationSettings
      visibleToPublic
      phone_number_details
      mute_other_notifications
    }
  }  
`;

const PRIVATE_CLUBS_OF_USER = `
query privateClubsUser($user_id: uuid!) {
  user_club_aggregate(where: {user_id: {_eq: $user_id}, club: {club_type: {_eq: ${constantOptions?.CLUB_TYPES?.PRIVATE}}}}) {
    aggregate {
        count
    }
  }
}`;

const USER_VISIBILITY = `
query user($user_id: uuid!) {
    user_by_pk(id: $user_id) {
        visibleToPublic
        private_network_user {
            private_network_id
            private_network {
              id
              name
            }
        }
    }
}`;

const FETCH_USER_WITH_ALL_CLUBS = `
query getUser($user_id: uuid) {
  user(where: {id: {_eq: $user_id}}) {
    about_yourself
    association_id
    user_association_number
    id
    created_at
    username
    birthYear
    email
    englishFluency
    facebook
    gender
    linkedin
    pace
    handicap
    first_name
    last_name
    playAsCouple
    phone
    tier
    profilePhoto
    playedClubs
    tokens
    premium
    declined
    deactivated
    last_active_session
    profile_complete
    registration_complete
    is_legacy_user
    legacy_password_reset
    account_activated
    visibleInNetwork
    visibleToPublic
    tierVisibility
    visibleToLowerTiers
    verificationMethod
    verified
    notificationSettings
    hasSendbirdAccount
    created_at
    annual_confirmation
    muted
    mute_until
    opt_in_email
    verified_on
    mute_other_notifications
    phone_number_details
    stripe_customer_id
    payment_method_details
    membership_active
    membership_plan_id
    favorite_clubs {
      club_id
    }
    network_admin {
      id
    }
    clubs {
      proximity
      paymentMethod
      muted
      restricted_membership
      otherInstructions
      favorite_restricted
      club {
        id
        name
        hasGuestRestrictions
        has_membership_privileges
        guest_time_restrictions
        guestFee
        dressCode
        caddieRequired
        caddieFee
        notes
        new
        lowest_visible_tier
        closurePeriods
        club_type
      }
    }
    private_network {
      private_network {
        name
        id
        createChatGroup
      }
    }
    personal_network_contacts {
      first_name
      last_name
      id
      tg_contact_id
      course {
        id
      }
    }
    association {
      name
    }
    user_association_number
    socialPublic
    additional_settings
    visible_to_favorite_clubs
    deleted_at
  }
}
`;

const USER_PRIVATE_CLUBS_ID = `
query fetchUserClubIds($user_id: uuid!) {
  user_club(where: {user_id: {_eq: $user_id}, club: {club_type: {_eq: 0}}}) {
    club_id
  }
}
`;

const FIND_CLUB_RELATED_OFFERS = `
query findOffersOnClub ($user_id: uuid!, $club_id: Int!) {
  offer(where: {club_id: {_eq: $club_id}, user_id: {_eq: $user_id}}) {
    id
  }
}`;

const FIND_STRIPE_CUSTOMER_ID = `
query getUsersCustomerId($user_id: uuid!) {
  user_by_pk(id: $user_id) {
    stripe_customer_id
  }
}`;

const FIND_USER_NGV = `
query userData($user_id: uuid!) {
  user_by_pk(id: $user_id) {
    net_games_value
    free_trial_availed
    is_tg_founder
  }
}`;

const FIND_USER_BY_CUSTOMER_ID = `
query getUser($customer_id: String!) {
  user(where: {stripe_customer_id: {_eq: $customer_id}}) {
    id
  }
}
`;

const COUNT_PASSWORD_RESET_ATTEMPT_COUNT = `
query resetPasswordLink($email: String!, $from: timestamptz, $to: timestamptz) {
  reset_password_attempts_aggregate(where: {email: {_like: $email}, created_at: {_lte: $to, _gte: $from}}) {
    aggregate {
      count
    }
  }
}`;

const GET_CLUB_USERS = `
query getClubMembers($club_id: Int) {
    user_club(where: {club_id: {_eq: $club_id}}) {
      user {
        created_at
        id
        first_name
        last_name
        username
        email
        phone
        account_activated
        deactivated
        declined
        activate_later
        registration_complete
        profilePhoto
        tokens
        tier
        temporary_tier {
            original_tier
            created_at
            expiration_date
          }
        net_games_value 
        membership_plan {
            name
        }
        private_network_user {
            private_network_id
            private_network {
                admin_id
                id
            }
        }
        visibleToPublic
        association {
          name
        }
        clubs {
          club {
            id
            name
            new
            lowest_visible_tier
            club_type
            address
          }
          proximity
        }
        user_association_number
      }
    }
    user_club_aggregate(where: {club_id: {_eq: $club_id}}) {
        aggregate {
          count
        }
    }
}
`;

export {
  FETCH_USER,
  FIND_EMAIL_DUPLICATE,
  GET_USER_PROFILE,
  FIND_USERNAME_DUPLICATE,
  CLUB_REQUEST_MEMBERS,
  MEMBERS_IN_PRIVATE_NETWORK_CLUB,
  GET_USER_PRIVATE_NETWORK,
  GET_GOLFER_PROFILE,
  GET_USER_EMAIL,
  FB_URL,
  LINKEDIN_URL,
  USER_PLAYED_CLUBS,
  ABOUT_YOURSELF_MAX_LENGTH,
  ADMIN_CUSTOM_MESSAGE_MAX_LENGTH,
  GET_USER_VERIFIED_ON,
  VALIDATE_PHONE,
  TOTAL_USER_COUNT,
  TOTAL_CLUB_COUNT,
  USER_CLUBS,
  ALL_USERS,
  US_ONLY_USERS,
  PRIVATE_CLUBS_OF_USER,
  USER_VISIBILITY,
  FETCH_USER_WITH_ALL_CLUBS,
  USER_PRIVATE_CLUBS_ID,
  FIND_CLUB_RELATED_OFFERS,
  FIND_STRIPE_CUSTOMER_ID,
  FIND_USER_NGV,
  FIND_USER_BY_CUSTOMER_ID,
  COUNT_PASSWORD_RESET_ATTEMPT_COUNT,
  GET_CLUB_USERS,
};
