// GraphQL mutation strings for requests
export const INSERT_REQUEST = `
mutation createRequest($request: request_insert_input!) {
    insert_request_one(object: $request) {
      id
    }
}  
`;

export const UPDATE_REQUEST = `
mutation updateRequest($id: uuid!, $request: request_set_input) {
  update_request_by_pk(pk_columns: {id: $id}, _set: $request) {
    id
  }
}
`;

export const UPDATE_REQUEST_HOSTS = `
mutation updateHostRequests($request_id: uuid!, $hosts: jsonb, $offer_details: jsonb) {
  update_request(where: {id: {_eq: $request_id}}, _set: {hosts_sent: $hosts, offer_details: $offer_details}) {
    returning {
      hosts_sent
    }
  }
}
`;

export const UPDATE_REQUEST_PN = `
mutation updateRequestPN($request_id: uuid!, $private_network_id: Int) {
  update_request(where: {id: {_eq: $request_id}}, _set: {private_network_id: $private_network_id}) {
    returning {
      hosts_sent
    }
  }
}
`;

export const ACCEPT_REQUEST = `
mutation acceptRequest($accepted_request: accepted_request_insert_input!) {
    insert_accepted_request_one(object: $accepted_request) {
      host_id
      status
      date
    }
  }
`;

export const REMOVE_OFFER = `
mutation updateRequest($offer_id: uuid!) {
  update_request(where: {offer_id: {_eq: $offer_id}}, _set: {offer_id: null}) {
    affected_rows
  }
}
`;

export const ADD_HOST_DECLINATION_REASON = `
mutation ($reason: host_declination_reasons_insert_input!) {
  insert_host_declination_reasons_one(object: $reason) {
    requestor_id
  }
}
`;

export const REMOVE_OFFER_ID_FROM_REQUESTS = `
mutation updateRequest($offer_id: [uuid!]!) {
  update_request(where: {offer_id: {_in: $offer_id}}, _set: {offer_id: null}) {
    affected_rows
  }
}
`;
