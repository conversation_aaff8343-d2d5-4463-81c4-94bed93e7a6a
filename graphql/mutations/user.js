const UPDATE_USER = `
mutation updateUser($user_id: uuid!, $user: user_set_input!) {
    update_user(where: {id: {_eq: $user_id}}, _set: $user) {
      affected_rows
    }
}
`

const UPDATE_USER_TOKENS = `
mutation updateUserTokens($user_id: uuid!, $tokens: Int!) {
  update_user_by_pk(pk_columns: {id: $user_id}, _set: {tokens: $tokens}) {
    tokens
  }
}
`

const UPDATE_USER_CLUB_REVIEW_DISABLED = `
mutation updateUserClubReviewDisabled($user_id: uuid!, $club_id: Int!, $review_disabled: Boolean!) {
  update_user_club(where: {user_id: {_eq: $user_id}, club_id: {_eq: $club_id}}, _set: {review_disabled: $review_disabled}) {
    affected_rows
  }
}
`

const ACTIVATE_USER = `
mutation activateUser($user_id: uuid, $tier: Int, $tokens: Int, $activated_date: date) {
  update_user(where: {id: {_eq: $user_id}}, _set: {activate_later_date: null, declination_date: null, activate_later: false, account_activated: true, deactivated: false, declined: null, tier: $tier, tokens: $tokens, activated_date: $activated_date}) {
    returning {
        id
        tier
        tokens
        account_activated
        email
        first_name
    }
  }
}
`

const DEACTIVATE_USERS = `
mutation deactivateUsers($users: [uuid!]) {
    update_user(where: {id: {_in: $users}}, _set: {activate_later_date: null, declination_date: null, activate_later: false, deactivated: true, declined: null, account_activated: false, activated_date: null}) {
      affected_rows
    }
}
`

const INSERT_NETWORK_ADMIN_USER = `
mutation insertNetworkAdminUser ($email: String) {
  insert_user_one(object: {email: $email}) {
    id
  }
}
`

const DELETE_USER = `
mutation deleteUser($id: uuid!) {
  delete_user_by_pk(id: $id) {
    email
  }
}
`

const VERIFY_USER_BY_EMAIL = `
mutation markUserAsVerified($email: String) {
  update_user(where: {email: {_eq: $email}}, _set: {verified: true, verified_on: {email: true, phone: false}}) {
    affected_rows
  }
}`

const VERIFY_USER_BY_PHONE = `
mutation markUserAsVerified($phone: String, $user: user_set_input!) {
  update_user(where: {phone: {_eq: $phone}}, _set: $user) {
    affected_rows
  }
}
`
const UPDATE_ADDITIONAL_SETTINGS = `
mutation updateSettings($additional_settings: jsonb, $user_id: uuid!) {
  update_user_by_pk(pk_columns: {id: $user_id}, _set: {additional_settings: $additional_settings}) {
    additional_settings
  }
}
`

const FAVORITE_CLUB = `
mutation favoriteClub($user_id: uuid, $club_id: Int) {
  insert_favorite_club_one(object: {club_id: $club_id, user_id: $user_id}) {
    club_id
  }
}
`

const UNFAVORITE_CLUB = `
mutation unfavoriteClub($user_id: uuid!, $club_id: Int!) {
  delete_favorite_club_by_pk(club_id: $club_id, user_id: $user_id) {
    club_id
  }
}
`

const DECLINE_USERS = `
mutation declineUsers($user_ids: [uuid!]!, $reason: String, $declination_date: date) {
  update_user(where: {id: {_in: $user_ids}}, _set: {
    activate_later: false, 
    account_activated: false, 
    deactivated: false,
    activated_date: null, 
    declination_date: $declination_date,
    activate_later_date: null,
    declined: $reason
}) {
    returning {
      id
    }
  }
}
`
const RESET_PASSWORD_UPDATE_BY_EMAIL = `
mutation userResetPassword($email: String) {
  update_user(where: {email: {_eq: $email}}, _set: {is_legacy_user: false, legacy_password_reset: true}) {
    affected_rows
  }
}`

const DELETE_USER_DATA = `
mutation deleteUserData($user_id: uuid!) {
  update_request(where: {offer: {user_id: {_eq: $user_id}}}, _set: {offer_id: null}) {
    returning {
      game_id
    }
  }
  update_game(where: {offer: {user_id: {_eq: $user_id}}}, _set: {offer_id: null}) {
    returning {
      game_id
    }
  }
  update_personal_network_contact(where: {tg_contact_id: {_eq: $user_id}}, _set: {tg_contact_id: null}) {
    returning {
      id
    }
  }
  delete_bookmark(where: {user_id: {_eq: $user_id}}) {
    affected_rows
  }
  delete_comment(where: {user_id: {_eq: $user_id}}) {
    affected_rows
  }
  delete_comment_like(where: {user_id: {_eq: $user_id}}) {
    affected_rows
  }
  delete_event(where: {user_id: {_eq: $user_id}}) {
    affected_rows
  }
  delete_favorite_club(where: {user_id: {_eq: $user_id}}) {
    affected_rows
  }
  delete_game_review(where: {user_id: {_eq: $user_id}}) {
    affected_rows
  }
  delete_notification(where: {user_id: {_eq: $user_id}}) {
    affected_rows
  }
  delete_offer(where: {user_id: {_eq: $user_id}}) {
    affected_rows
  }
  delete_personal_network_contact(where: {user_id: {_eq: $user_id}}) {
    affected_rows
  }
  delete_post(where: {user_id: {_eq: $user_id}}) {
    affected_rows
  }
  delete_post_like(where: {user_id: {_eq: $user_id}}) {
    affected_rows
  }
  delete_post_report(where: {reporting_user: {_eq: $user_id}}) {
    affected_rows
  }
  delete_private_network_removals(where: {user_id: {_eq: $user_id}}) {
    affected_rows
  }
  delete_private_network_user(where: {user_id: {_eq: $user_id}}) {
    affected_rows
  }
  delete_referral(where: {referred_by: {_eq: $user_id}}) {
    affected_rows
  }
  delete_system_message(where: {user_id: {_eq: $user_id}}) {
    affected_rows
  }
  delete_temporary_tier_bump(where: {user_id: {_eq: $user_id}}) {
    affected_rows
  }
  delete_user_club(where: {user_id: {_eq: $user_id}}) {
    affected_rows
  }
  delete_pegboard(where: {creator_id: {_eq: $user_id}}) {
    affected_rows
  }
}
`
const INSERT_RESET_PASSWORD_ATTEMPT = `
mutation insertResetPassword ($email: String!) {
  insert_reset_password_attempts_one(object: {email: $email}) {
    id
  }
}
`
const BULK_UPDATE_USERS = `
mutation bulkUpdateUsers($ids: [uuid!]!, $user: user_set_input!) {
  update_user(where: {id: {_in: $ids}}, _set: $user) {
    affected_rows
  }
}`

const BULK_UPDATE_USERS_BY_EMAIL = `
mutation bulkUpdateUsersByEmail($emails: [String!]!, $user: user_set_input!) {
  update_user(where: {email: {_in: $emails}}, _set: $user) {
    affected_rows
  }
}`

const UPDATE_USER_INFORMATION = `mutation updateUserInformation($userId: uuid!, $referral_count: Int!) {
  update_user_informations(where: {user_id: {_eq: $userId}}, _set: {referral_count: $referral_count}) {
    affected_rows
  }
}`

export {
  UPDATE_USER,
  ACTIVATE_USER,
  DEACTIVATE_USERS,
  INSERT_NETWORK_ADMIN_USER,
  UPDATE_USER_TOKENS,
  DELETE_USER,
  VERIFY_USER_BY_EMAIL,
  VERIFY_USER_BY_PHONE,
  UPDATE_ADDITIONAL_SETTINGS,
  FAVORITE_CLUB,
  UNFAVORITE_CLUB,
  DECLINE_USERS,
  RESET_PASSWORD_UPDATE_BY_EMAIL,
  DELETE_USER_DATA,
  INSERT_RESET_PASSWORD_ATTEMPT,
  BULK_UPDATE_USERS,
  BULK_UPDATE_USERS_BY_EMAIL,
  UPDATE_USER_INFORMATION,
  UPDATE_USER_CLUB_REVIEW_DISABLED
}
