console.log('Starting Next.js on port', process.env.PORT || 3000);

module.exports = {
  apps: [
    {
      name: 'TG-APP',
      script: 'node_modules/next/dist/bin/next',
      args: 'start',
      instances: process.env.WEB_CONCURRENCY || 'max', // Use Heroku's WEB_CONCURRENCY if available
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'production',
        NODE_OPTIONS: '-r newrelic',
        PORT: process.env.PORT || 3000,
      },
      // Heroku-specific optimizations
      max_memory_restart: '1G', // Restart if memory usage exceeds 1GB
      time: true, // Add timestamps to logs
      // Graceful shutdown
      kill_timeout: 5000,
      wait_ready: true,
      listen_timeout: 10000,
    },
  ],
};
