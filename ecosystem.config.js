console.log('Starting Next.js on port', process.env.PORT || 3000);

module.exports = {
  apps: [
    {
      name: 'TG-APP',
      script: 'node_modules/next/dist/bin/next',
      args: 'start',
      instances: 'max', // or 1 if you're debugging
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'production',
        NODE_OPTIONS: '-r newrelic',
        PORT: process.env.PORT, // optional: to ensure pm2 uses correct port
      },
    },
  ],
};
