{"FETCH_MY_CONTACTS": "/api/my-contacts/get-my-contacts", "FETCH_ALL_MY_CONTACTS": "/api/my-contacts/get-all-my-contacts", "ASSOCIATIONS": "/api/associations", "REGISTER_CLUBS": "/api/register/clubs", "CLUBS": "/api/clubs", "RESTRICT_CLUBS": "/api/favoriteClubs/restrict-clubs", "REQUEST_NEW_CLUB": "/api/users/search-club-for-requesting-addition", "MY_CONTACT_CLUB": "/api/my-contacts/search-club", "GET_MY_CLUB_MEMBERS": "/api/users/get-my-club-members", "SEARCH_CLUB": "/api/requests/search-club", "DELETE_MY_CONTACT": "/api/my-contacts/delete-my-contact", "EDIT_MY_CONTACT": "/api/my-contacts/edit-contact", "PN_GAMES": "/api/private_network/pn_games", "PN_USER_COUNT": "/api/private_network/userscount", "USERS": {"CHANGE_EMAIL": {"VERIFY_CODE": "/api/users/change-email/verify-email-code", "SEND_VERIFICATION_CODE": "/api/users/change-email/send-verification-code"}}, "TOKEN_ADJUSTMENT": {"TOKEN_ADJUSTMENT_REQUEST": "/api/users/club-token-adjustment-request"}, "HOST_INFO": "/api/requestsV2/gameUserInformation", "TOKEN_ADJUST_REQUEST_NOTIFICATION": "/api/notification/token-adjustment-request", "HANDLE_DECLINED_USER_CLUBS": "/api/admin/handle-declined-user", "REQUEST_MOBILE_OTP": "/api/signup-V2/send-phone-verification-code", "RESEND_REQUEST_MOBILE_OTP": "/api/signup-V2/resend-phone-verification-code", "VERIFY_OTP": "/api/signup-V2/create-account", "VALIDATE_USERNAME": "/api/auth/validateUsername", "REQUEST_MOBILE_OTP_PHONE_CHANGE": "/api/users/phone-change-otp", "VALIDATE_PHONE_NUMBER": "/api/auth/validatePhoneNumber", "ADD_USER_TO_TG": "/api/users/add-user-to-tg", "APP_STORE": "https://apps.apple.com/us/app/thousand-greens/id1536422684", "PLAY_STORE": "https://play.google.com/store/apps/details?id=com.thousandgreens", "CAN_DELETE_ACCOUNT": "/api/users/can-delete-account", "DELETE_USER_ACCOUNT": "/api/users/delete-user-account", "CHANGE_PASSWORD": "/api/admin/changePassword", "REGISTRATION_PROCESS": "/api/signup-V2/registration-process", "VERIFY_PRIVATE_NETWORK_CODE": "/api/private_network/verifyPrivateNetworkCode", "V2_MAP_CLUB_SEARCH": "/api/map/V2/club-search", "V3_MAP_CLUB_SEARCH": "/api/newclubs/searchclubs", "GET_EMAIL_BY_USERNAME": "/api/auth/getEmailByUsername", "RESET_PASSWORD_LINK": "/api/auth/password-reset-link", "TOTAL_CLUBS": "/api/clubs/get-total-clubs-count", "TOTAL_USERS": "/api/users/get-total-users-count", "STRIPE_CUSTOMER": "/api/membership/stripe-customer", "CHECK_DUE": "/api/membership/check-due", "INITIATE_PAYMENT": "/api/membership/initiate-payment", "COMPLETE_INTENT": "/api/membership/complete-intent", "FETCH_NGV_LOGS": "/api/membership/get-ngv-logs", "CREATE_STRIPE_CUSTOMER": "/api/membership/stripe-customer", "CHANGE_PAYMENT_METHOD_FROM_PROFILE": "/api/membership/create-intent-for-edit-payment", "UPDATE_PAYMENT_METHOD_FROM_PROFILE": "/api/membership/payment-method/update-payment", "CHECK_USERNAME_EXISTS": "/api/auth/validateUsername", "GET_MEMBERSHIP_PRICING_PLANS": "/api/membership/get-membership-pricing-plans", "UPDATE_SYSTEM_TG_CHAT_GROUP": "/api/chat/updateSystemTGChatGroup", "ADMIN_USER_LISTINGS": "/api/admin/user-listing", "ADMIN_USER_LISTING_COUNT": "/api/admin/user-listing-count", "GET_PAR_PRICING": "/api/membership/get-par-pricing", "LIFETIME_NGV": "/api/membership/get-lifetime-ngv", "MARK_INTENT_ATTEMPTED": "/api/membership/mark-intent-attempted", "PN_EXIST": "/api/private_network/exist", "FETCH_REQUESTS": "/api/admin/fetch_requests", "FETCH_GAMES": "/api/admin/games", "FETCH_PN_REQUESTS": "/api/network-admin/pn-user-games", "FETCH_PN_GAMES": "/api/private_network/pn-games", "FETCH_PN_USERS": "/api/private_network/users", "FETCH_PN_USERS_COUNT": "/api/private_network/userscount", "CHECK_PHONE_EXISTS": "/api/auth/validatePhoneNumber", "CHECK_EMAIL_EXISTS": "/api/auth/validateEmail", "FETCH_CLUBS_V2": "/api/newclubs/v2/fetch-clubs", "FETCH_CLUBS_V3": "/api/newclubs/v3/fetch-clubs", "FETCH_CLUB_DETAIL": "/api/newclubs/v3/fetch-club-detail", "ACCEPT_FRIEND_REQUEST": "/api/friends/requests/accept", "DECLINE_FRIEND_REQUEST": "/api/friends/requests/decline", "ADD_CLUB_REQUEST": "/api/users/add-club-request", "ADMIN_CHANGED_USER_CLUBS": "/api/notification/adminChangedUserClubs", "GET_EVENTS": "/api/event/get-event", "GET_EVENTS_V2": "/api/event/v2/get-events", "UPDATE_CLUB_LTV": "/api/clubs/update-club-ltv", "GET_OFFERS": "/api/offers/getOffersV2", "CREATE_REQUEST_TOKEN_CHECK": "/api/requestsV2/createRequestTokenCheck", "CAN_CREATE_OFFER": "/api/offers/canCreateOffer", "APPLY_PROMOCODE": "/api/membership/promocode/add-code", "REMOVE_PROMOCODE": "/api/membership/promocode/remove-code", "GET_PAYMENT_HISTORY": "/api/users/get-payment-history", "GET_PAYMENT_DETAILS": "/api/membership/get-payment-method-details", "FETCH_CREATED_FRIEND_REQUESTS": "/api/friends/requests/created", "FETCH_RECEIVED_FRIEND_REQUESTS": "/api/friends/requests/received", "FETCH_DECLINED_FRIEND_REQUESTS": "/api/friends/requests/declined", "SEARCH_USER": "/api/friends/search-user", "SEND_FRIEND_REQUEST": "/api/friends/requests/send", "FETCH_MUTUAL_FRIENDS": "/api/friends/mutual-friends", "REMOVE_DECLINED_FRIEND_REQUESTS": "/api/friends/requests/remove-declined", "MY_FRIENDS": "/api/friends/v2/my-friends", "UNFRIEND": "/api/friends/requests/unfriend", "EDIT_NOTES": "/api/friends/edit-notes", "WITHDRAW_REQUEST": "/api/friends/requests/withdraw", "GET_PROMOCODE": "/api/membership/promocode/get-code", "GET_STREAM_CHAT_TOKEN": "/api/chat-v2/get-token", "GET_MY_FRIENDS_FOR_CHAT": "/api/chat-v2/my-friends", "CREATE_ONE_TO_ONE_CHANNEL_STREAM": "/api/chat-v2/one-to-one-channel", "FORWARD_MESSAGE": "/api/chat-v2/forward-message", "CREATE_GROUP_CHAT": "/api/chat-v2/group/create", "EDIT_GROUP_CHAT": "/api/chat-v2/group/edit", "ADD_GROUP_ADMIN": "/api/chat-v2/group/add-admin", "BLOCK_USER": "/api/chat-v2/block-user", "BLOCK_LIST": "/api/chat-v2/block-list", "UNBLOCK_USER": "/api/chat-v2/unblock", "FRIENDS_FOR_GROUP_PARTICIPANTS": "/api/chat-v2/group/members", "ADD_NEW_GROUP_PARTICIPANTS": "/api/chat-v2/group/add-member", "REVOKE_GROUP_ADMIN_ACCESS": "/api/chat-v2/group/remove-admin", "FETCH_CLUB_FRIENDS": "/api/newclubs/v3/fetch-club-friends-detail", "FETCH_CLUB_FRIENDS_PLAYED": "/api/newclubs/v3/fetch-club-friends-played-detail", "REMOVE_GROUP_PARTICIPANT": "/api/chat-v2/group/remove-member", "GET_USER_PROFILE": "/api/users/v3/profile", "CAN_SEND_REQUEST": "/api/friends/can-send-request", "CHECK_VERIFICATION_CODE": "/api/auth/checkVerificationCode", "GET_NOTIFICATIONS": "/api/notification/allNotificationsV3", "CREATE_CHAT_GROUP": "/api/admin/chat-v2/create-group", "EDIT_CHAT_GROUP": "/api/admin/chat-v2/edit-group", "GET_ALL_CHAT_GROUP": "/api/admin/chat-v2/all-groups", "GET_GROUP_DETAILS": "/api/admin/chat-v2/group-info", "DELETE_ADMIN_CHAT_GROUP": "/api/admin/chat-v2/delete-group", "ADMIN_SEARCH_PARTICIPANTS": "/api/admin/chat-v2/search-user", "ADMIN_GROUP_CHAT_ADD_PARTICIPANTS": "/api/admin/chat-v2/add-member", "ADMIN_GROUP_CHAT_REMOVE_MEMBER": "/api/admin/chat-v2/remove-member", "BROADCAST_MESSAGE": "/api/chat-v2/broadcast-message", "CREATE_GROUP": "/api/chat-v2/my-tg-groups/create", "GET_GROUP_LIST": "/api/chat-v2/my-tg-groups/groups", "GET_MY_GROUP_DETAILS": "/api/chat-v2/my-tg-groups/details", "GET_MY_TG_MEMBERS": "/api/chat-v2/my-tg-groups/members", "JOIN_MY_TG_GROUP": "/api/chat-v2/my-tg-groups/join", "GET_MY_FRIENDS_ID": "/api/friends/my-friends-id", "GET_USER_FUTRUE_TEMP_TIER": "/api/admin/temp-tier/get-future-temp-tier", "ADD_TEMP_TIER": "/api/admin/temp-tier/add-temp-tier", "CALCULATE_AND_UPDATE_USER_TIER": "api/adjustments/updateUserTierV2", "ADMIN_SEARCH_HOSTS": "/api/admin/games/search-hosts", "EDIT_MY_TG_GROUP": "/api/chat-v2/my-tg-groups/edit", "DELETE_MY_TG_GROUP": "/api/chat-v2/my-tg-groups/delete-group", "FETCH_PENDING_GROUP_REQUESTS": "/api/chat-v2/my-tg-groups/requests", "SEND_REQUEST_TO_PRIVATE_GROUP": "/api/chat-v2/my-tg-groups/send-request", "ACCEPT_GROUP_REQUEST": "/api/chat-v2/my-tg-groups/accept-request", "DECLINE_GROUP_REQUEST": "/api/chat-v2/my-tg-groups/decline-request", "WITHDRAW_GROUP_REQUEST": "/api/chat-v2/my-tg-groups/withdraw-request", "CHECK_GROUP_CAPACITY": "/api/chat-v2/my-tg-groups/member-capacity-status", "FETCH_TAGS_SUGGESTIONS": "/api/chat-v2/my-tg-groups/tags-suggestions", "FETCH_GROUP_TAGS": "/api/chat-v2/my-tg-groups/fetch-tags", "UPDATE_CLUB_VISIBILITY_FOR_NON_FRIENDS": "/api/users/club/update-visibility-for-non-friends", "FETCH_FAVORITE_CLUBS": "/api/favoriteClubs/getUserFavoriteClubs", "MARK_CLUB_FAVOURITE_RESTRICTED": "/api/clubs/favourite-restricted", "UPDATE_CLUB_GENDER_PREFRENCE": "/api/clubs/update-gender-prefrence", "FETCH_OFFERS": "/api/offers/getOffersV5", "RECEIVED_OPEN": "/../../api/requestsV2/received/open", "RECEIVED_OPEN_V3": "/../../api/requestsV3/received/open", "RECEIVED_ACCEPTED": "../../api/requestsV2/received/accepted", "RECEIVED_ACCEPTED_V3": "../../api/requestsV3/received/accepted", "HISTORY_RECEIVED": "../../api/requestsV3/history/received", "HISTORY_RECEIVED_V4": "../../api/requestsV4/received/history", "REQUESTED_OPEN": "/../../api/requestsV4/requested/open", "REQUESTED_ACCEPTED": "../../api/requestsV4/requested/accepted", "REQUESTED_ACCEPTED_V3": "../../api/requestsV3/requested/accepted", "HISTORY_REQUESTED": "../../api/requestsV4/requested/history", "REQUESTED_REQUEST_HOSTS": "../../api/requestsV4/requested/hosts", "FETCH_REQUESTS_COUNT_FOR_ALL_TABS": "/api/requestsV2/fetch-tabs-count", "FETCH_UNREAD_MESSAGE_STATUS": "/api/requestsV2/unread-message-status", "GET_TIER_THRESHOLD": "/api/settings/getTierThreshold", "UPDATE_THRESHOLD_VALUES": "/api/settings/updateTierThreshold", "ADMIN_UPDATE_CLUB_DEMAND_TYPE": "/api/clubs/admin-update-club-demand-type", "UPDATE_BENEFIT_COUNT": "/api/benefits/update-benefit-count", "UPDATE_CLUB_DEMAND_TYPE": "/api/clubs/update-club-demand-type", "UPDATE_VISIBILITY_SETTINGS": "/api/auth/v2/visibility-settings", "UPDATE_VISIBILITY_SETTINGS_V1": "/api/auth/visibility-settings", "ADMIN_DELETE_CLUB": "/api/admin/delete-club", "UPDATE_VISIBLE_TO_OTHERS_TOGGLE": "/api/clubs/update-visible-to-others", "V2_GET_MY_CLUB_MEMBERS": "/api/users/v2/get-my-club-members", "ADMIN_COMPLETE_GAME": "/api/admin/games/complete-game", "CREATE_REQUEST": "../../api/requests/actions/v2/create", "ACCEPT_REQUEST": "../../api/requests/actions/accept", "EDIT_REQUEST": "../../api/requests/actions/edit", "UPDATE_PLAYERS": "../../api/requests/actions/update-players", "DECLINE_REQUEST": "../../api/requests/actions/decline", "CANCEL_REQUEST": "../../api/requests/actions/cancel", "EDIT_GAME": "../../api/requests/actions/edit-game", "COMPLETE_GAME": "../../api/requests/actions/v2/mark-complete", "DELETE_GAME": "../../api/requests/actions/delete", "VISIBILITY_SETTINGS": "/api/auth/visibility-settings", "MAP_MY_FRIENDS": "/api/friends/map-my-friends", "FETCH_TG_GROUP_MEMBER_LIST": "/api/map/tgGroupMembersList", "CREATE_REQUEST_AGAINST_OFFER": "/api/requests/actions/request-against-offer", "GET_MY_CLUBS_VISIBILITY_SETTINGS": "/api/chat-v2/my-tg-groups/get-club-setting", "UPDATE_MASTER_CLUB_SETTING": "/api/chat-v2/my-tg-groups/master-club-setting", "UPDATE_GROUP_CLUB_SETTING": "/api/chat-v2/my-tg-groups/group-club-setting", "MUTE_UNMUTE_CLUB": "../../api/clubs/muteClubs", "CREATE_OFFER": "/api/offers/create", "CREATE_OFFER_V2": "/api/offers/v2/create", "EDIT_OFFER": "/api/offers/edit", "EDIT_OFFER_V2": "/api/offers/v2/edit", "DELETE_OFFER": "/api/offers/delete", "CHECK_CREATE_REQUEST_TOKEN": "../../api/requestsV2/createRequestTokenCheck", "FETCH_LAST_REQUEST_NOTE": "/api/requests/get-last-note", "MAP_GET_FRIENDS_ID": "/api/friends/map-friends-id-send-message", "DOWNLOAD_CSV": "/api/friends/download-csv", "GET_MY_TG_GROUP_OFFER_COUNT": "/api/offers/my-tg-group/get-offers-count", "GET_MY_TG_GROUP_EVENT_COUNT": "/api/event/my-tg-group/get-event-count", "CREATE_EVENT": "/api/event/v4/create", "GET_MY_TG_GROUP_OFFERS": "api/offers/my-tg-group/get-offers", "GET_MY_TG_GROUP_EVENTS": "api/event/my-tg-group/get-events", "GET_GROUP_MEMBER_IDS": "/api/map/group-members-id-send-message", "BROADCAST_MESSAGE_TO_TG_MEMBERS": "/api/chat-v2/broadcast-message/tg-group", "GET_EVENTS_V4": "/api/event/v4/get-events", "GET_MY_TG_GROUP_CLUB_LIST": "/api/chat-v2/my-tg-groups/members", "EDIT_EVENT": "/api/event/edit-event", "EDIT_EVENT_V4": "/api/event/v4/edit-event", "DELETE_EVENT": "/api/event/delete-event", "ADMIN_CREATE_EVENT": "/api/admin/event/create-event", "ADMIN_GET_GROUPS_FOR_PEGBOARDS": "/api/admin/chat-v2/my-tg-group/get-group", "ADMIN_ADD_PEGBOARD": "/api/admin/pegboards/add-pegboard", "ADMIN_GET_PEGBOARD": "/api/admin/pegboards/get-pegboard", "USER_GET_PEGBOARDS": "/api/pegboards/user/get", "ADMIN_DELETE_PEGBOARD": "/api/admin/pegboards/delete-pegboard", "ADD_ADMIN_PEGBOARD_CLUB": "/api/admin/pegboards/add-club", "ADMIN_PEGBOARD_DETAILS": "/api/admin/pegboards/pegboard-detail", "ADMIN_DELETE_PEGBOARD_CLUB": "/api/admin/pegboards/delete-club", "ADMIN_EDIT_PEGBOARD": "/api/admin/pegboards/edit-pegboard", "ADMIN_EDIT_EVENT": "/api/admin/event/edit-event", "ADMIN_UPDATE_PEGBOARD_RANK": "/api/admin/pegboards/update-pegboard-rank", "DECLINE_REQUEST_NOTIFICATION": "/api/sign-in-with-token/request", "CHECK_TOKEN_AUTHORIZED": "/api/sign-in-with-token/check-token-authorized", "GET_GROUPS_FOR_PEGBOARD": "/api/pegboards/user/my-tg-groups/get", "GET_PEGBOARD_DETAILS": "/api/pegboards/user/pegboard-details", "GET_PEGBOARD_DETAILS_V3": "/api/pegboards/user/v3/pegboard-details", "CREATE_PEGBOARD": "/api/pegboards/user/create", "GET_PEGBOARDS": "/api/pegboards/user/v2/get", "ADMIN_ALL_TYPES_PEGBOARDS_COUNT": "/api/admin/pegboards/get-pegboards-count", "ADMIN_GET_USER_CREATED_PEGBOARDS": "/api/admin/pegboards/get-user-pegboards", "ADMIN_PEGBOARD_LEADERBOARD": "/api/pegboards/user/admin-leaderboard", "USER_PEGBOARD_LEADERBOARD": "/api/pegboards/user/leaderboard", "PEGBAORD_PLAYED_CLUBS_OF_USER": "/api/pegboards/user/played-clubs-of-user", "ADD_NEW_PEGBOARD_CLUB": "/api/clubs/add-pegboard-club", "ADMIN_PEGBAORD_PLAYED_CLUBS_OF_USER": "/api/admin/pegboards/played-clubs", "ADMIN_API_FOR_ADMIN_PEGBOARD_LEADERBOARD": "/api/admin/pegboards/leaderboards", "DELETE_PEGBOARD": "/api/pegboards/user/delete", "DELETE_PEGBOARD_CLUB": "/api/pegboards/user/delete-club", "ADD_PEGBOARD_CLUB": "/api/pegboards/user/add-club", "ADMIN_UPDATE_PEGBOARD_CLUB_RANK": "/api/admin/pegboards/update-club-rank", "EDIT_PEGBOARD": "/api/pegboards/user/edit", "FETCH_PLAYED_CLUBS": "/api/pegboards/pegboard-details", "USER_UPDATE_PEGBOARD_CLUB_RANK": "/api/pegboards/user/update-club-rank", "FETCH_FRIENDS_PLAYED_IN_CLUB": "/api/newclubs/v3/fetch-club-friends-played-detail", "ADMIN_ACTIVATE_USER": "/api/admin/activate-user", "CHECK_OPEN_REQUEST_POPUP": "/api/requestsV2/check-request-popup", "UPDATE_REQUEST_POPUP_STATUS": "/api/requestsV2/update-popup-status", "FETCH_CLUBS_FOR_PEGBOARD": "/api/clubs/search-clubs-for-pegboard", "GET_RECEIVED_REQUEST_INFO": "/api/requests/get-request-info", "GET_REQUESTED_REQUEST_INFO": "/api/requests/requested/get-request-info", "GET_GROUPS_WITH_MOST_MEMBERS": "/api/chat-v2/my-tg-groups/suggestions/most-members", "GET_GROUPS_WITH_MOST_FRIENDS": "/api/chat-v2/my-tg-groups/suggestions/most-friends", "GET_GROUPS_WITH_MOST_NEW_MEMBERS": "/api/chat-v2/my-tg-groups/suggestions/most-new-members", "GET_GROUPS_WITH_MOST_MUTUAL_MEMBERS": "/api/chat-v2/my-tg-groups/suggestions/most-mutual-members", "GET_NEWEST_GROUPS": "/api/chat-v2/my-tg-groups/suggestions/new-groups", "GET_CLUBS_VISIBLE_TO_GROUP_MEMBERS": "/api/chat-v2/my-tg-groups/club-visibility/groups-by-club", "UPDATE_CLUBS_VISIBILITY_TO_GROUP_MEMBERS": "/api/chat-v2/my-tg-groups/club-visibility/update-visibility", "SEARCH_CLUBS": "/api/favoriteClubs/V2/search-clubs", "USER_FAV_CLUBS": "/api/favoriteClubs/getUserFavoriteClubs", "MARK_FAV_CLUBS": "/api/clubs/update-favourite-club", "GET_FAV_CLUBS": "/api/favoriteClubs/V2/get-fav-clubs", "UPDATE_STATUS_EVENTS": "/api/event/v4/update-event-status", "UPDATE_AMBASSADOR_VISIBILITY": "/api/settings/update-ambassador-visibility", "SAVE_USER_ADDRESS": "../../api/users/user-save-address", "GET_RECOMMENDED_CLUBS": "/api/requests/recommended-clubs", "GET_MEMBERSHIP_CATEGORY": "/api/membershipCategory/get-membership-categories", "YEARLY_VALIDATE_CLUB": "/api/users/club/update-club", "GET_REFERRALS": "/api/referral/get", "REFER_A_FRIEND": "/api/referral/v1/add", "CREATE_REQUEST_CHAT_CHANNEL": "/api/requestChat/create-request-chat-channel", "SEND_CHAT_NOTIFICATION": "/api/notification/newChatNotification", "GET_ALL_HOSTS": "/api/requestsV4/requested/get-all-hosts", "SEND_UPDATE_CLUB_NOTIFICATION": "/api/clubs/send-update-notiification", "UPDATE_VISIBILITY_OF_CLUBS": "/api/clubs/club-visible-to-other", "GET_AMBASSADOR_MESSAGE": "/api/users/get-ambassador-message", "UPDATE_AMBASSADOR_MESSAGE": "/api/users/update-ambassador-message", "MARK_READ": "/api/notification/mark-all-read", "GET_TG_DATA": "/api/helpers/get-tg-data", "MARK_TUTORIAL_VIEWED": "/api/users/mark-tutorial-viewed", "MARK_WEB_TUTORIAL_VIEWED": "/api/users/mark-web-tutorial-viewed", "SEND_END_POLL_PUSH_NOTIFICATION": "/api/chat-v2/group/end-poll-push-notification", "SEND_NOTIFICATION": "/api/admin/send-notification", "TEAL_DOT_STATUS": "/api/users/teal-dot-status", "UPDATE_TEAL_DOT_STATUS": "/api/users/update-teal-dot-status", "SEARCH_GAME_USER": "/api/users/search-user", "LOG_OFFLINE_GAME": "/api/game/log-offline-game", "DELETE_NOTIFICATION_FROM_WEB_PANEL": "/api/notification/delete-from-web-panel", "ALL_HISTORY": "/api/requestsV4/all-history", "GET_GAME_REVIEWS_FOR_CLUB": "/api/game/game-review-by-club", "GET_OFFERS_IN_CLUB": "/api/offers/offers-in-club", "FETCH_CLUBS_WITH_OFFERS": "/api/newclubs/v3/fetch-clubs-with-offers", "GET_OFFER_DETAILS": "/api/offers/v2/detail", "TOGGLE_PEGBOARD_VISIBILITY": "/api/pegboards/toggle-visibility", "GET_GAME_REVIEWS_FOR_USER": "/api/game/game-review-by-user", "ADMIN_BROADCAST_EMAIL": "/api/admin/broadcast-email", "ADMIN_TOKEN_CHECK": "/api/admin/admin-token-check", "ADMIN_VERIFY_OTP": "/api/admin/verify-otp", "ADMIN_UPDATE_PLAYERS": "/api/admin/requests/update-players"}