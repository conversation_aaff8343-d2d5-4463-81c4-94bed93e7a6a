const EMAILS = {
  complete_profile_notification: {
    template: 'Remind Users to Complete Profile V3',
    reminder: {
      subject: 'Thousand Greens: Reminder to Complete Profile',
    },
    activation: {
      subject: 'Thousand Greens: Please Complete your Profile',
    },
  },
  remind_hosts_with_open_chat: {
    template: 'Remind Hosts with Open Chat',
    subject: function (game_id) {
      return `Thousand Greens: Action Needed on Request #${game_id}`;
    },
    message: function (game_id) {
      return `You've an open chat on request #${game_id}. Please mark this request either as Declined or Accepted.`;
    },
    text_message: function (game_id, shortLink) {
      return `${this.message(game_id)} To see the request, please click here ${shortLink}`;
    },
  },
  events: {
    admin: {
      subject: 'New Event Request by Member',
      template: 'New Event Request - Admin',
    },
    others: {
      subject: 'Thousand Greens Notification: A new event added',
      template: 'New Event V2',
    },
    creator: {
      message: function (title) {
        return `Your event ${title} has been approved`;
      },
      template: 'Event Approved by Admin',
      subject: 'Event Approved by <PERSON><PERSON>',
    },
    declined: {
      creator: {
        message: function (title) {
          return `Your event ${title} has been declined`;
        },
        template: 'Event Rejected by Admin',
        subject: 'Event Declined by Admin',
      },
    },
  },
  twilio_callback: {
    subject:
      'Thousand Greens: Message Delivery failed | Phone number verification required',
    template: 'Twilio Message Failed',
  },
  token_adjustment_request: {
    accepted: {
      type: 'my-thousand-greens',
      subject: 'Thousand Greens - Token Adjusted',
      message: function (club_name) {
        return `Your request for the token adjustment for the ${club_name} have been actioned by admin. `;
      },
      template: 'Token Adjustment Request Accepted',
      text_message: function (club_name, shortLink) {
        return `Your request for the token adjustment for the ${club_name} have been actioned by admin. Please review the same in your profile here: ${shortLink}`;
      },
    },
    declined: {
      type: 'my-thousand-greens',
      subject: 'Thousand Greens | Token Adjustment Request Declined',
      template: 'Token Adjustment Request Declined',
      message: function (club_name, reason) {
        return `Your request for the token adjustment for the ${club_name} have been decline by admin. The reason for declination: ${reason}`;
      },
      text_message: function (club_name, reason) {
        return `Your request for the token adjustment for the ${club_name} have been decline by admin. The reason for declination: ${reason}`;
      },
    },
  },
  LEGACY_USERS: {
    REMINDER_1: {
      subject: 'Action Required: Thousand Greens Account Information Update',
      template: 'Legacy User Reminder 1',
      dynamic_week_message:
        'Please note that if no action is taken in the next two weeks, your user record will be deleted from the system.',
    },
    REMINDER_2: {
      subject:
        'Last Reminder: Action Required: Thousand Greens Account Information Update',
      template: 'Legacy User Reminder 1',
      dynamic_week_message:
        'Please note that this is the last reminder, and if no action is taken in the next one week, your user record will be deleted from the system.',
    },
    DELETION: {
      subject: 'Thousand Greens - Account Deleted',
      template: 'User Deletion - Legacy User',
    },
  },
  DELETION: {
    PN_REMOVAL: {
      subject: 'Thousand Greens Network Update: Account Deletion',
      template: 'User Deletion - Post PN Removal',
    },
    ACCOUNT_BY_SELF_OR_ADMIN: {
      subject: "Golfer's account deleted",
      template: 'User Account Deleted - to admin',
    },
    ACCOUNT_BY_SELF_OR_ADMIN_TO_SELF: {
      subject: 'Thousand Greens - Account Deleted',
      template: 'User Account Deletion - To Self',
    },
  },
  PASSWORD_RESET_LINK: {
    subject: 'Reset your password for The Thousand Greens',
    template: 'Reset Password Link',
    text_message: function (link) {
      return `Your requested reset password link in the Thousand Greens is: ${link}.`;
    },
  },
  FOUNDING_MEMBERS_ELIGIBLE_USERS: {
    subject:
      'Exclusive Invitation for a select few to become a Founder Member at Thousand Greens',
    template: 'Founding Members Eligible Users',
  },
  MEMBERSHIP_BILLING_FAILED: {
    subject: 'Thousand Greens - Billing Issue',
    template: 'Membership Billing Issue',
    type: 'membership',
    text_message: (link) =>
      `Payment failed for your Thousand Greens membership renewal. Update your payment details here ${link} to avoid deletion of your account.`,
    message: (link) =>
      `Payment failed for your Thousand Greens membership renewal. Update your payment details here ${link} to avoid deletion of your account.`,
  },
  CARD_ONE_MONTH_EXPIRY: {
    type: 'membership',
    subject: 'Card Expiry Reminder',
    template: 'Membership - One Month - Card Expiry V3',
    message:
      'Thousand Greens - Your credit card on file is about to expire. To avoid losing access to your account, please update your payment method from here or by logging into your account on a web browser.',
    text_message:
      'Thousand Greens - Your credit card on file is about to expire. To avoid losing access to your account, please update your payment method from here or by logging into your account on a web browser.',
  },
  USER_PRE_APPROVAL: {
    NON_US: {
      subject: 'Thousand Greens -  Registration under review',
      template: 'Membership - NON US Welcome Email (pre-approval)',
    },
    US: {
      subject: 'Thousand Greens -  Registration under review',
      template: 'Membership - US Only Welcome Email (pre-approval)',
    },
    GENERIC: {
      subject: 'Thousand Greens -  Registration under review',
      template: 'Membership Welcome Email V2',
    },
  },
  MEMBERSHIP_ACCOUNT_ACTIVATION: {
    type: 'account-activated',
    template: 'Membership - Account Activation V1',
    subject: function (tier) {
      return `You have been registered as a ${tier} Tier member in Thousand Greens`;
    },
    text_message: function (tier) {
      return `You have been registered as a ${tier} Tier member in Thousand Greens`;
    },
    message: function (tier) {
      return `You have been registered as a ${tier} Tier member`;
    },
  },
  NGV_ACCOUNT_ACTIVATION: {
    type: 'account-activated',
    template: 'NGV - Account Activation',
    subject: function (tier) {
      return `You have been registered as a ${tier} Tier member in Thousand Greens`;
    },
    text_message: function (tier) {
      return `You have been registered as a ${tier} Tier member in Thousand Greens`;
    },
    message: function (tier) {
      return `You have been registered as a ${tier} Tier member`;
    },
  },
  WELCOME_NON_FCM_USERS: {
    template: 'Welcome Non Founding Club Member',
    subject: 'Thousand Greens - Paid Membership Live Now',
  },
  GAME_DECLINED_BY_ADMIN: {
    template: 'Game Declined By Admin',
    subject: 'Thousand Greens - Game declined by Admin',
  },
  PAYMENT_METHOD_UPDATED: {
    template: 'Payment Method Added or Updated',
    subject: function (action) {
      return `Thousand Greens - Payment method ${action}`;
    },
    type: 'membership',
    message:
      'Your payment method has been successfully updated for your Thousand Greens account. It will be used for future renewals. Keep it current to avoid disruptions.',
    text_message:
      'Your payment method has been successfully updated for your Thousand Greens account. It will be used for future renewals. Keep it current to avoid disruptions.',
  },
  UPDATED_INTENT_SUCCESSFUL: {
    type: 'membership',
    template: 'Payment Successful with Promocode',
    subject: 'Thousand Greens - Payment Successful',
    message: (date) =>
      `Membership Renewal Confirmed! Thank you for being part of the Thousand Greens Network. Your membership is now active. Next renewal: ${date}`,
    text_message: (date) =>
      `Membership Renewal Confirmed! Thank you for being part of the Thousand Greens Network. Your membership is now active. Next renewal: ${date}`,
  },
  MEMBERSHIP_ACTIVATED_NON_CARD: {
    template: 'Membership Assigned with Promocode',
    subject: 'Thousand Greens - Membership Assigned',
    type: 'membership',
    text_message: (date) =>
      `Membership Renewal Confirmed! Thank you for being part of the Thousand Greens Network. Your membership is now active. Next renewal: ${date} `,
    message: (date) =>
      `Membership Renewal Confirmed! Thank you for being part of the Thousand Greens Network. Your membership is now active. Next renewal: ${date} `,
  },
  INCOMPLETE_GAME_REMINDER: {
    type: 'game',
    template: 'Incomplete game reminder',
    subject: 'Thousand Greens - Update Game status',
    message: function (game_id, club_name) {
      return `Reminder: Please update the status of the Game ${game_id} made for ${club_name}.`;
    },
  },
  UPDATE_REQUEST_STATUS_REMINDER: {
    type: 'game',
    template: 'Update Request status reminder',
    subject: 'Thousand Greens - Update Request status',
    message: function (game_id, club_name) {
      return `Reminder: Please update the status of the request ${game_id} made for ${club_name}.`;
    },
  },
  GAME_DATE_UPDATED: {
    type: 'host-request',
    message: function (game_id, club_name) {
      return `The game date for game #${game_id} at ${club_name} has been changed by the host.`;
    },
    html_message: function (game_id, club_name) {
      return `The game date for game #${game_id} at ${club_name} has been changed by the host.`;
    },
    text_message: function (game_id, club_name, shortLink) {
      return `The host has edited the Game date for the game #${game_id} at ${club_name}. Please review the same in Request Tab`;
    },
    email_template_name: 'Game Edited by Host',
    subject: function (game_id) {
      return `Thousand Greens - Updates in Game #${game_id}.`;
    },
  },
  CLUB_ADD_REQUEST: {
    template: 'User Club Request',
    subject: "TG member's request to Add another Golf Club",
  },
  CLUB_REPLACE_REQUEST: {
    template: 'User Club Replace Request V3',
    subject: 'Thousand Greens - Club replacement request',
  },
  CLUB_REPLACED_SUCCESSFULLY: {
    template: 'Club replaced successfully',
    subject: 'Thousand Greens - Club replaced successfully',
  },
  CHANGES_IN_TIER_IMPLEMENTATION: {
    template: 'Changes in Tier implementation',
    subject:
      'Thousand Greens: Changes in Tier implementation affecting members belonging to multiple clubs',
  },
  SEND_REQUEST: {
    HOSTS: {
      template: 'New Request Received V7',
      subject: (clubName) => `New request was created for ${clubName}`,
      message: ({ gameId, clubName }) =>
        `New request (#${gameId}) was created for ${clubName}`,
      html_message: ({ gameId, clubName }) =>
        `New request (#${gameId}) was created for <b>${clubName}</b>`,
      text_message: ({
        gameId,
        clubs,
        tier,
        clubName,
        shortLinkMessage = '',
      }) => {
        return `New Request #${gameId} - A member from ${clubs} in the ${tier} tier has made a request to play at ${clubName}. ${shortLinkMessage}`;
      },
    },
  },
  GAME_COMPLETION_REMINDER: {
    template: 'Game Completion Reminder V3',
    subject: 'Game Completion Reminder',
    message: (gameId) =>
      `Please mark the game related to request #${gameId} as completed`,
    text_message: ({ gameId, shortLinkMessage }) =>
      `Please mark the game related to request #${gameId} as completed. ${shortLinkMessage}`,
  },
  PAYMENT_FAILURE_SECOND_REMINDER: {
    subject: 'Thousand Greens - Reminder to Update Payment details',
    template: 'Second Reminder to Update Payment details',
    type: 'membership',
    message: `Update Payment Method| Your membership renewal is still pending due to a payment failure. Update your payment details now to avoid account deletion`,
    text_message: (link) =>
      `Update Payment Method| Your membership renewal is still pending due to a payment failure. Update your payment details now to avoid account deletion - ${link}`,
  },
  PAYMENT_FAILURE_THIRD_REMINDER: {
    subject: 'Thousand Greens - Last Reminder to Update Payment details',
    template: 'Last Reminder to Update Payment details',
    type: 'membership',
    message: `Update Payment Method| Your membership renewal is still pending due to a payment failure. Update your payment details now to avoid losing access`,
    text_message: (link) =>
      `Update Payment Method| Your membership renewal is still pending due to a payment failure. Update your payment details now to avoid losing access - ${link}`,
  },
  INACTIVE_ACCOUNT_DELETION: {
    type: 'membership',
    subject: 'Thousand Greens - Account Deleted',
    template: 'Account Deletion due to Payment Failure V2',
    text_message:
      "Your Thousand Greens account has been deleted due to non payment. You're welcome to re-register anytime if you wish to rejoin",
  },
  MEMBERSHIP_RENEWAL_REMINDER_BEFORE_EXPIRY: {
    type: 'membership',
    template: 'Reminder for Membership renewal V3',
    text_message: (date) =>
      `Membership renewal reminder - Your Thousand Greens' Membership is scheduled for automatic renewal on the ${date}`,
    message: (date) =>
      `Membership renewal reminder - Your Thousand Greens' Membership is scheduled for automatic renewal on the ${date}`,
    subject: 'Thousand Greens: Membership renewal in 2 weeks',
    template: 'Reminder for Membership renewal V3',
  },
  MEMBERSHIP_RENEWAL_REMINDER_FOR_FREE_AND_SUPERHOST: {
    subject: 'Thousand Greens: Action needed to keep membership active',
    template: 'Activate Membership Renewal - Superhost/Free V2',
  },
  CHAT: {
    MY_TG_GROUPS: {
      ACCEPTED_INVITATION: {
        PUSH: (groupName) =>
          `Group Admin has accepted your request to join the group ${groupName}`,
      },
      DECLINE_INVITATION: {
        PUSH: (groupName, reason) =>
          `Group Admin has declined your request to join the group ${groupName}. ${reason ? `Reason: ${reason}` : ''}`,
      },
      DELETE_GROUP: {
        PUSH: (groupName) => `Group Creator has closed the group ${groupName}`,
      },
    },
  },
  CLUB_DEMAND_TYPE_UPDATE: {
    POPULAR_CLUB: {
      type: 'faq',
      message: function (clubName) {
        return `Your club ${clubName} has been designated as a Highly Requested club, click to know what it means!`;
      },
      text_message: function (clubName) {
        return `Your club ${clubName} has been designated as a Highly Requested club, click to know what it means!`;
      },
      email_template_name: 'Highly Requested club - Update in your Club V2',
      subject: `Thousand Greens - Update in your Club Designation`,
    },
    UNEXPLORED_CLUB: {
      type: 'faq',
      message: function (clubName) {
        return `Your club ${clubName} has been designated as an Unexplored club, click to know what it means!`;
      },
      text_message: function (clubName) {
        return `Your club ${clubName} has been designated as an Unexplored club, click to know what it means!`;
      },
      email_template_name: 'Unexplored club - Update in your Club settings V2',
      subject: `Thousand Greens - Update in your Club settings`,
    },
    NEUTRAL_CLUB: {
      type: 'faq',
      message: function (clubName) {
        return `Your club ${clubName} is no more a Highly Requested club, click to know what it means!`;
      },
      text_message: function (clubName) {
        return `Your club ${clubName} is no more an Highly Requested club, click to know what it means!`;
      },
      email_template_name: 'Neutral club - Update in your Club settings V4',
      subject: `Thousand Greens - Club Transitioned to Neutral Status`,
    },
  },
  GAME_COMPLETED: {
    REQUESTER: {
      FRIENDS_PUSH: {
        message: function (name, groupName) {
          return `Your Friend ${name} Just played at the ${groupName}.`;
        },
        html_message: function (name, groupName) {
          return `Your Friend ${name} Just played the ${groupName}.`;
        },
        type: 'friend-completed-game',
      },
    },
  },
  MY_TG_GROUP_JOINED_BY_FRIEND: {
    PUSH: {
      message: function (name, groupName) {
        return `Your Friend ${name} Just Joined a New TG Group ${groupName}.`;
      },
      html_message: function (name, groupName) {
        return `Your Friend <b>${name}</b> Just Joined a New TG Group <b>${groupName}</b>.`;
      },
      type: 'friend-joined-my-tg-group',
    },
  },
  REQUEST_AGAINST_OFFER: {
    PUSH: {
      message: function (gameId, clubName) {
        return `New request ${gameId} was created for ${clubName} against your offer.`;
      },
      html_message: function (gameId, clubName) {
        return `New request ${gameId} was created against your offer for ${clubName}`;
      },
      type: 'request-against-offer',
    },
  },
  REQUEST_DECLINED_BY_HOST: {
    PUSH: {
      message: function (gameId) {
        return `Your Accepted Request #${gameId} has been declined by the host`;
      },
      html_message: function (gameId) {
        return `Your Accepted Request #${gameId} has been declined by the host`;
      },
      type: 'request-declined-by-host',
    },
  },
  MUTUAL_FRIENDS_CONNECTED: {
    PUSH: {
      message: function (name1, name2) {
        return `Your Friends ${name1} and ${name2} just got connected to each other.`;
      },
      html_message: function (name1, name2) {
        return `Your Friends <b>${name1}</b> and <b>${name2}</b> just got connected to each other.`;
      },
      type: 'mutual-friends-connected',
    },
  },
  TG_AMBASSADOR_FRIEND_CONNECTED: {
    PUSH: {
      message:
        'You’ve been assigned to a new member as their TG Ambassador. A welcome message has been sent to them on your behalf. Tap to view the chat and continue the conversation',
      html_message: `<b>You’ve been assigned to a new member as their TG Ambassador.</b> A welcome message has been sent to them on your behalf. Tap to view the chat and continue the conversation</b>`,
      type: 'tga-friend-connected',
    },
  },
  MAKE_TG_AMBASSADOR: {
    subject: 'Congratulations on Becoming a TG Ambassador!',
    template: 'When user change in TG Ambassador',
  },
  REFERRAL_JOINED: {
    type: 'referral-joined',
    message: (firstName, lastName) => {
      return `${firstName} ${lastName} has joined Thousand Greens using your referral.`;
    },
    html_message: (firstName, lastName) => {
      return `<b>${firstName} ${lastName}</b> has joined Thousand Greens using your referral.`;
    },
    text_message: (firstName, lastName) => {
      return `${firstName} ${lastName} has joined Thousand Greens using your referral`;
    },
  },
  CLUB_DETAILS_UPDATE: {
    type: 'club-update',
    message: (clubName) => {
      return `Club Update: A member has updated the details of ${clubName}. Please review the club details section for the latest information.`;
    },
  },
  NGV_UPDATE: {
    type: 'ngv-update',
    subject: 'Thousand Greens - Your NGV has been Updated',
    template: 'NGV Update',
    message:
      'Your NGV (Net Game Value) has been updated by the administrator. You can view the latest value in your profile.',
    text_message: (shortLinkMessage) =>
      `Your NGV (Net Game Value) has been updated by Thousand Greens administrator. ${shortLinkMessage}`,
  },
  CLUB_UNMUTED: {
    type: 'club-unmuted',
    subject: (clubName) => {
      return `${clubName} unmuted`;
    },
    template: 'Club unmuted notification',
    message: (clubName) => {
      return `Your club ${clubName} is no longer muted. If you would like to continue being muted, please log in and edit your Club Settings.`;
    },
    text_message: (clubName) => {
      return `Your club ${clubName} is no longer muted. If you would like to continue being muted, please log in and edit your Club Settings.`;
    },
  },
  OFFLINE_GAME_LOGGED: {
    type: 'offline-game-logged',
    template: 'Offline Game Logged',
    subject: (clubName) =>
      `Thousand Greens - A Game has been recorded for ${clubName}`,
    message: (name, clubName) =>
      `${name} has successfully recorded an offline game for ${clubName}, and your NGV is updated according to this action.`,
  },
  FEMALE_MEMBER_JOINED: {
    type: 'female-member-joined',
    message: (clubName) => {
      return `A Female member from ${clubName} has just joined the network.`;
    },
  },
  COUPLE_MEMBER_JOINED: {
    type: 'couple-joined',
    message: (clubName) => {
      return `A member who "Plays as a Couple" from ${clubName} has just joined the network.`;
    },
  },
  ADMIN_UPDATED_GAME_STATUS: {
    type: 'admin-updated-game-status',
    subject: 'Thousand Greens - Admin has Reverted your completed game',
    template: 'Admin updated game status',
    message: (game_id, club_name) => {
      return `Status of your completed game #${game_id} for ${club_name} has been updated by Admin`;
    },
    text_message: (game_id, club_name) => {
      return `Thousand Greens - Status of your completed game #${game_id} for ${club_name} has been updated by Admin`;
    },
  },
  MULTIPLE_GAME_CANCELLATIONS: {
    type: 'multiple-game-cancellations',
    subject: 'Multiple Request Cancelled Post Acceptance',
    template: 'Multiple Game Cancellations',
    message: (userName) => {
      return `User ${userName} has cancelled multiple arranged games post acceptance`;
    },
    text_message: (userName) => {
      return `User ${userName} has cancelled multiple arranged games post acceptance`;
    },
  },
  ADMIN_2FA: {
    template: 'Admin 2FA OTP',
    subject: 'Thousand Greens Admin - 2FA Code',
  },
};

export default EMAILS;
