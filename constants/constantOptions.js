const constantOptions = {
  ADMIN_ID: '10c0a827-2985-4a6e-8b97-aa4422f78879',
  PROXIMITY_OPTIONS: ['local', 'seasonal', 'long-distance'],
  PROXIMITY: {
    LOCAL: 'local',
    SEASONAL: 'seasonal',
    LONG_DISTANCE: 'long-distance',
  },
  PAYMENTOPTIONS: {
    OTHER: 'other',
    CREDIT_CARD: 'pay by credit card at pro shop',
    CASH_AND_CHEQUE: 'cash & cheque',
    NO_PAYMENT_EXPECTED: 'no payment expected',
    PAY_BY_CREDIT_CARD_PRO_SHOP: 'pay by credit card at pro shop',
    DIGITAL: 'digital',
  },
  REQUEST_CANCELLED_BY: {
    REQUESTER: 'requester',
    SYSTEM: 'system',
  },
  CLUB_TYPES: {
    PRIVATE: 0,
    PUBLIC: 1,
    VIRTUAL: 2,
  },
  CLUB_TIERS: {
    FERN: 1,
    SAGE: 2,
    MOSS: 3,
    OLIVE: 5,
    TEAL: 11,
  },
  TG_PUBLIC_CHAT: 'public_thousand_greens',
  VERIFICATION_METHODS: {
    TEXT: 'text message',
    EMAIL: 'email',
  },
  NOTIFICATION_SETTINGS: {
    PUSH: 'push',
    EMAIL: 'email',
    TEXT: 'text',
  },
  GENDER: {
    MALE: 'male',
    FEMALE: 'female',
    BOTH: 'both',
  },
  GOLF_INDEX: ['< 5', '5-10', '> 10'],
  PACE: {
    FAST: 'fast',
    AVERAGE: 'average',
    LEISURE: 'leisure',
  },
  ENGLISH_FLUENCY: {
    NATIVE: 'native',
    FLUENT: 'fluent',
    UNDERSTANDABLE: 'understandable',
    BASIC: 'basic',
  },
  SIGNUP_STEPS: {
    CREATE_ACCOUNT: 1,
    PERSONAL_PROFILE: 2,
    GOLFER_PROFILE: 3,
    ADD_CLUB: 4,
    PROFILE_OVERVIEW: 5,
    PROFILE_COMPLETE: 6,
    POST_PROFILE_COMPLETION: 7,
    PROCEED_FOR_MEMBERSHIP: 8, // because after the profile is completed we mark the user's step as 6 + 1 = 7
  },
  DRESS_CODE: {
    USUAL: 'Usual Country Club Attire',
    LONG_PANTS: 'Long Pants Required',
    BLAZER: 'Blazer Required',
    BLAZER_LONG_PANTS: 'Blazer and Long Pants Required',
  },
  GUEST_TIME_RESTRICTIONS: {
    Friday: 'All Day',
    Monday: 'All Day',
    Saturday: 'All Day',
    Sunday: 'All Day',
    Thursday: 'All Day',
    Tuesday: 'All Day',
    Wednesday: 'All Day',
  },
  MEMBERSHIP_PRIVILEGES: {
    RESTRICTED: 'Restricted Guest Hosting Privileges',
    UNRESTRICTED: 'Unrestricted Guest Hosting Privileges',
  },
  SIGNUP_URLS: {
    '/register/personal-profile': 2,
    '/register/golfer-profile': 3,
    '/register/add-club': 4,
    '/register/overview': 5,
  },
  DELETED_USER: 'DELETED USER',
  REQUEST_CREATION_ALLOWANCE_MONTHS: {
    SAME_COUNTRY: 3,
    DIFFERENT_COUNTRY: 6,
  },
  FIREBASE_CONFIG: {
    apiKey: process.env.CONFIG?.firebase?.publicApiKey,
    authDomain: process.env.CONFIG?.firebase?.authDomain,
    databaseURL: process.env.CONFIG?.firebase?.databaseUrl,
    projectId: process.env.CONFIG?.firebase?.projectId,
    storageBucket: process.env.CONFIG?.firebase?.storageBucket,
    messagingSenderId: process.env.CONFIG?.firebase?.messagingSenderId,
    appId: process.env.CONFIG?.firebase?.appId,
    measurementId: process.env.CONFIG?.firebase?.measurementId,
  },
  MY_CONTACTS_CSV_HEADERS: [
    { label: 'First name', key: 'first_name' },
    { label: 'Last name', key: 'last_name' },
    { label: 'Email', key: 'email' },
    { label: 'Phone', key: 'phone' },
    { label: 'Clubs', key: 'courses' },
    { label: 'Type of Connection', key: 'connection' },
  ],
  USER_ACTIVITY: {
    MAP_FAV: { event: 'marked_favorite', from: 'Maps' },
    SEARCH_FAV: { event: 'marked_favorite', from: 'Search Favorite Club' },
    MAP_PLAYED: { event: 'marked_played', from: 'Maps' },
    ADD_CONTACT: { event: 'add_contact', from: 'My Contacts' },
    EVENT_DETAILS: { event: 'event_more_info', from: 'Event Details' },
    BENEFIT_DETAILS: { event: 'benefit_more_info', from: 'Benefit Details' },
    REQUEST: { event: 'post_request', from: 'Request against offer' },
    REQUEST_AGAINST_OFFER: { event: 'post_request', from: 'Requests' },
    USER_WELCOME_BACK: {
      event: 'Existing_User_Continue_Click',
      from: 'Welcome Back Page',
    },
    EXISTING_USER_DELETE_ACCOUNT: {
      event: 'Existing_User_Delete_Account',
      from: 'Welcome Back Page',
    },
    EXISTING_USER_DELETED_SUCCESSFULLY: {
      event: 'Existing_User_Deleted_successfully',
      from: 'Welcome Back Page',
    },
    EXISTING_USER_PAYMENT_CLICKED: {
      event: 'Existing_User_Payment_Clicked',
      from: 'Welcome Back Page',
    },
    PAYMENT_SUCCESSFUL: {
      event: 'Payment_Successful',
      from: 'Welcome Back Page',
    },
    PAYMENT_UNSUCCESSFUL: {
      event: 'Payment_unsuccessful',
      from: 'payment screen',
    },
    PAYMENT_ATTEMPTED: { event: 'Payment_attempted', from: 'payment screen' },
    EVENT: { event: 'event_details', from: 'event_page' },
    EVENT_REQUEST_INFO: { event: 'event_request_info', from: 'event_page' },
    BENEFIT: { event: 'benefit_details', from: 'benefit_page' },
    BENEFIT_REQUEST_INFO: {
      event: 'benefit_request_info',
      from: 'benefit_page',
    },
  },
  MEMBERSHIP_LOGS: {
    MODULE_TYPES: {
      REQUEST: 'request',
      EVENT: 'event',
      BENEFIT: 'benefit',
      PEGBOARD: 'pegboard',
    },
    MODULE_ACTIONS: {
      COMPLETED: 'completed',
      PLAYED: 'played',
      CREATED: 'created',
      VIEWED: 'viewed',
    },
  },
  PLAN_KEYS: {
    FREE: 1,
    SUPER_HOST: 2,
    PAR: 3,
    BIRDIE: 4,
    EAGLE: 5,
    FCM: 6,
    FREE_OLD_USER: 7,
    ALBATROSS: 8,
  },
  INTENTS: {
    PAYMENT: 1,
    SETUP: 0,
  },
  PAYMENT_ATTEMPTED_FROM: {
    FRONT_END: 0,
    BACK_END: 1,
  },
  ACCEPTED_CURRENCY: 'usd',

  STRIPE_RESPONSE_CODES: {
    PENDING: { key: 2, codes: ['processing'] },
    DECLINED: {
      key: 3,
      codes: [
        'card_declined',
        'expired_card',
        'incorrect_cvc',
        'processing_error',
        'incorrect_number',
        'requires_payment_method',
        'canceled',
        'setup_failed',
      ],
    },
    SUCCESS: { key: 1, codes: ['succeeded'] },
  },
  STRIPE_HOOKS_PAYMENT: [
    'payment_intent.payment_failed',
    'payment_intent.requires_action',
    'payment_intent.succeeded',
    'payment_intent.canceled',
    'payment_intent.processing',
    'setup_intent.setup_failed',
    'setup_intent.succeeded',
    'setup_intent.requires_action',
    'setup_intent.processing',
  ],
  STRIPE_HOOKS_PAYMENT_METHOD: ['payment_method.attached'],

  MEMBERSHIP_LEVEL_MESSAGE: [
    {
      KEY: 'FREE_OLD_USER',
      MESSAGE:
        "Congratulations! You're eligible to access Thousand Greens for free till your first completed request",
    },
    {
      KEY: 'SUPER_HOST',
      MESSAGE:
        "Congratulations! You're a super host hence on a FREE PLAN for a year.",
    },
    { KEY: 'EAGLE', MESSAGE: "You'll start as an Eagle Level member." },
    { KEY: 'PAR', MESSAGE: "You'll start as a Par Level member." },
    { KEY: 'BIRDIE', MESSAGE: "You'll start as a Birdie Level member." },
  ],

  ALL_TIERS: {
    Fern: 1,
    Sage: 2,
    Moss: 3,
    Olive: 5,
    Teal: 11,
  },
  TIER_TAG_OPTIONS: [, 'Fern', 'Sage', 'Moss', , 'Olive'],
  TIER_OPTIONS: ['All', 'Fern', 'Sage', 'Moss', 'Olive', 'Teal'],
  TIERS: [, 'Fern', 'Sage', 'Moss', , 'Olive', , , , , , 'Virtual'],

  THRESHOLD_OPTIONS: [
    'All',
    'Highly Requested',
    'Unexplored',
    'Neutral',
    'Manual',
  ],

  STATUS_OPTIONS: [
    'All',
    'Pending',
    'Active',
    'Deactivated',
    'Declined',
    'Activate Later',
  ],

  LEVEL_OPTIONS: [
    'All',
    'Trial',
    'Free Access',
    'Super Host',
    'Par',
    'Birdie',
    'Eagle',
    'Albatross',
    'Founder Club Member',
  ],
  MEMBERSHIP_OPTIONS: ['All', 'Active', 'In-Active', 'Membership Expired'],

  USER_SORTING_OPTIONS: {
    ['Newest - Oldest']: {
      type: 'created_at_desc',
      direction: 'desc',
    },
    ['Oldest - Newest']: {
      type: 'created_at_asc',
      direction: 'asc',
    },
    ['Name A - Z']: {
      type: 'name_asc',
      direction: 'asc',
    },
    ['Name Z - A']: {
      type: 'name_desc',
      direction: 'desc',
    },
    ['Username A - Z']: {
      type: 'username_asc',
      direction: 'asc',
    },
    ['Username Z - A']: {
      type: 'username_desc',
      direction: 'desc',
    },
    ['Email A - Z']: {
      type: 'email_asc',
      direction: 'asc',
    },
    ['Email Z - A']: {
      type: 'email_desc',
      direction: 'desc',
    },
    ['NGV Highest - Lowest']: {
      type: 'ngv_desc',
      direction: 'desc',
    },
    ['NGV Lowest - Highest']: {
      type: 'ngv_asc',
      direction: 'asc',
    },
  },

  ALL_TIERS_OPTIONS: [
    {
      name: 'Fern',
      value: 1,
    },
    {
      name: 'Sage',
      value: 2,
    },
    {
      name: 'Moss',
      value: 3,
    },
    {
      name: 'Olive',
      value: 5,
    },
    {
      name: 'Teal',
      value: 11,
    },
  ],

  ALL_THRESHOLD_OPTIONS: [
    {
      name: 'Neutral',
      value: 0,
    },
    {
      name: 'Highly Requested',
      value: 1,
    },
    {
      name: 'Unexplored',
      value: 2,
    },
    {
      name: 'All',
      value: 3,
    },
    {
      name: 'Manual',
      value: 9,
    },
  ],

  PROFILE_COMPLETE_MEMBERSHIP_KEY_POINTS: {
    US: 'As a new user, you can use the platform for free for 30 days upon successful addition of payment details in the next step. At end of the free trial period, you will be charged as per the pricing plan.',
    NON_US:
      'You can use the platform for free till the completion of your first request. Post that you will be converted to a paid member as per our pricing plan',
  },
  STRIPE_HOOKS_AMOUNT_CAPTURABLE: ['payment_intent.amount_capturable_updated'],
  STRIPE_HOOKS_CHARGE_STATUS: [
    'charge.captured',
    'charge.expired',
    'charge.failed',
    'charge.refunded',
    'charge.succeeded',
  ],
  STRIPE_CHARGE_STATUS: {
    'charge.succeeded': 1,
    'charge.captured': 2,
    'charge.expired': 3,
    'charge.failed': 4,
    'charge.refunded': 5,
  },
  PAGINATION_LIMIT: {
    PN_GAMES_HISTORY: 50,
  },
  PAGINATION_OFFSET: {
    PN_GAMES_HISTORY: 15,
  },
  MODAL_TITLE: {
    DECLINE_REQUEST: 'Decline Request',
    EDIT_GAME_DATE: 'Edit Game Date',
  },
  PROMOCODE: {
    CODE_STATUS: {
      ACTIVE: 1,
      INACTIVE: 0,
    },
    DEFAULT: { id: null, code: '', amount: null, percent: null },
  },
  EVENT_TITLE: {
    ALL_EVENT: 'all events',
    MY_PENDING_EVENT: 'pending events',
    PRIVATE_NETWORK: 'private network',
  },
  FRIEND_REQUEST_STATUS: {
    OPEN: 1,
    DECLINED: 2,
  },
  FRIEND_STATUS: {
    COMPLETED: 1,
    UNFRIENDED: 2,
  },
  USER_SEARCH_STATUS: {
    1: 'no',
    2: 'match',
    3: 'sent',
    4: 'received',
    5: 'friend',
    6: 'blocked',
  },
  LOG_GAME_STATUS: {
    1: 'requester_host',
    2: 'contact_info',
    3: 'no_result',
    4: 'match',
  },
  FRIENDS_TAB_OPTIONS: {
    ALL_FRIENDS: 'all-friends',
    SENT: 'sent',
    RECEIVED: 'received',
    DECLINED: 'declined',
  },
  FRIENDS_TAB_PAGE_SIZE: 50,
  MY_TG_HEADER: {
    MY_CONTACTS: 'My Contacts',
    MY_FRIENDS: 'My Friends',
    MY_CONTACTS_LOWERCASE: 'my-contacts',
    MY_FRIENDS_LOWERCASE: 'my-friends',
    GROUPS: 'Groups',
    GROUPS_LOWERCASE: 'groups',
    PENDING_REQUEST: 'pending-requests',
    CHAT: 'Chat',
    CHAT_LOWERCASE: 'chat',
  },
  STREAM_CHANNEL_TYPES: {
    ONE_TO_ONE: 'messaging',
    SYSTEM: 'system_channel',
    USER_CREATED_GROUP: 'user_created_group',
    SYSTEM_THOUSAND_GREENS_PUBLIC: 'system_thousand_greens_public',
    SYSTEM_PRIVATE_NETWORK: 'system_private_network',
    ADMIN_CREATED_GROUP: 'admin_created_group',
    MY_TG_GROUP: 'my_tg_group',
    REQUEST_CHAT_GROUP: 'request_chat_group',
  },
  CHAT_USER_CATEGORY: {
    RECENT: 'Recent Chats',
    GROUPS: 'Groups',
    FRIENDS: 'Friends On Thousand Greens',
    OTHERS: 'Other Users',
  },
  MAX_YEARS_ALLOWED_FOR_REQUEST_ACCEPTANCE: 5,
  QUOTED_MESSAGE_LENGTH_LIMIT: {
    QUOTED_MESSAGE_PREVIEW: 110,
    QUOTED_MESSAGE: 195,
  },
  STREAM_MESSAGE_LIMIT: 50,
  STREAM_CUSTOM_DATA_KEYS: {
    IS_FRIENDS: 'isFriends',
    BLOCKED_BY: 'blockedBy',
  },
  DB_OPERATION: {
    INSERT: 'INSERT',
    DELETE: 'DELETE',
  },
  TOAST_TYPE: {
    ERROR: 'error',
    SUCCESS: 'success',
    WARNING: 'warning',
    GROUP_LINK_COPIED: 'group-link-copied',
    PEGBOARD_CREATED: 'pegboard-created',
    REQUEST_MOVED: 'request-moved',
  },
  DISPLAY_PICTURE_FORMATS: ['image/png', 'image/jpg', 'image/jpeg'],
  FEED_TYPES: {
    POST: 1,
    GAME_REVIEW: 2,
    EVENT: 3,
    BENEFIT: 4,
  },
  NOTIFICATION_LIMIT: 15,
  CRON: {
    REQUEST_PER_CYCLE: 10,
    SLEEP_FOR_MS: 10000,
  },
  MAP_LAYERS: [
    'golf-club-unclustered-point-grey',
    'golf-club-unclustered-point-grey_contact',
    'golf-club-unclustered-point-green',
    'golf-club-unclustered-point-green_contact',
    'golf-club-unclustered-point-blue',
    'golf-club-unclustered-point-blue_contact',
    'golf-club-unclustered-point-yellow',
    'golf-club-unclustered-point-yellow_contact',
    'golf-club-unclustered-point-teal',
    'golf-club-unclustered-point-teal_contact',
  ],
  MY_TG_GROUPS_ROLES: {
    CREATOR: 1,
    ADMIN: 2,
    MEMBER: 3,
  },
  CRON: {
    REQUEST_PER_CYCLE: 10,
    SLEEP_FOR_MS: 10000,
  },
  SIGNUP_DEVICE_NAME_FOR_OTP: {
    WEB: '__W@eb',
    MOBILE: '__mObiLE',
  },
  TIER_THRESHOLD_TYPE: {
    HRT: 1, //High Request Threshold
    LRT: 2, //Low Request Threshold
  },
  CLUB_DEMAND_TYPE: {
    NEUTRAL: 0,
    OVER_REQUESTED: 1,
    UNDER_REQUESTED: 2,
    ALL: 3,
    MANUAL: 9,
  },
  DEMAND_TYPE_UPDATED_BY: {
    SYSTEM: 1,
    ADMIN: 2,
  },
  BENEFIT_CLICK_TYPE: {
    REVEAL: 1,
    INFO: 2,
  },
  BENEFIT_COUNT_COLUMN: {
    REVEAL: 'reveal_benefit_count',
    INFO: 'benefit_info_count',
  },
  DATE_FORMAT_Do_MM_YYYY: 'Do MMMM YYYY',
  COMPLETED_GAME_COUNT: 5,
  REQUEST: {
    STATUS: {
      PENDING: 'pending',
      CANCELLED: 'cancelled',
    },
    DATE_FORMATE: {
      YYYY_MM_DD: 'YYYY-MM-DD',
    },
  },
  OFFER_DETAILS_MAX_LENGTH: 300,
  DECLINE_REASON_MAX_LENGTH: 300,
  INT_CONSTANTS: {
    _5000: 5000,
  },
  REQUEST_NOTES_MAX_LENGTH: 1000,
  OFFER_FILTER: {
    ALL_OFFERS: 'all_offer',
    MY_TG_OFFERS: 'my_tg_offer',
  },
  REQUEST_TAB_FILTER_OPTIONS: {
    COMPLETED: 'completed',
    FULFILLED: 'fulfilled',
    CANCELLED: 'cancelled',
    DECLINED: 'declined',
    ALL: 'all',
  },
  MAP_FILTERS: {
    ALL: 'all',
    OPEN_OFFERS: 'open offers',
    FRIENDS: 'friends',
    MY_TG_GROUP_MEMBER: 'my tg group member',
    FAVORITE_CLUBS: 'favorite clubs',
    PLAYED: 'played',
    PLAY_AS_COUPLE: 'play as couple',
  },
  DRAWER_TYPE: {
    GROUPS: 'groups',
    EVENTS_AND_OFFERS: 'offer_events',
    PROFILE: 'profile',
    CHAT_GROUP: 'my-group-chat-details',
    MY_GROUP_DETAILS: 'my-group-details',
    GLOBAL_PROFILE_INFO: 'profile-info',
    GLOBAL_MY_TG_SETTINGS: 'my-tg-settings',
    GLOBAL_LEADERBOARD: 'leaderboard',
    GLOBAL_FRIENDS_PLAYED: 'friends-played',
    MAP_FILTERS: 'map-filters',
    EVENT_DETAILS: 'event_details',
    OTHER_TG_MEMBERS: 'other_tg_members',
    BENEFIT_DETAILS: 'benefit_details',
    MAP_CLUB_INFO: 'map_club_info',
    MAP_LIST_BOUND: 'map_list_bound',
    MAP_TOTAL_TG_GROUP_MEMBER: 'total_tg_group_member',
    MAP_FRIENDS_PLAYED: 'friends_played',
    MAP_ALL_MY_FRIENDS: 'map_all_my_friends',
    GAME_INFO: 'game_info',
    NOTIFICATION: 'notification',
    CLUB_VISIBILITY_DETAILS: 'club_visibility_detals',
    NGV_FAQ: 'ngv_faq',
    MY_FAV_CLUB: 'my_fav_club',
    FAV_CLUB_SEARCH: 'fav_club_search',
    REFERRAL: 'referral',
    POLL_DETAILS: 'poll_details',
    CLUB_DETAILS: 'club_details',
    OFFER_DETAILS: 'offer_details',
    ALL_GAME_REVIEWS: 'all_game_reviews',
    ADMIN_SEND_MAIL: 'admin_send_mail',
    GLOBAL_GAME_REVIEWS: 'global_game_reviews',
  },

  ADMIN_PEGBOARD_LIST_TYPES: {
    ADMIN: 0,
    USER: 1,
  },
  USER_PEGBOARD_DETAILS_HEADER: [
    '',
    'Rank',
    'Club Name',
    'Friends Played',
    'Mark as Played',
    '',
  ],
  SELECT_PARTICIPANTS: [1, 2, 3],
  USER_PEGBOARD_HEADER: [
    { title: 'Pegboard Name', width: '50' },
    { title: 'Played/Total Clubs', width: '20' },
    { title: 'Creator', width: '20' },
    { title: '', width: '10' },
  ],
  MAP_MARKERS: {
    BLUE_CONTACT: 'blue_contact',
    BLUE: 'blue',
  },
  GROUP_HEADINGS: ['My Groups', 'Suggested For You', 'All TG Groups'],
  GAME_USER_TYPE: {
    REQUESTER: 'requester',
    HOST: 'host',
  },
  GROUP_SUGGESTIONS: {
    MOST_MEMBERS: 'Groups with the most members',
    MOST_FRIENDS: 'Groups with the most friends',
    MOST_MUTUAL_MEMBERS: 'Groups with the most mutual members',
    NEW_GROUPS: 'New groups created',
    MOST_NEW_USERS: 'Groups getting the most new members',
  },
  MEMBERSHIP_FAQ: [
    {
      id: 1,
      title: 'What is TG Free Trial ?',
      description: [
        {
          heading:
            'Users can use all TG features and get familiar with the application in the 30 day trial. After the free trial, all members will be charged an annual subscription, which will be auto-renewed annually',
        },
      ],
    },
    {
      id: 2,
      title: 'How does the TG Membership model works?',
      description: [
        {
          heading:
            'We do not advertise or sell user data.  We have an annual subscription which is usage based. This subscription charge provides the member access to all the capabilities of the site.  Members cannot buy access to higher tiers.',
        },
      ],
    },
    {
      id: 3,
      title: 'Renewals, and Refunds',
      description: [
        {
          heading:
            'The subscription is annual, auto renewed, and there are no refunds in the event of a user account cancellation mid-plan.',
        },
        {
          heading:
            'If you do not wish to auto-renew, you can go to your settings and delete your account.',
        },
      ],
    },
    {
      id: 4,
      title: 'How does yearly renewal works?',
      description: [
        {
          heading:
            "All TG members are charged on the basis of their previous year's Net Game Value(NGV).",
        },
        {
          heading:
            'The NGV value in the preceding 12 months will be used to determine the renewal price at the time of the annual auto-renewal. (FYI, the limit on the max number of open requests at any time will still remain in place).',
        },
      ],
    },
    {
      id: 5,
      title: 'What is NGV (Net Game Value)',
      description: [
        {
          heading:
            'Every member will carry an NGV (Net game value). This is the cumulative running total of a users games played and hosted.',
        },
        {
          heading:
            "NGV Upgrading: Playing as a guest will increment the NGV and Hosting will decrement the NGV by a count dependent on Host and Requester's tiers and number of people request was made for.",
        },
        {
          heading: `For Games with "Number of people" 1`,
          value:
            'If Moss and Olive tier requesters play at a Fern or Sage club, it will cost 2 units of NGV (hosts will be credited 2 units). Otherwise, it will cost 1 unit of NGV.',
        },
        {
          heading: `For Games with "Number of people" 2`,
          value:
            'An additional 1 unit of NGV will be charged, along with the regular NGV transaction.',
        },
        {
          heading: `For Games with "Number of people" 3`,
          value:
            'An additional 2 units of NGV will be charged, along with the regular NGV transaction.',
        },
      ],
    },
  ],

  PROFILE_COMPLETION_CONTENT: (isUsUser) => {
    if (isUsUser === 'us') {
      return [
        {
          id: 1,
          type: 'US users',
          content:
            'As a new user,  you can use the platform for free for 30 days upon successful addition of payment details in the next step. At the end of the free trial, you will be charged an initial annual fee of $99 for Birdie Membership.  Subsequent annual renewal charges will be activity based, starting at $29/year.',
        },
        {
          id: 2,
          type: '',
          content:
            'You can make yourself visible to lower tiers (broad), or restrict receiving requests only to named clubs (narrow) in user settings.',
        },
        {
          id: 3,
          type: '',
          content:
            'By default, your notification preferences are turned on. You can change your notification preferences in user settings.',
        },
        {
          id: 4,
          type: '',
          content:
            'We would recommend you to go through our FAQs to get the full use out of our portal.',
        },
      ];
    } else {
      return [
        {
          id: 1,
          type: 'Non-US users',
          content:
            'As a new user,  you can use the platform for free for 30 days upon successful addition of payment details in the next step. At the end of the free trial, you will be charged an initial annual fee of $29 for Par Membership.  Subsequent annual renewal charges will be activity based, starting at $29/year.',
        },
        {
          id: 2,
          type: '',
          content:
            'You can make yourself visible to lower tiers (broad), or restrict receiving requests only to named clubs (narrow) in user settings.',
        },
        {
          id: 3,
          type: '',
          content:
            'By default, your notification preferences are turned on. You can change your notification preferences in user settings.',
        },
        {
          id: 4,
          type: '',
          content:
            'We would recommend you to go through our FAQs to get the full use out of our portal.',
        },
      ];
    }
  },
  IMAGE_CROP_MODAL_ASPECT_RATIO: {
    SQUARE: 1 / 1,
    RECTANGLE: 16 / 9,
  },
  IMAGE_CROP_PARAMS: {
    MAX_WIDTH: 1200,
    QUALITY: 0.9,
  },
  CLEVERTAP: {
    EMAIL: 'Email',
    NAME: 'Name',
    PHONE: 'Phone',
    GENDER: 'Gender',
    GOLF_INDEX: 'Golf index',
    COUNTRY: 'Country',
    CLUBS: 'Clubs',
    DATE_OF_JOINING: 'Date of joining',
    CURRENT_MEMBERSHIP_PLAN: 'Current membership plan',
    TG_AMBASSADOR: 'TG ambassador',
    TIER: 'Tier',
    USER_VISIBILITY: 'User visibility',
    ACCOUNT_MUTED: 'Account muted',
    LAST_ONBOARDING_STEP_COMPLETED: 'Last Onboarding Step completed',
    STATE: 'State',
    CITY: 'City',
    POSTAL_CODE: 'Postal Code',
    PROMOCODE: 'Promocode',
    USERNAME: 'Username',
    NEWSLETTER_SUBSCRIPTION: 'Newsletter Subscription',
    USER_EMAIL: 'User Email',
    GROUP_NAME: 'Group Name',
    GROUP_MEMBER_COUNT: 'Group Member Count',
    POLL_OPTIONS: 'Poll Options',
    COMMENTS: 'Comments',
    ANSWER_TYPE: 'Answer Type',
    POLL_CREATED: 'Poll Created',
    CURRENT_LOCATION: 'Current Location',
    GROUP_NAMES: 'Group Names',
    OFFER_ID: 'Offer ID',
    OFFER_CREATED_FOR: 'Offer created for',
    LOCATOR_COLOUR: 'Locator colour',
    CLUB_NAME: 'Club name',
    CLUB_TIER: 'Club Tier',
    CLUB_TYPE: 'Club Type',
    CLUB_ADDRESS: 'Club Address',
    CLUB_STATE: 'Club State',
    CLUB_COUNTRY: 'Club Country',
    USER_EMAIL: 'User email',
    USER_TIER: 'User tier',
    USER_MEMBERSHIP: 'User Membership',
    USER_CITY: 'User City',
    USER_STATE: 'User State',
    USER_COUNTRY: 'User Country',
    CLUB_IN_CURRENT_LOCATION: 'Club in Current Location or not',
    USER_TAG: 'User Tag',
  },
  CLEVERTAP_EVENTS: {
    MY_TG: 'My TG',
    SETTINGS: 'Settings',
    GET_VERIFICATION_CODE_STEP_1: 'Get Verification Code Step 1',
    COMPLETING_PROFILE_POST_ACCOUNT_ACTIVATION:
      'Completing Profile Post Account Activation',
    LOGOUT_FROM_POST_ACCOUNT_ACTIVATION: 'Logout From Post Account Activation',
    MEMBERSHIP_DETAILS_CONTINUE: 'Membership Details Continue',
    MEMBERSHIP_DETAILS_LOGOUT: 'Membership Details Logout',
    ADD_PAYMENT_DETAILS_ON_STRIPE: 'Add Payment Details On Stripe',
    LOGOUT_FROM_PAYMENT_DETAILS_ON_STRIPE:
      'Logout From Payment Details On Stripe',
    VERIFY_OTP: 'Verify Otp',
    PHONE_NUMBER_VERIFIED_STEP_1: 'Phone Number Verified Step 1',
    RESEND_OTP_STEP_1: 'Resend Otp Step 1',
    PERSONAL_PROFILE_CONTINUE_STEP_2: 'Personal Profile Continue Step 2',
    PERSONAL_PROFILE_LOGOUT_STEP_2: 'Personal Profile Logout Step 2',
    GOLFER_PROFILE_CONTINUE_STEP_3: 'Golfer Profile Continue Step 3',
    GOLFER_PROFILE_LOGOUT_STEP_3: 'Golfer Profile Logout Step 3',
    ADD_CLUB_CONTINUE_STEP_4: 'Add Club Continue Step 4',
    ADD_CLUB_LOGOUT_STEP_4: 'Add Club Logout Step 4',
    PROFILE_OVERVIEW_CONTINUE_STEP_5: 'Profile Overview Continue Step 5',
    PROFILE_OVERVIEW_LOGOUT_STEP_5: 'Profile Overview Logout Step 5',
    REQUESTS_RECEIVED_OPEN: 'Received > Open',
    REQUESTS_RECEIVED_ACCEPTED: 'Received > Accepted',
    REQUESTS_RECEIVED_HISTORY: 'Received > History',
    REQUESTS_REQUESTED_OPEN: 'Requested > Open',
    REQUESTS_REQUESTED_ACCEPTED: 'Requested > Accepted',
    REQUESTS_REQUESTED_HISTORY: 'Requested > History',
    REQUESTS_CREATE_NEW: 'Requests > Create Request',
    REQUESTS_POST_REQUEST: 'Requests > Post new request',
    ACCOUNT_SETTINGS_DELETE_ACCOUNT: 'Account Settings - Delete Account',
    ACCOUNT_SETTINGS_DELETE_ACCOUNT_1: 'Delete Account - First page Continue',
    ACCOUNT_SETTINGS_DELETE_ACCOUNT_2:
      'Delete Account - Second page Delete Account',
    ACCOUNT_SETTINGS_DELETE_ACCOUNT_CONFIRMATION:
      'Delete Account - confirmation Delete Account',
    TG_GROUPS_ON_MAP: 'TG Groups on map',
    CREATE_POLLS: 'Create Polls',
    MAP_TOTAL_TG_GROUP_MEMBER: 'View Group members in Club',
    MAP_FRIENDS_PLAYED: 'View Friends Played in Club',
    MAP_ALL_MY_FRIENDS: 'View Friends in Club',
    ///////
    CLICK_OFFER_IN_CLUB_DETAILS: 'Click Offer in Club Details',
    REQUEST_AGAINST_OFFER: 'Request Against Offer',
    CLICK_ON_CREATE_REQUEST_FROM_MAP: 'Click on Create Request from Map',
  },
  YEARLY_VALIDATION: {
    VERIFIED: 1,
    VERIFICATION_REQUIRED: 2,
  },
  SOURCES: {
    YEARLY_VALIDATION: 'yearly_validation',
    NOTIFICATION: 'notification',
  },
  TUTORIAL: {
    ACCOUNT_SETTINGS: [
      {
        id: 0,
        title: 'TG Account Visibility',
        description:
          'This setting provides you with the ability to control your level of participation and engagement on the TG platform according to your preferences.',
      },
      {
        id: 1,
        title: 'Mute account',
        description: `While your account is muted, you won't be able to create or receive any Game Requests, ensuring a quieter experience for you on the platform.`,
      },
    ],
  },
  POLL: {
    OPTIONS_LIMIT: 10,
  },
  WORD_LIMIT: {
    POLL_QUESTION: 250,
    POLL_OPTION: 250,
  },
  TEAL_DOT_FLAG_KEYS: {
    OFFER: 'offer',
    FRIEND: 'friend',
    EVENT: 'event',
    PEGBOARD: 'pegboard',
    BENEFIT: 'benefit',
    GROUP: 'group',
    REQUEST: 'request',
  },
  GAME_USER_TYPE: {
    REQUESTER: 'requester',
    HOST: 'host',
  },
  REQUEST_TABS: [
    {
      key: 'received',
      label: 'Received Open',
      function: 'Accept or Decline received requests',
    },
    {
      key: 'awaiting',
      label: 'Awaiting Acceptance',
      function: 'Your sent requests awaiting acceptance',
    },
    {
      key: 'accepted',
      label: 'Accepted Request',
      function: 'Both requested and received requests',
    },
    { key: 'history', label: 'History', function: '' },
  ],
  REQUEST_FAQS: {
    received: [
      'Please chat to confirm logistics before you accept',
      'The accept function will only get activated when there is a reply to your message from the requester',
      'Please be aware that the requester may be in communication with multiple potential hosts',
      `We really appreciate hosts extending an invite to 'first time requesters', it makes the network stronger`,
    ],
    awaiting: [
      'Please remember that this is a no obligation network',
      'If a host starts a chat with you, you have to respond to it and confirm logistics before a host can accept',
      'We recommend that if you are in communication with multiple hosts, then upon acceptance you send a polite message of appreciation to the other hosts',
    ],
    accepted: [
      {
        player: '',
        faq: 'If the game date changes, the host can edit the game date',
      },
      {
        player: '',
        faq: 'Please do not cancel at the last minute unless there is a good unavoidable reason. It is very disruptive',
      },
      {
        player: 'Hosts',
        faq: 'Please communicate your expectations very clearly to the requesters',
      },
      {
        player: 'Requesters',
        faq: 'Please remember that the host is giving you a gift of his time, treat it accordingly',
      },
    ],
    history: [
      'You can continue chatting if you wish even for requests in history',
      'You can delete any game from the history view. Please remember that once deleted, all associated chats will also be deleted and cannot be recovered',
      'For all completed games, the requesters and the hosts are connected to each other as friends',
    ],
  },

  ADMIN_OTP_EXPIRY_MINUTES: 30,
  ADMIN_TOKEN_EXPIRY_DAYS: 30,
};

constantOptions.PAID_PLANS = [
  constantOptions.PLAN_KEYS.PAR,
  constantOptions.PLAN_KEYS.BIRDIE,
  constantOptions.PLAN_KEYS.EAGLE,
  constantOptions.PLAN_KEYS.ALBATROSS,
];

constantOptions.FREE_1_YEAR_PLANS = [
  constantOptions.PLAN_KEYS.SUPER_HOST,
  constantOptions.PLAN_KEYS.FREE_OLD_USER,
];

constantOptions.FREE_TRIAL_PLANS = [constantOptions.PLAN_KEYS.FREE];

constantOptions.PRIORITY_NOTIFICATION_TYPES = [
  'host-request',
  'request-chat',
  'cancelled-request',
  'confirmed-request',
  'join-group-request',
  'tga-friend-connected',
  'game-reminder',
  'update-email',
  'offline-game-logged',
  'request-chat-new-message',
];

export default constantOptions;
