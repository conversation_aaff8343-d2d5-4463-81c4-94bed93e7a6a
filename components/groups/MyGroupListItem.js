import React, { useContext } from 'react'
import NameInitials from '../common/NameInitials'
import ReadMore from '../../utils/truncate/readmore'
import renderGroupRole from '../../utils/chat-v2/myTgGroups/renderGroupRole'
import constantOptions from '../../constants/constantOptions'
import { DrawerContext } from '../../context/DrawerContext'
import JoinGroup from './JoinGroup'
import streamOptions from '../../constants/streamOptions'
import { useRouter } from 'next/router'
import MultiMembersAvatar from './MultiMembersAvatar'
import LockIcon from '../icons/LockIcon'
import urlRenderer from '../../utils/truncate/urlRenderer'
import useThumbnail from '../../hooks/useThumbnail'

export default function MyGroupListItem({ group, setRefreshGroup = () => { } }) {
    const { setDrawer } = useContext(DrawerContext)
    const router = useRouter()
    const { CREATOR, ADMIN, MEMBER } = constantOptions?.MY_TG_GROUPS_ROLES
    const { MY_TG_GROUP } = streamOptions?.CHANNEL?.TYPES
    const { thumbnailUrl } = useThumbnail(group?.image, 256)
    
    const openGroupDetails = (e) => {
        // Don't open drawer if clicking on Join button or pending requests
        if (e.target.closest('.join-button') || e.target.closest('.pending-requests')) {
            return;
        }
        setDrawer({
            type: 'my-group-details',
            source: 'groups',
            global: true,
            channelType: MY_TG_GROUP,
            groupId: group?.streamId,
            group: group?.id,
            setRefreshGroup: setRefreshGroup,
        });
        window?.sessionStorage?.setItem("MY_TG_GROUP_DETAILS_BACK_HANDLER", JSON.stringify({
            channelType: MY_TG_GROUP,
            global: true,
            group: group?.id,
            groupId: group?.streamId,
            source: 'groups',
            type: 'my-group-details',
            setRefreshGroup: setRefreshGroup,
        }))
    };

    return (
        <div className='flex bg-white px-lm py-md mb-sm cursor-pointer rounded-lg justify-between hover:shadow'
            onClick={openGroupDetails}
        >
            <div className='flex w-[70%]'>
                <div className='flex items-center word-break break-words my-tg-group-image-col'>
                    {group?.image ? (
                        <div className='relative' style={{
                            height: 80,
                            width: 80,
                            borderRadius: 4,
                            backgroundImage: `url("${thumbnailUrl}")`,
                            backgroundPosition: 'center',
                            backgroundSize: 'cover',
                        }}>
                            {group?.inviteOnly ? (
                                <div className='absolute' style={{ top: -4, right: -4, zIndex: 1 }}>
                                    <LockIcon />
                                </div>
                            ) : (
                                null
                            )}
                        </div>
                    ) : (
                        <div className='relative'>
                            {group?.inviteOnly ? (
                                <div className='absolute' style={{ top: -4, right: -4, zIndex: 1 }}>
                                    <LockIcon />
                                </div>
                            ) : (
                                null
                            )}
                            <NameInitials fontSize={32} height={80} width={80} contact={{ full_name: group?.name }} />
                        </div>
                    )}
                </div>
                <div className='text-12 flex-col pl-lg flex'>
                    <div className='text-16 font-medium text-darkteal'>{group?.name}</div>
                    {group?.description?.length ? (
                        <div className="break-words text-grayLight text-12 py-sm">
                            {group?.description?.length < 100 ? urlRenderer(group?.description) :
                                <ReadMore className={'text-12'} length={100}>{group?.description}</ReadMore>
                            }
                        </div>
                    ) : (
                        <div className="text-12 darkteal py-sm">-</div>
                    )}
                    <div className='h-[20px]'>
                        <MultiMembersAvatar members={group?.members} memberCount={group?.memberCount} />
                    </div>
                </div>
            </div>


            <div className='flex-col flex justify-between items-end py-sm'>
                {(![MEMBER, null].includes(group?.role)) ?
                    <div className='text-darkteal p-xs text-10 rounded text-end' style={{ background: 'rgba(9, 128, 137, 0.1)', width: 75, height: 19 }}>{renderGroupRole(group?.role)}</div>
                    : <div></div>}

                {([CREATOR, ADMIN].includes(group?.role) && group?.inviteOnly) ?
                    (
                        <>
                            {group?.pendingRequestCount > 0 ? (
                                <div className='flex items-center cursor-pointer pending-requests'
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        router.push({
                                            pathname: `/dashboard/my-tg`,
                                            search: `?type=pending-requests&groupId=${group?.streamId}`
                                        })
                                    }}
                                >
                                    <img src='/svg/PendingRequest.svg' />
                                    <div className='text-12 ml-sm text-darkteal'>{group?.pendingRequestCount > 10 ? "10+" : group?.pendingRequestCount} Member Request{group?.pendingRequestCount >= 2 && "s"}</div>
                                </div>
                            ) : (
                                <div className='text-darkteal flex-center flex-1'>-</div>
                            )}
                        </>
                    ) : <div className=''></div>}
            </div>

            {!group?.isAMember ? (
                <div className='flex py-sm join-button' onClick={(e) => e.stopPropagation()}>
                    <JoinGroup type="white" className="bg-white border border-darkteal" group={group} textSize={'10'} setRefreshGroup={setRefreshGroup} width={65} height={30} />
                </div>
            ) : null
            }
        </div>
    )
}
