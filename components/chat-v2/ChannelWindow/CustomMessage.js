import React, { useRef, useContext, useState, useEffect } from 'react'
import { UserContext } from '../../../pages/_app';
import { ModalContext } from '../../../context/ModalContext';
import {
    MessageOptions,
    useChannelStateContext,
    useMessageContext,
    useChannelActionContext,
    MessageActions,
    ReactionSelector,
    ReactionsList,
} from 'stream-chat-react';
import CustomAvatar from '../CustomAvatar';
import ActionsIcon from '../../icons/ActionsIcon';
import isEmptyString from '../../../utils/helper/isEmptyString';
import { AttachmentOrUrlWrapper, DeletedMessage, MessageTimestampWrapper, TextMessage } from './MessageSeparateComponents';
import QuotedMessage from './QuotedMessage';
import streamOptions from '../../../constants/streamOptions';
import { CustomStreamContext } from '../../../context/CustomStreamContext';
import { DrawerContext } from '../../../context/DrawerContext';
import PollMessage from './PollMessage';
import GameReviewMessage from './GameReviewMessage';
import ReactionIcon from '../../icons/ReactionIcon';

const {
    USER_STATUS: { DELETED },
    CHANNEL: { TYPES: { SYSTEM_THOUSAND_GREENS_PUBLIC, USER_CREATED_GROUP, ADMIN_CREATED_GROUP, MY_TG_GROUP, REQUEST_CHAT_GROUP } }
} = streamOptions

const CustomMessage = () => {
    const optionsRef = useRef(null)
    const { user, token, streamChatClient } = useContext(UserContext)
    const { message, highlighted, handleReaction } = useMessageContext();
    const { updateMessage, setQuotedMessage } = useChannelActionContext()
    const { channel } = useChannelStateContext()
    const [hasAttachments, setHasAttachments] = useState(message?.attachments && message?.attachments?.length > 0);
    const [isSelfMessage] = useState(message?.user?.id === user?.id)
    const [isHovered, setIsHovered] = useState(false)
    const [isMessageDeleted, setIsMessageDeleted] = useState(message?.type === 'deleted')
    const [messageHasUrl, setMessageHasUrl] = useState(message?.attachments[0]?.title_link)
    const [hasTextMessage, setHasTextMessage] = useState(!isEmptyString(message?.text));
    const { myFriendsId, setShowDetails, setShowProfileDetail, setOtherUserId, streamDeletedUsers, setShowBackOption, headerRef, inputRef } = useContext(CustomStreamContext)
    const [isDeletedUser, setIsDeletedUser] = useState(message?.user?.deleted_at || streamDeletedUsers?.includes(message?.user?.id))
    const [showToolTip, setShowToolTip] = useState(false)
    const { drawer, setDrawer } = useContext(DrawerContext)
    const [position, setPosition] = useState("");
    const [isOptionsVisible, setIsOptionsVisible] = useState(false);
    const [isReactionVisible, setIsReactionVisible] = useState(false);
    const { setModal } = useContext(ModalContext)

    const deleteMessageHandler = async (messageId) => {
        if (streamChatClient) {
            await streamChatClient?.deleteMessage(messageId)
        }
    }
    const reply = async (message) => {
        setQuotedMessage(message)
    }
    const customActions = [
        !hasAttachments && { action: 'copy', label: 'Copy' },
        isSelfMessage && { action: 'delete', label: 'Delete' },
        { action: 'reply', label: 'Reply' },
        channel?.type !== REQUEST_CHAT_GROUP && { action: 'forward', label: 'Forward' }
    ].filter(Boolean);

    const handleActionClick = (action) => {
        switch (action) {
            case 'copy':
                navigator.clipboard.writeText(message.text || '');
                break;
            case 'delete':
                setModal({
                    type: "delete-chat",
                    width: 430,
                    onClickHandler: () => deleteMessageHandler(message?.id),
                    text: 'Are you sure you want to delete this message?'
                })
                break;
            case 'reply':
                reply(message)
                break;
            case 'forward':
                setModal({
                    type: 'forward-message',
                    width: 500,
                    messageId: message?.id
                });
                break;
            default:
                console.warn('Unhandled action:', action);
        }
    };


    // Close the options menu when clicking outside
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (optionsRef.current && !optionsRef.current.contains(event.target)) {
                setIsOptionsVisible(false);
                setIsReactionVisible(false);
            }
        };

        document.addEventListener("mousedown", handleClickOutside);
        return () => document.removeEventListener("mousedown", handleClickOutside);
    }, []);

    const handleReactionClick = (e) => {
        e.stopPropagation();
        setIsReactionVisible((prev) => !prev);
    }

    const handleMessageActionClick = (e) => {
        // Prevent the click event from bubbling up to the parent div
        e.stopPropagation();
        setIsOptionsVisible((prev) => !prev);
    };

    const handleMouseLeave = () => {
        setIsOptionsVisible(false);
        setIsReactionVisible(false);
    };

    useEffect(() => {
        adjustPosition();
        window.addEventListener("resize", adjustPosition); // Recheck on window resize

        return () => window.removeEventListener("resize", adjustPosition); // Cleanup
    }, []);

    useEffect(() => {
        if (message) {
            setHasAttachments(message?.attachments && message?.attachments?.length > 0)
            setMessageHasUrl(message?.attachments[0]?.title_link)
            setHasTextMessage(!isEmptyString(message?.text))
        }
    }, [message])

    useEffect(() => {
        setIsDeletedUser(message?.user?.deleted_at || streamDeletedUsers?.includes(message?.user?.id))
    }, [message?.user?.deleted_at, streamDeletedUsers])

    useEffect(() => {
        const messageDeletedEventListener = (event) => {
            if (message?.id === event?.message?.id && event?.message?.quoted_message_id && message?.type !== 'deleted') {
                // We have to update the message as incase of quoted messages deletion, as the context is not updating on the fly (TG-3453)
                updateMessage({ ...event?.message, type: 'deleted' })
            }
        }
        const eventListener = channel?.on('message.deleted', messageDeletedEventListener);
        return () => eventListener?.unsubscribe();
    }, [channel?.id])

    useEffect(() => {
        setIsMessageDeleted(message?.type === 'deleted')
    }, [message?.type])

    const adjustPosition = (optionsRef, headerRef, inputRef) => {

        if (!optionsRef?.current || !headerRef?.current || !inputRef?.current) return;

        // Get the bounding rectangles for the top and bottom boundaries
        const topBoundaryRect = headerRef.current.getBoundingClientRect();
        const bottomBoundaryRect = inputRef.current.getBoundingClientRect();


        // Get the bounding rectangle for the message options div
        const optionsRect = optionsRef?.current?.getBoundingClientRect();

        // Check if the options div crosses the top boundary
        if ((optionsRect?.top - topBoundaryRect?.bottom) < 120) {
            setPosition("bottom"); // Shift to bottom if it crosses the top
        }
        // Check if the options div crosses the bottom boundary
        else if (optionsRect?.bottom > bottomBoundaryRect?.top) {
            setPosition("top"); // Shift to top if it crosses the bottom
        }
    };

    //When channel type id USER_CREATED_GROUP
    const onAvatarClickHandler = () => {
        if ([
            USER_CREATED_GROUP,
            ADMIN_CREATED_GROUP,
            MY_TG_GROUP,
            SYSTEM_THOUSAND_GREENS_PUBLIC,
            REQUEST_CHAT_GROUP
        ]) {
            setOtherUserId(message?.user?.id)
            setDrawer({
                type: channel?.type === REQUEST_CHAT_GROUP ? "profile-info" : "profile", source: "chat", channelId: message?.user?.id, global: channel?.type === REQUEST_CHAT_GROUP ? true : false
            })
        }
    }

    const getAvatar = () => {
        if (!isSelfMessage)
            return (
                <div className={`relative message-list pt-md ${isDeletedUser && 'deleted-user-channel'}`}
                    onMouseLeave={() => { setShowToolTip(false) }}
                >
                    <CustomAvatar
                        style={{ cursor: 'pointer' }}
                        image={isDeletedUser ? '' : message.user?.image}
                        name={
                            isDeletedUser
                                ? '?'
                                : channel?.data?.type ===
                                    SYSTEM_THOUSAND_GREENS_PUBLIC
                                    ? message?.user?.username
                                    : !myFriendsId[message?.user?.id] ? message.user?.username : message.user?.name
                        }
                        size={28}
                        onClickHandler={() => {
                            if (!isDeletedUser) {
                                onAvatarClickHandler()
                            } else {
                                setShowToolTip((prev) => !prev)
                            }
                        }}
                    />
                    {showToolTip &&
                        <div className='absolute bg-white p-sm rounded-lg' style={{ width: 310, top: -20 }}>
                            This user doesn't exist in the system anymore.
                        </div>
                    }
                </div>
            )
    }

    const getName = () => {
        if (!isSelfMessage) {
            return (
                <>
                    {isDeletedUser ? (
                        <div className="text-lightergray">{DELETED}</div>
                    ) : !myFriendsId[message?.user?.id] ? (
                        <div className="text-12 text-black">
                            {message.user?.username}
                        </div>
                    ) : (
                        <div className="text-12 text-black">
                            {message.user?.name}
                        </div>
                    )}
                </>
            )
        }
    }

    const showMessage = () => {
        return (
            <div
                onMouseEnter={() => setIsHovered(true)}
                className={`custom-message-bubble flex-col ${isSelfMessage ? "is-self-message" : "not--my-message"} ${hasAttachments ? "has--attachments" : "no--attachments"} ${messageHasUrl ? "has--url-message" : "no--url-message"} ${hasTextMessage ? "has--text-message" : "has--no-text-message"} ${highlighted ? "highlighted" : ""} ${isMessageDeleted ? "deleted--message" : ""} ${message?.quoted_message_id ? "cust-quoted-message" : ""}`}
            >
                <DeletedMessage isMessageDeleted={isMessageDeleted} extraClasses="p-3px" />
                {!isMessageDeleted && (
                    <>
                        <ReactionsList />
                        {message?.quoted_message_id && <QuotedMessage channel={channel} />}
                        {message?.poll ?
                            <PollMessage poll={message?.poll} message={message} channel={channel} /> :
                            message?.gameReview ?
                                <GameReviewMessage message={message} /> :
                                <div className={`flex flex-col ${hasAttachments ? "w-[354px]" : "w-full"} ${hasAttachments ? "flex-col-reverse" : ""}`}>
                                    <TextMessage mentionedUsers={message?.mentioned_users} hasTextMessage={hasTextMessage} message={message} />
                                    <AttachmentOrUrlWrapper messageHasUrl={messageHasUrl} isSelfMessage={isSelfMessage} hasAttachments={hasAttachments} message={message} />
                                    <MessageTimestampWrapper messageHasUrl={messageHasUrl} hasAttachments={hasAttachments} message={message} />
                                </div>}

                        {isHovered && (
                            <div
                                className={`absolute top-[-5px] ${isSelfMessage ? "left-[-45px]" : "right-[-40px]"}`}
                                onMouseLeave={handleMouseLeave}
                                ref={optionsRef}
                            >
                                {isOptionsVisible && (
                                    <div className={`bg-white shadow-xl rounded-lg w-[100px] absolute ${isSelfMessage ? "left-[-90px]" : "right-[-90px]"}`} style={{ zIndex: 1000 }}>
                                        {customActions.map((action, i) => (
                                            <div
                                                key={i}
                                                className="text-black cursor-pointer hover:bg-lightestgray px-md py-xs border-b border-lightestgray"
                                                onClick={() => handleActionClick(action?.action)}
                                            >
                                                {action?.label}
                                            </div>
                                        ))}
                                    </div>
                                )}
                                {isReactionVisible && (
                                    <div className={` absolute ${isSelfMessage ? "left-[-200px]" : "right-[-200px]"}`} style={{ zIndex: 1000 }}>
                                        <ReactionSelector />
                                    </div>
                                )}
                                {!isOptionsVisible && !isReactionVisible && (
                                    <div className="flex items-center justify-between w-[35px]">
                                        <ReactionIcon onClick={handleReactionClick} />
                                        <ActionsIcon onClick={handleMessageActionClick} />
                                    </div>
                                )}
                            </div>
                        )}
                    </>
                )}
            </div>
        );
    };

    return (
        <div
            onMouseLeave={() => setIsHovered(false)}
            className={`flex ${isSelfMessage ? "justify-end" : ""}`}
        >
            <div className={`flex py-md ${isSelfMessage ? "justify-end" : ""}`} style={{ width: "56%" }}>
                {getAvatar()}
                <div className="ml-10">
                    {getName()}
                    {showMessage()}
                </div>
            </div>
        </div>
    );
}

export default CustomMessage