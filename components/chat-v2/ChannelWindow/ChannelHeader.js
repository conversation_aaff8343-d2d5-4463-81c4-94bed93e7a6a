import React, { useContext, useEffect, useState } from 'react'
import { useChannelStateContext } from 'stream-chat-react'
import CustomAvatar from '../CustomAvatar'
import getChannelName from '../../../utils/stream/getChannelName'
import { UserContext } from '../../../pages/_app'
import getChannelImageURL from '../../../utils/stream/getChannelImageURL'
import streamOptions from '../../../constants/streamOptions'
import { CustomStreamContext } from '../../../context/CustomStreamContext'
import { get1to1ChannelStatus } from '../../../utils/stream/get1to1ChannelStatus'
import useCheckDeviceScreen from '../../../hooks/useCheckDeviceScreen'
import ENDPOINTS from "../../../constants/endpoints.json"
import { DrawerContext } from '../../../context/DrawerContext'
import constantOptions from '../../../constants/constantOptions'
import { useRouter } from 'next/router'
import { ModalContext } from '../../../context/ModalContext'


const { SYSTEM_THOUSAND_GREENS_PUBLIC, USER_CREATED_GROUP, MY_TG_GROUP, ONE_TO_ONE, REQUEST_CHAT_GROUP } = streamOptions?.CHANNEL?.TYPES
const { DELETED } = streamOptions?.USER_STATUS
const { EVENTS_AND_OFFERS, CHAT_GROUP, GROUPS, PROFILE } = constantOptions?.DRAWER_TYPE



const ChannelHeader = ({ request }) => {
    const { channel } = useChannelStateContext()
    const { user, token } = useContext(UserContext)
    const [offerCount, setOfferCount] = useState(0)
    const [eventCount, setEventCount] = useState(0)
    const { setModal } = useContext(ModalContext)
    const {
        showDetails,
        setOtherUserId,
        streamDeletedUsers,
        setChannelSelected,
        setSelectedChannelId,
        headerRef,
        myFriendsId
    } = useContext(CustomStreamContext)
    const [isDeletedUser1to1, setIsDeletedUser1to1] = useState(get1to1ChannelStatus(channel, streamDeletedUsers))
    const { isMobile, isTablet, isDesktop, isWideScreen } = useCheckDeviceScreen()
    const { drawer, setDrawer } = useContext(DrawerContext)
    const router = useRouter()
    useEffect(() => {
        setIsDeletedUser1to1(get1to1ChannelStatus(channel, streamDeletedUsers))
    }, [streamDeletedUsers])

    useEffect(() => {
        if (channel?.id && showDetails && window?.sessionStorage.getItem("MY_TG_GROUP_CHANNEL_ID_OFFER_EVENT_PANEL")) {
            // If there was a value for MY_TG_GROUP_CHANNEL_ID_OFFER_EVENT_PANEL, then we need to remove it, so that
            // from the next render when channel id changes, the details sidebar does not open automatically
            window.sessionStorage.setItem("MY_TG_GROUP_CHANNEL_ID_OFFER_EVENT_PANEL", "")
        } else if (channel?.id && showDetails) {
            setDrawer();
        }
    }, [channel?.id])

    useEffect(() => {
        getOfferCount()
        getEventCount()
    }, [channel])

    const getOfferCount = async () => {
        try {
            fetch(ENDPOINTS.GET_MY_TG_GROUP_OFFER_COUNT, {
                method: 'POST',
                headers: {
                    ['Content-Type']: 'application/json',
                    ['Authorization']: `Bearer ${token}`,
                }, body: JSON.stringify({
                    userId: user.id,
                    groupId: channel?.id
                }),
            }).then((res) => res.json())
                .then((data) => setOfferCount(data?.count))
        } catch (error) {
            console.log("------->", error);
        }
    }
    const getEventCount = async () => {
        try {
            fetch(ENDPOINTS.GET_MY_TG_GROUP_EVENT_COUNT, {
                method: 'POST',
                headers: {
                    ['Content-Type']: 'application/json',
                    ['Authorization']: `Bearer ${token}`,
                }, body: JSON.stringify({
                    userId: user.id,
                    groupId: channel?.id
                }),
            }).then((res) => res.json())
                .then((data) => setEventCount(data?.count))
        } catch (error) {
            console.log("------->", error);
        }
    }

    const channelOptions = () => {
        if (![SYSTEM_THOUSAND_GREENS_PUBLIC].includes(channel?.type) && !isDeletedUser1to1) {
            return (
                <img
                    className='cursor-pointer'
                    src="/svg/info-circle.svg"
                    width={18.33}
                    height={18.33}
                />
            )
        }
    }

    const handleOpenDetails = () => {
        if ((![SYSTEM_THOUSAND_GREENS_PUBLIC].includes(channel?.type) && !isDeletedUser1to1)) {
            if (drawer) {
                setDrawer()
            } else {
                setOtherUserId(Object.values(channel?.state?.members).find((member) => member?.user_id !== user?.id)?.user_id)
                if (![ONE_TO_ONE, REQUEST_CHAT_GROUP].includes(channel?.type)) {
                    setDrawer({
                        type: 'my-group-chat-details',
                        groupId: channel?.id,
                        channelType: channel?.type,
                        source: "chat"
                    })
                } else {
                    setDrawer({
                        type: channel?.type === REQUEST_CHAT_GROUP ? "profile-info" : "profile", source: "chat", channelId: channel?.id, global: channel?.type === REQUEST_CHAT_GROUP ? true : false
                    })
                }
            }
        }
    }

    return (
        <div ref={headerRef} className={`flex text-black ${!isMobile ? "channel-header" : "pl-sm py-sm"} ${isDeletedUser1to1 && 'deleted-user-channel'}`}>
            {(isMobile) &&
                <img className='cursor-pointer mr-sm' onClick={() => {
                    if (channel?.type === REQUEST_CHAT_GROUP) {
                        router.back()
                    } else {
                        setChannelSelected(false)
                    }
                }} width={19} src='/svg/BackArrow.svg' />
            }
            <div className={`flex flex-1 items-center justify-between`}>
                <div className={`flex channel-header cursor-pointer`}>
                    <div className={`${[USER_CREATED_GROUP, MY_TG_GROUP, REQUEST_CHAT_GROUP].includes(channel?.type) ? "cursor-pointer" : ""}`}>
                        <CustomAvatar
                            image={isDeletedUser1to1 ? '' : getChannelImageURL(channel, user)}
                            name={isDeletedUser1to1 ? '?' : getChannelName(channel, user, myFriendsId)}
                            size={38}
                            onClickHandler={() => {
                                handleOpenDetails()
                            }}
                        />
                    </div>
                    <div className={`px-md text-14 flex-center font-medium ${isDeletedUser1to1 ? 'text-lightgray' : 'text-black'} ${[USER_CREATED_GROUP, MY_TG_GROUP].includes(channel?.type) ? "cursor-pointer" : ""}`}
                        onClick={() => {
                            handleOpenDetails()
                        }}
                    >
                        {isDeletedUser1to1 ? DELETED : getChannelName(channel, user, myFriendsId)}
                    </div>
                </div>
                {(offerCount || eventCount) ?
                    <>
                        {isMobile ? (<div className='flex items-center'>
                            {offerCount ? (
                                <span
                                    onClick={() => {
                                        setSelectedChannelId(channel?.id)
                                        setDrawer({
                                            type: EVENTS_AND_OFFERS, source: "chat", channel
                                        })
                                    }}
                                    className='flex-center mr-xs rounded-full bg-lightestteal' style={{ height: 32, width: 32 }}><img width={18} src='/svg/OfferDarkTeal.svg' /></span>) : null}
                            {eventCount ? (
                                <span
                                    onClick={() => {
                                        setSelectedChannelId(channel?.id)
                                        setDrawer({
                                            type: EVENTS_AND_OFFERS, source: "chat", channel
                                        })
                                    }}
                                    className='flex-center mr-xs rounded-full bg-lightestteal' style={{ height: 32, width: 32 }}><img width={18} src='/svg/EventDarkTeal.svg' /></span>) : null}
                        </div>) :
                            <div className='flex bg-lighttealbackground2 px-sm items-center cursor-pointer h-[40px]' style={{ borderRadius: 39 }}
                                onClick={() => {
                                    setSelectedChannelId(channel?.id)
                                    setDrawer({
                                        type: EVENTS_AND_OFFERS, source: "chat", channel
                                    })
                                }}
                            >
                                {offerCount ? (
                                    <div className='flex px-xs'><span className='flex-center mr-xs rounded-full bg-lightestteal' style={{ height: 20, width: 20 }}><img width={13} src='/svg/OfferDarkTeal.svg' /></span>{`Offer${offerCount > 1 ? 's' : ''} : ${offerCount}`} </div>
                                ) : null}
                                {eventCount ? (
                                    <div className='flex px-xs'><span className='flex-center mr-xs rounded-full bg-lightestteal' style={{ height: 20, width: 20 }}><img width={13} src='/svg/EventDarkTeal.svg' /></span>{`Event${eventCount > 1 ? 's' : ''} : ${eventCount}`} </div>
                                ) : null}
                            </div>
                        }
                    </>
                    : null}
            </div>
            {((isMobile || isTablet) || ((isDesktop || isWideScreen) && channel?.type === ONE_TO_ONE)) ?
                <div className={`flex pr-sm ${(![SYSTEM_THOUSAND_GREENS_PUBLIC, REQUEST_CHAT_GROUP].includes(channel?.type) && !isDeletedUser1to1) && 'icon--wrapper cursor-pointer'}`}
                    onClick={() => {
                        if (channel?.type === REQUEST_CHAT_GROUP) {
                            setModal({
                                type: 'game-requester-info',
                                request: request,
                                width: 400,
                            })
                        } else {
                            handleOpenDetails()
                        }
                    }}
                >
                    {channelOptions()}
                </div>
                :
                null
            }
        </div>
    )
}

export default ChannelHeader
