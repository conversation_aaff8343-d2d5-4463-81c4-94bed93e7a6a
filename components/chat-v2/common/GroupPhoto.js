import CustomAvatar from "../CustomAvatar";
import { useContext, useRef, useState, useEffect } from "react";
import { ModalContext } from "../../../context/ModalContext";
import { uploadFile } from "../../../utils/upload";
import Loader, { ThreeDots } from "react-loader-spinner";
import streamOptions from "../../../constants/streamOptions";
import MESSAGES from "../../../constants/messages";
import constantOptions from "../../../constants/constantOptions";
import compressImage from "../../../utils/helper/compressImage";
import useThumbnail from "../../../hooks/useThumbnail";
import NameInitials from "../../common/NameInitials";

const { GROUP_PHOTO_SIZE_LIMIT } = streamOptions


/**
 * The group photo component that will have the logic for uploading a new photo
 * @param {*} param
 * @returns 
 */
const GroupPhoto = ({
    imageUrl = '',
    name = '',
    size = 118,
    isSelfAdminInChannel = false,
    getGroupPhoto,
    isPhotoLoading,
    setIsPhotoLoading = () => { },
    groupInfoSection = false,
    groupPhoto = '',
    bgColor = 'gray',
    bgImage,
    photoEmptyText = 'Add Group Icon',
    setBase64ForCrop,
    groupDp,
    groupUploadImage,
    uploadPath = "stream/group-display-pictures",
    
}) => {
    const { modal, setModal } = useContext(ModalContext)
    const uploadRef = useRef()
    const [photo, setPhoto] = useState()
    const [base64, setBase64] = useState()
    const [dp, setDp] = useState(groupDp)
    const [photoError, setPhotoError] = useState('')
    const [uploadImage, setUploadImage] = useState()
    const { thumbnailUrl } = useThumbnail(dp || imageUrl, 256)

    //We are setting the 
    //groupDp - cropped picture to be displayed &
    //& uploadImage - cropped image for creating firebase URL in ImageCropModal
    //to display it in the groupPhoto component we are setting it here.
    useEffect(() => {
        if (groupDp || groupUploadImage) {
            setDp(groupDp)
            setUploadImage(groupUploadImage)
        }
    }, [groupDp])

    useEffect(() => {
        if (photo) {
            const reader = new FileReader()
            reader.addEventListener('load', () =>
                setBase64(reader.result?.toString() || ''),
            )
            reader.readAsDataURL(photo)
        }
    }, [photo])

    useEffect(() => {
        if (base64) {
            //Setting base64 data in CreateGroupModal as we cannot open 2 popups at a time.
            if (modal?.type === "create-edit-group") {
                setBase64ForCrop(base64)
            } else {
                setModal({
                    type: 'crop-image',
                    image: base64,
                    width: 343,
                    setDp,
                    setUploadImage
                })
            }
        }
    }, [base64])

    useEffect(() => {
        if (uploadImage) {
            initUpload()
            setBase64('')
        }
    }, [uploadImage])

    const onSelectFile = (e) => {
        setPhotoError()
        if (e.target.files && e.target.files.length > 0) {
            if (e.target.files[0].size > GROUP_PHOTO_SIZE_LIMIT) {
                setPhotoError(MESSAGES?.GROUP_CHAT?.GROUP_PHOTO_SIZE_ERROR)
            } else if (!constantOptions?.DISPLAY_PICTURE_FORMATS.includes(e.target.files[0]?.type)) {
                setPhotoError(MESSAGES?.GROUP_CHAT?.GROUP_PHOTO_FORMAT_ERROR)
            } else {
                setPhoto(e.target.files[0]);
            }
        }
        e.target.value = ''
    }

    const initUpload = async () => {
        setIsPhotoLoading(true)
        //Else it will close the popup
        if (modal?.type !== 'create-edit-group') {
            setModal()
        }
        //Converting blob to file for compression
        const newFileFromBlob = new File([uploadImage], uploadImage.name, { type: uploadImage.type });
        const compressedFile = await compressImage({ file: newFileFromBlob })
        const firebaseFileURL = await uploadFile({ file: compressedFile, path: uploadPath })
        await getGroupPhoto(firebaseFileURL)
        setIsPhotoLoading(false)
    }

    return (
        <div className="flex-col flex-center">
            <div className="relative">
                {dp ? (
                    <div
                        onClick={() => {
                            if (groupPhoto) {
                                setModal({
                                    channelName: name,
                                    type: 'channel-photo',
                                    photo: groupPhoto,
                                    width: 380
                                })
                            }
                        }}
                        className="rounded-full"
                        style={{
                            height: 118,
                            width: 118,
                            backgroundImage: `url("${thumbnailUrl}")`,
                            backgroundPosition: 'center',/* Center the image */
                            backgroundRepeat: 'no-repeat', /* Do not repeat the image */
                            backgroundSize: 'cover' /* Resize the background image to cover the entire container */
                        }}>
                        {isPhotoLoading && (
                            <div
                                className="flex-center w-full h-full rounded-full"
                                style={{
                                    backgroundColor: 'rgba(0,0,0,0.3)',
                                }}>
                                <ThreeDots
                                    visible={true}
                                    height="25"
                                    width="25"
                                    color={"#FFFFFF"}
                                    radius="9"
                                    ariaLabel="three-dots-loading"
                                    wrapperStyle={{}}
                                    wrapperClass=""
                                />
                            </div>
                        )}
                    </div>
                ) : (<>
                    {(groupInfoSection) ? (
                        <div
                            className={`${imageUrl ? "cursor-pointer" : ""}`}
                            onClick={() => {
                                if (imageUrl) {
                                    setModal({
                                        channelName: name,
                                        type: 'channel-photo',
                                        photo: imageUrl,
                                        width: 380
                                    })
                                }
                            }}>
                            {imageUrl ? (
                                <div
                                    className="rounded-full mr-sm"
                                    style={{
                                        width: size,
                                        height: size,
                                        backgroundImage: `url("${thumbnailUrl}")`,
                                        backgroundPosition: 'center',
                                        backgroundSize: 'cover',
                                        borderColor: 'rgba(0,0,0,0)',
                                    }}></div>
                            ) : (
                                <div
                                    className="rounded-full mr-sm">
                                    <NameInitials
                                        user={name}
                                        height={size}
                                        width={size}
                                        fontSize={25}
                                        rounded="full"
                                    />
                                </div>
                            )}
                        </div>
                    ) : (
                        <div
                            onClick={() => uploadRef.current.click()}
                            className={` cursor-pointer flex-center px-md text-center rounded-full`}
                            style={{ height: size, width: size, backgroundColor: bgColor === 'offgray' ? '#DADADA' : '#808080' }}>
                            {photoEmptyText}
                        </div>
                    )}
                </>)}
                {
                    (isSelfAdminInChannel || photo || dp) &&
                    <div
                        className="flex-center rounded-full bg-white absolute shadow-lg cursor-pointer"
                        style={{
                            width: 30,
                            height: 30,
                            top: 0,
                            right: 0,
                        }}>
                        <img
                            onClick={() => uploadRef.current.click()}
                            src="/svg/edit-chat.svg"
                            width={15}
                            height={15}
                        />
                    </div>
                }
            </div>

            {photoError &&
                <div className="text-center text-sm text-red">
                    {photoError}
                </div>}
            <input
                onChange={(e) => {
                    if (isPhotoLoading) {
                        null
                    } else {
                        onSelectFile(e)
                    }
                }}
                ref={uploadRef}
                type="file"
                accept=".png,.jpg,.jpeg"
                style={{ display: 'none' }}
            />
        </div>
    )
}

export default GroupPhoto;