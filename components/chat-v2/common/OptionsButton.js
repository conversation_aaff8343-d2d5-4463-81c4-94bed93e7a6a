import React, { useRef, useEffect, useState } from 'react'
import useDropdown from '../../../hooks/useDropdown'
import { CSVLink } from 'react-csv';
import constantOptions from '../../../constants/constantOptions';

const OptionsButton = ({
    rotate = '',
    options,
    handleAfterAction = () => { },
    IconComponent = () => <img src="/svg/three-dots-gray.svg" width={4} height={18} />,
    top = 0,
    right = 0,
    left = -107,
    width,
    labelContainerStyle = 'border border-lightestgray',
    labelStyle = 'hover:text-darkteal hover:underline',
    iconStyle = '',
    dataForDownload,
    iconStyleHeight = 32,
    iconStylewidth = 32,
    activate = () => { },
    selectedOption,
    setOptionsActive = () => { },
    optionsImage
}) => {
    
    const {
        dropdownVisible,
        setDropdownVisible,
        DropdownWrapper,
    } = useDropdown()

    const [dropdownPosition, setDropdownPosition] = useState({ top, left });
    const wrapperRef = useRef(null);

    useEffect(() => {
        const calculatePosition = () => {
            const dropdownElement = wrapperRef.current;
            if (dropdownElement) {
                const { bottom } = dropdownElement.getBoundingClientRect();
                const { innerHeight } = window;
                if (bottom > innerHeight) {
                    const top = dropdownElement.offsetTop - (bottom - innerHeight);
                    setDropdownPosition({ top, left });
                }
            }
        };

        if (dropdownVisible) {
            calculatePosition();
            window.addEventListener("resize", calculatePosition);
        }

        return () => {
            window.removeEventListener("resize", calculatePosition);
        };
    }, [dropdownVisible]);

    const useOutsideAlerter = (ref) => {
        useEffect(() => {
            function handleClickOutside(event) {
                if (ref.current && !ref.current.contains(event.target)) {
                    setDropdownVisible(false);
                    setOptionsActive(false)
                    activate(false)
                }
            }

            // Bind the event listener
            document.addEventListener("mousedown", handleClickOutside);
            return () => {
                // Unbind the event listener on clean up
                document.removeEventListener("mousedown", handleClickOutside);
            };
        }, [ref]);
    }

    useOutsideAlerter(wrapperRef);

    return (
        <DropdownWrapper className="options--button self-center">
            <div className={`cursor-pointer flex-center ${iconStyle}`}
                style={{ height: iconStyleHeight, width: iconStylewidth, transform: `${rotate ? 'rotate(90deg)' : ''}` }}
                onClick={() => {
                    activate(true)
                    setDropdownVisible(true)
                    setOptionsActive(true)
                }}>
                <IconComponent isActive={dropdownVisible} activate={activate} />
            </div>
            {dropdownVisible && (
                <div
                    className={`absolute rounded-xl bg-white flex flex-col z-50 rounded shadow black font-medium text-12 text-black capitalize`}
                    style={{ ...dropdownPosition, width }}
                    onClick={(e) => e.stopPropagation()}
                    ref={wrapperRef}>
                    {Object.entries(options).length && Object.entries(options).map(([label, action], i) => (
                        <React.Fragment key={label}>
                            {dataForDownload?.length ? (
                                <CSVLink
                                    headers={
                                        constantOptions?.MY_CONTACTS_CSV_HEADERS
                                    }
                                    data={dataForDownload}
                                    filename="tg-my-contacts.csv">
                                    <div key={label}
                                        onClick={() => {
                                            activate(false)
                                            setDropdownVisible(false)
                                            setOptionsActive(false)
                                            handleAfterAction()
                                        }}
                                        className={`cursor-pointer py-sm px-md whitespace-no-wrap flex ${labelContainerStyle}`}>{label}</div>
                                </CSVLink>
                            ) : (
                                <div
                                    key={label}
                                    onClick={() => {
                                        action()
                                        setDropdownVisible(false)
                                        setOptionsActive(false)
                                        activate(false)
                                        handleAfterAction()
                                    }}
                                    className={`cursor-pointer py-sm px-sm whitespace-no-wrap flex ${labelContainerStyle}`}>
                                    <div className={`flex cursor-pointer hover:underline z-20 ${labelStyle} ${selectedOption === label ? "text-darkteal" : ""}`}>
                                        {optionsImage?.length &&
                                            <img className='mr-xs' src={optionsImage[i]} />
                                        }
                                        {label}
                                    </div>
                                </div>
                            )}
                        </React.Fragment>
                    ))}
                </div>)}
        </DropdownWrapper >
    )
}

export default OptionsButton