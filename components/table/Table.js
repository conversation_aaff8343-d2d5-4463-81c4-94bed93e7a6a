import React from 'react'

export default function Table({
    headings,
    children,
    clubPage,
    editing,
    showGrayBg = false,
    autoWidth = false,
    titleSize = 'sm',
    titleWeight = "thin",
    padding = "0"
}) {

    return (
        <div className={`overflow-x-scroll  ${padding}`}>
            <table className="table table-auto mt-md w-full min-h-[500px]">
                <thead className={`${showGrayBg ? 'bg-lightestgray' : ''} `}>
                    <tr className="" style={{}}>
                        {headings.map((t, i) => (
                            <th
                                key={i}
                                style={{ whiteSpace: 'nowrap' }}
                                className={`pl-md ${(showGrayBg && !clubPage && !autoWidth) ? "w-1/2" : ''} ${autoWidth && i === 0 ? 'px-sm' : ''} font-${titleWeight} text-${titleSize} text-black text-left ${showGrayBg ? 'pb-3 pt-3' : 'pb-md'} ${(t === 'Actions' && !autoWidth) && 'float-right mr-12'} ${(t === 'Actions' && autoWidth) && 'pl-lg'}`}>
                                {t}
                            </th>
                        ))}
                    </tr>
                </thead>
                {showGrayBg &&
                    <tbody>
                        <tr>
                            <td>
                                <div className="bg-mediumgray h-8" style={{ width: "100%" }}></div>
                            </td>
                        </tr>
                    </tbody>
                }
                <tbody className="">{children}</tbody>
            </table>
        </div>
    )
}
