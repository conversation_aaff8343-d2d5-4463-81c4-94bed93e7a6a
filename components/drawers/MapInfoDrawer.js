import React, { useContext, useEffect, useState } from 'react'
import { ThreeDots } from 'react-loader-spinner'
import { UserContext } from '../../pages/_app';
import END_POINTS from '../../constants/endpoints.json';
import constantOptions from '../../constants/constantOptions';
import analyticsEventLog from '../../utils/firebase/analytics';
import useClient from '../../graphql/useClient';
import { UNFAVORITE_CLUB, FAVORITE_CLUB } from '../../graphql/mutations/club'
import { UPDATE_ADDITIONAL_SETTINGS, UPDATE_USER } from '../../graphql/mutations/user'
import router from 'next/router';
import { ModalContext } from '../../context/ModalContext';
import MESSAGES from '../../constants/messages';
import { DrawerContext } from '../../context/DrawerContext';
import CustomButton from '../buttons/CustomButton';

const { TIERS } = constantOptions
const { MY_TG_HEADER: { MY_FRIENDS_LOWERCASE }, FRIENDS_TAB_OPTIONS: { ALL_FRIENDS } } = constantOptions
const { MAP_TOTAL_TG_GROUP_MEMBER, MAP_FRIENDS_PLAYED, MAP_ALL_MY_FRIENDS } = constantOptions?.DRAWER_TYPE

const MapInfoDrawer = ({ }) => {
  const { setModal } = useContext(ModalContext)
  const { drawer, setDrawer, searchActive } = useContext(DrawerContext)
  const [played, setPlayed] = useState(user?.playedClubs?.includes(selectedClub.id))
  const [favorited, setFavorited] = useState(user && user?.favorite_clubs.filter(({ club_id }) => club_id === selectedClub.id).length === 1)
  const { user, fetchUser, token } = useContext(UserContext)
  const [club, setClub] = useState(null);
  const [isShowLoading, setIsShowLoading] = useState(true);
  const [requestRestricted, setRequestRestricted] = useState(true)
  const [contactOnlyClub, setContactOnlyClub] = useState(false)
  const client = useClient();
  const userClubIds = user?.clubs?.map(club => club?.id)
  const [showAcceptRequestPopup, setShowAcceptRequestPopup] = useState();
  const [showCreateRequestPopup, setShowCreateRequestPopup] = useState();
  const { setClubsRecommended = () => { }, setActiveTab = () => { } } = drawer

  const {
    setIsMapInfoShow = () => { },
    selectedClub,
    filterActive,
    offerRestricted,
    setShowCreateRequestForm,
    setClubForRequest = () => { },
    dataForClub,
    setDataForClub
  } = drawer

  const isShowCreateRequest = () => {
    if (selectedClub?.color?.includes("blue")) {
      if (user?.private_network?.id) {
        return !!((club?.pnCount || 0) + club?.isEligibleForCreateRequest)
      } else {
        return !!(club?.isEligibleForCreateRequest)
      }

    } else if (!['grey', 'grey_contact', 'teal', 'teal_contact'].includes(selectedClub?.color)) {
      return true
    }
    return false
  }
  const canCreateRequest = isShowCreateRequest() && !contactOnlyClub


  useEffect(() => {
    if (user) {
      setShowAcceptRequestPopup(user?.additional_settings?.showAcceptRequestPopup);
      setShowCreateRequestPopup(user?.additional_settings?.showCreateRequestPopup);
    }
  }, [user])

  useEffect(() => {
    if (user, client) {
      if (user?.additional_settings?.showAcceptRequestPopup && showAcceptRequestPopup === false) {
        client.request(UPDATE_ADDITIONAL_SETTINGS, {
          user_id: user?.id,
          additional_settings: {
            ...user?.additional_settings,
            showAcceptRequestPopup: false
          }
        })
        fetchUser()
      }
      if (user?.additional_settings?.showCreateRequestPopup && showCreateRequestPopup === false) {
        client.request(UPDATE_ADDITIONAL_SETTINGS, {
          user_id: user?.id,
          additional_settings: {
            ...user?.additional_settings,
            showCreateRequestPopup: false
          }
        })
        fetchUser()
      }
    }
  }, [showAcceptRequestPopup, showCreateRequestPopup, user, client])

  useEffect(() => {
    if (user) {
      setShowAcceptRequestPopup(user?.additional_settings?.showAcceptRequestPopup);
      setShowCreateRequestPopup(user?.additional_settings?.showCreateRequestPopup);
    }
  }, [user])

  useEffect(() => {
    if (user, client) {
      if (user?.additional_settings?.showAcceptRequestPopup && showAcceptRequestPopup === false) {
        client.request(UPDATE_ADDITIONAL_SETTINGS, {
          user_id: user?.id,
          additional_settings: {
            ...user?.additional_settings,
            showAcceptRequestPopup: false
          }
        })
        fetchUser()
      }
      if (user?.additional_settings?.showCreateRequestPopup && showCreateRequestPopup === false) {
        client.request(UPDATE_ADDITIONAL_SETTINGS, {
          user_id: user?.id,
          additional_settings: {
            ...user?.additional_settings,
            showCreateRequestPopup: false
          }
        })
        fetchUser()
      }
    }
  }, [showAcceptRequestPopup, showCreateRequestPopup, user, client])


  useEffect(() => {
    setFavorited((
      user && user?.favorite_clubs.filter(({ club_id }) => club_id === selectedClub?.id).length === 1
    ));
    setIsShowLoading(true);
    getClubInfo();
    checkIfCreateRequest();
  }, [selectedClub, dataForClub])

  const checkIfCreateRequest = async () => {
    fetch(END_POINTS.CREATE_REQUEST_TOKEN_CHECK, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        user_id: user.id
      }),
    }).then((data) => data.json())
      .then((data) => {
        if (data.canCreate) {
          setRequestRestricted(null);
        } else {
          setRequestRestricted(data.message);
        }
      })
      .catch(err => console.error('Error', err));
  }

  const getClubInfo = async () => {

    if (selectedClub?.id || dataForClub?.clubId) {
      fetch(END_POINTS.FETCH_CLUB_DETAIL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ['Authorization']: `Bearer ${token}`,
        },
        body: JSON.stringify({
          userId: user.id,
          clubId: selectedClub.id || dataForClub?.clubId,
          clubColor: selectedClub?.color || dataForClub?.clubColor
        }),
      }).then((data) => data.json())
        .then((data) => {
          setClub(data);
          setIsShowLoading(false);
          setPlayed(data?.userPlayedAtClub)
          //Check if the club is a contact only Club
          if (!user.clubs?.includes(data?.clubs?.id) // if the user is not a part of this club
            && (data?.contacts || data?.friends) // and the user has contacts or friends
            && (
              (!user?.visibleToPublic && selectedClub?.color === "teal_contact") // this means it is a MY TG club
              || !data?.clubMemberCount // and there is no member in this club
            )
          ) {
            setContactOnlyClub(true)
          } else {
            setContactOnlyClub(false)
          }
          if (Object.entries(dataForClub).length !== 0) {
            window?.sessionStorage.removeItem("MAP_LIST_TYPE")
            // setDataForClub({})
          }
        })
    } else {
      setClub(null)
    }
  }

  const isShowSeeOffer = () => {
    if (['blue', 'blue_contact'].includes(selectedClub?.color)) {
      return true
    }
    return false
  }

  const isShowCreateOffer = () => {
    return (userClubIds?.includes(selectedClub?.id) && selectedClub?.ct !== constantOptions?.CLUB_TYPES?.VIRTUAL)
  }

  const setClubFavourite = () => {
    const user_id = user.id
    const club_id = selectedClub.id
    setFavorited(!favorited);
    if (favorited) {
      client
        .request(UNFAVORITE_CLUB, { user_id, club_id })
        .then(() => {
          fetchUser();
        })
    } else {
      client
        .request(FAVORITE_CLUB, { user_id, club_id })
        .then(() => {
          fetchUser();
        })
    }
  }

  const setClubPlayed = () => {
    setPlayed(!played);
    if (played) {
      client.request(UPDATE_USER, {
        user_id: user.id,
        user: {
          playedClubs: user.playedClubs.filter(
            (id) => id !== selectedClub.id
          ),
        },
      })
        .then(() => {
          fetchUser();
        })
    } else {
      client.request(UPDATE_USER, {
        user_id: user.id,
        user: {
          playedClubs: [...user.playedClubs, selectedClub.id],
        },
      })
        .then(() => {
          analyticsEventLog(constantOptions.USER_ACTIVITY.MAP_PLAYED.event, {
            id: Math.random(),
            userId: user?.id,
            from: constantOptions.USER_ACTIVITY.MAP_PLAYED.from
          })
          fetchUser();
        })
    }
  }

  const fetchClubSearchDetail = async () => {
    setIsShowLoading(true);
    try {
      fetch(END_POINTS.SEARCH_CLUB,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ['Authorization']: `Bearer ${token}`,
          },
          body: JSON.stringify({
            searchValue: selectedClub?.name,
            userId: user?.id,
            exactMatch: true
          }),
        })
        .then((data) => data.json())
        .then(({ clubs }) => {
          const clubDetail = clubs[0]
          setIsMapInfoShow(false)
          setClubForRequest(clubDetail)
          setIsShowLoading(false);
          // setShowCreateRequestForm(true)
          setActiveTab('create-request')
          router.push({
            pathname: '/dashboard/play',
            search: `?type=create-request`
          })
        })
    } catch (error) {
      console.error('fetchClubSearchDetail --->', error);
    }
  }

  const openCreateOfferModal = () => {
    //Checking of the club is muted
    let isClubMuted = false
    user?.clubs.map(club => {
      if (club?.id === selectedClub?.id && club?.muted) {
        isClubMuted = true
      }

    })

    if (isClubMuted) {
      setModal({
        title: 'Offer Limit',
        message: MESSAGES['can_create_offer']['CLUB_IS_MUTED'],
        type: 'warning',
      })
    }
    else {
      if (offerRestricted) {
        setModal({
          title: 'Offer Limit',
          message: offerRestricted,
          type: 'warning',
        })
      } else {
        setModal({
          title: 'Create Offer',
          img: {
            src: '/svg/offer-outline.svg',
            style: { height: 48, marginBottom: 10 },
          },
          width: 829,
          type: 'offer',
          club: club?.clubs,
          refresh: () => { }
        })
      }
    }
  }

  let mapInfoReturnData = JSON.parse(window?.sessionStorage.getItem('MAP_INFO_RETURN_DATA'))

  const openTgGroupsList = () => {
    setDrawer({
      type: MAP_TOTAL_TG_GROUP_MEMBER,
      club: club?.clubs,
      clubColor: selectedClub?.color,
      returnToMapInfo: () => setDrawer({ ...mapInfoReturnData, setActiveTab, setClubsRecommended, setClubForRequest }),
      canCreateRequest,
      source: "map-info-drawer",
      customBackClickHandler: openTgGroupsList,
    })
  }

  const openFriendsPlayedDrawer = () => {
    setDrawer({
      type: MAP_FRIENDS_PLAYED,
      club: club?.clubs,
      clubColor: selectedClub?.color,
      returnToMapInfo: () => setDrawer({ ...mapInfoReturnData, setActiveTab, setClubsRecommended, setClubForRequest }),
      canCreateRequest,
      source: "map-info-drawer",
      customBackClickHandler: openFriendsPlayedDrawer,
    })
  }

  const openTotalFriendsDrawer = () => {
    setDrawer({
      type: MAP_ALL_MY_FRIENDS,
      club: club?.clubs,
      clubColor: selectedClub?.color,
      returnToMapInfo: () => setDrawer({ ...mapInfoReturnData, setActiveTab, setClubsRecommended, setClubForRequest }),
      source: "map-info-drawer",
      canCreateRequest,
      customBackClickHandler: openTotalFriendsDrawer
    })
  }

  return (
    <div
      id="map-drawer-clubs"
      className='absolute bottom-0 right-0'
      style={{
        width: '500px',
        boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.14)',
        background: '#fff',
        top: (searchActive) ? 48 : 0,
        zIndex: 2
      }}>
      <div className="relative w-full h-full flex flex-col bg-white items-center z-3">
        <img
          src="/images/golf-sticks.png"
          width='100%'
          style={{
            position: 'absolute',
            bottom: 0,
            right: 0,
          }}
        />
        {isShowLoading && (
          <div className='absolute w-full h-full flex-center' style={{
            zIndex: 99999
          }}>
            <ThreeDots
              visible={true}
              height="50"
              width="50"
              color={"#098089"}
              radius="9"
              ariaLabel="three-dots-loading"
              wrapperStyle={{}}
              wrapperClass=""
            />
          </div>
        )}
        {
          club !== null && (
            <div
              className="px-md absolute h-full overflow-scroll w-full"
              style={{
                top: 0,
                zIndex: 9
              }}>
              <div className="clubInfoWrap">
                <div className="flex justify-between items-center pt-md">
                  <div className='flex items-center'>
                    <div
                      className="cursor-pointer pr-sm text-18 font-medium text-tealTier">
                      {club?.clubs?.name}
                    </div>
                    {
                      TIERS[club?.clubs?.lowest_visible_tier] ?
                        <div className="rounded-lg py-1 px-sm bg-tealTierBg text-tealTier font-medium text-12">
                          {
                            club?.clubs?.club_type === constantOptions?.CLUB_TYPES?.VIRTUAL ?
                              'Virtual' :
                              TIERS[club?.clubs?.lowest_visible_tier]
                          }
                        </div>
                        :
                        ''
                    }
                  </div>
                  <div
                    className='cursor-pointer'
                    onClick={() => {
                      setDrawer()
                      sessionStorage.removeItem('selected-club')
                    }}>
                    <img width={12} src="/svg/CrossIconBlack.svg" />
                  </div>
                </div>

                <div className="text-14 font-normal py-sm flex items-start text-grayLight">
                  <img width={11} src="/svg/location-grey.svg" style={{ marginRight: 5, marginTop: 3 }} />
                  {club?.clubs?.address}
                </div>

                {selectedClub?.tier !== constantOptions?.CLUB_TIERS?.TEAL && !contactOnlyClub &&
                  <div className="text-12 font-medium py-sm flex items-start text-darkteal">
                    <img src="/svg/request-acceptance.svg" width={16} style={{ marginRight: 5 }} />
                    Requests Accepted : {club?.request} %
                  </div>
                }

                {
                  !contactOnlyClub && <div className="text-12 font-medium py-sm flex items-start text-black">
                    <img src="/svg/TotalMembers.svg" width={12} style={{ marginRight: 5, marginTop: 3 }} />
                    Total Members : {club?.totalMemberCount}
                  </div>
                }

                {
                  <>
                    {/* For Virtual clubs these options will not be shown */}
                    {/* 
                    Total Contacts Found
                  */}
                    {club?.contacts ? <div
                      className="bg-white shadow py-4 px-8 border border-darkteal rounded-7px cursor-pointer map-window mt-5 text-12 font-medium flex items-start text-black"
                      onClick={() =>
                        router.push({
                          pathname: '/dashboard/my-tg',
                          search: `?type=my-friends&subtype=all-friends&club_name=${club?.clubs?.name}`
                        })
                      }
                    >
                      <div className="w-3/5 flex">
                        <p className="w-full flex items-center">
                          <img src="/svg/TotalContacts.svg"
                            width={13.5}
                            style={{ marginRight: 5, marginTop: 0, display: 'inline-block' }} />
                          Total Contacts Found : {club?.contacts}
                        </p>
                      </div>
                      <div className="w-2/5 flex text-right">
                        <p
                          className="w-full text-tealTier cursor-pointer"
                        >
                          View Contacts
                          <img
                            src="/markers/right-chevron.svg"
                            height={5} width={5}
                            style={{
                              marginLeft: 5,
                              display: 'inline-block',
                              verticalAlign: 'baseline'
                            }}
                          />
                        </p>
                      </div>
                    </div> : null}

                    {/* 
                    Total Friends Found
                  */}
                    {club?.friends ? <div
                      className="bg-white shadow py-4 px-8 border border-darkteal rounded-7px cursor-pointer map-window mt-5 text-12 font-medium flex items-start text-black"
                      onClick={() => {
                        openTotalFriendsDrawer()
                      }}
                    >
                      <div className="w-3/5 flex">
                        <p className="w-full flex items-center">
                          <img src="/svg/TotalFriends.svg"
                            width={13.5}
                            style={{ marginRight: 5, marginTop: 0, display: 'inline-block' }} />
                          Total Friends Found : {club?.friends}
                        </p>
                      </div>
                      <div className="w-2/5 flex text-right cursor-pointer">
                        <p
                          className="w-full text-tealTier"
                        >
                          View Friends
                          <img
                            src="/markers/right-chevron.svg"
                            height={5} width={5}
                            style={{
                              marginLeft: 5,
                              display: 'inline-block',
                              verticalAlign: 'baseline'
                            }}
                          />
                        </p>
                      </div>
                    </div> : null}

                    {/* 
                    Friends Played
                  */}
                    {club?.friendsPlayed ? <div
                      className="bg-white shadow py-4 px-8 border border-darkteal rounded-7px cursor-pointer map-window mt-5 text-12 font-medium flex items-start text-black"
                      onClick={() => {
                        openFriendsPlayedDrawer()
                      }}
                    >
                      <div className="w-3/5 flex">
                        <p className="w-full flex items-center">
                          <img src="/svg/FriendsPlayed.svg"
                            width={18}
                            style={{ marginRight: 5, marginTop: 0, display: 'inline-block' }} />
                          Friend's Played : {club?.friendsPlayed}
                        </p>
                      </div>
                      <div className="w-2/5 flex text-right">
                        <p
                          className="w-full text-tealTier cursor-pointer"
                        >
                          View All
                          <img
                            src="/markers/right-chevron.svg"
                            height={5} width={5}
                            style={{
                              marginLeft: 5,
                              display: 'inline-block',
                              verticalAlign: 'baseline'
                            }}
                          />
                        </p>
                      </div>
                    </div> : null}

                    {/* Total TG Group Members */}
                    {club?.tgGroupMembersCount
                      ? <div
                        className="bg-white shadow py-4 px-8 border border-darkteal rounded-7px cursor-pointer map-window mt-5 text-12 font-medium flex items-start text-black"
                        onClick={() => { openTgGroupsList() }}>
                        <div className="w-3/5 flex">
                          <p className="w-full flex items-center">
                            <img src="/svg/TGGroup.svg"
                              width={18}
                              style={{ marginRight: 5, marginTop: 0, display: 'inline-block' }} />
                            Total TG Group Members : {club?.tgGroupMembersCount}
                          </p>
                        </div>
                        <div className="w-2/5 flex text-right">
                          <p
                            className="w-full text-tealTier cursor-pointer"
                          >
                            View All
                            <img
                              src="/markers/right-chevron.svg"
                              height={5} width={5}
                              style={{
                                marginLeft: 5,
                                display: 'inline-block',
                                verticalAlign: 'baseline'
                              }}
                            />
                          </p>
                        </div>
                      </div>
                      : ''}

                    {
                      // If the club is virtual, then we don't have to show any action buttons
                      selectedClub?.tier !== constantOptions?.CLUB_TIERS?.TEAL &&
                      <>
                        {/* 
                        Played Toggle
                      */}
                        <div
                          className="mt-5 text-16 font-medium py-sm flex items-start text-black"
                          style={{
                            padding: '1rem 1.8rem',
                            borderRadius: '6px',
                            background: '#fff',
                            boxShadow: '0px 2px 14px 1px rgb(0 0 0 / 6%)'
                          }}
                        >
                          <div className="w-3/5 flex">
                            <p className="w-full">
                              Played
                            </p>
                          </div>
                          <div className="w-2/5 flex justify-end">
                            <div
                              onClick={() => setClubPlayed()}
                              className="relative cursor-pointer"
                              style={{ width: 30 }}>
                              <div
                                className={`w-[30px] h-[8.5px] top-[4px] rounded-full ${played ? 'bg-darkteal' : 'bg-gray'
                                  } absolute opacity-50`}
                                style={{
                                  transition:
                                    'background-color 200ms ease-in-out',
                                }}
                              />
                              <div
                                className={`w-[17px] h-[17px] rounded-full ${played ? 'bg-darkteal' : 'bg-gray'
                                  } relative`}
                                style={{
                                  transform: `translateX(${played ? '15' : '0'
                                    }px)`,
                                  transition:
                                    'background-color 200ms ease-in-out, transform 200ms ease-in-out',
                                }}
                              />
                            </div>
                          </div>
                        </div>

                        {/* 
                        Favorite Toggle
                      */}
                        {
                          (!userClubIds?.includes(club?.clubs?.id) && !contactOnlyClub) ?
                            <div
                              className="mt-5 text-16 font-medium py-sm flex items-start text-black"
                              style={{
                                padding: '1rem 1.8rem',
                                borderRadius: '6px',
                                background: '#fff',
                                boxShadow: '0px 2px 14px 1px rgb(0 0 0 / 6%)'
                              }}
                            >
                              <div className="w-3/5 flex">
                                <p className="w-full">
                                  Favorite
                                </p>
                              </div>
                              <div className="w-2/5 flex justify-end items-center">
                                <div
                                  onClick={() => setClubFavourite()}
                                  className="relative cursor-pointer"
                                  style={{ width: 30 }}>
                                  <div
                                    className={`w-[30px] h-[8.5px] top-[4px] rounded-full ${favorited ? 'bg-darkteal' : 'bg-gray'
                                      } absolute opacity-50`}
                                    style={{
                                      transition:
                                        'background-color 200ms ease-in-out',
                                    }}
                                  />
                                  <div
                                    className={`w-[17px] h-[17px] rounded-full ${favorited ? 'bg-darkteal' : 'bg-gray'
                                      } relative`}
                                    style={{
                                      transform: `translateX(${favorited ? '15' : '0'
                                        }px)`,
                                      transition:
                                        'background-color 200ms ease-in-out, transform 200ms ease-in-out',
                                    }}
                                  />
                                </div>
                              </div>
                            </div>
                            : ''
                        }

                        {/* 
                        Buttons Section
                      */}
                        <div
                          className="mt-5 py-sm flex items-start text-black justify-end"
                          style={{
                            justifyContent: 'justify-content: space-around'
                          }}
                        >
                          {isShowSeeOffer() ? (
                            <CustomButton
                              height={45} width={211}
                              text='See Offer'
                              buttonImage='/markers/see-offer.svg'
                              onClick={() =>
                                router.push({
                                  pathname: '/dashboard/play',
                                  search: `?type=offers&clubname=${club?.clubs?.name}`
                                })
                              }
                            />
                          ) : ''}
                          {(canCreateRequest && !contactOnlyClub) ? (
                            <CustomButton
                              height={45} width={211}
                              text='Create Request'
                              buttonImage='/markers/create-request.svg'
                              onClick={() => {
                                if (requestRestricted) {
                                  setModal({
                                    title: 'Request Limit',
                                    message: requestRestricted,
                                    type: 'warning',
                                  })
                                } else {
                                  setClubsRecommended(false)
                                  fetchClubSearchDetail()
                                }
                              }}
                            />
                          ) : ''}
                          {isShowCreateOffer() ? (
                            <CustomButton
                              height={45} width={211}
                              text='Create Offer'
                              buttonImage='/svg/offer.svg'
                              buttonImageWidth={25}
                              imageMarginBottom='0'
                              onClick={openCreateOfferModal}
                            />
                          ) : ''}
                        </div>
                      </>
                    }
                  </>}
              </div>
            </div>
          )
        }
      </div>
    </div>
  )
}

export default MapInfoDrawer