import React, { useContext, useState, useEffect } from 'react'
import { CustomStreamContext } from '../../context/CustomStreamContext'
import { UserContext } from '../../pages/_app'
import { ModalContext } from '../../context/ModalContext'
import CustomButton from '../buttons/CustomButton'
import ENDPOINTS from '../../constants/endpoints.json'
import moment from 'moment'
import { useChatContext } from 'stream-chat-react'
import { ThreeDots } from 'react-loader-spinner'
import { useRouter } from 'next/router'
import constantOptions from '../../constants/constantOptions'
import InfoActionOptionWrapper from '../chat-v2/common/InfoActionOptionWrapper'
import streamOptions from '../../constants/streamOptions'
import useCheckDeviceScreen from '../../hooks/useCheckDeviceScreen'
import { DrawerContext } from '../../context/DrawerContext'
import toastNotification from '../../utils/notifications/toastNotification'
import useClient from '../../graphql/useClient'
import { UPDATE_ADDITIONAL_SETTINGS } from '../../graphql/mutations/user'
import AmbassadorTag from '../common/AmbassadorTag'
import FcmTag from '../common/FcmTag'
import SuperHostTag from '../common/SuperHostTag'
import ProfilePhoto from '../profile/ProfilePhoto'
import NameInitials from '../common/NameInitials'
import TierTag from '../common/TierTag'
import GolfClubIcon from '../icons/GolfClubIcon'
import MutualGroupsIcon from '../icons/MutualGroupsIcon'
import ReviewItem from '../club/ReviewItem'
import useFriends from '../../hooks/chat-v2/useFriends'
import GameReviewListItem from '../common/GameReviewListItem'
import formatPhoneNumber from '../../utils/helper/formatPhoneNumber';
import useMaintenanceStatus from '../../hooks/useMaintenanceStatus'

const {
    GLOBAL_GAME_REVIEWS
} = constantOptions.DRAWER_TYPE

const {
    MY_TG_HEADER: { MY_FRIENDS_LOWERCASE },
    FRIENDS_TAB_OPTIONS: { RECEIVED },
} = constantOptions
const {
    ONE_TO_ONE,
    SYSTEM_THOUSAND_GREENS_PUBLIC,
} = streamOptions?.CHANNEL?.TYPES

const GlobalProfileInfo = ({
    optionalCallbackAfterSendMessage = () => { },
    source = 'chat',
}) => {
    const client = useClient()
    const { user, token, streamChatClient, fetchUser } = useContext(UserContext)
    const {
        setOtherUserId,
        otherUserId,
        channelsBlockState,
        updateBlockStateOfChannel,
        setProfileRedirectionFrom,
    } = useContext(CustomStreamContext)
    const { drawer, setDrawer, searchActive } = useContext(DrawerContext)
    const { modal, setModal } = useContext(ModalContext)
    const [profileInfo, setProfileInfo] = useState()
    const { channel, setActiveChannel } =
        source === 'chat'
            ? useChatContext()
            : { channel: {}, setActiveChannel: () => { } }
    const [loading, setLoading] = useState(false)
    const [showAddFriend, setshowAddFriend] = useState(false)
    const isPnOnlyUser =
        profileInfo?.private_network_id && profileInfo?.userClubs.length === 0
    const [messagingChannelId, setMessagingChannelId] = useState(
        channel?.data?.id
    )
    const [isBlockedByMe, setIsBlockedByMe] = useState(
        drawer?.source === 'my-contacts'
            ? channelsBlockState[drawer?.channelId]?.youHaveBlocked
            : channelsBlockState[channel?.data?.id]?.youHaveBlocked
    )
    const [isBlocked, setIsBlocked] = useState(
        drawer?.source === 'my-contacts'
            ? channelsBlockState[drawer?.channelId]?.blocked
            : channelsBlockState[channel?.data?.id]?.blocked
    )
    const { isMobile, isTablet, isDesktop } = useCheckDeviceScreen()
    const router = useRouter()
    const [showAcceptRequestPopup, setShowAcceptRequestPopup] = useState();
    const [showCreateRequestPopup, setShowCreateRequestPopup] = useState();
    const [requestRestricted, setRequestRestricted] = useState(true)
    const [unmutedUserClubs, setUnmutedUserClubs] = useState([])
    const [clubsForSelection, setClubsForSelection] = useState([])
    const [ambassadorTagHovered, setAmbassadorTagHovered] = useState(false)
    const { refreshList = () => { } } = drawer
    const { myFriendsId } = useContext(CustomStreamContext)
    const { allFriendsId } = useFriends();
    const [showPhoneCopy, setShowPhoneCopy] = useState(false)
    const [showEmailCopy, setShowEmailCopy] = useState(false)
    const { maintenanceStatus } = useMaintenanceStatus()

    useEffect(() => {
        const userClubIds = user?.clubs?.map((obj) => obj.id)
        const filteredArray1 = unmutedUserClubs?.filter(
            (obj) => !userClubIds?.includes(obj.id)
        )
        const filteredArray2 = unmutedUserClubs.filter(
            (obj) => !userClubIds?.includes(obj.id)
        )

        setClubsForSelection([
            ...new Set(
                [...filteredArray1, ...filteredArray2]?.map((club) => club?.name)
            ),
        ])
    }, [unmutedUserClubs, user?.clubs])

    let isMounted = 1

    useEffect(() => {
        const abortController = new AbortController()
        getUserProfile()
        canSendRequest()

        // If channel type is not messaging then we need to fetch channel details and update the context
        // with the block state of the channel.
        if (
            channel?.data?.type !== streamOptions.CHANNEL.TYPES.ONE_TO_ONE &&
            drawer?.source !== 'my-contacts'
        ) {
            getOneToOneChannel()
        }

        return () => {
            isMounted = 0
            abortController.abort()
        }
    }, [otherUserId])

    const refreshViewProfile = () => {
        getUserProfile()
        canSendRequest()
    }

    useEffect(() => {
        setIsBlockedByMe(
            drawer?.source === 'my-contacts'
                ? channelsBlockState[drawer?.channelId]?.youHaveBlocked
                : channelsBlockState[messagingChannelId]?.youHaveBlocked
        )
        setIsBlocked(
            drawer?.source === 'my-contacts'
                ? channelsBlockState[drawer?.channelId]?.blocked
                : channelsBlockState[messagingChannelId]?.blocked
        )
    }, [channelsBlockState, messagingChannelId])

    useEffect(() => {
        if (user) {
            checkIfCreateRequest()
            setShowAcceptRequestPopup(
                user?.additional_settings?.showAcceptRequestPopup
            )
            setShowCreateRequestPopup(
                user?.additional_settings?.showCreateRequestPopup
            )
        }
    }, [])

    useEffect(() => {
        if (user && client) {
            if (
                user?.additional_settings?.showAcceptRequestPopup &&
                showAcceptRequestPopup === false
            ) {
                client.request(UPDATE_ADDITIONAL_SETTINGS, {
                    user_id: user?.id,
                    additional_settings: {
                        ...user?.additional_settings,
                        showAcceptRequestPopup: false,
                    },
                })
                fetchUser()
            }
            if (
                user?.additional_settings?.showCreateRequestPopup &&
                showCreateRequestPopup === false
            ) {
                client.request(UPDATE_ADDITIONAL_SETTINGS, {
                    user_id: user?.id,
                    additional_settings: {
                        ...user?.additional_settings,
                        showCreateRequestPopup: false,
                    },
                })
                fetchUser()
            }
        }
    }, [showAcceptRequestPopup, showCreateRequestPopup, user, client])

    const checkIfCreateRequest = async () => {
        fetch(ENDPOINTS.CREATE_REQUEST_TOKEN_CHECK, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                user_id: user.id,
                isRequestToAll: false
            }),
        })
            .then((data) => data.json())
            .then((data) => {
                if (data.canCreate) {
                    setRequestRestricted(null)
                } else {
                    setRequestRestricted(data.message)
                }
            })
            .catch((err) => console.error('Error', err))
    }

    const getUserProfile = async () => {
        setLoading(true)
        try {
            await fetch(`${ENDPOINTS?.GET_USER_PROFILE}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    ['Authorization']: `Bearer ${token}`,
                },
                body: JSON.stringify({
                    userId: user?.id,
                    golferId: otherUserId,
                }),
            })
                .then((data) => data.json())
                .then((data) => {
                    if (isMounted) {
                        setProfileInfo(data?.golferInfo[0])
                        setUnmutedUserClubs(
                            data?.golferInfo[0]?.userClubs.filter(
                                (club) => !club?.muted
                            )
                        )
                    }
                })
                .catch((error) => {
                    console.log(error)
                })
            if (isMounted) {
                setLoading(false)
                setModal()
            }
        } catch (error) {
            console.log('User Profile------>', error)
        }
    }

    const handleCreateRequest = (requestType, friendId, clubs) => {
        if (requestRestricted) {
            setModal({
                title: 'Request Limit',
                message: requestRestricted,
                type: 'warning',
            })
        } else {
            if (showCreateRequestPopup) {
                setModal({
                    title: '',
                    type: 'request-confirmation',
                    width: 500,
                    requestType,
                    friendId,
                    clubs,
                    drawer,
                    setShowCreateRequestForm: (val) => {
                        setModal({
                            requestType,
                            title: 'Create Request',
                            friendId,
                            clubs,
                            img: {
                                src: '/svg/request-offer.svg',
                                style: {
                                    height: 80,
                                    marginBottom: 10,
                                },
                            },
                            type: 'request-against-offer',
                            fetchTabsCount: drawer?.fetchTabsCount,
                            fetchUnreadMessageStatus: drawer?.fetchUnreadMessageStatus,
                            refresh: drawer?.refresh
                        })
                    },
                })
            } else {
                setModal({
                    requestType,
                    friendId,
                    clubs,
                    drawer,
                    title: 'Create Request',
                    img: {
                        src: '/svg/request-offer.svg',
                        style: {
                            height: 80,
                            marginBottom: 10,
                        },
                    },
                    type: 'request-against-offer',
                    fetchTabsCount: drawer?.fetchTabsCount,
                    fetchUnreadMessageStatus: drawer?.fetchUnreadMessageStatus,
                    refresh: drawer?.refresh
                })
            }
        }
    }

    //API: Withdraw friend request
    const withdrawRequest = async (id) => {
        try {
            setLoading(true)
            await fetch(ENDPOINTS?.WITHDRAW_REQUEST, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    ['Authorization']: `Bearer ${token}`,
                },
                body: JSON.stringify({
                    userId: user?.id,
                    requestId: id,
                }),
            })
                .then((data) => data.json())
                .then(async () => {
                    setLoading(false)
                    refreshViewProfile()
                    await refreshList()
                })
                .catch((error) => {
                    console.log(error)
                })
        } catch (error) {
            setLoading(false)
            console.log(error)
        }
    }

    const blockButton = () => {
        if (isBlockedByMe) {
            return (
                <InfoActionOptionWrapper
                    onClick={() =>
                        setModal({
                            width: 488,
                            type: 'block-unblock-user',
                            action: 'unblock',
                            otherUserId: otherUserId,
                            channelId:
                                drawer?.source === 'my-contacts'
                                    ? drawer?.channelId
                                    : messagingChannelId,
                            refreshList: getOneToOneChannel,
                        })
                    }>
                    <img src="/svg/TealCrossIcon.svg" />{' '}
                    <div className="text-darkteal font-medium">Unblock User</div>
                </InfoActionOptionWrapper>
            )
        } else {
            return (
                <InfoActionOptionWrapper
                    onClick={() => {
                        setModal({
                            width: 488,
                            type: 'block-unblock-user',
                            action: 'block',
                            otherUserId: otherUserId,
                            channelId:
                                drawer?.source === 'my-contacts'
                                    ? drawer?.channelId
                                    : messagingChannelId,
                            refreshList: getOneToOneChannel,
                        })
                    }}>
                    <img src="/svg/RedCrossIcon.svg" />{' '}
                    <div className="text-red font-medium">Block User</div>
                </InfoActionOptionWrapper>
            )
        }
    }

    const socialInfo = () => {
        return (
            <div className="flex flex-col py-sm px-lm">
                <div className='text-16 font-medium mb-xs'>Social Link(s)</div>
                {
                    (profileInfo?.facebook || profileInfo?.linkedin) &&
                    <div className="flex gap-2">
                        {profileInfo?.facebook && <a className="flex mb-sm px-sm w-[100%] h-[36px] text-14 cursor-pointer border-2 border-offwhite4 rounded-lg justify-between" href={profileInfo?.facebook} target="_blank">
                            <div className="flex items-center">
                                <img src="/icons/facebook.svg" className="mr-sm" />
                                Facebook
                            </div>
                            <img width={12} src="/icons/NewWindow.svg" />
                        </a>}
                        {profileInfo?.linkedin && <a className="flex px-sm w-[100%] h-[36px] text-14 cursor-pointer border-2 border-offwhite4 rounded-lg justify-between" href={profileInfo?.linkedin} target="_blank">
                            <div className="flex items-center">
                                <img src="/icons/linkedin.svg" className="mr-sm" />
                                Linkedin
                            </div>
                            <img width={12} src="/icons/NewWindow.svg" />
                        </a>}

                    </div>
                }
            </div>
        )
    }

    const redirectToMyTg = () => {
        router.push({
            pathname: `/dashboard/my-tg`,
            search: `?type=${MY_FRIENDS_LOWERCASE}&subtype=${RECEIVED}`,
        })
    }

    const sendMessage = async () => {
        setLoading(true)
        try {
            await fetch(ENDPOINTS?.CREATE_ONE_TO_ONE_CHANNEL_STREAM, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    ['Authorization']: `Bearer ${token}`,
                },
                body: JSON.stringify({
                    userId: user?.id,
                    otherUserId: otherUserId,
                }),
            })
                .then((data) => data.json())
                .then(async (data) => {
                    if (!data?.status) {
                        toastNotification({
                            type: constantOptions.TOAST_TYPE.ERROR,
                            message: data?.message,
                        })
                    } else {
                        if (data?.channel?.id && isMounted) {
                            if (source === 'chat' && drawer?.source !== 'map') {
                                const channel = await streamChatClient.queryChannels(
                                    {
                                        members: { $in: [user?.id] },
                                        id: { $eq: data?.channel?.id },
                                        $or: [
                                            { hidden: { $eq: true } },
                                            { hidden: { $eq: false } },
                                        ],
                                    }
                                )

                                setActiveChannel(channel?.[0], {
                                    limit: 1,
                                    offset: 0,
                                })
                            } else {
                                router.push({
                                    pathname: `/dashboard/my-tg`,
                                    search: `?type=chat&channel_id=${data?.channel?.id}`,
                                })
                            }

                            optionalCallbackAfterSendMessage()
                        }
                    }
                })
                .catch((error) => {
                    console.log(error)
                })
        } catch (error) {
            console.log('Send message--->', error)
        }
    }

    const sendMessageHandler = () => {
        if (
            (channel?.type !== ONE_TO_ONE &&
                ['profile', 'profile-info'].includes(drawer?.type)) ||
            channel?.type === SYSTEM_THOUSAND_GREENS_PUBLIC
        ) {
            window?.localStorage.setItem(
                'SELECTED_PEGBOARD',
                JSON.stringify({ selectedPegboard: drawer?.selectedPegboard })
            )
            window?.sessionStorage.setItem(
                'MAP_LIST_TYPE',
                JSON.stringify({
                    type: drawer?.club?.type,
                    club: drawer?.club?.club,
                    clubColor: drawer?.club?.clubColor,
                })
            )

            sendMessage()
        } else {
            setDrawer()
        }
    }

    const canSendRequest = async () => {
        try {
            await fetch(`${ENDPOINTS?.CAN_SEND_REQUEST}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    ['Authorization']: `Bearer ${token}`,
                },
                body: JSON.stringify({
                    userId: user?.id,
                    requesterId: otherUserId,
                }),
            })
                .then((data) => data.json())
                .then((data) => {
                    if (isMounted) {
                        setshowAddFriend(data?.showAddFriend)
                    }
                })
                .catch((error) => {
                    console.log(error)
                })
        } catch (error) {
            console.log('Profile Info------>', error)
        }
    }

    //Get 1:1 channel id between the user and golfer
    const getOneToOneChannel = async () => {
        try {
            await fetch(`${ENDPOINTS?.CREATE_ONE_TO_ONE_CHANNEL_STREAM}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    ['Authorization']: `Bearer ${token}`,
                },
                body: JSON.stringify({
                    userId: user?.id,
                    otherUserId: otherUserId,
                }),
            })
                .then((data) => data.json())
                .then(async (data) => {
                    if (isMounted) {
                        updateBlockStateOfChannel(data?.channel?.data)
                        setMessagingChannelId(data?.channel?.id)

                        const [channel] = await streamChatClient.queryChannels({
                            id: { $eq: data?.channel?.id },
                            $or: [
                                { hidden: { $eq: true } },
                                { hidden: { $eq: false } },
                            ],
                        })
                        if (!!channel) {
                            await channel.watch()
                        }
                    }
                })
                .catch((error) => {
                    console.log(error)
                })
        } catch (error) {
            console.log('getOneToOneChannel------>', error)
        }
    }

    const handleCopy = (text, type) => {
        navigator.clipboard.writeText(text)
        toastNotification({
            type: constantOptions.TOAST_TYPE.SUCCESS,
            message: `${type} copied to clipboard`
        })
    }
    return (
        <div
            className={`fade-in text-black flex flex-col shadow-lg allow-scroll bg-white`}
            style={{
                height: '100vh',
                width: isMobile ? "100%" : "500px",
                overflowY: 'auto',
                position: 'fixed',
                top: 0,
                right: 0,
                zIndex: 9999,
                WebkitOverflowScrolling: 'touch' // For smooth scrolling on iOS
            }}
        >
            {loading ? (
                <div className="flex-center w-full h-full">
                    <ThreeDots
                        visible={true}
                        height="50"
                        width="50"
                        color="#098089"
                        radius="9"
                        ariaLabel="three-dots-loading"
                        wrapperStyle={{}}
                        wrapperClass=""
                    />
                </div>
            ) : (
                <>
                    <div className={`flex justify-between flex-center relative bg-lightestgray min-h-[58px]`} >
                        {drawer?.customBackClickHandler &&
                            <div className='flex text-16 font-medium absolute cursor-pointer text-gray'
                                style={{ left: 10 }}
                                onClick={() => {
                                    drawer?.customBackClickHandler()
                                }}
                            >
                                <img width={15}
                                    style={{ transform: 'rotate(90deg)', marginRight: 10 }}
                                    src='/svg/arrow-down.svg' />Back
                            </div>
                        }
                        <div className='text-21 font-medium'>Profile Info</div>
                        <div
                            onClick={() => {
                                setDrawer()
                                setOtherUserId(null)
                            }}
                            className="cursor-pointer absolute" style={{ right: 20, top: 20 }}>
                            <img src='/svg/CrossIconBlack.svg' />
                        </div>
                    </div>
                    <div className="min-h-[130px] w-full relative"
                        style={{
                            background: "#9AD0C2",
                            background: "linear-gradient(90deg, rgba(154, 208, 194, 1) 0%, rgba(236, 244, 214, 1) 50%)",
                        }}>
                        <div className="cursor-pointer absolute bottom-[-24px] left-[20px] outline outline-white outline-4 rounded-full"
                            onClick={() => {
                                if (profileInfo?.profilePhoto) {
                                    setModal({
                                        channelName: profileInfo?.name,
                                        type: 'channel-photo',
                                        photo: profileInfo?.profilePhoto,
                                        width: 380,
                                    })
                                }
                            }}
                        >
                            {/* {profileInfo?.tg_ambassador_visibility && */}
                            <div className='absolute cursor-pointer'
                                onMouseLeave={() => setAmbassadorTagHovered(false)}
                                onMouseEnter={() => { setAmbassadorTagHovered(true) }}
                                style={{ zIndex: 1, right: ambassadorTagHovered ? -94 : 0, top: 2 }}>
                                <AmbassadorTag smallTag={!ambassadorTagHovered} />
                            </div>
                            {/* } */}
                            {profileInfo?.profilePhoto ? (
                                <ProfilePhoto
                                    initialPhoto={profileInfo?.profilePhoto}
                                    source={"profile-info"}
                                    radius='full'
                                    height={92}
                                    width={92}
                                />
                            ) : (
                                <div className={``}>
                                    <NameInitials
                                        height={92}
                                        width={92}
                                        user={{ first_name: profileInfo?.name }}
                                        fontSize={50}
                                        rounded={'full'}
                                    />
                                </div>
                            )}
                        </div>
                    </div>
                    <div className='flex justify-end pt-sm px-lm items-center gap-2'>
                        <div className='text-gray uppercase text-11'>Member since {moment(profileInfo?.memberSince).format('YYYY')}</div>
                        <TierTag tier={profileInfo?.tier} />
                    </div>

                    <div className='flex-col py-md px-lm'>
                        <div className={`text-20`}>{profileInfo?.name}</div>
                        <div className='flex pt-xs'>
                            {profileInfo?.is_tg_founder && <FcmTag className={''} />}
                            {profileInfo?.isSuperHost && <SuperHostTag />}
                        </div>
                        <div className="pt-sm">
                            {profileInfo?.about_yourself}
                        </div>

                        {isBlocked ? null : (
                            <div className="flex pt-sm gap-2">
                                <div className="text-grayLight rounded-lg border border-offwhite4 h-[22px] px-[10px]">Age:<span className='text-black pl-xs'>{profileInfo?.age}</span></div>
                                <div className="text-grayLight rounded-lg border border-offwhite4 h-[22px] px-[10px]">Golf Index:<span className='text-black pl-xs'>{profileInfo?.golfIndex}</span></div>
                            </div>
                        )}

                        {profileInfo?.gamesPlayed?.total ?
                            <div className='flex pt-sm gap-2 items-center'>
                                <img src="/svg/Game-Played.svg" />
                                <div className='text-grayLight'>Games Played: <span className='text-green'>{profileInfo?.gamesPlayed?.total}</span></div>
                            </div> : null
                        }
                    </div>
                    <div className="flex pb-sm px-lm">
                        {/* View Request button */}
                        {(!profileInfo?.isFriends || !profileInfo?.hasActiveRequestsBetween) &&
                            profileInfo?.friendRequestId &&
                            !profileInfo?.isFriendRequestDeclined &&
                            profileInfo?.isFriendRequestReceived ?(
                                <CustomButton
                                    onClick={() => {
                                        if (drawer?.source === 'received') {
                                            setDrawer()
                                        } else {
                                            redirectToMyTg()
                                        }
                                    }}
                                    text="View"
                                    height={40}
                                    width={'100%'}
                                    imageRightMargin={'10px'}
                                    imageMarginBottom={'0'}
                                    color="tealTierBg"
                                    borderColor="border-darkteal"
                                    textColor="darkteal"
                                    cssClassName="py-xs px-md hover:bg-hover"
                                    buttonImage="/icons/eye.svg"
                                />
                            ):null}
                        {!isBlocked ?<CustomButton
                            onClick={() => {
                                sendMessageHandler()
                                setProfileRedirectionFrom()
                            }}
                            borderColor='border-darkteal'
                            imageMarginBottom='0'
                            height={40}
                            width={"100%"}
                            text="Message"
                            color="tealTierBg"
                            textColor="darkteal"
                            cssClassName="hover:bg-hover"
                            buttonImage="/icons/message.svg"
                        />:null}
                        {showAddFriend &&
                            !isBlocked &&
                            (!profileInfo?.isFriends) &&
                            (!profileInfo?.friendRequestId ||
                                (profileInfo?.friendRequestId &&
                                    profileInfo?.isFriendRequestDeclined)) ?(
                                <CustomButton
                                    onClick={() => {
                                        setModal({
                                            type: 'friend-request-notes',
                                            title: 'Add a Note',
                                            request: { sender_id: otherUserId },
                                            user: { id: user?.id },
                                            refreshViewProfile,
                                            refreshList: refreshList,
                                        })
                                    }}
                                    height={40}
                                    width={'100%'}
                                    imageRightMargin={'10px'}
                                    imageMarginBottom={'0'}
                                    text="Add Friend"
                                    color="darkteal"
                                    textColor="white"
                                    cssClassName="py-xs px-md "
                                    buttonImage="/svg/PlusWhite.svg"
                                />
                            ):null}
                        {/* Withdraw Request button */}
                        {(!profileInfo?.isFriends || !profileInfo?.hasActiveRequestsBetween) &&
                            profileInfo?.friendRequestId &&
                            !profileInfo?.isFriendRequestDeclined &&
                            profileInfo?.isFriendRequestSent ?(
                                <CustomButton
                                    onClick={() => {
                                        withdrawRequest(
                                            profileInfo?.friendRequestId
                                        )
                                    }}
                                    height={40}
                                    width={'100%'}
                                    imageRightMargin={'10px'}
                                    text="Withdraw"
                                    color="tealTierBg"
                                    imageMarginBottom='0'
                                    textColor="darkteal"
                                    borderColor='border-darkteal'
                                    cssClassName="font-medium py-xs px-md"
                                    buttonImage="/icons/withdraw.svg"
                                />
                            ):null}
                        {(!isBlocked && (profileInfo?.isFriends) &&
                            !profileInfo?.muted &&
                            clubsForSelection?.length) ?(
                                <div className="flex w-full">
                                    <CustomButton
                                        onClick={() => {
                                            if (maintenanceStatus?.request) {
                                                toastNotification({
                                                    type: constantOptions.TOAST_TYPE.ERROR,
                                                    message: "Request flow is currently under maintenance. Please refer Request Tab for more details"
                                                })
                                            } else {
                                                handleCreateRequest(
                                                    'PROFILE_INFO',
                                                    profileInfo?.id,
                                                    clubsForSelection
                                                )
                                            }
                                        }}
                                        imageMarginBottom='0'
                                        height={40}
                                        width={"100%"}
                                        text="Request"
                                        color="darkteal"
                                        textColor="white"
                                        cssClassName=""
                                        buttonImage="/svg/CreateRequestWhite-2.svg"
                                    />

                                </div>
                            ):null}
                    </div>
                    <div className='min-h-[12px] bg-lightestgray'></div>

                    {((profileInfo?.isFriends || profileInfo?.hasActiveRequestsBetween) && !isBlocked) &&
                        <>
                            <div className='flex flex-col gap-2 px-lm py-sm'>
                                <div className='text-16 font-medium'>Contact</div>
                                <div className='flex items-center gap-2 group'
                                    onMouseEnter={() => setShowPhoneCopy(true)}
                                    onMouseLeave={() => setShowPhoneCopy(false)}
                                >
                                    <img src="/svg/PhoneGray-2.svg" />
                                    <a className='ml-sm hover:underline hover:text-darkteal' href={`tel:${profileInfo?.phone}`}>
                                        {formatPhoneNumber(profileInfo?.phone)}
                                    </a>

                                    {showPhoneCopy && (
                                        <img
                                            src="/svg/copy.svg"
                                            className="cursor-pointer hover:scale-110 transition-all duration-300"
                                            onClick={() => handleCopy(profileInfo?.phone, 'Phone number')}
                                        />
                                    )}
                                </div>
                                <div className='flex items-center gap-2 group'
                                    onMouseEnter={() => setShowEmailCopy(true)}
                                    onMouseLeave={() => setShowEmailCopy(false)}
                                >
                                    <img src="/svg/EmailGray.svg" />
                                    <a className='ml-sm hover:underline hover:text-darkteal' href={`mailto:${profileInfo?.email}`} target={"_blank"}>
                                        {profileInfo?.email}
                                    </a>
                                    {showEmailCopy && (
                                        <img
                                            src="/svg/copy.svg"
                                            className="cursor-pointer hover:scale-110 transition-all duration-300"
                                            onClick={() => handleCopy(profileInfo?.email, 'Email')}
                                        />
                                    )}
                                </div>
                            </div>
                            <div className='min-h-[12px] bg-lightestgray'></div>
                        </>
                    }
                    {isBlocked && !isBlockedByMe ?
                        <div className="flex-col flex-center py-60px">
                            <img src="/svg/BlockedUser.svg" />
                            <div className="text-14 mt-28px">
                                This user has blocked you!
                            </div>
                        </div>
                        : null
                    }
                    {(!isBlocked && !isBlockedByMe && (profileInfo?.facebook || profileInfo?.linkedin)) ?
                        <>
                            {socialInfo()}
                            <div className='min-h-[12px] bg-lightestgray'></div>
                        </> : null
                    }
                    {(profileInfo?.userClubs?.length && (!isBlocked || (isBlocked && isBlockedByMe))) ?
                        <>
                            <div className='flex flex-col px-lm py-sm'>
                                <div className='flex justify-between items-center'>
                                    <div className='text-16 mb-sm' >Golf Club(s)</div>
                                    {profileInfo?.userClubs?.length > 3 ? (
                                        <div className='text-darkteal cursor-pointer' onClick={() => {
                                            setModal({
                                                type: 'simple-list',
                                                heading: 'All Golf Clubs',
                                                items: profileInfo?.userClubs,
                                                icon: <GolfClubIcon imageWidth='26px' height='40px' width='40px' />
                                            })
                                        }}>View All</div>) : null}
                                </div>
                                <div className='flex flex-col rounded-lg border border-lightestgray'>
                                    {profileInfo?.userClubs?.slice(0, 3).map(
                                        (club) => {
                                            return <div className='border-b border-lightestgray py-sm px-sm flex items-center gap-2' key={club?.id}>
                                                <GolfClubIcon />
                                                {club?.name}
                                            </div>
                                        }
                                    )}
                                </div>
                            </div>
                            <div className='min-h-[12px] bg-lightestgray'></div>
                        </>
                        : null
                    }
                    {(profileInfo?.commonGroups?.length && !isBlocked) ?
                        <div className='flex flex-col px-lm py-sm'>
                            <div className='flex justify-between items-center'>
                                <div className='text-16 mb-sm'>Mutual TG Group(s)</div>
                                {profileInfo?.commonGroups?.length > 3 ? (
                                    <div className='text-darkteal cursor-pointer' onClick={() => {
                                        setModal({
                                            type: 'simple-list',
                                            heading: 'All Mutual TG Groups',
                                            items: profileInfo?.commonGroups,
                                            icon: <MutualGroupsIcon imageWidth='26px' height='40px' width='40px' />
                                        })
                                    }}>View All</div>) : null}
                            </div>
                            <div className='flex flex-col rounded-lg border border-lightestgray'>
                                {profileInfo?.commonGroups?.slice(0, 3)?.map(
                                    (club) => {
                                        return <div className='border-b border-lightestgray py-sm px-sm flex items-center gap-2' key={club?.id}>
                                            {club?.image ? <img src={club?.image} className='w-[24px]  h-[24px] rounded-full' /> : <NameInitials height={24} width={24} user={{ first_name: club?.name }} fontSize={16} rounded={'full'} />}
                                            {club?.name}
                                        </div>
                                    }
                                )}
                            </div>
                        </div> : null
                    }
                    {(profileInfo?.gameReviewsCount > 0 && !isBlocked) ?
                        <>
                            <div className='flex flex-col px-lm py-md'>
                                <div className='flex justify-between'>
                                    <div className='text-16 mb-sm'>Game Review(s) ({profileInfo?.gameReviewsCount})</div>
                                    {profileInfo?.gameReviews?.length > 3 ? (
                                        <div
                                            className='text-darkteal cursor-pointer'
                                            onClick={() => {
                                                setDrawer({
                                                    type: GLOBAL_GAME_REVIEWS,
                                                    golferId: profileInfo?.id,
                                                    globalProfileInfo: true,
                                                    global: drawer?.global,
                                                    customBackClickHandler: () => {
                                                        setOtherUserId(profileInfo?.id)
                                                        setDrawer({
                                                            type: "profile-info",
                                                            global: drawer?.global,
                                                            customBackClickHandler: drawer?.customBackClickHandler
                                                        })
                                                    }
                                                })
                                            }}
                                        >
                                            View All
                                        </div>
                                    ) : null}
                                </div>
                                <div className=' py-sm justify-between'>
                                    {
                                        profileInfo?.gameReviews?.slice(0, 3)?.map((review) => {
                                            return (
                                                <GameReviewListItem
                                                    key={review?.game_id}
                                                    review={review}
                                                    allFriendsId={allFriendsId}
                                                    customBackClickHandler={() => { }}
                                                />)
                                        })
                                    }
                                </div>
                            </div>
                            <div className='min-h-[12px] bg-lightestgray'></div>
                        </> : null
                    }

                    {((!isBlocked || (isBlocked && isBlockedByMe)) && !profileInfo?.isFriends) ?
                        <div className="mt-md">{blockButton()}</div> : null
                    }

                </>
            )}
        </div>
    )
}

export default GlobalProfileInfo