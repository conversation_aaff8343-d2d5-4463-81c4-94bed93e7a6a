import React, { useEffect, useState, useContext, useMemo } from 'react'
import { ModalContext } from '../../context/ModalContext'
import { UserContext } from '../../pages/_app'
import streamOptions from '../../constants/streamOptions'
import { CustomStreamContext } from '../../context/CustomStreamContext'
import CustomButton from '../buttons/CustomButton'
import GroupPhoto from '../chat-v2/common/GroupPhoto'
import constantOptions from '../../constants/constantOptions'
import ENDPOINTS from '../../constants/endpoints.json'
import toastNotification from '../../utils/notifications/toastNotification'
import PulseSkeleton from '../common/PulseSkeleton'
import GroupMemberPreview from '../chat-v2/Group/GroupMemberPreview'
import InfoActionOptionWrapper from '../chat-v2/common/InfoActionOptionWrapper'
import { sortByLastName } from '../../utils/helper/sort'
import { ThreeDots } from 'react-loader-spinner'
import JoinGroup from '../groups/JoinGroup'
import { useRouter } from 'next/router'
import getMyClubsVisibilitySettings from '../../utils/groups/getMyClubsVisibilitySettings'
import Checkbox from '../chat-v2/CheckBox'
import { ChevronToggle } from '../common'
import MultiMembersAvatar from '../groups/MultiMembersAvatar'
import { DrawerContext } from '../../context/DrawerContext'
import shareDynamicLink from '../../utils/toast/shareDynamicLink'

// OTHER GROUPS
import { useChannelStateContext, useChatContext } from 'stream-chat-react'
import useChannelMembers from '../../hooks/chat-v2/useChannelMembers'
import { Switch } from '../common'
import GroupName from '../chat-v2/Group/GroupName'
import MESSAGES from '../../constants/messages'
import memberRole from '../../utils/chat-v2/myTgGroups/memberRole'
import useThumbnail from '../../hooks/useThumbnail'
import useCheckDeviceScreen from '../../hooks/useCheckDeviceScreen'

const {
    EVENTS_AND_OFFERS,
    CHAT_GROUP,
    GROUPS,
    PROFILE,
    MY_GROUP_DETAILS,
} = constantOptions?.DRAWER_TYPE
const { CREATOR, ADMIN, MEMBER } = constantOptions?.MY_TG_GROUPS_ROLES
const {
    AVATAR_CHANNEL_NAME_HEIGHT,
    CHANNEL_ROLES: { CHANNEL_MODERATOR, CHANNEL_MEMBER },
} = streamOptions
const { ARCHIVE_CHANNEL, MUTE_CHANNEL } = MESSAGES
const GROUP_LIMIT = process.env.CONFIG.STREAM_GROUP_PARTICIPANTS_LIMIT
const {
    SYSTEM_THOUSAND_GREENS_PUBLIC,
    USER_CREATED_GROUP,
    MY_TG_GROUP,
    ONE_TO_ONE,
    ADMIN_CREATED_GROUP,
} = streamOptions?.CHANNEL?.TYPES

const MyGroupDetails = () => {
    const { isMobile } = useCheckDeviceScreen()
    const { drawer, setDrawer } = useContext(DrawerContext)
    const [loading, setLoading] = useState(false)
    const { user, token, streamChatClient } = useContext(UserContext)
    const {
        myFriendsId,
        setShowDetails,
        setChannelSelected,
        addMutedChannel,
        mutedChannels,
        removeMutedChannel,
        setshowArchiveList,
        updateMembersOfChannel,
        setShowBackOption,
        profileRedirectionFrom,
        setProfileRedirectionFrom,
        setSelectedChannelId,
    } = useContext(CustomStreamContext)
    const [groupId] = useState(drawer?.groupId)
    const [group_id] = useState(drawer?.group)
    const [isButtonLoading, setIsButtonLoading] = useState(false)
    const { setModal } = useContext(ModalContext)
    const [myGroupMembers, setMyGroupMembers] = useState([])
    const [userRole, setUserRole] = useState(null)
    const [channelData, setChannelData] = useState({})
    const [groupDetails, setGroupDetails] = useState({})
    const [reloadDetails, setReloadDetails] = useState(0)
    const [closeDrawer, setCloseDrawer] = useState(false)
    const router = useRouter()
    const [clubs, setClubs] = useState([])
    const [isCollapsed, setIsCollapsed] = useState(true)
    const [clubLoading, setClubLoading] = useState(false)
    const [listDataType, setListDataType] = useState(
        profileRedirectionFrom === 'my-clubs-list' ? 'Clubs' : 'Members'
    )
    const [clubsData, setClubsData] = useState([])
    const [offerRestricted, setOfferRestricted] = useState(true)
    const { thumbnailUrl } = useThumbnail(channelData?.image, 512)

    //**************************** FOR OTHER CHAT GROUPS */
    const RENDER_FOR_MY_TG_GROUP =
        drawer?.channelType === MY_TG_GROUP || channel?.type === MY_TG_GROUP
    const { channel } = !drawer?.global && useChannelStateContext()
    const { client, setActiveChannel } = !drawer?.global && useChatContext()
    const [uploading, setUpLoading] = useState(false)
    const [groupPhoto, setGroupPhoto] = useState()
    const channelRole = channel?.state?.membership?.channel_role
    const { allMembers, showMemberList } = useChannelMembers(channel)
    const selfRoleInGroup = memberRole(
        channel?.data?.creator_id,
        user?.id,
        channelRole
    )
    const [groupCapacity, setGroupCapacity] = useState(0)
    const [isArchived, setIsArchived] = useState(channel?.data?.hidden)
    const [isMyGroupMember] = useState(
        channel?.type === MY_TG_GROUP && channelRole === CHANNEL_MEMBER
    )
    const [isMyGroupAdmin] = useState(
        channel?.type === MY_TG_GROUP &&
        channelRole === CHANNEL_MODERATOR &&
        channel?.data?.creator_id !== user?.id
    )
    const [isMyGroupCreator] = useState(
        channel?.type === MY_TG_GROUP &&
        channelRole === CHANNEL_MODERATOR &&
        channel?.data?.creator_id === user?.id
    )

    useEffect(() => {
        setProfileRedirectionFrom()
    }, [])
    //MY TG GROUP FUNCTION ONLY
    useEffect(() => {
        if ([MEMBER, CREATOR, ADMIN].includes(userRole)) {
            getMyClubsVisibilitySettings(
                user.id,
                token,
                group_id || groupDetails?.id,
                setClubs,
                setLoading
            )
        }
    }, [userRole, group_id || groupDetails])

    useEffect(() => {
        if (closeDrawer) {
            setDrawer(false)
        }
    }, [closeDrawer])

    //MY TG GROUP FUNCTION ONLY
    useEffect(() => {
        if (
            (user?.id &&
                groupId &&
                channelData?.name &&
                drawer?.channelType === MY_TG_GROUP) ||
            channel?.type === MY_TG_GROUP
        ) {
            fetchAllParticipants()
        }
    }, [user?.id, groupId, reloadDetails, channelData])

    //MY TG GROUP FUNCTION ONLY
    useEffect(() => {
        if (
            (groupId && drawer?.channelType === MY_TG_GROUP) ||
            (channel?.type === MY_TG_GROUP && channel?.id)
        ) {
            getGroupDetails()
        }
    }, [groupId, reloadDetails])

    //MY TG GROUP FUNCTION ONLY
    useEffect(() => {
        if (
            user?.id &&
            (drawer?.channelType === MY_TG_GROUP ||
                channel?.type === MY_TG_GROUP)
        ) {
            canCreateOffer()
        }
    }, [user?.id])

    //MY TG GROUP FUNCTION ONLY
    useEffect(() => {
        if (
            listDataType === 'Clubs' &&
            channelData?.id &&
            (drawer?.channelType === MY_TG_GROUP ||
                channel?.type === MY_TG_GROUP)
        ) {
            fetchClubsList()
        }
    }, [listDataType, channelData, reloadDetails])

    //FOR OTHER GROUPS
    useEffect(() => {
        if (
            drawer?.channelType === MY_TG_GROUP ||
            channel?.type === MY_TG_GROUP
        ) {
            checkGroupCapacity()
        }

        const handleVisibilityChange = async () => {
            await channel.watch()
            setIsArchived(channel?.data?.hidden)
        }

        const visibilityHidden = channel?.on(
            'channel.hidden',
            handleVisibilityChange
        )
        const visibilityShown = channel?.on(
            'channel.visible',
            handleVisibilityChange
        )

        return () => {
            visibilityHidden?.unsubscribe()
            visibilityShown?.unsubscribe()
        }
    }, [channel])

    //FOR OTHER GROUPS
    useEffect(() => {
        const updateGroupInfo = async () => {
            const URL =
                drawer?.channelType === MY_TG_GROUP ||
                    channel?.type === MY_TG_GROUP
                    ? ENDPOINTS?.EDIT_MY_TG_GROUP
                    : ENDPOINTS?.EDIT_GROUP_CHAT

            let body = {
                userId: user?.id,
                groupId: channel?.data?.id,
                groupImage: groupPhoto,
            }
            if (
                drawer?.channelType === MY_TG_GROUP ||
                channel?.type === MY_TG_GROUP
            ) {
                body = {
                    ...body,
                    description: channel?.data?.description,
                    groupName: channel?.data?.name,
                }
            }

            try {
                await fetch(URL, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        ['Authorization']: `Bearer ${token}`,
                    },
                    body: JSON.stringify(body),
                }).then((response) => response.json())
            } catch (error) {
                console.log('Group Photo Edit------', error)
            }
        }

        if (groupPhoto) {
            updateGroupInfo()
        }
    }, [groupPhoto])

    //MY TG GROUP FUNCTION ONLY
    const canCreateOffer = async () => {
        try {
            await fetch(ENDPOINTS?.CAN_CREATE_OFFER, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    user_id: user?.id,
                }),
            })
                .then((data) => data.json())
                .then((data) => {
                    if (data.canCreate) {
                        setOfferRestricted(null)
                    } else {
                        setOfferRestricted(data.message)
                    }
                })
                .catch((err) => console.log('Error'))
        } catch (error) {
            console.log('Can Create Offer-----', error)
        }
    }

    const renderMyTgGroupDescription = (description) => {
        // Regex to match URLs in the text
        const urlRegex = /(https?:\/\/[^\s]+)/g
        const parts = description?.split(urlRegex)

        return parts?.map((part, index) => {
            if (part?.match(urlRegex)) {
                return (
                    <a
                        target="_blank"
                        href={`${part?.match(urlRegex)}`}
                        key={index}
                        style={{
                            color: '#098089',
                            textDecorationLine: 'underline',
                        }}>
                        {part}
                    </a>
                )
            }
            return part
        })
    }

    //MY TG GROUP FUNCTION ONLY
    const fetchClubsList = async () => {
        try {
            await fetch(ENDPOINTS?.GET_MY_TG_GROUP_CLUB_LIST, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    ['Authorization']: `Bearer ${token}`,
                },
                body: JSON.stringify({
                    groupId: channelData?.id || channel?.id,
                    userId: user?.id,
                }),
            })
                .then((response) => response.json())
                .then((data) => {
                    if (!data?.status) {
                        toastNotification({
                            type: constantOptions.TOAST_TYPE.ERROR,
                            message: data?.message,
                        })
                    } else {
                        setClubsData(data?.clubMembers?.clubObject)
                    }
                })
        } catch (error) {
            console.log('My TG Group Clubs List----> ', error)
        }
    }

    //MY TG GROUP FUNCTION ONLY
    const getGroupDetails = async () => {
        try {
            setLoading(true)
            await fetch(ENDPOINTS?.GET_MY_GROUP_DETAILS, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    ['Authorization']: `Bearer ${token}`,
                },
                body: JSON.stringify({
                    userId: user?.id,
                    groupId: groupId || channel?.id || drawer?.groupId,
                }),
            })
                .then((response) => response.json())
                .then((data) => {
                    if (!data?.status) {
                        setCloseDrawer(true)
                        router.push({
                            pathname: '/dashboard/errors/404',
                        })
                    } else {
                        setUserRole(data?.roleInGroup)
                        setChannelData(data?.channel?.data)
                        setGroupDetails(data)
                    }
                })
            setLoading(false)
        } catch (error) {
            console.log('My Group Details---------', error)
        }
    }

    //MY TG GROUP FUNCTION ONLY
    const fetchAllParticipants = async () => {
        try {
            setLoading(true)
            await fetch(ENDPOINTS?.GET_MY_TG_MEMBERS, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    ['Authorization']: `Bearer ${token}`,
                },
                body: JSON.stringify({
                    userId: user?.id,
                    groupId: groupId || channel?.id,
                }),
            })
                .then((response) => response.json())
                .then(async (data) => {
                    if (!data?.status) {
                        toastNotification({
                            type: constantOptions.TOAST_TYPE.ERROR,
                            message: data?.message,
                        })
                    } else {
                        const sortedMembers = data?.members.sort(sortByLastName)
                        setMyGroupMembers(sortedMembers)
                    }
                })
                .catch((error) => setLoading(false))
        } catch (error) {
            setLoading(false)
            console.log('My Groups------------>', error)
        }
        setLoading(false)
    }

    //MY TG GROUP FUNCTION ONLY
    const viewAllParticipantsHandler = async () => {
        setIsButtonLoading(true)
        const channel = await streamChatClient.queryChannels({
            members: { $in: [user?.id] },
            id: { $eq: channelData?.id },
            $or: [{ hidden: { $eq: true } }, { hidden: { $eq: false } }],
        })

        setModal({
            type: 'group-members',
            allMembers: myGroupMembers,
            isSelfAdminInChannel: userRole === CREATOR,
            channelId: channelData?.id,
            client: streamChatClient,
            channel: channel?.[0],
            setActiveChannel: setActiveChannel,
            source: drawer?.source,
            // origin: "details",
            selfRoleInGroup: userRole,
            creatorId: groupDetails?.channel?.data?.creator_id,
            fetchAllParticipants,
            myFriendsId,
            setReloadDetails,
            customDrawer: drawer,
            customSetDrawer: setDrawer,
            customBackClickHandler:
                drawer?.channelType === 'my_tg_group'
                    ? () => {
                        setDrawer({
                            type: drawer?.global
                                ? MY_GROUP_DETAILS
                                : CHAT_GROUP,
                            source: drawer?.source,
                            global: drawer?.global,
                            channelType: drawer?.channelType,
                            groupId: drawer?.groupId,
                            group: drawer?.group,
                            setRefreshGroup: drawer?.setRefreshGroup,
                        })
                    }
                    : () => {
                        setSelectedChannelId(channel?.id)
                        setDrawer({
                            type: CHAT_GROUP,
                            groupId: channel?.id,
                            channelType: channel?.type,
                            source: 'chat',
                        })
                    },
        })
        setIsButtonLoading(false)
    }

    //MY TG GROUP FUNCTION ONLY
    const showMyGroupOptions = (role) => {
        switch (role) {
            case null:
                return null
            case CREATOR:
                return (
                    <>
                        <InfoActionOptionWrapper
                            extraClasses="mb-md"
                            onClick={() =>
                                setModal({
                                    type: 'create-edit-group',
                                    function: 'edit',
                                    group: {
                                        ...channelData,
                                        tags: groupDetails?.tags,
                                        inviteOnly: groupDetails?.inviteOnly,
                                    },
                                    setRefreshGroup: drawer?.setRefreshGroup,
                                    setReloadDetails: setReloadDetails,
                                    source: 'my-group-details',
                                })
                            }>
                            <img src="/svg/EditGroup.svg" width={18} /> Edit Group
                        </InfoActionOptionWrapper>

                        <InfoActionOptionWrapper extraClasses="mb-md" onClick={() => { shareDynamicLink(groupDetails?.dynamicLink) }}>
                            <img src="/svg/ShareGroup.svg" width={18} /> Share Group
                        </InfoActionOptionWrapper>
                        <InfoActionOptionWrapper extraClasses="mb-md" onClick={() => {
                            if (offerRestricted) {
                                setModal({
                                    title: 'Offer Limit',
                                    message: offerRestricted,
                                    type: 'warning',
                                })
                            } else {
                                setModal({
                                    title: 'Create Offer',
                                    img: {
                                        src: '/svg/offer-outline.svg',
                                        style: { height: 48, marginBottom: 10 },
                                    },
                                    width: 829,
                                    type: 'group-offer',
                                    groupName: groupDetails?.channel?.data?.name,
                                    groupId: groupDetails?.id,
                                    refresh: drawer?.setRefreshGroup || setReloadDetails,
                                    source: "groups"
                                })
                            }
                        }}>
                            <img src='/svg/CreateOfferBlack.svg' /> Create Offer
                        </InfoActionOptionWrapper>

                        {groupDetails?.inviteOnly && (
                            <InfoActionOptionWrapper
                                extraClasses=""
                                onClick={() => {
                                    setDrawer()
                                    if (drawer?.source === 'chat') {
                                        window?.sessionStorage?.setItem(
                                            'PENDING_REQUEST_REDIRECTED_FROM_CHAT',
                                            channel?.id
                                        )
                                    }
                                    router.push({
                                        pathname: `/dashboard/my-tg`,
                                        search: `?type=pending-requests&groupId=${channelData?.id}`,
                                    })
                                }}>
                                <img src="/svg/GroupMembers.svg" width={18} />{' '}
                                View Member Requests
                            </InfoActionOptionWrapper>
                        )}
                        <InfoActionOptionWrapper
                            extraClasses="text-red"
                            onClick={() => {
                                setModal({
                                    type: 'delete-group',
                                    buttonColor: 'red',
                                    groupId: channelData?.id,
                                    setRefreshGroup:
                                        drawer?.setRefreshGroup ||
                                        setReloadDetails,
                                    setReloadDetails: setReloadDetails,
                                    setCloseDrawer: setCloseDrawer,
                                })
                            }}>
                            <div className='w-[20px] h-[20px] pl-xs'>
                                <img src="/svg/DeleteGroup.svg" width={15} />
                            </div>
                            Delete Group
                        </InfoActionOptionWrapper>
                    </>
                )
            case MEMBER:
                if (groupDetails?.isAdminAdded) {
                    return (
                        <>
                            <InfoActionOptionWrapper extraClasses="mb-md" onClick={() => {
                                if (offerRestricted) {
                                    setModal({
                                        title: 'Offer Limit',
                                        message: offerRestricted,
                                        type: 'warning',
                                    })
                                } else {
                                    setModal({
                                        title: 'Create Offer',
                                        img: {
                                            src: '/svg/offer-outline.svg',
                                            style: { height: 48, marginBottom: 10 },
                                        },
                                        width: 829,
                                        type: 'group-offer',
                                        groupName: groupDetails?.channel?.data?.name,
                                        groupId: groupDetails?.id,
                                        refresh: drawer?.setRefreshGroup || setReloadDetails,
                                        source: "groups"
                                    })
                                }
                            }}>
                                <img src='/svg/CreateOfferBlack.svg' /> Create Offer
                            </InfoActionOptionWrapper>
                            <InfoActionOptionWrapper extraClasses="text-red" onClick={() => {
                                setModal({
                                    type: 'remove-group-participant',
                                    title: "Leave Group",
                                    source: 'my-group-details',
                                    setReloadDetails: () => { setDrawer() },
                                    setRefreshGroup: drawer?.setRefreshGroup || setReloadDetails,
                                    extraData: { userId: user?.id, channelId: channelData?.id, channel: { type: MY_TG_GROUP } }
                                })
                            }}>
                                <img src="/svg/exit-chat.svg" width={18} /> Leave Group
                            </InfoActionOptionWrapper>
                        </>
                    )
                } else {
                    return (
                        <>
                            <InfoActionOptionWrapper extraClasses="mb-md" onClick={() => { shareDynamicLink(groupDetails?.dynamicLink) }}>
                                <img src="/svg/ShareGroup.svg" width={18} /> Share Group
                            </InfoActionOptionWrapper>

                            <InfoActionOptionWrapper extraClasses="mb-md" onClick={() => {
                                if (offerRestricted) {
                                    setModal({
                                        title: 'Offer Limit',
                                        message: offerRestricted,
                                        type: 'warning',
                                    })
                                } else {
                                    setModal({
                                        title: 'Create Offer',
                                        img: {
                                            src: '/svg/offer-outline.svg',
                                            style: { height: 48, marginBottom: 10 },
                                        },
                                        width: 829,
                                        type: 'group-offer',
                                        groupName: groupDetails?.channel?.data?.name,
                                        groupId: groupDetails?.id,
                                        refresh: drawer?.setRefreshGroup || setReloadDetails,
                                        source: "groups"
                                    })
                                }
                            }}>
                                <img src='/svg/CreateOfferBlack.svg' /> Create Offer
                            </InfoActionOptionWrapper>

                            <InfoActionOptionWrapper extraClasses="text-red" onClick={() => {
                                setModal({
                                    type: 'remove-group-participant',
                                    title: "Leave Group",
                                    source: 'my-group-details',
                                    setReloadDetails: setReloadDetails,
                                    setRefreshGroup: drawer?.setRefreshGroup || setReloadDetails,
                                    extraData: { userId: user?.id, channelId: channelData?.id, channel: { type: MY_TG_GROUP } }
                                })
                            }}>
                                <img src="/svg/exit-chat.svg" width={18} /> Leave Group
                            </InfoActionOptionWrapper>
                        </>
                    )
                }

            case ADMIN:
                return (
                    <>
                        <InfoActionOptionWrapper
                            extraClasses="mb-md"
                            onClick={() =>
                                setModal({
                                    type: 'create-edit-group',
                                    function: 'edit',
                                    group: {
                                        ...channelData,
                                        tags: groupDetails?.tags,
                                        inviteOnly: groupDetails?.inviteOnly,
                                    },
                                    setRefreshGroup: drawer?.setRefreshGroup,
                                    setReloadDetails: setReloadDetails,
                                    source: 'my-group-details',
                                })
                            }>
                            <img src="/svg/EditGroup.svg" width={18} /> Edit Group
                        </InfoActionOptionWrapper>

                        <InfoActionOptionWrapper
                            extraClasses="mb-md"
                            onClick={() => {
                                shareDynamicLink(groupDetails?.dynamicLink)
                            }}>
                            <img src="/svg/ShareGroup.svg" width={18} /> Share
                            Group
                        </InfoActionOptionWrapper>
                        <InfoActionOptionWrapper
                            extraClasses="mb-md"
                            onClick={() => {
                                if (offerRestricted) {
                                    setModal({
                                        title: 'Offer Limit',
                                        message: offerRestricted,
                                        type: 'warning',
                                    })
                                } else {
                                    setModal({
                                        title: 'Create Offer',
                                        img: {
                                            src: '/svg/offer-outline.svg',
                                            style: {
                                                height: 48,
                                                marginBottom: 10,
                                            },
                                        },
                                        width: 829,
                                        type: 'group-offer',
                                        groupName:
                                            groupDetails?.channel?.data?.name,
                                        groupId: groupDetails?.id,
                                        refresh:
                                            drawer?.setRefreshGroup ||
                                            setReloadDetails,
                                        source: 'groups',
                                    })
                                }
                            }}>
                            <img src="/svg/CreateOfferBlack.svg" /> Create Offer
                        </InfoActionOptionWrapper>
                        {groupDetails?.inviteOnly && (
                            <InfoActionOptionWrapper
                                extraClasses=""
                                onClick={() => {
                                    setDrawer()
                                    if (drawer?.source === 'chat') {
                                        window?.sessionStorage?.setItem(
                                            'PENDING_REQUEST_REDIRECTED_FROM_CHAT',
                                            channel?.id
                                        )
                                    }
                                    router.push({
                                        pathname: `/dashboard/my-tg`,
                                        search: `?type=pending-requests&groupId=${channelData?.id}`,
                                    })
                                }}>
                                <img src="/svg/GroupMembers.svg" width={18} />{' '}
                                View Member Requests
                            </InfoActionOptionWrapper>
                        )}
                        <InfoActionOptionWrapper
                            extraClasses="text-red"
                            onClick={() =>
                                setModal({
                                    type: 'remove-group-participant',
                                    title: 'Leave Group',
                                    source: 'my-group-details',
                                    setReloadDetails: setReloadDetails,
                                    setRefreshGroup:
                                        drawer?.setRefreshGroup ||
                                        setReloadDetails,
                                    extraData: {
                                        userId: user?.id,
                                        channelId: channelData?.id,
                                        channel: { type: MY_TG_GROUP },
                                    },
                                })
                            }>
                            <img src="/svg/exit-chat.svg" width={18} /> Leave
                            Group
                        </InfoActionOptionWrapper>
                    </>
                )
            default:
                break
        }
        return ''
    }

    //MY TG GROUP FUNCTION ONLY
    const toggleCollapse = () => {
        setIsCollapsed(!isCollapsed)
    }

    //MY TG GROUP FUNCTION ONLY
    const showMyTgGroupData = () => {
        return (
            <div className="my-tg-group my-group-members bg-lightestgray w-full py-md">
                <div className="flex justify-between px-md">
                    <div className="text-darkteal pt-xs">
                        {myGroupMembers?.length} Member
                        {myGroupMembers?.length > 1 ? 's' : ''}
                    </div>
                    <div
                        className="bg-white text-12 flex items-center px-0.5 justify-between rounded-lg"
                        style={{ width: 150, height: 26 }}>
                        {listDataType === 'Members' ? (
                            <div className="my-tg-group-toggle-active">
                                Members
                            </div>
                        ) : (
                            <div
                                className="cursor-pointer pl-1"
                                onClick={() => setListDataType('Members')}>
                                Members
                            </div>
                        )}
                        {listDataType === 'Clubs' ? (
                            <div className="my-tg-group-toggle-active">
                                Clubs
                            </div>
                        ) : (
                            <div
                                className="cursor-pointer pr-1"
                                onClick={() => setListDataType('Clubs')}>
                                Clubs
                            </div>
                        )}
                    </div>
                </div>
                <div className="text-12 text-grayLight p-md">
                    You can check out members organized by their respective
                    clubs in the "Clubs" tab.
                </div>
                {loading ? (
                    <div className="px-sm">
                        <PulseSkeleton
                            times={4}
                            height={AVATAR_CHANNEL_NAME_HEIGHT}
                        />
                    </div>
                ) : (
                    <>
                        {listDataType === 'Members' ? (
                            <>
                                {myGroupMembers?.slice(0, 10)?.map((member) => {
                                    return (
                                        <GroupMemberPreview
                                            full_name={member?.user?.name}
                                            profilePhoto={member?.user?.image}
                                            isUserAdmin={
                                                member?.channel_role ===
                                                CHANNEL_MODERATOR
                                            }
                                            isSelfAdminInChannel={
                                                member?.channel_role ===
                                                CHANNEL_MODERATOR
                                            }
                                            isSelf={
                                                member?.user?.id === user?.id
                                            }
                                            isFriend={
                                                myFriendsId[member?.user_id]
                                            }
                                            extraData={{
                                                channelId: channelData?.id,
                                                userId: member?.user?.id,
                                                memberCount:
                                                    myGroupMembers?.length,
                                                username:
                                                    member?.user?.username,
                                            }}
                                            key={member?.user_id}
                                            memberId={member?.user_id}
                                            source={drawer?.source}
                                            // origin={'details'}
                                            channelType={channelData?.type}
                                            selfRoleInGroup={userRole}
                                            fetchAllParticipants={
                                                fetchAllParticipants
                                            }
                                            creatorId={
                                                groupDetails?.channel?.data
                                                    ?.creator_id
                                            }
                                            setReloadDetails={setReloadDetails}
                                            setActiveChannel={setActiveChannel}
                                            customBackClickHandler={() => {
                                                if (drawer?.global) {
                                                    setDrawer({
                                                        type:
                                                            'my-group-details',
                                                        source: drawer?.source,
                                                        global: true,
                                                        channelType: MY_TG_GROUP,
                                                        groupId:
                                                            drawer?.groupId,
                                                        group: drawer?.group,
                                                        setRefreshGroup:
                                                            drawer?.setRefreshGroup,
                                                    })
                                                } else {
                                                    setSelectedChannelId(
                                                        channel?.id
                                                    )
                                                    setDrawer({
                                                        type: CHAT_GROUP,
                                                        groupId: channel?.id,
                                                        channelType:
                                                            channel?.type,
                                                        source: 'chat',
                                                    })
                                                }
                                            }}
                                        />
                                    )
                                })}
                                {myGroupMembers?.length > 10 ? (
                                    <div className="divider-light-t pt-sm pb-xs mx-sm">
                                        <CustomButton
                                            onClick={() =>
                                                viewAllParticipantsHandler()
                                            }
                                            text="View All Participants"
                                            width="auto"
                                            color="transparent"
                                            textColor="darkteal"
                                            loading={isButtonLoading}
                                            darkLoader={true}
                                            cssClassName="font-medium mt-xs hover:text-darkestteal"
                                        />
                                    </div>
                                ) : (
                                    ''
                                )}
                            </>
                        ) : (
                            <div>
                                {clubsData.map((club) => {
                                    return (
                                        <div
                                            className="flex py-xs my-sm px-md"
                                            key={club?.id}>
                                            <div
                                                className="bg-white flex-center rounded-full"
                                                style={{
                                                    height: 24,
                                                    width: 24,
                                                }}>
                                                <img
                                                    src={
                                                        club?.id === -1
                                                            ? '/svg/QuestionGray.svg'
                                                            : '/svg/GolfPostGray.svg'
                                                    }
                                                />
                                            </div>
                                            <div className="px-md ">
                                                <div className="font-medium ">{club?.clubName}</div>
                                                <div className='flex-1 flex'>
                                                    <MultiMembersAvatar
                                                        groupId={
                                                            channelData?.id
                                                        }
                                                        groupName={
                                                            channelData?.name
                                                        }
                                                        members={club?.members}
                                                        memberCount={
                                                            club?.members
                                                                ?.length
                                                        }
                                                        source={drawer?.source}
                                                        // origin={'non-chat'}
                                                        drawer={drawer}
                                                        setDrawer={setDrawer}
                                                        customBackClickHandler={
                                                            drawer?.global
                                                                ? () => {
                                                                    setDrawer(
                                                                        {
                                                                            type: MY_GROUP_DETAILS,
                                                                            source:
                                                                                drawer?.source,
                                                                            global: true,
                                                                            channelType:
                                                                                drawer?.channelType,
                                                                            groupId:
                                                                                drawer?.groupId,
                                                                            group:
                                                                                drawer?.group,
                                                                            setRefreshGroup:
                                                                                drawer?.setRefreshGroup,
                                                                        }
                                                                    )
                                                                }
                                                                : () => {
                                                                    setSelectedChannelId(
                                                                        channel?.id
                                                                    )
                                                                    setDrawer(
                                                                        {
                                                                            type: CHAT_GROUP,
                                                                            groupId:
                                                                                channel?.id,
                                                                            channelType:
                                                                                channel?.type,
                                                                            source:
                                                                                'chat',
                                                                        }
                                                                    )
                                                                }
                                                        }
                                                    />
                                                </div>
                                            </div>
                                        </div>
                                    )
                                })}
                            </div>
                        )}
                    </>
                )}
            </div>
        )
    }

    //MY TG GROUP FUNCTION ONLY
    const renderMyTgGroupButton = (role) => {
        switch (role) {
            case null:
                return (
                    <JoinGroup
                        group={{
                            streamId: groupId,
                            canJoin: groupDetails?.canJoin,
                            inviteOnly: groupDetails?.inviteOnly,
                            joinRequest: groupDetails?.pendingJoinRequest,
                            requestId: groupDetails?.requestId,
                        }}
                        setRefreshGroup={drawer?.setRefreshGroup}
                        setReloadDetails={setReloadDetails}
                        source={'details'}
                        width={200}
                        height={50}
                        type={'white'}
                    />
                )
            case MEMBER:
                {
                    return (
                        <div className="flex">
                            {drawer?.global ? (
                                <CustomButton
                                    text="Group Chat"
                                    color="lightestgray"
                                    height={70}
                                    width={105}
                                    buttonImagePosition={'col'}
                                    imageRightMargin={'0'}
                                    cssClassName="hover:bg-hover"
                                    textColor="darkteal"
                                    buttonImage="/icons/group-chat.svg"
                                    fontWeight="medium"
                                    onClick={() => {
                                        router.push({
                                            pathname: `/dashboard/my-tg`,
                                            search: `?type=chat&channel_id=${channelData?.id}`,
                                        })
                                    }}
                                />
                            ) : null}
                            <CustomButton
                                text="Go To Map"
                                color="lightestgray"
                                height={70}
                                width={105}
                                buttonImagePosition={'col'}
                                imageRightMargin={'0'}
                                cssClassName="hover:bg-hover"
                                textColor="darkteal"
                                buttonImage="/icons/map.svg"
                                fontWeight="medium"
                                onClick={() => {
                                    setDrawer()
                                    router.push({
                                        pathname: `/dashboard/map`,
                                    })
                                    window?.sessionStorage?.setItem(
                                        'MAP_FILTERS',
                                        JSON.stringify({
                                            ...groupDetails,
                                            source: drawer?.source,
                                        })
                                    )
                                }}
                            />
                        </div>
                    )
                }
                break
            case ADMIN: {
                return (
                    <div className="flex">
                        <CustomButton
                            onClick={() => {
                                setModal({
                                    type: 'add-group-participants',
                                    channelId: channelData?.id,
                                    participantsCount: myGroupMembers?.length,
                                    groupType: channelData?.type,
                                    setReloadDetails: setReloadDetails,
                                    setRefreshGroup: drawer?.setRefreshGroup,
                                    origin: 'details',
                                })
                            }}
                            color="lightestgray"
                            buttonImage="/icons/add-members.svg"
                            text="Add"
                            height={70}
                            width={105}
                            buttonImagePosition={'col'}
                            imageRightMargin={'0'}
                            cssClassName="hover:bg-hover"
                            textColor="darkteal"
                            fontWeight="medium"
                            disabled={!groupDetails?.capacityLeft}
                        />
                        <CustomButton
                            text="Go To Map"
                            color="lightestgray"
                            height={70}
                            width={105}
                            buttonImagePosition={'col'}
                            imageRightMargin={'0'}
                            cssClassName="hover:bg-hover"
                            textColor="darkteal"
                            buttonImage="/icons/map.svg"
                            fontWeight="medium"
                            onClick={() => {
                                setDrawer()
                                router.push({
                                    pathname: `/dashboard/map`,
                                })
                                window?.sessionStorage?.setItem(
                                    'MAP_FILTERS',
                                    JSON.stringify({
                                        ...groupDetails,
                                        source: drawer?.source,
                                    })
                                )
                            }}
                        />
                        {drawer?.global ? (
                            <div>
                                <CustomButton
                                    text="Group Chat"
                                    color="lightestgray"
                                    height={70}
                                    width={105}
                                    buttonImagePosition={'col'}
                                    imageRightMargin={'0'}
                                    cssClassName="hover:bg-hover"
                                    textColor="darkteal"
                                    buttonImage="/icons/group-chat.svg"
                                    fontWeight="medium"
                                    onClick={() => {
                                        router.push({
                                            pathname: `/dashboard/my-tg`,
                                            search: `?type=chat&channel_id=${channelData?.id}`,
                                        })
                                    }}
                                />
                            </div>
                        ) : null}
                    </div>
                )
            }
            case CREATOR: {
                return (
                    <div className="flex">
                        <CustomButton
                            onClick={() => {
                                setModal({
                                    type: 'add-group-participants',
                                    channelId: channelData?.id,
                                    participantsCount: myGroupMembers?.length,
                                    groupType: channelData?.type,
                                    setReloadDetails: setReloadDetails,
                                    setRefreshGroup: drawer?.setRefreshGroup,
                                    origin: 'details',
                                })
                            }}
                            color="lightestgray"
                            buttonImage="/icons/add-members.svg"
                            text="Add"
                            height={70}
                            width={105}
                            buttonImagePosition={'col'}
                            imageRightMargin={'0'}
                            cssClassName="hover:bg-hover"
                            textColor="darkteal"
                            fontWeight="medium"
                            disabled={!groupDetails?.capacityLeft}
                        />
                        <CustomButton
                            text="Go To Map"
                            color="lightestgray"
                            height={70}
                            width={105}
                            buttonImagePosition={'col'}
                            imageRightMargin={'0'}
                            cssClassName="hover:bg-hover"
                            textColor="darkteal"
                            buttonImage="/icons/map.svg"
                            fontWeight="medium"
                            onClick={() => {
                                setDrawer()
                                router.push({
                                    pathname: `/dashboard/map`,
                                })
                                window?.sessionStorage?.setItem(
                                    'MAP_FILTERS',
                                    JSON.stringify({
                                        ...groupDetails,
                                        source: drawer?.source,
                                    })
                                )
                            }}
                        />
                        {drawer?.global ? (
                            <div>
                                <CustomButton
                                    text="Group Chat"
                                    color="lightestgray"
                                    height={70}
                                    width={105}
                                    buttonImagePosition={'col'}
                                    imageRightMargin={'0'}
                                    cssClassName="hover:bg-hover"
                                    textColor="darkteal"
                                    buttonImage="/icons/group-chat.svg"
                                    fontWeight="medium"
                                    onClick={() => {
                                        router.push({
                                            pathname: `/dashboard/my-tg`,
                                            search: `?type=chat&channel_id=${channelData?.id}`,
                                        })
                                    }}
                                />
                            </div>
                        ) : null}
                    </div>
                )
            }
        }
    }

    //MY TG GROUP FUNCTION ONLY
    const updateGroupClubVisibility = (clubId, value) => {
        setClubLoading(true)
        try {
            fetch(ENDPOINTS?.UPDATE_GROUP_CLUB_SETTING, {
                method: 'POST',
                credentials: 'same-origin',
                headers: {
                    ['Content-Type']: 'application/json',
                    ['Authorization']: `Bearer ${token}`,
                },
                body: JSON.stringify({
                    userId: user.id,
                    groupId: group_id || groupDetails?.id,
                    clubId: clubId,
                    clubVisibility: value,
                }),
            })
                .then((data) => data.json())
                .then((data) => {
                    if (!data?.status) {
                        toastNotification({
                            type: constantOptions.TOAST_TYPE.ERROR,
                            message: data?.message,
                        })
                    } else {
                        if ([MEMBER, CREATOR, ADMIN].includes(userRole)) {
                            getMyClubsVisibilitySettings(
                                user.id,
                                token,
                                group_id || groupDetails?.id,
                                setClubs,
                                setLoading
                            )
                        }
                        setClubLoading(false)
                    }
                })
        } catch (error) {
            console.error('Update club visibility Settings', error)
        }
    }

    //******************* FOR OTHER CHAT GROUP ******************/

    // FOR OTHER CHAT GROUP
    const getGroupPhoto = (photo) => {
        setGroupPhoto(photo)
    }
    // FOR OTHER CHAT GROUP
    // Add participant button for the admin of the channel
    const addParticipant = () => {
        const isDisabled =
            (channel?.type === MY_TG_GROUP && !groupCapacity > 0) ||
            (channel?.type !== MY_TG_GROUP &&
                allMembers?.length === GROUP_LIMIT + 1)
        if (channelRole === CHANNEL_MODERATOR) {
            return (
                <CustomButton
                    onClick={() => {
                        setModal({
                            type: 'add-group-participants',
                            channelId: channel?.data?.id,
                            participantsCount: allMembers?.length,
                            groupType: channel?.type,
                            setReloadDetails: () => { },
                        })
                    }}
                    buttonImage="/icons/add-members.svg"
                    text="Add"
                    height={70}
                    width={105}
                    buttonImagePosition={'col'}
                    imageRightMargin={'0'}
                    color="lightestgray"
                    textColor="darkteal"
                    cssClassName={`font-medium px-lg ${isDisabled ? '' : 'hover:bg-hover'
                        }`}
                    disabled={isDisabled}
                />
            )
        }
        return ''
    }

    //FOR OTHER CHAT GROUP
    // Member list can be seen by any user, the options in a list are according to the logged in user's role in the channel
    const membersList = () => {
        if (!showMemberList) {
            return ''
        }

        return (
            <div className="bg-lightestgray w-full rounded-md group-members-list pb-sm mb-md">
                <p className="text-darkteal rounded-md px-10px py-md top-0 z-10 bg-lightestgray">
                    Group Members ({channel?.data?.member_count}{' '}
                    {channel?.data?.member_count < 2
                        ? 'Participant'
                        : 'Participants'}
                    )
                </p>
                {loading ? (
                    <div className="px-sm">
                        <PulseSkeleton
                            times={4}
                            height={AVATAR_CHANNEL_NAME_HEIGHT}
                        />
                    </div>
                ) : (
                    <>
                        {allMembers?.slice(0, 10).map((member) => {
                            return (
                                <GroupMemberPreview
                                    full_name={member?.user?.name}
                                    profilePhoto={member?.user?.image}
                                    isUserAdmin={
                                        member?.channel_role ===
                                        CHANNEL_MODERATOR
                                    }
                                    isSelfAdminInChannel={
                                        channelRole === CHANNEL_MODERATOR
                                    }
                                    isSelf={member?.user?.id === user?.id}
                                    isFriend={myFriendsId[member?.user_id]}
                                    key={member?.user_id}
                                    extraData={{
                                        channelId: channel?.id,
                                        userId: member?.user?.id,
                                        username: member?.user?.username,
                                        memberCount: allMembers?.length,
                                    }}
                                    client={client}
                                    setActiveChannel={setActiveChannel}
                                    memberId={member?.user_id}
                                    channelType={channel?.type}
                                    selfRoleInGroup={selfRoleInGroup}
                                    creatorId={channel?.data?.creator_id}
                                    setShowBackOption={setShowBackOption}
                                    customBackClickHandler={() => {
                                        setSelectedChannelId(channel?.id)
                                        setDrawer({
                                            type: CHAT_GROUP,
                                            groupId: channel?.id,
                                            channelType: channel?.type,
                                            source: 'chat',
                                        })
                                    }}
                                />
                            )
                        })}
                        {allMembers?.length > 10 ? (
                            <div className="divider-light-t pt-sm pb-xs mx-sm">
                                <CustomButton
                                    onClick={() => {
                                        setModal({
                                            type: 'group-members',
                                            allMembers,
                                            isSelfAdminInChannel:
                                                channelRole ===
                                                CHANNEL_MODERATOR,
                                            channelId: channel?.id,
                                            client,
                                            myFriendsId,
                                            setActiveChannel,
                                            channelType: channel?.type,
                                            selfRoleInGroup: selfRoleInGroup,
                                            channel,
                                            customDrawer: drawer,
                                            customSetDrawer: setDrawer,
                                            customBackClickHandler: () => {
                                                setSelectedChannelId(
                                                    channel?.id
                                                )
                                                setDrawer({
                                                    type: CHAT_GROUP,
                                                    groupId: channel?.id,
                                                    channelType: channel?.type,
                                                    source: 'chat',
                                                })
                                            },
                                        })
                                    }}
                                    text="View All Participants"
                                    width="auto"
                                    color="transparent"
                                    textColor="darkteal"
                                    cssClassName="font-medium mt-xs hover:text-darkestteal"
                                />
                            </div>
                        ) : null}
                    </>
                )}
            </div>
        )
    }

    //FOR OTHER CHAT
    const checkGroupCapacity = async () => {
        try {
            await fetch(ENDPOINTS?.CHECK_GROUP_CAPACITY, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    ['Authorization']: `Bearer ${token}`,
                },
                body: JSON.stringify({
                    groupId: channel?.data?.id || drawer?.groupId,
                }),
            })
                .then((response) => response.json())
                .then((data) => {
                    setGroupCapacity(data?.capacityLeft)
                })
        } catch (error) {
            console.log('Create Group Modal------', error)
        }
    }

    //FOR OTHER CHAT
    // Mute channel option can be seen by any user who is an active member of the group (not removed by self or admin)
    const muteChannelOption = useMemo(() => {
        let isMuted = mutedChannels[channel?.id] !== undefined
        if ([CHANNEL_MEMBER, CHANNEL_MODERATOR].includes(channelRole)) {
            return (
                <InfoActionOptionWrapper
                    onClick={() => updateMuteStatus(isMuted)}>
                    <img src="/svg/mute-chat.svg" width={17} /> Mute Group
                    <div className="ml-auto">
                        <Switch
                            value={isMuted}
                            update={() => {
                                updateMuteStatus(isMuted)
                            }}
                        />
                    </div>
                </InfoActionOptionWrapper>
            )
        }
        // return ''
    }, [Object.keys(mutedChannels)?.length])

    //FOR OTHER CHAT
    const handleLeave = () => {
        setModal({
            type: 'remove-group-participant',
            title: 'Leave Group',
            source: 'chat',
            setShowDetails,
            setChannelSelected,
            extraData: {
                userId: user?.id,
                channelId: channel?.id,
                channel,
                setActiveChannel,
            },
        })
    }
    //FOR OTHER CHAT
    const handleArchive = () => {
        setModal({
            type: 'archive-chat',
            text: ARCHIVE_CHANNEL.title(channel?.data?.type, true),
            subType: 'archive',
            width: 430,
            channel,
            setShowDetails,
            setshowArchiveList,
            userId: user?.id,
        })
    }
    //FOR OTHER CHAT
    const handleUnarchive = () => {
        setModal({
            type: 'archive-chat',
            text: ARCHIVE_CHANNEL.title(channel?.data?.type, false),
            subType: 'unarchive',
            width: 430,
            channel,
            setShowDetails,
            setshowArchiveList,
            userId: user?.id,
        })
    }
    //FOR OTHER CHAT
    const updateMuteStatus = (muted) => {
        setModal({
            type: 'mute-chat',
            width: 430,
            text: MUTE_CHANNEL.title(channel?.data?.type, !muted),
            subType: muted ? 'unmute' : 'mute',
            channel,
            addMutedChannel,
            removeMutedChannel,
            userId: user?.id,
        })
    }

    //FOR OTHER CHAT
    // Channel options that the logged in user can see - like Exit group, delete group, close group etc. based on role
    const showChannelOptions = useMemo(() => {
        if ([CHANNEL_MEMBER, CHANNEL_MODERATOR].includes(channelRole)) {
            return (
                <>
                    {isArchived ? (
                        <InfoActionOptionWrapper
                            extraClasses="text-darkteal"
                            onClick={handleUnarchive}>
                            <img src="/svg/archive-channel.svg" width={18} />{' '}
                            Unarchive Group
                        </InfoActionOptionWrapper>
                    ) : (
                        <InfoActionOptionWrapper
                            extraClasses="text-darkteal"
                            onClick={handleArchive}>
                            <img src="/svg/archive-channel.svg" width={18} />{' '}
                            Archive Group
                        </InfoActionOptionWrapper>
                    )}
                    {[
                        ADMIN_CREATED_GROUP,
                        ONE_TO_ONE,
                        USER_CREATED_GROUP,
                    ].includes(channel?.type) ||
                        isMyGroupAdmin ||
                        isMyGroupMember ? (
                        <>
                            {![
                                ADMIN_CREATED_GROUP,
                                ONE_TO_ONE,
                                USER_CREATED_GROUP,
                            ].includes(channel?.type) &&
                                !isMyGroupMember &&
                                channel?.data?.inviteOnly ? (
                                <InfoActionOptionWrapper
                                    extraClasses=""
                                    onClick={() => {
                                        if (drawer?.source === 'chat') {
                                            window?.sessionStorage?.setItem(
                                                'PENDING_REQUEST_REDIRECTED_FROM_CHAT',
                                                channel?.id
                                            )
                                        }
                                        router.push({
                                            pathname: `/dashboard/my-tg`,
                                            search: `?type=pending-requests&groupId=${channel?.id}`,
                                        })
                                    }}>
                                    <img
                                        src="/svg/GroupMembers.svg"
                                        width={15}
                                    />{' '}
                                    View Member Requests
                                </InfoActionOptionWrapper>
                            ) : null}
                            <InfoActionOptionWrapper
                                extraClasses="text-red"
                                onClick={handleLeave}>
                                <img src="/svg/exit-chat.svg" width={18} />{' '}
                                Leave Group
                            </InfoActionOptionWrapper>
                        </>
                    ) : isMyGroupCreator ? (
                        <>
                            {channel?.data?.inviteOnly && (
                                <InfoActionOptionWrapper
                                    extraClasses=""
                                    onClick={() => {
                                        if (drawer?.source === 'chat') {
                                            window?.sessionStorage?.setItem(
                                                'PENDING_REQUEST_REDIRECTED_FROM_CHAT',
                                                channel?.id
                                            )
                                        }
                                        router.push({
                                            pathname: `/dashboard/my-tg`,
                                            search: `?type=pending-requests&groupId=${channel?.id}`,
                                        })
                                    }}>
                                    <img
                                        src="/svg/GroupMembers.svg"
                                        width={15}
                                    />{' '}
                                    View Member Requests
                                </InfoActionOptionWrapper>
                            )}
                            <InfoActionOptionWrapper
                                extraClasses="text-red"
                                onClick={() => {
                                    setModal({
                                        type: 'delete-group',
                                        buttonColor: 'red',
                                        source: 'chat',
                                        groupId: channel?.id,
                                        extraData: {
                                            userId: user?.id,
                                            channel,
                                            setActiveChannel,
                                        },
                                    })
                                }}>
                                <img src="/svg/DeleteGroup.svg" width={18} />{' '}
                                Delete Group
                            </InfoActionOptionWrapper>
                        </>
                    ) : null}
                </>
            )
        }

        return ''
    }, [isArchived])

    return (
        <div className="fade-in text-black flex flex-col shadow-lg w-full overflow-y-auto relative"
            style={drawer?.global ? {
                height: '100vh',
                width: isMobile ? "100%" : "500px",
                overflowY: 'auto',
                position: 'fixed',
                top: 0,
                right: 0,
                zIndex: 9999,
                WebkitOverflowScrolling: 'touch' // For smooth scrolling on iOS
            } : {}}
        >
            <div className="bg-lightestgray flex justify-between py-lm flex-center relative">
                <div className="text-21 font-medium">Group Details</div>
                <div
                    onClick={() => {
                        setDrawer()
                        setProfileRedirectionFrom()
                    }}
                    className="cursor-pointer absolute"
                    style={{ right: 20, top: 28 }}>
                    <img src="/svg/CrossIconBlack.svg" />
                </div>
            </div>
            {loading ? (
                <div className="flex-center h-full">
                    <ThreeDots
                        visible={true}
                        height="50"
                        width="50"
                        color={"#098089"}
                        radius="9"
                        ariaLabel="three-dots-loading"
                        wrapperStyle={{}}
                        wrapperClass=""
                    />
                </div>
            ) : (
                <div className="overflow-y-auto flex-1">
                    {RENDER_FOR_MY_TG_GROUP ? (
                        <div className="mt-20px">
                            <GroupPhoto
                                groupInfoSection={true}
                                imageUrl={thumbnailUrl}
                                name={channelData?.name}
                                size={92}
                            />
                        </div>
                    ) : (
                        <div className="group-info-avatar py-md">
                            {/* Group photo of the channel with option to edit */}
                            <GroupPhoto
                                imageUrl={channel?.data?.image}
                                name={channel?.data?.name}
                                size={118}
                                isSelfAdminInChannel={
                                    channelRole === CHANNEL_MODERATOR
                                }
                                isPhotoLoading={uploading}
                                setIsPhotoLoading={setUpLoading}
                                getGroupPhoto={getGroupPhoto}
                                groupInfoSection={true}
                                groupPhoto={groupPhoto}
                            />
                        </div>
                    )}

                    {RENDER_FOR_MY_TG_GROUP ? (
                        <div className="text-24 font-medium text-center mt-md">
                            {channelData?.name}
                        </div>
                    ) : (
                        <GroupName
                            extraClasses="mb-sm px-md"
                            isSelfAdminInChannel={
                                channelRole === CHANNEL_MODERATOR
                            }
                            name={channel?.data?.name}
                            groupId={channel?.data?.id}
                            channelType={channel?.type}
                            groupDescription={channel?.data?.description}
                        />
                    )}

                    {RENDER_FOR_MY_TG_GROUP ? (
                        <div className="mt-md flex-center">
                            {renderMyTgGroupButton(userRole)}
                        </div>
                    ) : (
                        <div className="py-md flex-center">
                            {addParticipant()}
                        </div>
                    )}

                    <div className="">
                        {RENDER_FOR_MY_TG_GROUP ||
                            ([ADMIN_CREATED_GROUP].includes(channel?.data?.type) &&
                                channel?.data?.description &&
                                channel?.data?.description.trim() !== '') ? (
                            <div className="bg-lightestgray p-md mt-25px rounded-md">
                                <div className="text-gray font-medium mb-sm">
                                    About
                                </div>
                                <div className="break-words">
                                    {renderMyTgGroupDescription(
                                        channelData?.description ||
                                        channel?.data?.description
                                    )}
                                </div>
                            </div>
                        ) : null}
                        {groupDetails?.tags?.length ? (
                            <div className="p-md">
                                <div className="text-gray">Tags</div>
                                <div className="flex flex-wrap">
                                    {groupDetails?.tags?.map((tag) => {
                                        return (
                                            <div
                                                key={tag}
                                                className="flex items-center rounded-lg p-sm mr-sm mb-sm lightest-gray-border"
                                                style={{
                                                    maxWidth: 'fit-content',
                                                }}>
                                                {tag}
                                            </div>
                                        )
                                    })}
                                </div>
                            </div>
                        ) : null}
                        <div className=" rounded-md">
                            {RENDER_FOR_MY_TG_GROUP ? (
                                <>
                                    {userRole === null ? (
                                        <div className="p-md">
                                            <div className="font-medium text-gray">
                                                Group Members
                                            </div>
                                            <div>
                                                {myGroupMembers?.length}{' '}
                                                Participant
                                                {myGroupMembers?.length > 1 &&
                                                    's'}
                                            </div>
                                        </div>
                                    ) : (
                                        <div>{showMyTgGroupData()}</div>
                                    )}
                                </>
                            ) : (
                                membersList()
                            )}
                        </div>

                        {RENDER_FOR_MY_TG_GROUP ? (
                            <div
                                className={
                                    'pl-md pt-md bg-lightestgray pb-sm mt-md'
                                }>
                                <div className="flex items-center justify-between">
                                    <div className="text-14 font-medium text-darkteal">
                                        Manage Clubs Visibility for this Group
                                    </div>
                                    <div className="mb-sm">
                                        <ChevronToggle
                                            iconStyle={"chevron"}
                                            isCollapsed={isCollapsed}
                                            handleOpen={toggleCollapse}
                                        />
                                    </div>
                                </div>
                                {!isCollapsed && (
                                    <div className="flex items-center">
                                        <div className="text-12 font-normal text-gray pt-sm pb-sm">
                                            All checked clubs below will be
                                            visible to all TG users belonging to
                                            this group irrespective of their tier
                                            and club's defined LTV settings.
                                        </div>
                                    </div>
                                )}

                                {clubLoading ? (
                                    <div
                                        className="flex-center"
                                        style={{ height: 150 }}>
                                        <ThreeDots
                                            visible={true}
                                            height="50"
                                            width="50"
                                            color={"#098089"}
                                            radius="9"
                                            ariaLabel="three-dots-loading"
                                            wrapperStyle={{}}
                                            wrapperClass=""
                                        />
                                    </div>
                                ) : (
                                    <div
                                        className={`${isCollapsed ? 'hidden' : ''
                                            }`}>
                                        {clubs?.map((club) => {
                                            return (
                                                <div
                                                    className="flex pt-sm"
                                                    key={club?.id}>
                                                    <Checkbox
                                                        label={club?.name}
                                                        value={club?.isVisible}
                                                        update={(value) => {
                                                            updateGroupClubVisibility(
                                                                club?.id,
                                                                value
                                                            )
                                                        }}
                                                    />
                                                </div>
                                            )
                                        })}
                                    </div>
                                )}
                            </div>
                        ) : null}
                        <>
                            {RENDER_FOR_MY_TG_GROUP ? (
                                <div className="pt-md">
                                    {showMyGroupOptions(userRole)}
                                </div>
                            ) : (
                                <>
                                    {muteChannelOption}
                                    {showChannelOptions}
                                </>
                            )}
                        </>
                    </div>
                </div>
            )}
        </div>
    )
}

export default MyGroupDetails
