import React, { useContext, useEffect, useState, useRef } from 'react'
import { ThreeDots } from 'react-loader-spinner'
import { UserContext } from '../../pages/_app';
import ENDPOINTS from "../../constants/endpoints.json";
import constantOptions from '../../constants/constantOptions'
import useClient from '../../graphql/useClient'
import toastNotification from '../../utils/notifications/toastNotification'
import MESSAGES from '../../constants/messages'
import NotificationCard from '../notifications/NotificationCard'
import RequestEmptyState from '../common/RequestEmptyState'

const NotificationDrawer = ({ drawer, setDrawer }) => {
    const { NOTIFICATION_LIMIT } = constantOptions;
    const { user, fetchUser, token } = useContext(UserContext)
    const [notifications, setNotifications] = useState([])
    const [currentPageNotifCount, setCurrentPageNotifCount] = useState(0);
    const [loading, setLoading] = useState(false)
    const client = useClient()
    const scrollContainerRef = useRef(null)

    let isMounted = 1
    useEffect(() => {
        fetchNotifications()
    }, [])

    const markAllRead = async (notificationId) => {
        try {
            let body = {
                userId: user?.id,
            }
            if (notificationId) {
                body = {
                    ...body,
                    notificationId
                }
            }
            await fetch(ENDPOINTS.MARK_READ, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    ['Authorization']: `Bearer ${token}`,
                },
                body: JSON.stringify(body),
            })
                .then((data) => data.json())
                .then(() => {
                    fetchUser()
                    fetchNotifications()
                    setNotifications([])
                })
        } catch (error) {
            console.log(error);

        }
    }

    const filterNotifications = (ids) => {
        const updatedNotifications = notifications.filter((notification) => !ids.includes(notification.id));
        setNotifications(updatedNotifications);
    }

    async function deleteNotification(id, userId) {
        await fetch(ENDPOINTS?.DELETE_NOTIFICATION_FROM_WEB_PANEL, {
            method: "POST",
            headers: {
                'Content-Type': 'application/json',
                ['Authorization']: `Bearer ${token}`,
            },
            body: JSON.stringify({
                userId,
                notificationIds: id
            })
        })
            .then((response) => response.json())
            .then(async (data) => {
                filterNotifications(id)
            })
    }

    const fetchNotifications = async (paginate = true) => {
        setLoading(true)
        // Store current scroll position if paginating
        const scrollContainer = scrollContainerRef.current
        const scrollPosition = paginate ? scrollContainer?.scrollTop : 0

        try {
            await fetch(ENDPOINTS.GET_NOTIFICATIONS, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    ['Authorization']: `Bearer ${token}`,
                },
                body: JSON.stringify({
                    user_id: user?.id,
                    last_id: paginate ? notifications?.[notifications?.length - 1]?.row_id : 0
                }),
            })
                .then((data) => data.json())
                .then(async (data) => {
                    if (isMounted) {
                        setNotifications(prevNotifications => [...prevNotifications, ...data])
                        setCurrentPageNotifCount(data?.length)
                        setLoading(false)
                        // Restore scroll position after state updates
                        if (paginate && scrollContainer) {
                            setTimeout(() => {
                                scrollContainer.scrollTop = scrollPosition
                            }, 0)
                        }
                    }
                })
                .catch((error) => {
                    toastNotification({ type: constantOptions.TOAST_TYPE.ERROR, message: MESSAGES[500] })
                    setLoading(false)
                })
        } catch (error) {
            toastNotification({ type: constantOptions.TOAST_TYPE.ERROR, message: MESSAGES[500] })
            setLoading(false)
        }
    }

    const processNotifications = (notifications) => {
        // Create a map to track groups and their counts
        const chatGroups = {};
        const seenGameIds = new Set();
        const groupedNotificationIds = {};

        // Process notifications in their original order
        return notifications.map(notification => {
            if (notification.type === "request-chat-new-message") {
                const gameId = notification.data.gameId;

                // Initialize arrays for notification IDs if not exists
                if (!groupedNotificationIds[gameId]) {
                    groupedNotificationIds[gameId] = [];
                }
                groupedNotificationIds[gameId].push(notification.id);

                // Count messages for this gameId if not already counted
                if (!chatGroups[gameId]) {
                    chatGroups[gameId] = notifications.filter(n =>
                        n.type === "request-chat-new-message" &&
                        n.data.gameId === gameId
                    ).length;
                }

                // Only show the first occurrence of each gameId
                if (!seenGameIds.has(gameId)) {
                    seenGameIds.add(gameId);
                    return {
                        ...notification,
                        groupCount: chatGroups[gameId],
                        groupedNotificationIds: groupedNotificationIds[gameId]
                    };
                }
                // Skip other occurrences of this gameId
                return null;
            }
            // Return non-chat notifications as is
            return notification;
        }).filter(Boolean); // Remove null entries
    };

    return (
        <div
            ref={scrollContainerRef}
            className='fade-in bg-white shadow-lg allow-scroll'
            style={{
                width: "500px",
                height: '100vh',
                overflowY: 'scroll',
                position: 'fixed',
                top: 0,
                right: 0,
                zIndex: 9999,
                WebkitOverflowScrolling: 'touch'
            }}
        >
            <div className='bg-lightestgray flex justify-between h-[58px] flex-center relative'>
                <div className='text-21 font-medium'>Notifications</div>
                <div
                    onClick={() => {
                        setDrawer()
                    }}
                    className="cursor-pointer absolute right-[20px] top-[28px]">
                    <img src='/svg/CrossIconBlack.svg' />
                </div>
            </div>

            {
                notifications.length > 0 && (
                    <div className='flex justify-between items-center px-md py-sm'>
                        <div className='flex items-center gap-sm'>
                            <div className='text-12 font-medium text-white bg-darkteal rounded-full px-sm'>{user?.unread?.aggregate?.count || notifications.length || 0}</div>
                            <div className=' font-medium pl-xs'>New Notifications</div>
                        </div>
                        <div className='text-darkteal cursor-pointer'
                            onClick={() => {
                                markAllRead()
                            }}
                        >Clear All</div>
                    </div>
                )
            }

            {
                loading ? (
                    <div className='flex-center h-[calc(100vh-58px)]'>
                        <ThreeDots
                            visible={true}
                            height="50"
                            width="50"
                            color={"#098089"}
                            radius="9"
                            ariaLabel="three-dots-loading"
                            wrapperStyle={{}}
                            wrapperClass=""
                        />
                    </div>
                ) :
                    <>
                        {notifications?.length > 0 ? (
                            <div className='overflow-y-auto pt-md'>
                                {(currentPageNotifCount < NOTIFICATION_LIMIT ? notifications : [...notifications, { more: true }])?.map((notification, index) => {
                                    const processedNotifications = processNotifications(currentPageNotifCount < NOTIFICATION_LIMIT ? notifications : [...notifications, { more: true }]);
                                    const currentNotification = processedNotifications[index];

                                    if (!currentNotification) return null;
                                    return (
                                        <NotificationCard
                                            key={`${currentNotification?.data?.gameId}-group`}
                                            notification={currentNotification}
                                            groupLength={currentNotification.groupCount}
                                            groupedNotificationIds={currentNotification.groupedNotificationIds}
                                            closeDrawer={() => setDrawer()}
                                            user={user}
                                            fetchUser={fetchUser}
                                            client={client}
                                            currentPageNotifCount={currentPageNotifCount}
                                            fetchNotifications={fetchNotifications}
                                            deleteNotification={deleteNotification}
                                            markAllRead={markAllRead}
                                        />
                                    );
                                })}
                            </div>
                        ) : (
                            <div className={'h-[calc(100vh-58px)] flex-center'}>
                                <RequestEmptyState className={`border-none`} shadow={false} message="No new notification to show." />
                            </div>
                        )}
                    </>
            }
        </div>
    )
}

export default NotificationDrawer
