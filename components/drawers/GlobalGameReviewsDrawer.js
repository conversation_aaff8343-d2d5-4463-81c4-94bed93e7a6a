import React from 'react'
import { useContext, useEffect, useState } from 'react'
import { ThreeDots } from 'react-loader-spinner'
import { UserContext } from '../../pages/_app';
import ENDPOINTS from "../../constants/endpoints.json";
import constantOptions from '../../constants/constantOptions'
import { DrawerContext } from '../../context/DrawerContext'
import useCheckDeviceScreen from '../../hooks/useCheckDeviceScreen'
import ClubReviewSection from '../club/ClubReviewSection'
import useClient from '../../graphql/useClient'
import { ALL_GAME_REVIEWS } from '../../constants/constantOptions'
import fetchClubGameReviews from '../../utils/club/fetchClubGameReviews';
import ReviewItem from '../club/ReviewItem';
import useFriends from '../../hooks/chat-v2/useFriends';
import InfiniteScroll from 'react-infinite-scroll-component';
import PulseSkeleton from '../common/PulseSkeleton';
import fetchUserGameReviews from '../../utils/user/fetchUserGameReviews';

const GlobalGameReviewsDrawer = ({ drawer, setDrawer }) => {

  const { isMobile, isTablet, isDesktop, isWideScreen } = useCheckDeviceScreen()
  const { club, clubColor } = drawer
  const { user, fetchUser, token } = useContext(UserContext)
  const [isShowLoading, setIsShowLoading] = useState(true);
  const client = useClient();
  const [gameReviews, setGameReviews] = useState([])
  const [reviewsCount, setReviewsCount] = useState(0)
  const { allFriendsId } = useFriends([])
  const pageSize = process.env.CONFIG.MY_GROUP_LIST_LIMIT
  const [page, setPage] = useState(1)
  const [hasMore, setHasMore] = useState(false)

  useEffect(() => {
    if (drawer?.selectedClub) {
      fetchGameReviews()
    } else {
      fetchGameReviewsByUser()
    }
  }, [drawer?.selectedClub, drawer?.golferId])

  const fetchGameReviews = async (pageToLoad = page) => {
    try {
      const result = await fetchClubGameReviews({
        token,
        userId: user?.id,
        clubId: drawer?.selectedClub?.id,
        page: pageToLoad - 1,
        limit: 10
      });
      setPage(pageToLoad)
      if (pageToLoad !== 1) {
        setGameReviews((prev) => [...prev, ...result.reviews])
      } else {
        setGameReviews(result.reviews)
      }
      setReviewsCount(result.reviewsCount);
      setHasMore(result.hasMore)
      setIsShowLoading(false)
    } catch (error) {
      console.log('Error in fetchGameReviews:', error);
    }
  }

  const fetchGameReviewsByUser = async (pageToLoad = page) => {
    try {
      const result = await fetchUserGameReviews({
        token,
        userId: user?.id,
        golferId: drawer?.golferId,
        page: pageToLoad - 1,
        limit: 10
      });
      setPage(pageToLoad)
      if (pageToLoad !== 1) {
        setGameReviews((prev) => [...prev, ...result.reviews])
      } else {
        setGameReviews(result.reviews)
      }
      setReviewsCount(result.reviewsCount);
      setHasMore(result.hasMore)
      setIsShowLoading(false)
    } catch (error) {
      console.log('Error in fetchGameReviewsByUser:', error);
    }
  }

  return (
    <div
      id="map-drawer-clubs"
      className='bottom-0 right-0 h-full'
      style={{
        minWidth: (isDesktop || isWideScreen) ? '500px' : '100%',
        boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.14)',
        zIndex: 2
      }}>
      <div className="relative bg-white w-full h-full flex flex-col items-center">
        {isShowLoading && (
          <div className='absolute w-full flex-center h-full ' style={{ zIndex: 99999 }}>
            <ThreeDots
              visible={true}
              height="50"
              width="50"
              color={"#098089"}
              radius="9"
              ariaLabel="three-dots-loading"
              wrapperStyle={{}}
              wrapperClass=""
            />
          </div>
        )}
        <div
          className="pl-[2px] absolute w-full"
          style={{
            bottom: 45, // Add space for the button at bottom
            top: 0,
            overflowX: 'hidden'
          }}>
          <div className={`flex justify-between flex-center relative bg-lightestgray min-h-[58px]`} >
            {drawer?.customBackClickHandler &&
              <div className='flex text-16 font-medium absolute cursor-pointer text-gray'
                style={{ left: 10 }}
                onClick={() => {
                  drawer?.customBackClickHandler()
                }}
              >
                <img width={15}
                  style={{ transform: 'rotate(90deg)', marginRight: 10 }}
                  src='/svg/arrow-down.svg' />Back
              </div>
            }
            <div className='text-18 md:text-20 font-medium h-[58px] flex-center bg-lightestgray relative'>Game Reviews</div>
            <div
              onClick={() => {
                setDrawer()
              }}
              className="cursor-pointer absolute" style={{ right: 20, top: 20 }}>
              <img src='/svg/CrossIconBlack.svg' />
            </div>
          </div>
          <div className='w-full flex-1 px-md pt-md'>
            <InfiniteScroll
              height={(window.innerHeight - 103)}
              dataLength={gameReviews?.length}
              next={() => {
                if (drawer?.selectedClub) {
                  fetchGameReviews((page + 1))
                } else {
                  fetchGameReviewsByUser((page + 1))
                }
              }}
              hasMore={hasMore}
              loader={<CustomLoader />}
              scrollThreshold={0.8}>
              {gameReviews.map((review) => (
                <ReviewItem
                  isReviewByUser={!drawer?.selectedClub}
                  key={review?.id}
                  review={review}
                  allFriendsId={allFriendsId}
                  customBackClickHandler={drawer?.customBackClickHandler}
                  imageHeight='169px'
                  imageWidth='428px'
                />
              ))}
            </InfiniteScroll>
          </div>
        </div>
      </div>
    </div>)
}

export default GlobalGameReviewsDrawer

const CustomLoader = () => <div className="ml-m-7px"><PulseSkeleton times={3} /></div>
