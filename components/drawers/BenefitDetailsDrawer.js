import React, { useContext, useEffect, useState } from 'react'
import useCheckDeviceScreen from '../../hooks/useCheckDeviceScreen'
import { UserContext } from '../../pages/_app';
import constantOptions from '../../constants/constantOptions'
import { ModalContext } from '../../context/ModalContext'
import { useRouter } from 'next/router'
import { RichTextDisplay } from '../common'
import useQuery from '../../graphql/hooks/useQuery'
import { GET_BENEFIT_DETAILS } from '../../graphql/queries/benefits'
import updateCount from '../../utils/benefits/updateCount'
import useThumbnail from '../../hooks/useThumbnail';
import { ThreeDots } from 'react-loader-spinner';


const BenefitDetailsDrawer = ({ drawer, setDrawer }) => {

    const { isMobile } = useCheckDeviceScreen();
    const { user, token } = useContext(UserContext)
    const { setModal } = useContext(ModalContext);
    const router = useRouter()
    const { benefit_id } = drawer
    const { data } = useQuery(GET_BENEFIT_DETAILS, {
        benefit_id,
    })
    const [benefit, setBenefit] = useState([])
    const [liked, setLiked] = useState(benefit?.views?.includes(user.id))
    const { thumbnailUrl } = useThumbnail(benefit?.photo, 1280)
    const [loading, setLoading] = useState(false)


    useEffect(() => {
        if (benefit && user) {
            setLiked(benefit?.views?.includes(user.id))
        }
    }, [benefit, user])

    useEffect(() => {
        setLoading(true)
        if (data) {
            if (!data?.benefit_by_pk) {
                setDrawer()
                router.push({
                    pathname: '/dashboard/errors/404'
                })
            }
            setBenefit(data?.benefit_by_pk)
            setLoading(false)
        }
    }, [data])

    return (
        <div
            className='fade-in bg-white shadow-lg allow-scroll'
            style={{
                width: isMobile ? "100%" : "500px",
                height: '100vh',
                overflowY: 'scroll',
                position: 'fixed',
                top: 0,
                right: 0,
                zIndex: 9999,
                WebkitOverflowScrolling: 'touch'
            }}
        >
            <div className='bg-lightestgray flex justify-between py-md flex-center relative'>
                <div className='text-21 font-medium'>Benefit Details</div>
                <div
                    onClick={() => {
                        setDrawer()
                        router.push({ pathname: "/dashboard/benefits" })
                    }}
                    className="cursor-pointer absolute right-[20px] top-[25px]">
                    <img src='/svg/CrossIconBlack.svg' />
                </div>
            </div>
            <>
                {loading ? (
                    <div className='h-[500px] w-full flex-center'>
                        <ThreeDots
                            visible={true}
                            height="50"
                            width="50"
                            color="#098089"
                            radius="9"
                            ariaLabel="three-dots-loading"
                            wrapperStyle={{}}
                            wrapperClass=""
                        />
                    </div>
                ) : (
                    <div className="relative flex flex-col items-center flex-1 w-full p-md">
                        <div className="container flex flex-col justify-center px-md lg:px-0"
                            style={{ zIndex: 1 }}>
                            <div
                                className="h-56 rounded-lg cursor-pointer z-20"
                                onClick={() => {
                                    setModal({
                                        type: "image-preview",
                                        image: thumbnailUrl,
                                        width: 600,
                                        height: 600
                                    })
                                }}
                                style={{
                                    backgroundImage: `url("${thumbnailUrl}")`,
                                    backgroundPosition: 'center',
                                    backgroundSize: 'contain',
                                    backgroundRepeat: 'no-repeat',
                                }}></div>
                            <div className="bg-white rounded-b-lg pb-22">
                                <div className="flex items-center pt-md text-24 font-medium">
                                    {benefit?.title}
                                </div>
                                <div className="text-14 font-medium py-sm text-gray uppercase">
                                    {benefit?.category}
                                </div>
                                <div
                                    className="benefit-details text-14 text-gray"
                                    style={{
                                        whiteSpace: 'pre-line',
                                    }}>
                                    {benefit?.description ?
                                        <RichTextDisplay content={benefit?.description} /> : null
                                    }
                                </div>
                                <div className="flex">
                                    {benefit?.promo_code && (
                                        <div
                                            onClick={() => {
                                                setModal({
                                                    type: 'promo-code',
                                                    title: 'Promo Code',
                                                    promo_code: benefit?.promo_code,
                                                    benefit,
                                                    user
                                                })
                                                if (user?.role === 'user') {
                                                    updateCount(user?.id, constantOptions?.BENEFIT_CLICK_TYPE?.REVEAL, benefit?.id, token)
                                                }
                                            }}
                                            className="flex-1 mt-4 cursor-pointer text-darkteal hover:underline">
                                            REVEAL BENEFIT
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>
                )}

            </>
        </div>
    )
}

export default BenefitDetailsDrawer