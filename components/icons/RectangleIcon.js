import React, { useState } from 'react'

const RectangleIcon = ({ isActive = false, onClick = () => { } }) => {
    const [isHovered, setIsHovered] = useState(false);

    const renderIcon = () => {
        if (isActive) {
            return (
                <img src="/icons/RectangleTeal.svg" alt="RectangleTeal" />
            )
        } else if (isHovered) {
            return (
                <img src="/icons/RectangleTeal.svg" alt="RectangleTeal" />
            )
        } else {
            return (
                <img src="/icons/RectangleGray.svg" alt="RectangleGray" />
            )
        }
    }


    return (
        <div className={`h-[40px] w-[40px] rounded-full hover:bg-tealTierBg bg-${isActive ? 'tealTierBg' : ''} flex-center cursor-pointer`}
            onClick={onClick}
            onMouseOver={e => setIsHovered(true)}
            onMouseEnter={e => setIsHovered(true)}
            onMouseLeave={e => setIsHovered(false)}
        >
            {renderIcon()}
        </div>
    )
}

export default RectangleIcon