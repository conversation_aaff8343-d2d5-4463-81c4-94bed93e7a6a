import React, { useState } from 'react'
import Image from 'next/image'

const ThreeDotsIcon = ({ isActive = false }) => {
    const [isHovered, setIsHovered] = useState(false);

    const renderIcon = () => {
        if (isActive) {
            return (
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M7.99967 8.66616C8.36786 8.66616 8.66634 8.36768 8.66634 7.99949C8.66634 7.6313 8.36786 7.33282 7.99967 7.33282C7.63148 7.33282 7.33301 7.6313 7.33301 7.99949C7.33301 8.36768 7.63148 8.66616 7.99967 8.66616Z" stroke="#098089" style={{ stroke: "#098089", strokeColor: "color(display-p3 0.0353 0.5020 0.5373)", strokeOpacity: 1 }} strokeWidth="1.33333" strokeLinecap="round" strokeLinejoin="round" />
                    <path d="M7.99967 4.00051C8.36786 4.00051 8.66634 3.70203 8.66634 3.33384C8.66634 2.96565 8.36786 2.66718 7.99967 2.66718C7.63148 2.66718 7.33301 2.96565 7.33301 3.33384C7.33301 3.70203 7.63148 4.00051 7.99967 4.00051Z" stroke="#098089" style={{ stroke: "#098089", strokeColor: "color(display-p3 0.0353 0.5020 0.5373)", strokeOpacity: 1 }} strokeWidth="1.33333" strokeLinecap="round" strokeLinejoin="round" />
                    <path d="M7.99967 13.3333C8.36786 13.3333 8.66634 13.0349 8.66634 12.6667C8.66634 12.2985 8.36786 12 7.99967 12C7.63148 12 7.33301 12.2985 7.33301 12.6667C7.33301 13.0349 7.63148 13.3333 7.99967 13.3333Z" stroke="#098089" style={{ stroke: "#098089", strokeColor: "color(display-p3 0.0353 0.5020 0.5373)", strokeOpacity: 1 }} strokeWidth="1.33333" strokeLinecap="round" strokeLinejoin="round" />
                </svg>
            )
        } else if (isHovered) {
            return (
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M7.99967 8.66616C8.36786 8.66616 8.66634 8.36768 8.66634 7.99949C8.66634 7.6313 8.36786 7.33282 7.99967 7.33282C7.63148 7.33282 7.33301 7.6313 7.33301 7.99949C7.33301 8.36768 7.63148 8.66616 7.99967 8.66616Z" stroke="#098089" style={{ stroke: "#098089", strokeColor: "color(display-p3 0.0353 0.5020 0.5373)", strokeOpacity: 1 }} strokeWidth="1.33333" strokeLinecap="round" strokeLinejoin="round" />
                    <path d="M7.99967 4.00051C8.36786 4.00051 8.66634 3.70203 8.66634 3.33384C8.66634 2.96565 8.36786 2.66718 7.99967 2.66718C7.63148 2.66718 7.33301 2.96565 7.33301 3.33384C7.33301 3.70203 7.63148 4.00051 7.99967 4.00051Z" stroke="#098089" style={{ stroke: "#098089", strokeColor: "color(display-p3 0.0353 0.5020 0.5373)", strokeOpacity: 1 }} strokeWidth="1.33333" strokeLinecap="round" strokeLinejoin="round" />
                    <path d="M7.99967 13.3333C8.36786 13.3333 8.66634 13.0349 8.66634 12.6667C8.66634 12.2985 8.36786 12 7.99967 12C7.63148 12 7.33301 12.2985 7.33301 12.6667C7.33301 13.0349 7.63148 13.3333 7.99967 13.3333Z" stroke="#098089" style={{ stroke: "#098089", strokeColor: "color(display-p3 0.0353 0.5020 0.5373)", strokeOpacity: 1 }} strokeWidth="1.33333" strokeLinecap="round" strokeLinejoin="round" />
                </svg>
            )
        } else {
            return (
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M7.99967 8.66616C8.36786 8.66616 8.66634 8.36768 8.66634 7.99949C8.66634 7.6313 8.36786 7.33282 7.99967 7.33282C7.63148 7.33282 7.33301 7.6313 7.33301 7.99949C7.33301 8.36768 7.63148 8.66616 7.99967 8.66616Z" stroke="#333333" style={{ stroke: "#333333", strokeColor: "color(display-p3 0.2000 0.2000 0.2000)", strokeOpacity: 1 }} strokeWidth="1.33333" strokeLinecap="round" strokeLinejoin="round" />
                    <path d="M7.99967 4.00051C8.36786 4.00051 8.66634 3.70203 8.66634 3.33384C8.66634 2.96565 8.36786 2.66718 7.99967 2.66718C7.63148 2.66718 7.33301 2.96565 7.33301 3.33384C7.33301 3.70203 7.63148 4.00051 7.99967 4.00051Z" stroke="#333333" style={{ stroke: "#333333", strokeColor: "color(display-p3 0.2000 0.2000 0.2000)", strokeOpacity: 1 }} strokeWidth="1.33333" strokeLinecap="round" strokeLinejoin="round" />
                    <path d="M7.99967 13.3333C8.36786 13.3333 8.66634 13.0349 8.66634 12.6667C8.66634 12.2985 8.36786 12 7.99967 12C7.63148 12 7.33301 12.2985 7.33301 12.6667C7.33301 13.0349 7.63148 13.3333 7.99967 13.3333Z" stroke="#333333" style={{ stroke: "#333333", strokeColor: "color(display-p3 0.2000 0.2000 0.2000)", strokeOpacity: 1 }} strokeWidth="1.33333" strokeLinecap="round" strokeLinejoin="round" />
                </svg>)
        }
    }

    return (
        <div
            onMouseOver={e => setIsHovered(true)}
            onMouseEnter={e => setIsHovered(true)}
            onMouseLeave={e => setIsHovered(false)}
        >
            {renderIcon()}
        </div>
    )
}

export default ThreeDotsIcon
