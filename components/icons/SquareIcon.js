import React, { useState } from 'react'

const SquareIcon = ({ isActive = false, onClick = () => { } }) => {
    const [isHovered, setIsHovered] = useState(false);

    const renderIcon = () => {
        if (isActive) {
            return (
                <img src="/icons/SquareTeal.svg" alt="SquareTeal" />
            )
        } else if (isHovered) {
            return (
                <img src="/icons/SquareTeal.svg" alt="SquareTeal" />
            )
        } else {
            return (
                    <img src="/icons/SquareGray.svg" alt="SquareGray" />
            )
        }
    }


    return (
        <div className={`h-[40px] w-[40px] rounded-full hover:bg-tealTierBg bg-${isActive ? 'tealTierBg' : ''} flex-center cursor-pointer`}
            onClick={onClick}
            onMouseOver={e => setIsHovered(true)}
            onMouseEnter={e => setIsHovered(true)}
            onMouseLeave={e => setIsHovered(false)}
        >
            {renderIcon()}
        </div>
    )
}

export default SquareIcon