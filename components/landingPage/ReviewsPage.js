import React from "react";
import ReviewCard from "./ReviewCard";
import useCheckDeviceScreen from "../../hooks/useCheckDeviceScreen";
import { Swiper, SwiperSlide } from 'swiper/react';

// Import Swiper styles
import 'swiper/css';
import 'swiper/css/pagination';

// import required modules
import { Pagination, Autoplay } from 'swiper/modules';

const pagination = {
    clickable: true,
    renderBullet: function (index, className) {
        return '<span class="' + className + '">' + '</span>';
    },
};

const reviews = [
    { description: "Just had a great time this weekend with a TG member from FC.  I didn’t even request to play golf, just a cocktail in his locker room which is immaculate. Because of his trust in your system, my foursome and his friends nerded out on golf in a way impossible before your app. Keep up the good work.", name: "<PERSON>" },
    { description: "Thank you for this incredible service. I have met some amazing people and played some great courses that would've been all but impossible without <PERSON> Greens. With snow on the ground here in NJ I am jealous of your rounds in Hawaii, but I'm quite looking forward to a few rounds already lined up this year with people I've met through TG", name: "<PERSON>" },
    { description: "I am newer member and have just begun to use the platform.  My first experience was a trip to the DC area and things couldn’t have worked out better.  Our hosts were exceptional and the golfing experience was better than our high expectations.  Thank you for all you do to make this run as smoothly as it does.", name: "Bill C" },
    { description: "TG has enabled multiple highly positive interactions between myself, my son, and members at others clubs in the San Diego and Palm Springs area as well as hosting here at Granite Club in Toronto, Canada.", name: "Rob R" },
]

const ReviewsPage = () => {

    const { isMobile, isDesktop } = useCheckDeviceScreen()

    return (
        <>
            <div id="review" className="reviewpage relative bg-offwhite flex flex-col h-[70%] justify-center xxxs:py-md md:p-xxl">
                <div className="absolute left-0 top-0"><img src="/svg/ReviewGolfStickSmall.svg" /></div>
                <div className="absolute left-0 top-0"><img src="/svg/ReviewGolfStickBig.svg" /></div>
                <div className="absolute left-0 bottom-0"><img src="/svg/ReviewGolfBall.svg" /></div>
                <div className="absolute right-0 bottom-0"><img src="/svg/ReviewIronGolfClub.svg" /></div>
                <div className="xxxs:text-24 text-center md:text-48 lg:text-40 font-medium self-center md:pt-[100px]">Hear from Our Golfing <span className="text-darkteal">Family</span></div>
                <div className='xxxs:text-16 md:text-18 py-lg xxxs:w-full md:w-3/5 text-center self-center md:mb-lg' style={{ color: "#666666" }}>
                    Thousand Greens is a private and confidential community of golfers who belong to highly regarded and top rated private clubs.
                </div>
                <div className="mb-md block w-full landing-page px-sm md:px-0" style={{ minHeight: 300 }}>
                    <Swiper
                        autoplay={{
                            delay: 3000,
                            disableOnInteraction: false,
                        }}
                        speed={3000}
                        slidesPerView={isMobile ? 1 : 1.8}
                        loop={true}
                        spaceBetween={30}
                        centeredSlides={true}
                        pagination={{
                            clickable: true,
                        }}
                        modules={[Pagination, Autoplay]}
                        className="mySwiper"
                    >
                        {
                            reviews.map((review, i) => {
                                return (
                                    <SwiperSlide key={i}>
                                        <ReviewCard key={i} review={review} />
                                    </SwiperSlide>
                                )
                            })
                        }
                    </Swiper>
                </div>
                <img style={{ right: isDesktop ? 0 : -60, top: 20 }} className="absolute" src="/images/LandingPage/SmallGolfClub.svg" />
            </div>
        </>
    )
}

export default ReviewsPage;