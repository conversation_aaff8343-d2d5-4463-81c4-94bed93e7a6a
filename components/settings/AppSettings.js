import React, { useState, useContext } from 'react'
import { UserContext } from '../../pages/_app'
import TextInput from '../common/TextInput'
import useClient from '../../graphql/useClient'
import { UPDATE_SYSTEM_SETTING } from '../../graphql/mutations/system_setting'
import { ThreeDots } from 'react-loader-spinner'
import { Checkbox, RadioSelect, TextArea } from '../common'
import CustomButton from '../buttons/CustomButton'
import ENDPOINTS from '../../constants/endpoints.json'
import { ADMIN_CUSTOM_MESSAGE_MAX_LENGTH } from '../../graphql/queries/user'
import constantOptions from '../../constants/constantOptions'
import { DrawerContext } from '../../context/DrawerContext'

const appSettings = ['push', 'web panel', 'playing card', 'text']

export default function AppSettings({ refresh, settings }) {
    const [fields, setFields] = useState(settings)
    const [loading, setLoading] = useState(false)
    const [saved, setSaved] = useState(false)
    const client = useClient()
    const { token } = useContext(UserContext)
    const [message, setMessage] = useState('')
    const [sendingNotification, setSendingNotification] = useState(false)
    const [selectedTypes, setSelectedTypes] = useState({
        push: true,
        'web panel': true,
        'playing card': true,
        text: true
    })
    const { setDrawer } = useContext(DrawerContext)
    async function saveSettings() {
        await client
            .request(UPDATE_SYSTEM_SETTING, {
                name: "app_version",
                value: fields
            })
            .then(console.log)
        await refresh()
        setSaved(true)
        setLoading(false)
    }

    const handleSendNotification = async () => {
        if (!message.trim()) {
            return
        }

        const selectedChannels = Object.entries(selectedTypes)
            .filter(([_, isSelected]) => isSelected)
            .map(([type]) => type)

        if (selectedChannels.length === 0) {
            return
        }

        try {
            setSendingNotification(true)
            const response = await fetch(ENDPOINTS.SEND_NOTIFICATION, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify({
                    message: message.trim(),
                    sendPush: selectedTypes['push'],
                    sendWebPanel: selectedTypes['web panel'],
                    sendPlayingCard: selectedTypes['playing card'],
                    sendText: selectedTypes['text']
                }),
            })

            if (response.ok) {
                setMessage('')
                setSelectedTypes({
                    push: false,
                    'web panel': false,
                    'playing card': false,
                    text: false
                })
            } else {
                console.error('Failed to send notification')
            }
        } catch (error) {
            console.error('Error sending notification:', error)
        } finally {
            setSendingNotification(false)
        }
    }

    const handleSendMail = async () => {
        setDrawer({
            type: constantOptions.DRAWER_TYPE.ADMIN_SEND_MAIL,
            global: true,
        })
    }

    return (
        <div className='flex w-full'>
            <div className="bg-white rounded-lg p-xl flex flex-col shadow"
                style={{
                    width: 500
                }}
            >
                <RadioSelect
                    disableJustifyBetween={true}
                    title="Force Update"
                    buttonWidth="40%"
                    value={fields?.force === 1 ? 'yes' : "no"}
                    update={(value) => {
                        setSaved(false)
                        setFields({ ...fields, force: value === "yes" ? 1 : 0 })
                    }
                    }
                    options={["yes", "no"]}
                />
                <TextInput
                    title={"Message iOS"}
                    value={fields?.message?.ios}
                    update={(value) => {
                        setSaved(false)
                        setFields({ ...fields, message: { ...fields?.message, ios: value } })
                    }}
                />
                <TextInput
                    title={"Message Android"}
                    value={fields?.message?.android}
                    update={(value) => {
                        setSaved(false)
                        setFields({ ...fields, message: { ...fields?.message, android: value } })
                    }}
                />
                <TextInput
                    title={"Version iOS"}
                    type={'number'}
                    value={fields?.version?.ios}
                    update={(value) => {
                        setSaved(false)
                        setFields({ ...fields, version: { ...fields?.version, ios: +value } })
                    }}
                />
                <TextInput
                    title={"Version Android"}
                    type={'number'}
                    value={fields?.version?.android}
                    update={(value) => {
                        setSaved(false)
                        setFields({ ...fields, version: { ...fields?.version, android: +value } })
                    }}
                />
                <div className="flex justify-end mt-lg">
                    <div
                        onClick={saveSettings}
                        style={{ width: 90, height: 35 }}
                        className="flex-center rounded bg-darkteal text-white text-sm cursor-pointer">
                        {loading ? (
                            <ThreeDots
                                visible={true}
                                height="25"
                                width="25"
                                color={"#FFFFFF"}
                                radius="9"
                                ariaLabel="three-dots-loading"
                                wrapperStyle={{}}
                                wrapperClass=""
                            />
                        ) : saved ? (
                            'Saved!'
                        ) : (
                            'Save'
                        )}
                    </div>
                </div>
            </div>

            <div className='bg-white rounded-lg p-xl flex flex-col shadow ml-md w-[500px]'>
                <div className='w-full pt-4'>
                    <TextArea
                        title={'Add a Message'}
                        disableError={''}
                        error={''}
                        placeholder={"Type the note here"}
                        value={message}
                        className={' rounded-lg px-md'}
                        maxLength={500}
                        minHeight={100}
                        fontSize={"text-12"}
                        update={(val) => {
                            setMessage(val)
                        }} />
                    <div className='text-gray text-xs mt-2 text-right'>{ADMIN_CUSTOM_MESSAGE_MAX_LENGTH - (message ? message?.length : 0)}</div>
                </div>
                <div className='flex justify-between items-center'>
                    {appSettings.map((item, index) => (
                        <div key={index} className='flex items-center capitalize'>
                            <Checkbox
                                marginBottom={'0'}
                                value={selectedTypes[item]}
                                update={(val) => setSelectedTypes(prev => ({
                                    ...prev,
                                    [item]: val
                                }))}
                            />
                            <div className='pt-xs'>{item}</div>
                        </div>
                    ))}
                </div>
                <div className='flex justify-end mt-lg'>
                    <CustomButton
                        text={sendingNotification ? 'Sending...' : 'Send'}
                        marginX={'none'}
                        disabled={!message.trim() || !Object.values(selectedTypes).some(v => v) || sendingNotification}
                        onClick={handleSendNotification}
                    />
                </div>
            </div>
            <div className='max-w-[300px] flex flex-col items-center justify-center bg-white rounded-lg px-sm shadow ml-md h-[233px]'>
                <div className='h-[72px] w-[72px] bg-lighttealbackground2 rounded-full flex-center'>
                    <img src='/svg/Mail.svg' className='w-[32px] h-[32px]' />
                </div>
                <div className='text-12 text-center my-lg'>Generate and send custom email notifications to all active members from the Admin panel.</div>
                <CustomButton
                    text={'Send Mail'}
                    marginX={'none'}
                    width={'100%'}
                    onClick={handleSendMail}
                />
            </div>
        </div>
    )
}