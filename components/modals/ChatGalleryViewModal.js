import React, { useState, useEffect } from 'react'
import { FiDownload } from 'react-icons/fi'
import { IoMdCloseCircleOutline } from 'react-icons/io'
import downloadImage from '../../utils/helper/downloadImage'

const ChatGalleryViewModalContext = React.createContext()
export { ChatGalleryViewModalContext }

function Modal({ chatImageModal, setChatImageModal }) {
  
  const [transition, setTransition] = useState(false)

  useEffect(() => {
    setTransition(true)
  }, [])

  const [images] = useState(chatImageModal?.images)
  const [imageIndex, setImageIndex] = useState(chatImageModal?.imageIndex);

  return (
    <div
      id='ChatImageViewModalContext'
      className="fixed inset-0"
      style={{
        backgroundColor: 'rgba(0,0,0,0.5)',
        boxSizing: 'border-box',
        minHeight: '100vh',
        zIndex: 1000000000,
        overflowY: 'scroll',
        opacity: transition ? 1 : 0,
        transition: 'opacity 0.2s ease-in-out',
      }}>
      <div className="flex flex-end absolute py-sm px-md" style={{ right: 0 }}>
        <div onClick={() => downloadImage(images[imageIndex]?.image_url)} className="cursor-pointer pr-lg">
          <FiDownload style={{
            width: '28px',
            height: '28px',
            color: 'white'
          }} />
        </div>
        <div onClick={() => setChatImageModal()} className="cursor-pointer">
          <IoMdCloseCircleOutline
            style={{
              width: '32px',
              height: '32px',
              color: 'white'
            }}
          />
        </div>
      </div>

      <div
        className="flex-center w-full px-md"
        style={{ minHeight: '100%' }}
      >
        <div
          onClick={(e) => {
            e.stopPropagation()
          }}
          className={`rounded-lg flex flex-col flex-center relative w-full`}
          style={{
            transform: transition
              ? 'scale(1) translateY(0px)'
              : 'scale(0.9) translateY(50px)',
            transition: 'transform 0.2s ease-in-out',
          }}
        >
          <div className="w-full flex flex-col flex-center">
            <div className="relative">
              {
                imageIndex !== 0 && (
                  <button
                    type="button"
                    onClick={() => {
                      setImageIndex(imageIndex - 1)
                    }}
                    disabled={imageIndex === 0}
                    className="image-gallery-icon image-gallery-left-nav"
                    aria-label="Previous Slide"
                  >
                    <svg style={{ width: '20px' }} className="image-gallery-svg" xmlns="http://www.w3.org/2000/svg" viewBox="6 0 12 24" fill="none" stroke="currentColor" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round">
                      <polyline points="15 18 9 12 15 6"></polyline>
                    </svg>
                  </button>
                )
              }
              {
                imageIndex !== (images.length - 1) && (
                  <button
                    onClick={() => {
                      setImageIndex(imageIndex + 1)
                    }}
                    type="button"
                    disabled={imageIndex === (images.length - 1)}
                    className="image-gallery-icon image-gallery-right-nav"
                    aria-label="Next Slide">
                    <svg style={{ width: '20px' }} className="image-gallery-svg" xmlns="http://www.w3.org/2000/svg" viewBox="6 0 12 24" fill="none" stroke="currentColor" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round">
                      <polyline points="9 18 15 12 9 6"></polyline>
                    </svg>
                  </button>
                )
              }

              {
                <div className='relative'>
                  <img
                    style={{
                      maxHeight: 'calc(100vh - 80px)',
                      maxWidth: 'unset',
                      objectFit: 'contain',
                      width: '100%',
                      padding: '3rem'
                    }}
                    alt="Crop me"
                    src={images[imageIndex]?.image_url || images[imageIndex]}
                  />
                  <div
                    style={{
                      position: 'absolute',
                      justifyContent: 'center',
                      width: '100%',
                      bottom: '0px'
                    }}
                    className="flex"
                  >
                    <div style={{ position: 'unset' }} className="image-gallery-index">
                      <span className="image-gallery-index-current">{imageIndex + 1}</span>
                      <span className="image-gallery-index-separator"> / </span>
                      <span className="image-gallery-index-total">{images.length}</span>
                    </div>
                  </div>
                </div>
              }
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}


export default function ChatGalleryViewGlobalModal({ user, children }) {
  const [chatImageModal, setChatImageModal] = useState()
  return (
    <ChatGalleryViewModalContext.Provider value={{ chatImageModal, setChatImageModal }}>
      {children}
      {chatImageModal && <Modal chatImageModal={chatImageModal} setChatImageModal={setChatImageModal} />}
    </ChatGalleryViewModalContext.Provider>
  )
}