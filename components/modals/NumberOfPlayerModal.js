import React, { useContext, useEffect, useState } from 'react'
import constantOptions from '../../constants/constantOptions'
import CustomButton from '../buttons/CustomButton'
import ENDPOINTS from '../../constants/endpoints.json'
import { UserContext } from '../../pages/_app'

const NumberOfPlayerModal = ({ modal, setModal }) => {

  const [participantCount, setParticipantCount] = useState(1)
  const { token, user } = useContext(UserContext)
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    setParticipantCount(modal?.request?.number_of_players)
  }, [modal])

  const updateParticipants = async () => {
    setLoading(true)
    await fetch(`${ENDPOINTS?.ADMIN_UPDATE_PLAYERS}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({ requestId: modal?.request?.id, players: participantCount }),
    })
      .then((data) => data.json())
      .then((data) => {
        if (data?.status) {
          setModal()
        }
      })
      .finally(() => {
        setLoading(false)
      })
  }

  return (
    <>
      <div className='text-24 font-medium flex-center mb-md'>Confirm No. Of Players</div>
      <div className="w-full text-center text-grayLight  mb-lg">{user?.role === 'admin' ? 'Please select the number of players to update the same.' : 'Please re-confirm the number of players who played against this request.'}</div>
      <div className='flex w-full justify-evenly'>
        {constantOptions?.SELECT_PARTICIPANTS.map((t) => {
          return (<div
            onClick={() => setParticipantCount(t)}
            key={t} className={`rounded-md w-[100px] h-[42px] text-16 flex-center cursor-pointer border-2 ${participantCount === t ? "border-darkteal text-darkteal bg-tealTierBg" : "border-lightestgray"} hover:border-darkteal hover:text-darkteal hover:bg-tealTierBg`}>{t}</div>)
        })}
      </div>
      <div className="flex justify-center mt-xl w-full">
        <CustomButton
          text="Cancel"
          color='lightestgray'
          textColor='black'
          loading={loading}
          onClick={() => setModal()}
        />
        <CustomButton
          loading={loading}
          text="Continue"
          onClick={() => {
            updateParticipants()
          }}
        />
      </div>
    </>
  )
}

export default NumberOfPlayerModal