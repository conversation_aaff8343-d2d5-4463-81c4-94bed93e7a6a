import React, { useState, useEffect, useContext } from 'react'
import DateRange from '../common/DateRange'
import { UserContext } from '../../pages/_app'
import TextInput from '../common/TextInput'
import Checkbox from '../common/Checkbox'
import 'firebase/compat/auth'
import 'firebase/compat/storage'
import * as yup from 'yup'
import Moment from 'moment'
import { DateSelect, RichTextEditor, ToolTip } from '../common'
import Checkbox2 from '../common/Checkbox2'
import useCheckDeviceScreen from '../../hooks/useCheckDeviceScreen'
import CustomButton from '../buttons/CustomButton'
import MultiplePhotoUpload2 from '../common/MultiplePhotoUpload2'
import MESSAGES from '../../constants/messages'
import ENDPOINTS from "../../constants/endpoints.json"
import { uploadFile } from '../../utils/upload'
import firebase from 'firebase/compat/app'
import 'firebase/compat/auth'
import CheckboxMultiselect from '../common/CheckboxMultiselect'
import InfoIcon from '../icons/InfoIcon'
import moment from 'moment'
import constantOptions from '../../constants/constantOptions'
import ImageCropModal from './ImageCropModal'


export default function EventModal({ modal, setModal, globalDropdownState }) {
    const { refresh = () => { } } = modal
    const { isMobile } = useCheckDeviceScreen()
    const [loading, setLoading] = useState(false)
    const { user, token } = useContext(UserContext)
    const [eventForm, setEventForm] = useState({
        title: modal.event && modal.event.title,
        eventType: modal.event && modal.event.event_type || "Personal",
        details: modal.event && modal.event.details || "",
        dateRange: modal.event && { from: modal.event.start_date, to: modal.event.end_date, },
        registrationDate: modal.event && new Date(modal.event.registration_start_date),
        image: modal.event && modal.event.image,
        requestEmail: modal.event && modal.event.request_email,
        redirectUrl: modal.event && modal.event.redirect_url,
        forAll: modal.event ? modal.event.event_for_all : user?.visibleToPublic ? true : false,
        redirectionType: modal.event && modal.event.redirect_url ? "Website" : "Email",
        notifyUsers: modal.event ? modal.event.is_mailable : false,
        // notificationReceivers: (modal.event && modal.event.is_us_only) ? 'US Users only' : 'Worldwide',
        isForUsOnly: modal?.event?.is_us_only
    })
    const [eventForAll, setEventForAll] = useState(modal?.event?.event_for_all)
    const [myTgCommunity, setMyTgCommunity] = useState(!user?.visibleToPublic ? true : false)
    const [allMyFriends, setAllMyFriends] = useState(true)
    const [onlyTgGroups, setOnlyTgGroups] = useState(true)
    const [errors, setErrors] = useState({})
    const [showErrors, setShowErrors] = useState(false)
    const [eventPhoto, setEventPhoto] = useState()
    const [groups, setGroups] = useState([])
    const [allGroupsSelected, setAllGroupsSelected] = useState(true)
    const [allMyTgGroups, setAllMyTgGroups] = useState([])
    const [totalGroupsCount, setTotalGroupsCount] = useState(0)
    const [page, setPage] = useState(1)
    const [myGroupId, setMyGroupId] = useState()
    const [showImageCrop, setShowImageCrop] = useState(false)
    const [base64ForCrop, setBase64ForCrop] = useState(null)
    const ADMIN_ID = process.env.NEXT_PUBLIC_ADMIN_ID
    const pageSize = process.env.CONFIG.MY_GROUP_LIST_LIMIT
    const [eventDp, setEventDp] = useState(modal?.event?.image || null)
    const [eventUploadImage, setEventUploadImage] = useState(null)

    // Cleanup URLs on unmount
    useEffect(() => {
        return () => {
            if (eventDp && !eventDp.startsWith('http')) {
                URL.revokeObjectURL(eventDp)
            }
        }
    }, [])

    // Handle image crop completion
    useEffect(() => {
        if (eventUploadImage && !base64ForCrop) {
            setShowImageCrop(false)
        }
    }, [eventUploadImage, base64ForCrop])

    // Show crop modal when base64 is set
    useEffect(() => {
        if (base64ForCrop) {
            setShowImageCrop(true)
        }
    }, [base64ForCrop])

    useEffect(() => {
        const getGroupData = async () => {
            try {
                await fetch(ENDPOINTS?.GET_MY_GROUP_DETAILS, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        ['Authorization']: `Bearer ${token}`,
                    },
                    body: JSON.stringify({
                        groupId: modal?.groupId,
                        userId: user?.id
                    }),
                })
                    .then((data) => data.json())
                    .then((data) => setMyGroupId(data?.id))
            } catch (error) {

            }
        }
        if (modal?.source === "chat") {
            getGroupData()
        }
    }, [])

    useEffect(() => {
        if (modal?.event) {
            //Condition for All Thousand Greens 
            if (modal?.event?.event_for_all) {
                setEventForm({ ...eventForm, forAll: true })
                setMyTgCommunity(false)
            } else {
                //My TG Community
                setMyTgCommunity(true)
                //If created only for friends
                if (!modal?.event?.my_tg_group_id?.length) {
                    setAllMyFriends(true)
                    setOnlyTgGroups(false)
                    setAllGroupsSelected(true)
                } else {
                    //Checking Also created for friends
                    if (modal?.event?.for_friends) {
                        setAllMyFriends(true)
                    } else {
                        setAllMyFriends(false)
                    }
                    //Check for All groups selected
                    if (modal?.event?.my_tg_group_id[0] === -1 && modal?.event?.my_tg_group_id?.length === 1) {
                        setAllGroupsSelected(true)
                    } else {
                        //For selcted groups
                        setAllGroupsSelected(false)
                        setGroups(modal?.event?.chatChannels)
                    }
                }
            }
        }

    }, [modal?.event, allMyTgGroups])

    useEffect(() => {
        if (user?.role !== 'admin') {
            fetchGroupsList()
        }
    }, [])

    function validateEvent() {
        let newErrors = {}
        if (!eventForm.title) {
            newErrors.title = MESSAGES?.EVENT?.TITLE
        }
        if (!eventForm.eventType) {
            newErrors.type = MESSAGES?.EVENT?.PURPOSE
        }
        if (eventForm.redirectionType) {
            if (eventForm.redirectionType === "Email") {
                if (!eventForm.requestEmail) {
                    newErrors.requestEmail = MESSAGES?.EVENT?.EMAIL
                } else {
                    try {
                        yup.string()
                            .email(MESSAGES?.EVENT?.EMAIL)
                            .validateSync(eventForm.requestEmail)
                    } catch (e) {
                        newErrors.requestEmail = MESSAGES?.EVENT?.EMAIL
                    }
                }
            } else if (eventForm.redirectionType === "Website") {

                try {
                    yup.string()
                        .url(MESSAGES?.EVENT?.URL)
                        .required(MESSAGES?.EVENT?.URL)
                        .validateSync(eventForm.redirectUrl)
                } catch (e) {
                    newErrors.redirectUrl = MESSAGES?.EVENT?.URL
                }
            }
        }

        if (!eventForm.details || eventForm?.details.trim() == '' || eventForm?.details === "" || eventForm?.details?.replace(/<\/?[^>]+(>|$)/g, "").trim() === "") {
            newErrors.details = MESSAGES?.EVENT?.DETAILS
        }
        if (!eventForm.image) {
            newErrors.image = MESSAGES?.EVENT?.PHOTO
        }
        if (!eventForm.dateRange) {
            newErrors.date_range = MESSAGES?.EVENT?.DATE
        }
        if (eventForm.dateRange && (Moment.utc(eventForm.dateRange.from).isSameOrBefore(Moment.utc(eventForm.registrationDate)) || !eventForm?.registrationDate)) {
            newErrors.registration_start_date = MESSAGES?.EVENT?.REGISTRATION_DATE
        }

        setErrors(newErrors)
        return Object.keys(newErrors).length === 0
    }

    useEffect(() => {
        if (showErrors) {
            validateEvent()
        }
    }, [eventForm, showErrors])


    const handleSubmit = async () => {
        setLoading(true)
        const validate = validateEvent()
        if (validate) {
            let URL = modal?.isAdmin ?
                modal?.event
                    ? ENDPOINTS?.ADMIN_EDIT_EVENT
                    : ENDPOINTS?.ADMIN_CREATE_EVENT
                : modal?.event
                    ? ENDPOINTS?.EDIT_EVENT_V4
                    : ENDPOINTS?.CREATE_EVENT
            let body = {
                userId: firebase.auth().currentUser.uid,
                eventId: modal?.event?.event_id || modal?.event?.id,
                title: eventForm?.title,
                details: eventForm?.details,
                event_type: eventForm?.eventType,
                start_date: eventForm?.dateRange?.from,
                end_date: eventForm?.dateRange?.to,
                // registration_start_date: eventForm?.registrationDate,
                registration_start_date: moment(eventForm?.registrationDate).format('YYYY-MM-DD'),
                image: eventForm?.image,
                redirect_url: eventForm?.redirectUrl || '',
                request_email: eventForm?.requestEmail || '',
                event_for_all: modal?.source === "chat" ? false : (modal?.isAdmin && !modal?.event) ? true : eventForm?.forAll,
                is_mailable: modal?.isAdmin ? eventForm?.notifyUsers : false,
                is_us_only: eventForm?.isForUsOnly,
                forFriends: eventForm?.forAll ? false : allMyFriends,
                myTGGroupId: modal?.source === "chat" ? [myGroupId] : (!onlyTgGroups || eventForm?.forAll) ? [] : allGroupsSelected ? [-1] : groups.map((g) => g.id)
            }

            if (user?.role === 'admin') {
                delete body?.forFriends
                delete body?.myTGGroupId
                if (!modal?.event) {
                    delete body?.eventId
                    body = {
                        ...body,
                        approved: true,
                    }
                }
            } else {
                if (!modal?.event) {
                    delete body?.eventId
                    body = {
                        ...body,
                        approved: false,
                    }
                }
            }



            try {
                await fetch(URL, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        ['Authorization']: `Bearer ${token}`,
                    },
                    body: JSON.stringify(body),
                })
                    .then((data) => data.json())
                    .then((data) => { })
                setLoading(false)
                refresh()
                setModal()

            } catch (error) {
                console.log("Create Event----->", error);
            }
        } else {
            setLoading(false)
        }
    }

    const fetchGroupsList = async (pageToLoad = page) => {
        const URL = ENDPOINTS?.GET_GROUPS_FOR_PEGBOARD

        let body = {
            limit: pageSize,
            page: pageToLoad - 1
        }

        if (user?.role === 'user') {
            body = {
                ...body,
                userId: user?.id
            }
        }

        await fetch(URL, {
            method: "POST",
            headers: {
                'Content-Type': 'application/json',
                ['Authorization']: `Bearer ${token}`,
            },
            body: JSON.stringify(body)
        })
            .then((response) => response.json())
            .then((data) => {
                if (pageToLoad !== 1) {
                    setAllMyTgGroups((prev) => [...prev, ...data?.groups])
                    setTotalGroupsCount(data?.totalCount)
                } else {
                    setAllMyTgGroups(data?.groups)
                    setTotalGroupsCount(data?.totalCount)
                }
            })
        setPage(pageToLoad)
    }

    const handleSelection = (group) => {
        const temp = groups?.map(group => group?.id)
        if (temp?.includes(group?.id)) {
            const updated = groups?.filter((t) => t.id !== group?.id)
            if (updated?.length === 0) {
                setAllGroupsSelected(true)
            }
            setGroups(updated)

        } else {
            const updated = [...groups, group]
            if (updated?.length === allMyTgGroups?.length && groups?.length) {
                setAllGroupsSelected(true)
                setGroups([])
            } else {
                setAllGroupsSelected(false)
                setGroups(updated)
            }
        }
    }

    console.log(eventForm.image)

    return (
        <div className={`flex-col w-full pr-md ${loading ? "pointer-events-none mix-blend-luminosity" : ""}`}>
            <div className="flex flex-1 flex-col md:flex-row w-full pt-xl px-lg relative md:gap-10">
                <div className="flex flex-col flex-1">
                    <div className='mb-md'>
                        <TextInput
                            addClubClasses={true}
                            className={""}
                            title="Event Title"
                            placeholder={"Enter Title here"}
                            value={eventForm.title}
                            disableError={!errors.title}
                            update={(value) => {
                                setErrors({ ...errors, title: '' })
                                setEventForm({
                                    ...eventForm,
                                    title: value,
                                })
                            }}
                            error={errors.title}
                        />
                    </div>
                    <DateRange
                        singular
                        titleSize={isMobile ? '12' : '14'}
                        textSize='16'
                        fontWeight='normal'
                        placeholder={"Select date"}
                        error={errors.date_range}
                        value={eventForm.dateRange}
                        maxDateMonth={12}
                        update={(value) => {
                            setErrors({ ...errors, date_range: '' })
                            setEventForm({
                                ...eventForm,
                                dateRange: value,
                            })
                        }}
                        minDate={Moment().add(1, 'days')}
                        globalDropdownState={globalDropdownState}
                    />
                    {eventForm.dateRange && (
                        <>
                            <DateSelect
                                fontSize='16'
                                fontWeight='normal'
                                calendarIcon='/svg/GrayCalendar.svg'
                                title="Registration Begins"
                                placeholder={"Select date"}
                                error={errors.registration_start_date}
                                value={eventForm.registrationDate}
                                update={(value) => {
                                    setErrors({ ...errors, registration_start_date: '' })
                                    setEventForm({
                                        ...eventForm,
                                        registrationDate: value,
                                    })
                                }}
                                globalDropdownState={globalDropdownState}
                                minDate={Moment.utc().toDate()}
                                maxDate={Moment.utc(eventForm.dateRange.from)
                                    .add(-1, 'd')
                                    .toDate()}

                            />
                        </>
                    )}

                    <div className='event-details'>
                        <div className=' text-14 text-grayLight font-normal mb-sm'>Enter Details Here...</div>
                        <RichTextEditor
                            editorHtml={eventForm?.details}
                            updateEditorHtml={(value) => {
                                setEventForm({ ...eventForm, details: value })
                            }}
                            modules={{
                                toolbar: [['bold', 'italic', 'underline'], [{ 'list': 'ordered' }, { 'list': 'bullet' }], ['clean']],
                                clipboard: {
                                    matchVisual: false,
                                },
                            }}
                            formats={['bold', 'italic', 'underline', 'list']}
                            placeholder={"Enter here..."}
                        />
                    </div>

                    {errors?.details && <div
                        className="text-12 font-normal flex-center text-red"
                        style={{ color: 'red', height: 30 }}>
                        {errors.details}
                    </div>}
                    <div className='flex-flex-col mt-md'>
                        <div className="mb-sm text-16 font-medium ">Requests Info</div>
                        <div className={`flex mb-md`} >
                            <div className='mr-8'>
                                <Checkbox2
                                    value={eventForm?.redirectionType === "Email"}
                                    label={"Email"}
                                    update={(val) => setEventForm({ ...eventForm, redirectionType: "Email", redirectUrl: '' })}
                                    smallLabel={true}
                                    customClasses="text-14 text-black"
                                    roundedButton={true}
                                />
                            </div>
                            <div>
                                <Checkbox2
                                    value={eventForm?.redirectionType === "Website"}
                                    label={"Website"}
                                    update={(val) => setEventForm({ ...eventForm, redirectionType: "Website", requestEmail: '' })}
                                    smallLabel={true}
                                    customClasses="text-14 text-black"
                                    roundedButton={true}
                                />
                            </div>
                        </div>
                        <TextInput
                            border={'none'}
                            error={eventForm?.redirectionType === "Website" ? errors?.redirectUrl : errors?.requestEmail}
                            value={eventForm?.redirectUrl || eventForm?.requestEmail}
                            className={"border-none bg-lightestgray px-sm rounded-lg"}
                            placeholder={eventForm?.redirectionType === "Website" ? "Website" : "Email Address"}
                            update={(val) => {
                                if (eventForm?.redirectionType === "Website") {
                                    setErrors({ ...errors, redirectUrl: '' })
                                    setEventForm({ ...eventForm, redirectUrl: val })
                                } else {
                                    setErrors({ ...errors, requestEmail: '' })
                                    setEventForm({ ...eventForm, requestEmail: val })
                                }
                            }}
                        />
                    </div>

                </div>
                <div className="flex-col flex-1">
                    {((!modal?.isAdmin) || (modal?.isAdmin && modal?.event)) && modal?.event?.user_id !== ADMIN_ID ? (
                        <div className='mb-sm'>
                            <div className="mb-sm text-16 font-medium ">Event Purpose</div>
                            <div className={`flex`} >
                                <div className='mr-8'>
                                    <Checkbox2
                                        value={eventForm?.eventType === "Personal"}
                                        label={"Personal"}
                                        update={(val) => setEventForm({ ...eventForm, eventType: "Personal" })}
                                        smallLabel={true}
                                        customClasses="text-14 text-black"
                                        roundedButton={true}
                                    />
                                </div>
                                <div>
                                    <Checkbox2
                                        value={eventForm?.eventType === "Commercial"}
                                        label={"Commercial"}
                                        update={(val) => setEventForm({ ...eventForm, eventType: "Commercial" })}
                                        smallLabel={true}
                                        customClasses="text-14 text-black"
                                        roundedButton={true}
                                    />
                                </div>
                            </div>
                            {(eventForm?.eventType === "Commercial" && user?.role !== "admin") ? (
                                <div className='text-12 text-darkteal my-md'>{MESSAGES?.EVENT?.COMMERCIAL}</div>
                            ) : (
                                null
                            )}
                        </div>
                    ) : null}

                    {((modal?.isAdmin && !modal?.event)) &&
                        <Checkbox
                            customClasses={'text-14 mt-md'}
                            label="Notify users via Email"
                            value={eventForm?.notifyUsers}
                            onClick={() => {
                                setEventForm({ ...eventForm, notifyUsers: (!eventForm?.notifyUsers) })
                            }}
                            roundedButton={true}
                        />
                    }

                    {showImageCrop ? (
                        <ImageCropModal
                            modal={{
                                type: 'create-edit-group',
                                image: base64ForCrop,
                                setDp: setEventDp,
                                setUploadImage: setEventUploadImage,
                            }}
                            setBase64ForCrop={setBase64ForCrop}
                        />
                    ) : (
                        <MultiplePhotoUpload2
                            setBase64ForCrop={setBase64ForCrop}
                            eventForm={eventForm}
                            setEventForm={setEventForm}
                            error={errors.image}
                            value={eventUploadImage}
                            eventDp={eventDp}
                            setEventDp={setEventDp}
                            setEventUploadImage={setEventUploadImage}
                            update={(value) => {
                                setErrors({ ...errors, image: '' })
                            }}
                        />
                    )}

                    {(eventForm?.notifyUsers && (modal?.isAdmin && !modal?.event)) && (
                        <div className='flex'>
                            <Checkbox2
                                customClasses={'text-14 mr-md'}
                                label="US Users only"
                                value={eventForm?.isForUsOnly}
                                onClick={() => {
                                    setEventForm({ ...eventForm, isForUsOnly: true })
                                }}
                                roundedButton={true}
                            />
                            <Checkbox2
                                customClasses={'text-14'}
                                label="Worldwide"
                                value={!eventForm?.isForUsOnly}
                                onClick={() => {
                                    setEventForm({ ...eventForm, isForUsOnly: false })
                                }}
                                roundedButton={true}
                            />
                        </div>
                    )}
                    {((user?.role !== 'admin' && (modal?.source !== "chat")) || (modal?.event && (modal?.event?.user_id !== constantOptions?.ADMIN_ID))) ?
                        <div className={`${isMobile && 'mt-md'} py-md ${user?.role === 'admin' ? 'mix-blend-luminosity pointer-events-none' : ''}`}>
                            <div className='text-16 font-medium pb-md'>Create Event For</div>
                            {(user?.visibleToPublic || user?.role === "admin") ? (
                                <div className={`pb-md ${user?.role === "user" && eventForAll === false ? 'opacity-50 cursor-not-allowed' : ''}`}>
                                    <Checkbox2
                                        customClasses={'text-14'}
                                        label="All Thousand Greens Members"
                                        value={eventForAll === false ? false : (modal?.isAdmin && !modal?.event) ? true : eventForm?.forAll}
                                        onClick={() => {
                                            if (eventForAll !== false) { // Prevent click if disabled
                                                setEventForm({ ...eventForm, forAll: (!eventForm?.forAll) })
                                                setMyTgCommunity(!myTgCommunity)
                                            }
                                        }}
                                        roundedButton={true}
                                        isDisabled={eventForAll === false}
                                    />
                                    <div className='text-12 text-gray pl-lg pt-sm'>
                                        If this is checked, then along with your TG Community, your event will be visible to all Thousand Greens Members
                                    </div>
                                </div>
                            ) : (
                                null
                            )}
                            <div className=''>
                                <Checkbox2
                                    customClasses={'text-14'}
                                    label="My TG community only"
                                    value={eventForAll === false ? true : myTgCommunity}
                                    onClick={() => {
                                        if (user?.visibleToPublic && eventForAll !== false) {
                                            setMyTgCommunity((prev) => !prev)
                                            setEventForm({ ...eventForm, forAll: !eventForm?.forAll })
                                            setAllMyFriends(true)
                                            setOnlyTgGroups(true)
                                        }
                                    }}
                                    roundedButton={true}
                                />
                                <div className='text-12 text-gray pl-lg pt-sm'>This is your mandatory event visibility. All of your created events will always be visible to your friends and TG group members</div>

                                <div className={`pl-lg mt-sm ${myTgCommunity ? '' : 'pointer-events-none'}`}>
                                    <Checkbox
                                        label={"All My Friends"}
                                        customClasses={`text-14 w-fit cursor-pointer ${myTgCommunity ? '' : 'text-gray'}`}
                                        value={allMyFriends}
                                        onClick={() => {
                                            setAllMyFriends((prev) => !prev)
                                            if (allMyFriends && !onlyTgGroups) {
                                                setOnlyTgGroups(true)
                                            } else {
                                                null
                                            }
                                        }}
                                        isDisabled={!myTgCommunity}
                                    />
                                    <Checkbox label={"My Groups"}
                                        customClasses={`text-14 w-fit cursor-pointer ${myTgCommunity ? '' : 'text-gray'}`}
                                        value={onlyTgGroups}
                                        onClick={() => {
                                            setOnlyTgGroups((prev) => !prev)
                                            if (!allMyFriends && onlyTgGroups) {
                                                setAllMyFriends(true)
                                            }
                                        }}
                                        isDisabled={!myTgCommunity}
                                    />
                                    <>
                                        {(onlyTgGroups || user?.role === 'user') && allMyTgGroups?.length ? (
                                            <div className={`mb-md ${user?.role === 'user' ? 'ml-[30px]' : ''} ${(onlyTgGroups && myTgCommunity || user?.role === 'admin' && onlyTgGroups) ? '' : 'pointer-events-none'}`}>
                                                <CheckboxMultiselect
                                                    title={'Select Group'}
                                                    placeholder={'Select'}
                                                    options={allMyTgGroups}
                                                    addClubClasses={true}
                                                    titleWeight={'normal'}
                                                    fontWeignt={'normal'}
                                                    update={handleSelection}
                                                    textSize='16'
                                                    groups={groups}
                                                    setGroups={setGroups}
                                                    value={groups}
                                                    allGroupsSelected={allGroupsSelected}
                                                    showAll={allGroupsSelected}
                                                    setPage={setPage}
                                                    totalGroupsCount={totalGroupsCount}
                                                    fetchGroupsList={fetchGroupsList}
                                                    page={page}
                                                    pageSize={pageSize}
                                                    setAllGroupsSelected={setAllGroupsSelected}
                                                />
                                            </div>
                                        ) : (
                                            <>
                                                {user?.role === 'user' &&
                                                    <div className='flex bg-tealTierBg rounded-lg text-12 text-gray ml-[28px] mt-sm p-sm'>
                                                        <div className='mr-1 mt-1'><InfoIcon color='#098089' /></div>
                                                        You're not in any TG Group. Selecting 'TG Groups' when creating the Events will display it in future groups; otherwise, it won't.
                                                    </div>
                                                }
                                            </>
                                        )}
                                    </>
                                </div>
                            </div>
                        </div>
                        :
                        <div className={`bg-custom-green flex items-center rounded-b-lg rounded-md mt-md`} style={{ height: 45 }}>
                            <div className='relative ml-sm cursor-pointer'>
                                <ToolTip style={{ right: 20, width: 150 }} />
                            </div>
                            <div className='ml-sm'>
                                Event created for: Members of {modal?.channelName}
                            </div>
                        </div>
                    }

                </div>
            </div>
            <div className='flex w-full justify-evenly md:justify-end'>
                <CustomButton
                    text='Cancel'
                    color='white'
                    textColor='black'
                    width={160}
                    height={45}
                    borderRadius={8}
                    borderColor='black'
                    onClick={() => setModal()}
                    loading={loading}
                    darkLoader={true}
                />
                <CustomButton
                    text={modal?.event ? "Save" : 'Create'}
                    color='darkteal'
                    textColor='white'
                    width={160}
                    height={45}
                    borderRadius={8}
                    onClick={handleSubmit}
                    loading={loading}
                />
            </div>
        </div>
    )
}
