import React, { useState, useContext, useRef, useEffect } from 'react'
import { UserContext } from '../../pages/_app'
import XIcon from '../icons/RequestV2/XIcon'
import CustomButton from '../buttons/CustomButton'
import ReactCrop, {
    centerCrop,
    makeAspectCrop,
} from 'react-image-crop'
import 'react-image-crop/dist/ReactCrop.css'

export default function ImageCropModal({ modal, setModal, setBase64ForCrop }) {
    const { user } = useContext(UserContext)
    const [image] = useState(modal?.image)
    const imageRef = useRef(null)
    const [crop, setCrop] = useState()
    const [completedCrop, setCompletedCrop] = useState()
    const [aspect] = useState(16 / 9)
    const [imageData, setImageData] = useState()

    useEffect(() => {
        if (image) {
            setImageData(imageRef.current)
        }

    }, [image])

    function onImageLoad(e) {
        if (aspect) {
            const { width, height } = e.currentTarget
            setCrop(centerAspectCrop(width, height, aspect))
        }
    }

    function centerAspectCrop(
        mediaWidth,
        mediaHeight,
        aspect,
    ) {
        return centerCrop(
            makeAspectCrop(
                {
                    unit: '%',
                    width: 50,
                },
                aspect,
                mediaWidth,
                mediaHeight,
            ),
            mediaWidth,
            mediaHeight,
        )
    }

    const handleUpload = async () => {
        const croppedImageUrl = await getCroppedImg(imageData, completedCrop, user?.id)
        modal?.setDp(croppedImageUrl)
    }

    const getCroppedImg = (imageData, crop, fileName) => {
        const canvas = document.createElement('canvas');
        const pixelRatio = window.devicePixelRatio;
        const scaleX = imageData.naturalWidth / imageData.width;
        const scaleY = imageData.naturalHeight / imageData.height;
        const ctx = canvas.getContext('2d');

        canvas.width = crop.width * pixelRatio * scaleX;
        canvas.height = crop.height * pixelRatio * scaleY;

        ctx.setTransform(pixelRatio, 0, 0, pixelRatio, 0, 0);
        ctx.imageSmoothingQuality = 'high';

        ctx.drawImage(
            imageData,
            crop.x * scaleX,
            crop.y * scaleY,
            crop.width * scaleX,
            crop.height * scaleY,
            0,
            0,
            crop.width * scaleX,
            crop.height * scaleY
        );

        return new Promise((resolve, reject) => {
            canvas.toBlob(
                (blob) => {
                    modal?.setUploadImage(blob);
                    if (modal?.type === 'create-edit-group') {
                        setBase64ForCrop()
                    }
                    if (!blob) {
                        //reject(new Error('Canvas is empty'));
                        console.error('Canvas is empty');
                        return;
                    }
                    blob.name = fileName;
                    let fileUrl
                    window.URL.revokeObjectURL(fileUrl);
                    fileUrl = window.URL.createObjectURL(blob);
                    resolve(fileUrl);
                },
                'image/jpeg',
                1
            );
        });
    }

    return (
        <div className="w-full flex flex-col flex-center">
            <div className="flex w-full justify-between px-md">
                <div className="text-black text-24 pb-md">
                    Drag the image to adjust
                </div>
                <div onClick={() => {
                    if (modal?.type === 'create-edit-group') {
                        setBase64ForCrop()
                    } else {
                        setModal()
                    }
                }} className="cursor-pointer">
                    <XIcon color="#333333" />
                </div>
            </div>
            <div className="">
                {!!image && (
                    <ReactCrop
                        crop={crop}
                        onChange={(_, percentCrop) => { setCrop(percentCrop) }}
                        onComplete={(c) => { setCompletedCrop(c) }}
                        aspect={aspect}
                    >
                        <img
                            width={394}
                            ref={imageRef}
                            alt="Crop me"
                            src={image}
                            onLoad={onImageLoad}
                        />
                    </ReactCrop>
                )}
            </div>
            <CustomButton
                text='Upload'
                height={45}
                width={164}
                onClick={handleUpload}
            />
        </div>
    )
}
