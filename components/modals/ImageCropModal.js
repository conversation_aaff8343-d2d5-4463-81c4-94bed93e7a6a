import React, { useState, useRef, useEffect, useCallback } from 'react'
import ReactCrop, { centerCrop, makeAspectCrop } from 'react-image-crop'
import 'react-image-crop/dist/ReactCrop.css'
import XIcon from '../icons/RequestV2/XIcon'
import CustomButton from '../buttons/CustomButton'
import SquareIcon from '../icons/SquareIcon'
import RectangleIcon from '../icons/RectangleIcon'
import constantOptions from '../../constants/constantOptions'

const { SQUARE, RECTANGLE } = constantOptions?.IMAGE_CROP_MODAL_ASPECT_RATIO
const { MAX_WIDTH, QUALITY } = constantOptions?.IMAGE_CROP_PARAMS

export default function ImageCropModal({ modal, setModal, setBase64ForCrop }) {
    const [crop, setCrop] = useState()
    const [completedCrop, setCompletedCrop] = useState()
    const [aspect, setAspect] = useState(SQUARE)
    const [isLoading, setIsLoading] = useState(false)

    const imageRef = useRef(null)

    // Initialize crop when image loads
    const onImageLoad = useCallback((e) => {
        const { width, height } = e.currentTarget
        const initialCrop = centerAspectCrop(width, height, aspect)
        setCrop(initialCrop)
    }, [aspect])

    // Update crop when aspect changes
    useEffect(() => {
        if (imageRef.current) {
            const { width, height } = imageRef.current
            setCrop(centerAspectCrop(width, height, aspect))
        }
    }, [aspect])

    function centerAspectCrop(mediaWidth, mediaHeight, aspect) {
        return centerCrop(
            makeAspectCrop(
                {
                    unit: '%',
                    width: 50,
                },
                aspect,
                mediaWidth,
                mediaHeight,
            ),
            mediaWidth,
            mediaHeight,
        )
    }

    const createCroppedImage = useCallback(async (image, crop) => {
        if (!crop || !image) return null

        const canvas = document.createElement('canvas')
        const ctx = canvas.getContext('2d')
        if (!ctx) return null

        // Calculate scaled dimensions
        const scaleX = image.naturalWidth / image.width
        const scaleY = image.naturalHeight / image.height

        // Set canvas size based on crop, maintaining aspect ratio
        const pixelRatio = window.devicePixelRatio
        const scaledWidth = crop.width * scaleX
        const scaledHeight = crop.height * scaleY

        // Limit maximum dimensions
        const maxScale = Math.min(1, MAX_WIDTH / scaledWidth)
        canvas.width = scaledWidth * maxScale
        canvas.height = scaledHeight * maxScale

        // Enable image smoothing
        ctx.imageSmoothingQuality = 'high'
        ctx.imageSmoothingEnabled = true

        // Draw cropped image
        ctx.drawImage(
            image,
            crop.x * scaleX,
            crop.y * scaleY,
            scaledWidth,
            scaledHeight,
            0,
            0,
            canvas.width,
            canvas.height
        )

        // Convert to blob
        return new Promise((resolve) => {
            canvas.toBlob(
                (blob) => {
                    if (!blob) {
                        console.error('Failed to create blob')
                        resolve(null)
                        return
                    }

                    // Create preview URL
                    const previewUrl = URL.createObjectURL(blob)
                    resolve({ blob, previewUrl })
                },
                'image/jpeg',
                QUALITY
            )
        })
    }, [])

    const handleUpload = async () => {
        if (!imageRef.current || !completedCrop) return

        setIsLoading(true)
        try {
            const result = await createCroppedImage(imageRef.current, completedCrop)
            if (!result) throw new Error('Failed to create cropped image')

            const { blob, previewUrl } = result

            // Update parent components
            modal?.setDp(previewUrl)
            modal?.setUploadImage(blob)

            // Close modal
            if (modal?.type === 'create-edit-group') {
                setBase64ForCrop(null)
            } else {
                setModal(null)
            }
        } catch (error) {
            console.error('Error processing crop:', error)
        } finally {
            setIsLoading(false)
        }
    }

    // Cleanup URLs on unmount
    useEffect(() => {
        return () => {
            if (imageRef.current?.src) {
                URL.revokeObjectURL(imageRef.current.src)
            }
        }
    }, [])

    return (
        <div className="w-full flex flex-col flex-center">
            <div className="flex w-full justify-between px-md">
                <div className="text-16 font-medium pb-md">
                    Drag the cropper to adjust image
                </div>
                <div
                    onClick={() => {
                        if (modal?.type === 'create-edit-group') {
                            setBase64ForCrop(null)
                        } else {
                            setModal(null)
                        }
                    }}
                    className="cursor-pointer"
                >
                    <XIcon color="#333333" />
                </div>
            </div>

            <div className="relative">
                {modal?.image && (
                    <ReactCrop
                        crop={crop}
                        onChange={(_, percentCrop) => setCrop(percentCrop)}
                        onComplete={(c) => setCompletedCrop(c)}
                        aspect={aspect}
                    >
                        <img
                            ref={imageRef}
                            alt="Crop preview"
                            src={modal.image}
                            style={{ maxWidth: '100%' }}
                            onLoad={onImageLoad}
                        />
                    </ReactCrop>
                )}
            </div>

            <div className='w-full flex justify-center pt-md gap-2'>
                <SquareIcon
                    isActive={aspect === SQUARE}
                    onClick={() => setAspect(SQUARE)}
                />
                <RectangleIcon
                    isActive={aspect === RECTANGLE}
                    onClick={() => setAspect(RECTANGLE)}
                />
            </div>

            <div className='w-full flex justify-center pt-md'>
                <CustomButton
                    text={isLoading ? 'Processing...' : 'Upload'}
                    height={45}
                    width={164}
                    onClick={handleUpload}
                    disabled={isLoading || !completedCrop}
                />
            </div>
        </div>
    )
}
