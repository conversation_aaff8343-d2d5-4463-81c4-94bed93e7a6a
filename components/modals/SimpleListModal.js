import React from 'react'
import NameInitials from '../common/NameInitials'

export default function SimpleListModal({ modal, setModal }) {
    return (
        <div className="w-full relative">
            <div
                onClick={() => setModal()}
                className="cursor-pointer absolute"
                style={{ right: 12, top: -10 }}>
                <img src='/svg/CrossIconBlack.svg' />
            </div>
            <div className="px-md pb-md text-24 text-black">{modal?.heading || "All Clubs"}</div>
            <div className='overflow-y-scroll' style={{ maxHeight: 290 }}>
                {modal?.items.map((item) => {
                    if (modal?.heading === 'All Mutual TG Groups') {
                        return (
                            <div key={item?.id} className='px-md py-xs flex items-center'>
                                {item?.image ? <img src={item?.image} className='w-[40px]  h-[40px] rounded-full' /> : <NameInitials height={40} width={40} user={{ first_name: item?.name }} fontSize={16} rounded={'full'} />}
                                <div className='ml-md text-16 py-sm text-black'>{item?.name}</div>
                            </div>
                        )
                    } else {
                        return (
                            <div
                                key={item.id}
                                className='px-md py-xs flex items-center'>
                                {modal?.icon}
                                <div className='ml-md text-16 py-sm text-black'>{item?.name}</div>
                            </div>
                        )
                    }
                })}
            </div>
        </div>
    )
}