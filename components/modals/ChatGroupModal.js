import React, { useState, useContext, useEffect } from 'react'
import { TextArea, TextInput } from '../common'
import CustomButton from '../buttons/CustomButton'
import Counter from '../common/Counter'
import MESSAGES from '../../constants/messages'
import ChatGroupPhotoUpload from '../common/ChatGroupPhotoUpload'
import ENDPOINTS from "../../constants/endpoints.json"
import { uploadFile } from '../../utils/upload'
import { UserContext } from '../../pages/_app'
import constantOptions from '../../constants/constantOptions'
import { CHAT_GROUP_DESCRIPTION_LIMIT } from '../../utils/validationConstants'
import ImageCropModal from './ImageCropModal'
import compressImage from '../../utils/helper/compressImage'

const ChatGroupModal = ({ modal, setModal }) => {
    const { token } = useContext(UserContext)
    const [groupName, setGroupName] = useState('')
    const [groupDescription, setGroupDescription] = useState('')
    const [loading, setLoading] = useState(false)
    const [groupPhoto, setGroupPhoto] = useState('')
    const [initialPhoto, setInitialPhoto] = useState('')
    const [errors, setErrors] = useState({})
    const [base64forCrop, setBase64ForCrop] = useState()
    const [groupDp, setGroupDp] = useState('')
    const [groupUploadImage, setGroupUploadImage] = useState()
    const [isUploading, setIsUploading] = useState(false)

    useEffect(() => {
        if (modal?.chatGroup) {
            setGroupName(modal?.chatGroup?.name)
            setGroupDescription(modal?.chatGroup?.description)
            setInitialPhoto(modal?.chatGroup?.image)
            setGroupDp(modal?.chatGroup?.image)
        }
    }, [modal?.chatGroup])

    useEffect(() => {
        if (groupUploadImage) {
            initUpload()
        }
    }, [groupUploadImage])

    const initUpload = async () => {
        try {
            setIsUploading(true)
            //Converting blob to file for compression
            const newFileFromBlob = new File([groupUploadImage], groupUploadImage.name, { type: groupUploadImage.type });
            const compressedFile = await compressImage({ file: newFileFromBlob })
            setGroupPhoto(compressedFile)
            setGroupDp(URL.createObjectURL(compressedFile))
            setBase64ForCrop(null)
            setIsUploading(false)
        } catch (error) {
            console.error('Error uploading image:', error)
            setIsUploading(false)
            setBase64ForCrop(null)
        }
    }

    const validate = () => {
        let newErrors = {}

        if (groupName.trim() === '' || groupName === '' || !groupName) {
            newErrors.groupName = MESSAGES.CHAT.GROUP_NAME_REQUIRED
        }
        if (groupDescription.trim() === '' || groupDescription === '' || !groupDescription) {
            newErrors.groupDescription = MESSAGES.CHAT.GROUP_DESCRIPTION_REQUIRED
        }
        if ((groupPhoto && !constantOptions?.DISPLAY_PICTURE_FORMATS.includes(groupPhoto?.type))) {
            newErrors.groupPhoto = MESSAGES?.GROUP_CHAT?.GROUP_PHOTO_FORMAT_ERROR
        }

        setErrors(newErrors)
        return Object.keys(newErrors).length === 0
    }

    const createGroup = async () => {
        if (validate()) {
            try {
                const URL = modal?.chatGroup ? ENDPOINTS?.EDIT_CHAT_GROUP : ENDPOINTS?.CREATE_CHAT_GROUP
                setLoading(true)
                let body = {
                    name: groupName,
                    image: groupDp || initialPhoto,
                    description: groupDescription,
                    groupId: modal?.groupId
                }
                if (groupPhoto) {
                    await fetch(URL, {
                        method: "POST",
                        headers: {
                            'Content-Type': 'application/json',
                            ['Authorization']: `Bearer ${token}`,
                        },
                        body: JSON.stringify(body)
                    })
                        .then((response) => response.json())
                        .then((data) => { modal?.refresh() })
                }
                setLoading(false)
                setModal()

            } catch (error) {
                console.log("CreateChatGroup------------>", error);
                setLoading(false)
            }
        }
    }

    return (
        <div className='w-full px-56px pt-md'>
            {base64forCrop ? (
                <div>
                    <ImageCropModal
                        setBase64ForCrop={setBase64ForCrop}
                        modal={{
                            type: 'create-edit-group',
                            image: base64forCrop,
                            setDp: setGroupDp,
                            setUploadImage: setGroupUploadImage
                        }}
                    />
                </div>
            ) : (
                <ChatGroupPhotoUpload
                    initialPhoto={groupDp || initialPhoto}
                    setInitialPhoto={setInitialPhoto}
                    update={(value) => setGroupPhoto(value)}
                    editing={true}
                    setBase64ForCrop={setBase64ForCrop}
                    isUploading={isUploading}
                    setGroupDp={setGroupDp}
                    setGroupPhoto={setGroupPhoto}
                />
            )}
            {errors?.groupPhoto && <div className='text-sm text-red text-center py-sm'>{errors?.groupPhoto}</div>}

            <TextInput
                value={groupName}
                title={'Group Name'}
                update={(value) => {
                    setErrors({ ...errors, groupName: '' })
                    setGroupName(value)
                }}
                maxLength={30}
                error={errors?.groupName}
            />
            <TextArea
                titleSize={'14'}
                title={'Group Description'}
                value={groupDescription}
                update={(value) => {
                    setErrors({ ...errors, groupDescription: '' })
                    setGroupDescription(value)
                }}
                maxLength={CHAT_GROUP_DESCRIPTION_LIMIT}
                error={errors?.groupDescription}
            />
            {errors?.groupDescription && <div className='text-sm text-red text-center py-sm'>{errors?.groupDescription}</div>}

            <div className='flex justify-end w-full'>
                <Counter count={groupDescription?.length} limit={CHAT_GROUP_DESCRIPTION_LIMIT} color='text-grayLight' textSize='text-10' />
            </div>
            <div className='flex justify-between pt-md'>
                <CustomButton
                    height={45}
                    width={164}
                    color='lightestgray'
                    text='Cancel'
                    textColor='black'
                    marginX='none'
                    disabled={loading}
                    onClick={() => {
                        setModal()
                    }}
                />
                <CustomButton
                    height={45}
                    width={164}
                    color='darkteal'
                    text={modal?.chatGroup ? "Save" : 'Create'}
                    textColor='white'
                    marginX='none'
                    onClick={createGroup}
                    loading={loading}
                />
            </div>
        </div>
    )
}

export default ChatGroupModal