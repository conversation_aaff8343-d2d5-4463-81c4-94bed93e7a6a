import React, { useContext, useState, useEffect } from 'react'
import RequestIcon from '../icons/RequestIcon'
import { getTier } from '../../utils/tiers';
import ENDPOINTS from "../../constants/endpoints.json"
import { UserContext } from '../../pages/_app';
import { ThreeDots } from 'react-loader-spinner'
import { UPDATE_ADDITIONAL_SETTINGS } from '../../graphql/mutations/user';
import useClient from '../../graphql/useClient';
import constantOptions from '../../constants/constantOptions';

const { ALL, FAVORITE_CLUBS, FRIENDS, MY_TG_GROUP_MEMBER, OPEN_OFFERS, PLAYED, PLAY_AS_COUPLE } = constantOptions.MAP_FILTERS



const RecommendationsModal = ({ modal, setModal }) => {
    const client = useClient()
    const { user, token, fetchUser } = useContext(UserContext)
    const [loading, setLoading] = useState(false)
    const [requestRestricted, setRequestRestricted] = useState(true)
    const [showAcceptRequestPopup, setShowAcceptRequestPopup] = useState();
    const [showCreateRequestPopup, setShowCreateRequestPopup] = useState(true);
    const [showCreateRequestForm, setShowCreateRequestForm] = useState(false)
    const [options, setOptions] = useState(OPTIONS)

    const OPTIONS = [
        ALL,
        OPEN_OFFERS,
        FRIENDS,
        MY_TG_GROUP_MEMBER,
        FAVORITE_CLUBS,
        PLAYED
    ];


    useEffect(() => {
        if (user) {
            setShowAcceptRequestPopup(user?.additional_settings?.showAcceptRequestPopup);
            setShowCreateRequestPopup(user?.additional_settings?.showCreateRequestPopup);
            setOptions(() => {
                if (user?.playAsCouple) {
                    return [...OPTIONS, PLAY_AS_COUPLE]
                } else {
                    return OPTIONS
                }
            })
        }
    }, [user])


    async function checkToken() {
        fetch(ENDPOINTS?.CHECK_CREATE_REQUEST_TOKEN, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                user_id: user.id
            }),
        })
            .then((data) => data.json())
            .then((data) => {
                if (data.canCreate) {
                    setRequestRestricted(null);
                } else {
                    setRequestRestricted(data.message);
                }
            })
            .catch(err => console.log('Error'));
    }

    useEffect(() => {
        if (user, client) {
            if (user?.additional_settings?.showAcceptRequestPopup && showAcceptRequestPopup === false) {
                client.request(UPDATE_ADDITIONAL_SETTINGS, {
                    user_id: user?.id,
                    additional_settings: {
                        ...user?.additional_settings,
                        showAcceptRequestPopup: false
                    }
                })
                fetchUser()
            }
            if (user?.additional_settings?.showCreateRequestPopup && showCreateRequestPopup === false) {
                client.request(UPDATE_ADDITIONAL_SETTINGS, {
                    user_id: user?.id,
                    additional_settings: {
                        ...user?.additional_settings,
                        showCreateRequestPopup: false
                    }
                })
                fetchUser()
            }
            checkToken()
        }
    }, [showAcceptRequestPopup, showCreateRequestPopup, user, client])

    const fetchClubSearchDetail = async (name) => {
        setLoading(true)
        try {
            fetch(ENDPOINTS.SEARCH_CLUB,
                {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        ['Authorization']: `Bearer ${token}`,
                    },
                    body: JSON.stringify({
                        searchValue: name,
                        userId: user?.id,
                        exactMatch: true
                    }),
                })
                .then((data) => data.json())
                .then(({ clubs }) => {
                    const clubDetail = clubs[0]
                    setLoading(false)
                    modal?.setClubForRequest(clubDetail)
                    modal?.setActiveTab('create-request')
                    setModal()
                })
        } catch (error) {
            console.error('fetchClubSearchDetail --->', error);
        }
    }

    const handleCreateRequest = (club) => {
        if (requestRestricted) {
            setModal({
                title: 'Request Limit',
                message: requestRestricted,
                type: 'warning',
            })
        } else {
            modal?.setClubsRecommended(true)
            fetchClubSearchDetail(club?.name)
        }
    }

    return (
        <div className={`flex relative flex-col px-md ${loading ? "pointer-events-none mix-blend-luminosity" : ""}`}>
            {loading &&
                <div
                    className="absolute h-full w-full flex-center px-md"
                    style={{
                        opacity: 1,
                        transition: 'opacity ease-in-out 200ms',
                        // backgroundColor: 'rgba(0,0,0,0.5)',
                        zIndex: 1001
                    }}
                >
                    <ThreeDots
                        visible={true}
                        height="50"
                        width="50"
                        color={"#098089"}
                        radius="9"
                        ariaLabel="three-dots-loading"
                        wrapperStyle={{}}
                        wrapperClass=""
                    />
                </div>}
            <div
                className='cursor-pointer pr-md absolute right-0'
                onClick={() => { setModal() }}>
                <img width={12} src="/svg/CrossIconBlack.svg" />
            </div>
            <div className='h-[72px] w-[72px] bg-tealTierBg rounded-full flex-center self-center'>
                <img width={36} src='/svg/golf-post-small.svg' />
            </div>
            <div className='text-24 font-medium text-center py-md'>Club Recommendations</div>
            <div className='text-center text-grayLight'>
                <span>Thank you for your game request at </span><strong className='text-black'>{modal?.recommendedAgainstClub}. </strong><span>While it's being processed, we’d like to introduce you to nearby golf clubs with higher request acceptance rates.</span>
            </div>
            <div className='py-md'>Here are some recommended golf club(s):</div>
            {modal?.recommendedClubs?.slice(0, 3).map((club) => {
                return (
                    <div key={club?.id} className='flex justify-between border border-lightestgray rounded-lg shadow py-sm px-md mb-[12px]'>
                        <div className=''>
                            <div style={{ letterSpacing: "10%" }} className='ubuntu inline rounded py-1 px-sm bg-tealTierBg text-tealTier font-bold text-10 uppercase'>{getTier(club?.lowest_visible_tier)}</div>
                            <div className='text-16 font-medium py-sm'>{club?.name}</div>
                            <div className='flex items-start'>
                                <img className='mt-1' width={10} src='/svg/location-grey.svg' />
                                <div className='text-12 text-grayLight pl-sm'>{club?.address}</div>
                            </div>
                        </div>
                        <div className='items-center flex cursor-pointer'
                            onClick={() => {
                                handleCreateRequest(club)

                            }}
                        >
                            <RequestIcon />
                        </div>
                    </div>
                )
            })}
        </div>
    )
}

export default RecommendationsModal