import React, { useState, useEffect, useContext } from 'react'
import Select from '../common/Select'
import Checkbox from '../common/Checkbox'
import DateRange from '../common/DateRange'
import TextArea from '../common/TextArea'
import useClient from '../../graphql/useClient'
import { UserContext } from '../../pages/_app'
import moment from 'moment'
import calculateGuestTimeRestrictionDates from '../../utils/clubs/calculateGuestTimeRestrictionDates'
import Moment from 'moment';
import { FETCH_CREATE_OFFER_ALLOWED_MONTHS, FETCH_INTERNATIONAL_CLUB_DURATION, FETCH_LOACAL_CLUB_DURATION } from '../../graphql/queries/system-settings'
import constantOptions from '../../constants/constantOptions'
import Counter from '../common/Counter'
import CustomButton from '../buttons/CustomButton'
import useCheckDeviceScreen from '../../hooks/useCheckDeviceScreen'
import ENDPOINTS from '../../constants/endpoints.json'
import toastNotification from '../../utils/notifications/toastNotification'
import RequestBlockedIcon from '../icons/RequestBlockedIcon'
const { PROFILE, GLOBAL_PROFILE_INFO } = constantOptions.DRAWER_TYPE

export default function RequestAgainstOfferModal({
    modal,
    setModal,
    globalDropdownState,
    scrollToBottom
}) {
    const [loading, setLoading] = useState(false)
    const { user, token } = useContext(UserContext)
    const client = useClient()
    const [requestForm, setRequestForm] = useState({
        club: {},
        number_of_players: 1,
        createPublicRequest: false
    })
    const [errors, setErrors] = useState({})
    const [showErrors, setShowErrors] = useState(false)
    const [disabledDates, setDisabledDates] = useState([]);
    const [createOfferAllowedMonths, setCreateOfferAllowedMonths] = useState(0)
    const { isMobile } = useCheckDeviceScreen()
    const { requestType, friendId, clubs, club, offer, fetchTabsCount = () => { }, fetchUnreadMessageStatus = () => { }, refresh = () => { } } = modal
    const [clubSearchValue, setClubSearchValue] = useState('')
    const [requestDuration, setRequestDuration] = useState()
    //This restrictRequestBlocker condition checks the following conditions & prevents the error message for frequestly requested clubs-
    //1. Offer created by friend
    //2. Request created from friend list
    //3. Request Created from map friends list
    const restrictRequestBlocker = (modal?.requestType === "OFFER" && !modal?.creatorIsFriend) || (!["FRIEND", "ALL_MY_FRIENDS", "ALL_FRIEND", "PROFILE_INFO"].includes(modal?.requestType) && modal?.requestType !== "OFFER")

    useEffect(() => {
        if (showErrors) {
            validateRequest()
        }
    }, [requestForm, showErrors])

    useEffect(() => {
        if (client) {
            fetchOfferSystemSettings()
        }
    }, [modal, client])

    useEffect(() => {
        if (clubs?.length) {
            if (clubs?.length === 1) {
                setClubSearchValue(clubs[0])
            } else {
                setClubSearchValue('')
            }
        }
    }, [clubs])

    useEffect(() => {
        if (club?.name) {
            setClubSearchValue(club?.name)
        } else if (offer?.club_name) {
            setClubSearchValue(offer?.club_name)
        }
    }, [club])

    useEffect(() => {
        const fetchClubData = async () => {
            setLoading(true)

            try {
                let body = {
                    searchValue: clubSearchValue,
                    userId: user?.id
                }
                if (requestType !== undefined) {
                    body = { ...body, exactMatch: true }
                }
                fetch(ENDPOINTS.SEARCH_CLUB,
                    {
                        method: 'POST',
                        credentials: 'same-origin',
                        headers: {
                            'Content-Type': "application/json",
                            ['Authorization']: `Bearer ${token}`,
                        },
                        body: JSON.stringify(body),
                    }
                ).then((data) => data.json())
                    .then(({ clubs }) => {
                        setRequestForm({ ...requestForm, club: clubs[0] });
                        setLoading(false)
                    })
            } catch (error) {
                console.log("Request Against Offer Club Search--------", error);
            }
        }

        if (clubSearchValue) {
            fetchClubData()
        }

    }, [clubSearchValue])


    useEffect(() => {
        if (client) {
            if (user?.phone_number_details?.countryCode?.toLowerCase() !== (requestForm?.club?.country_code)) {
                const duration = client.request(FETCH_INTERNATIONAL_CLUB_DURATION)
                    .then((data) => setRequestDuration(data?.system_setting[0]?.value?.value))

            } else {
                const duration = client.request(FETCH_LOACAL_CLUB_DURATION)
                    .then((data) => setRequestDuration(data?.system_setting[0]?.value?.value))
            }
        }
    }, [user, client, requestForm])

    function validateRequest() {
        let newErrors = {}
        if (!requestForm.date_range) {
            newErrors.date_range = 'A date range is required'
        }
        if (!requestForm.message || requestForm?.message.trim() === "") {
            newErrors.message = 'A Request note is required'
        }
        if (!clubSearchValue) {
            newErrors.club = 'A Club is required'
        }

        setErrors(newErrors)
        return Object.keys(newErrors).length === 0
    }

    const createRequest = async () => {
        try {
            if (validateRequest()) {
                setLoading(true)

                const {
                    number_of_players,
                    message,
                    date_range: { from: start_date, to: end_date },
                } = requestForm

                const club_id = !["ALL_FRIEND", "PROFILE_INFO"].includes(requestType) ? modal?.club?.id : requestForm?.club?.id

                let URL = ENDPOINTS?.CREATE_REQUEST

                let body = {
                    userId: user?.id,
                    clubId: club_id,
                    players: number_of_players,
                    message,
                    startDate: start_date,
                    endDate: end_date,
                    requestForAll: false,
                }
                if (modal?.requestType === "ALL_MY_TG_GROUP_MEMBERS") {
                    body = {
                        ...body,
                        isForAllFriends: false,
                        isForMyTgGroupMembers: true,
                        isForAFriend: false,
                        friendId: ''
                    }
                } else if (modal?.requestType === "ALL_MY_FRIENDS") {
                    body = {
                        ...body,
                        isForAllFriends: true,
                        isForMyTgGroupMembers: false,
                        isForAFriend: false,
                        friendId: ''
                    }
                } else if (modal?.requestType === "MY_TG_GROUP_MEMBER") {
                    body = {
                        ...body,
                        isForAGroupMember: true,
                        groupMemberId: friendId
                    }
                }
                else {
                    body = {
                        ...body,
                        isForAllFriends: false,
                        isForMyTgGroupMembers: false,
                        isForAFriend: true,
                        friendId: friendId
                    }
                }

                await fetch(URL, {
                    method: "POST",
                    headers: {
                        'Content-Type': 'application/json',
                        ['Authorization']: `Bearer ${token}`,
                    },
                    body: JSON.stringify(body)
                })
                    .then((response) => response.json())
                    .then(async (data) => {
                        if (!data?.status) {
                            toastNotification({
                                type: constantOptions.TOAST_TYPE.ERROR,
                                message: data?.message,
                            })
                        } else {
                            setLoading(false)
                            setModal()
                            await fetchTabsCount()
                            await fetchUnreadMessageStatus()
                            refresh(prev => prev + 1)
                            toastNotification({
                                type: constantOptions.TOAST_TYPE.REQUEST_MOVED,
                                message: 'Request created successfully',
                            })

                        }
                    })
            }

        } catch (error) {
            console.log("Create Request ------>", error);
        }
    }

    const createRequestAgainstOffer = async () => {
        setShowErrors(true)
        try {
            if (validateRequest()) {
                const {
                    number_of_players,
                    message,
                    date_range: { from: startDate, to: endDate },
                    createPublicRequest
                } = requestForm

                let URL = ENDPOINTS?.CREATE_REQUEST_AGAINST_OFFER

                let body = {
                    userId: user?.id,
                    offerId: modal?.offer?.id,
                    startDate,
                    endDate,
                    message,
                    players: number_of_players,
                    createPublicRequest
                }

                setLoading(true)
                await fetch(URL, {
                    method: "POST",
                    headers: {
                        'Content-Type': 'application/json',
                        ['Authorization']: `Bearer ${token}`,
                    },
                    body: JSON.stringify(body)
                })
                    .then((response) => response.json())
                    .then(async (data) => {
                        if (!data?.status) {
                            toastNotification({
                                type: constantOptions.TOAST_TYPE.ERROR,
                                message: data?.message,
                            })
                        } else {
                            setLoading(false)
                            setModal()
                            toastNotification({
                                type: constantOptions.TOAST_TYPE.REQUEST_MOVED,
                                message: 'Request created successfully',
                            })
                            await modal.refresh(prev => prev + 1)
                        }
                    })
            }

        } catch (error) {
            console.log("Create Request Against Offer ------>", error);
        }
    }

    const handleDisabledDates = (closurePeriods, guestTimeRestrictions, allowedMonths) => {
        let allDisabledDates = [];
        if (closurePeriods) {
            const closurePeriodDates = closurePeriods
                .map(({ from, to }) => {
                    const startDate = Moment.utc(from).add(-1, 'days');
                    const endDate = Moment.utc(to);
                    const nDaysBetween = endDate.diff(startDate, 'd')
                    const dates = new Array(nDaysBetween)
                        .fill('')
                        .map((_, index) => {
                            return Moment.utc(from).add(index, 'days').toDate()
                        })
                    return dates
                })
                .flat()
            allDisabledDates = [...closurePeriodDates];
        }
        // guestTimeRestrictions is actually guest time availability - the verbiage for it has been changed in recent past (23 Aug 2021)
        if (guestTimeRestrictions && typeof guestTimeRestrictions === 'object') {
            let guestTimeRestrictionDates = calculateGuestTimeRestrictionDates(guestTimeRestrictions, allowedMonths);
            allDisabledDates = [...allDisabledDates, ...guestTimeRestrictionDates];
        }
        setDisabledDates(allDisabledDates)
    }

    useEffect(() => {
        if (requestType !== "ALL_FRIEND") {
            if (modal?.title === "Create Request") {
                handleDisabledDates(modal?.club?.closurePeriods, modal?.club?.guest_time_restrictions, constantOptions?.REQUEST_CREATION_ALLOWANCE_MONTHS?.DIFFERENT_COUNTRY)
            } else {
                handleDisabledDates(modal?.offer?.closurePeriods, modal?.offer?.guest_time_restrictions, createOfferAllowedMonths)
            }
        }
    }, [modal, createOfferAllowedMonths])

    useEffect(() => {
        if (Object.keys(requestForm?.club || {}).length !== 0) {
            handleDisabledDates(requestForm?.club?.closurePeriods, requestForm?.club?.guest_time_restrictions, constantOptions?.REQUEST_CREATION_ALLOWANCE_MONTHS?.DIFFERENT_COUNTRY)
        }

    }, [requestForm?.club])

    const fetchOfferSystemSettings = async () => {
        const { system_setting } = await client.request(
            FETCH_CREATE_OFFER_ALLOWED_MONTHS
        )
        setCreateOfferAllowedMonths(system_setting[0]?.value?.value)
    }

    return (
        <div className="flex flex-col w-full p-lg pb-0 relative">
            {clubSearchValue &&
                <div className="text-grayLight font-normal text-12 md:text-14">Course</div>
            }
            {["ALL_FRIEND", "PROFILE_INFO"].includes(requestType) ? (
                <div className='mb-md' style={{ zIndex: 1002 }}>
                    <Select
                        placeholder="Select"
                        addClubClasses={isMobile ? false : 'font-normal'}
                        customAddressClasses={isMobile ? true : false}
                        title={!clubSearchValue && "Select Club"}
                        value={clubSearchValue}
                        updateSearch={setClubSearchValue}
                        update={(value) => {
                            setClubSearchValue(value)
                            setErrors({ ...errors, date_range: "" })
                        }}
                        options={clubs}
                        disableError={!errors.club}
                        error={errors.club}
                        fontWeignt="normal"
                    />
                </div>
            ) : (
                <div className="border-b border-lightgray py-sm mb-md text-14 md:font-normal md:text-16">
                    {modal?.offer?.club_name || modal?.club?.name}
                </div>
            )}

            {![null, undefined].includes(requestForm?.club?.requestRestriction?.errorMessage) && restrictRequestBlocker ? <div className={`rounded-lg flex items-start text-12 text-gray bg-${requestForm?.club?.requestRestriction?.isBlocked ? "lightRed" : 'lightYellow'} py-sm px-md mb-sm`}>
                <RequestBlockedIcon isBlocked={requestForm?.club?.requestRestriction?.isBlocked} /> {requestForm?.club?.requestRestriction?.errorMessage}
            </div> : null}


            <div className="text-grayLight font-normal text-12 md:text-14">Number of People</div>
            <div className="mb-md relative " style={{ zIndex: 1001 }}>
                <Select
                    globalDropdownState={globalDropdownState}
                    disableError={true}
                    value={
                        requestForm.number_of_players
                            ? requestForm?.number_of_players
                            : 1
                    }
                    update={(value) =>
                        setRequestForm({
                            ...requestForm,
                            number_of_players: value,
                        })
                    }
                    options={[1, 2, 3]}
                    className={isMobile ? 'text-14' : 'text-16'}
                    addClubClasses={isMobile ? false : 'font-normal'}
                    customAddressClasses={isMobile && "true"}
                    titleWeight='normal'
                    fontWeignt={isMobile ? "normal" : "normal"}

                />
            </div>
            <DateRange
                singular
                showNote={modal?.requestType !== undefined}
                title="Date Range"
                placeholder="Select date"
                minDate={moment.utc(modal?.offer?.start_date).isAfter(moment.utc()) ? moment.utc(modal?.offer?.start_date).format('YYYY-MM-DD') : moment.utc().format('YYYY-MM-DD')}
                maxDate={(modal?.title === 'Create Request') ? undefined : moment.utc(modal?.offer?.end_date).format('YYYY-MM-DD')}
                maxDateMonth={requestDuration}
                error={errors?.date_range}
                value={requestForm?.date_range}
                update={(value) =>
                    setRequestForm({
                        ...requestForm,
                        date_range: value,
                    })
                }
                setParentErrors={() => {
                    (clubSearchValue) ? setErrors({ ...errors, date_range: "" }) : setErrors({ ...errors, date_range: 'To select a date range, please select a club first.' })
                }}
                disabledDates={disabledDates}
                scrollToBottom={scrollToBottom}
                textSize={isMobile ? '14' : '16'}
                titleSize={isMobile ? '12' : '14'}
                fontWeight="normal"
                calendarDisabled={!clubSearchValue}
            />

            <div className="text-grayLight font-normal text-12 md:text-14 pb-sm">Additional Notes</div>
            <TextArea
                value={requestForm?.message}
                error={errors?.message}
                update={(value) =>
                    setRequestForm({
                        ...requestForm,
                        message: value,
                    })
                }
                fontColor='text-gray'
                placeholder="Please type a courteous note introducing yourself and your request to potential hosts, a polite note improves acceptance chances"
                lineHeight="20px"
                minHeight={90}
                fontSize={16}
                maxLength={constantOptions?.REQUEST_NOTES_MAX_LENGTH}
            />
            <div className={`flex flex-center relative mt-sm`}>
                {
                    errors?.message &&
                    <div className='text-12 font-normal' style={{ minHeight: 40, color: 'red' }}>{errors?.message}</div>
                }
                <div className='absolute' style={{ right: 0 }}>
                    <Counter
                        count={requestForm.message?.length}
                        limit={constantOptions.REQUEST_NOTES_MAX_LENGTH}
                        color={'text-grayLight'}
                        textSize={'text-12'}
                        extraClasses='text-right'
                    />
                </div>
            </div>
            {(requestType === undefined && !offer?.my_tg_group_id?.length) &&
                <div className="mt-md items-center">
                    <Checkbox
                        value={requestForm.createPublicRequest}
                        update={(value) =>
                            setRequestForm({
                                ...requestForm,
                                createPublicRequest: value,
                            })
                        }
                        label="Send request to other members at the club if offering member unresponsive for 24 hours"
                        customClasses={`text-16 ${isMobile ? "font-normal" : 'font-normal'}`}
                        checkMarginTop="xs"
                        checkboxPosition={'start'}
                    />
                </div>
            }
            <div className="flex justify-evenly mt-xl">
                <CustomButton
                    text="Cancel"
                    color='lightestgray'
                    textColor='black'
                    width={160}
                    onClick={() => setModal()}
                    loading={loading}
                    textSize={14}
                />

                <CustomButton
                    loading={loading}
                    disabled={requestForm?.club?.requestRestriction?.isBlocked && restrictRequestBlocker}
                    text="Post Request"
                    onClick={() => {
                        if (modal?.title !== 'Create Request') {
                            createRequestAgainstOffer()
                        } else {
                            createRequest()
                        }
                    }}
                />
            </div>
        </div>
    )
}
