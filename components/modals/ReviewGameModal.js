import React, { useState, useContext, useEffect } from 'react'
import GreenSubmitButton from '../buttons/GreenSubmitButton'
import { RadioSelect, TextArea } from '../common'
import { UserContext } from '../../pages/_app'
import constantOptions from '../../constants/constantOptions'
import toastNotification from '../../utils/notifications/toastNotification'
import ENDPOINTS from '../../constants/endpoints.json'
import { uploadFile } from '../../utils/upload'
import ThumbsUp from '../icons/ThumbsUp'
import ThumbsDown from '../icons/ThumbsDown'
import CustomButton from '../buttons/CustomButton'
import MultiplePhotoUpload2 from '../common/MultiplePhotoUpload2'
import ImageCropModal from './ImageCropModal'

export default function ReviewGameModal({ modal, setModal }) {
    const [satisfied, setSatisfied] = useState(null)
    const [reason, setReason] = useState()
    const [error, setError] = useState(false);
    const [review, setReview] = useState('')
    const [photo, setPhoto] = useState()
    const [loading, setLoading] = useState()
    const [activeForm, setActiveForm] = useState('experience')
    const { user, token } = useContext(UserContext)
    const [participantCount, setParticipantCount] = useState(0)
    const [reasonError, setReasonError] = useState(false)
    const [showImageCrop, setShowImageCrop] = useState(false)
    const [base64ForCrop, setBase64ForCrop] = useState(null)
    const [reviewDp, setReviewDp] = useState(null)
    const [reviewUploadImage, setReviewUploadImage] = useState(null)

    // Cleanup URLs on unmount
    useEffect(() => {
        return () => {
            if (reviewDp && !reviewDp.startsWith('http')) {
                URL.revokeObjectURL(reviewDp)
            }
        }
    }, [])

    // Handle image crop completion
    useEffect(() => {
        if (reviewUploadImage && !base64ForCrop) {
            setShowImageCrop(false)
            setPhoto(reviewUploadImage)
        }
    }, [reviewUploadImage, base64ForCrop])

    // Show crop modal when base64 is set
    useEffect(() => {
        if (base64ForCrop) {
            setShowImageCrop(true)
        }
    }, [base64ForCrop])

    useEffect(() => {
        setParticipantCount(modal?.request?.number_of_players)
    }, [modal?.request])

    const updateParticipants = async () => {
        let URL = ENDPOINTS?.UPDATE_PLAYERS

        let body = {
            userId: user?.id,
            players: participantCount,
            requestId: modal?.request?.request_id
        }

        setLoading(true)
        await fetch(URL, {
            method: "POST",
            headers: {
                'Content-Type': 'application/json',
                ['Authorization']: `Bearer ${token}`,
            },
            body: JSON.stringify(body)
        })
            .then((response) => response.json())
            .then(async (data) => {
                if (!data?.status) {
                    setLoading(false)
                    toastNotification({
                        type: constantOptions.TOAST_TYPE.ERROR,
                        message: data?.message,
                    })
                } else {
                    console.log("1")
                    completeGame()
                }
            })
    }

    const completeGame = async () => {
        let reviewPhoto
        let actualReview = review.trim();
        setReview(actualReview);
        setError(!!!actualReview.length);

        let URL = ENDPOINTS?.COMPLETE_GAME

        if (photo) {
            reviewPhoto = await uploadFile({ file: photo, path: 'game_reviews' })
        }

        if (actualReview || modal?.reviewDisabled) {
            try {
                setLoading(true)
                await fetch(URL, {
                    method: "POST",
                    headers: {
                        'Content-Type': 'application/json',
                        ['Authorization']: `Bearer ${token}`,
                    },
                    body: JSON.stringify({
                        userId: user?.id,
                        requestId: modal?.request?.request_id,
                        satisfied,
                        review,
                        reason,
                        photo: reviewPhoto
                    })
                })
                    .then((response) => response.json())
                    .then(async (data) => {
                        if (!data?.status) {
                            toastNotification({
                                type: constantOptions.TOAST_TYPE.ERROR,
                                message: data?.message,
                            })
                        } else {
                            await modal?.fetchTabsCount()
                            await modal?.fetchUnreadMessageStatus()
                            setModal()
                            await modal.fetchRequests((prev) => prev + 1)
                            toastNotification({
                                type: constantOptions.TOAST_TYPE.REQUEST_MOVED,
                                message: 'This request has now been completed and moved to your history tab.',
                            })
                        }
                    })
            } catch (error) {
                console.log("Review Game------", error);
            } finally {
                setLoading(false)
            }
        } else {
            setError(!!!actualReview.length);
        }
    }

    return (
        <div className="flex flex-col w-full px-md">
            {activeForm === 'experience' && (
                <>
                    <div className='text-24 font-medium flex-center mb-md'>Rate the Experience</div>
                    <div className="w-full text-center text-16 text-16 mb-lg">
                        Was the game experience satisfactory?
                    </div>
                    <div className='flex justify-evenly'>
                        <div>
                            <ThumbsUp isActive={satisfied === true} onClick={() => setSatisfied(true)} />
                            <div className='text-16 flex-center pt-sm'>Yes</div>
                        </div>
                        <div>
                            <ThumbsDown isActive={satisfied === false} onClick={() => setSatisfied(false)} />
                            <div className='text-16 flex-center pt-sm'>No</div>
                        </div>
                    </div>
                    {(!satisfied && satisfied !== null) && (
                        <>
                            <div className='flex-center py-md'>Please let us know why you are not satisfied with the game</div>
                            <TextArea
                                title={'Add Reason'}
                                value={reason}
                                update={(value) => {
                                    setReason(value);
                                    setReasonError(!value?.trim());
                                }}
                                placeholder="Please elaborate reason for the dissatisfaction"
                                error={reasonError}
                                disableError={true}
                                maxLength={300}
                            />
                        </>
                    )}
                </>
            )}
            {activeForm === 'review' && (
                <>
                    <div className='text-24 font-medium flex-center mb-md'>Add Game Review</div>
                    <div className='text-grayLight text-center mb-md'>
                        Your review will be shared in TG Public Chat with other fellow golfers
                    </div>
                    <TextArea
                        className={'mb-md'}
                        value={review}
                        update={setReview}
                        placeholder={'Any observations on course architecture, club facilities and experience, notable signature food and drinks, etc. tend to be appreciated by other members'}
                        error={error}
                        disableError={true}
                        minHeight={"120px"}
                        maxLength={300}
                    />
                    {showImageCrop ? (
                        <ImageCropModal
                            modal={{
                                type: 'review-game',
                                image: base64ForCrop,
                                setDp: setReviewDp,
                                setUploadImage: setReviewUploadImage,
                            }}
                            setBase64ForCrop={setBase64ForCrop}
                            setModal={() => {
                                setShowImageCrop(false)
                                setReviewDp(null)
                                setReviewUploadImage(null)
                                setBase64ForCrop(null)
                                setPhoto(null)
                            }}
                        />
                    ) : (
                        <MultiplePhotoUpload2
                            setBase64ForCrop={setBase64ForCrop}
                            value={reviewUploadImage}
                            eventDp={reviewDp}
                            setEventDp={setReviewDp}
                            setEventUploadImage={setReviewUploadImage}
                            update={(value) => setPhoto(value)}
                        />
                    )}
                </>
            )}
            {activeForm === 'participants' &&
                <>
                    <div className='text-24 font-medium flex-center mb-md'>Confirm No. Of Players</div>
                    <div className="w-full text-center text-lg mb-lg">Please re-confirm the number of players who played against this request.</div>
                    <div className='flex justify-evenly'>
                        {constantOptions?.SELECT_PARTICIPANTS.map((t) => {
                            return (<div
                                onClick={() => setParticipantCount(t)}
                                key={t} className={`w-[100px] h-[42px] text-16 flex-center cursor-pointer border-2 ${participantCount === t ? "border-darkteal text-darkteal bg-tealTierBg" : "border-lightestgray"} hover:border-darkteal hover:text-darkteal hover:bg-tealTierBg`}>{t}</div>)
                        })}
                    </div>
                </>
            }
            <div className="flex justify-center mt-xl w-full">
                <CustomButton
                    text={['experience', 'participants'].includes(activeForm) ? 'Cancel' : 'Back'}
                    width={"100%"}
                    height={45}
                    onClick={() => {
                        if (['experience', 'participants'].includes(activeForm)) {
                            setModal()
                        } else {
                            setActiveForm('experience')
                        }
                    }}
                    color='lightestgray'
                    textColor='black'
                />
                <CustomButton
                    loading={loading}
                    text={['experience', 'participants'].includes(activeForm) ? 'Continue' : 'Submit'}
                    width={"100%"}
                    height={45}
                    disabled={(activeForm === 'review' && !review) ||
                        (activeForm === 'participants' && !participantCount) ||
                        (activeForm === 'experience' && !satisfied && !reason?.trim())}
                    onClick={() => {

                        if (['experience'].includes(activeForm)) {
                            if (!satisfied && !reason?.trim()) {
                                setReasonError(true);
                                return;
                            }
                            if (modal?.request?.host_completed && !modal?.reviewDisabled) {
                                setActiveForm('review')
                            } else {
                                setActiveForm('participants')
                            }
                        } else if (['participants'].includes(activeForm) && !modal?.reviewDisabled) {
                            setActiveForm('review')
                        }
                        else {
                            updateParticipants()
                        }
                    }}
                />
            </div>
        </div>
    )
}
