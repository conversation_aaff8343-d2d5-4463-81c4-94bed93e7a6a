import React, { useContext } from 'react'
import XIcon from '../icons/RequestV2/XIcon'
import { CustomStreamContext } from '../../context/CustomStreamContext';


export default function TgGroupMembersModal({ modal, setModal }) {

    const { setOtherUserId, setShowDetails, setShowProfileDetail, setShowBackOption, setProfileRedirectionFrom } = useContext(CustomStreamContext)

    return (
        <div className="w-full relative">
            <div
                onClick={() => setModal()}
                className="cursor-pointer absolute"
                style={{ right: 12, top: -10 }}>
                <XIcon color="#333333" />
            </div>
            <div className="px-md text-24 text-black">Members</div>
            <div className="px-md pb-md border-b border-lightestgray text-gray">{modal?.members?.length} members in {modal?.groupName}</div>
            <div className='overflow-y-scroll' style={{ maxHeight: 290 }}>
                {modal?.members.map((member) => {
                    return (
                        <div
                            onClick={() => {
                                setProfileRedirectionFrom("my-clubs-list")
                                setOtherUserId(member?.id)
                                modal?.setDrawer({
                                    source: modal?.drawer?.source,
                                    type: modal?.drawer?.source === 'chat' ? 'profile' : 'profile-info',
                                    groupId: modal?.groupId,
                                    global: modal?.drawer?.source === 'chat' ? false : true,
                                    customBackClickHandler: modal?.customBackClickHandler
                                })
                                setModal()

                            }}
                            key={member?.id}
                            className='border-b border-lightestgray px-md py-sm flex cursor-pointer'>
                            {(member?.profilePhoto) ? (
                                <div className='rounded-full multi-member-avatar'
                                    style={{
                                        height: 28, width: 28,
                                        backgroundImage: `url("${member?.profilePhoto}")`,
                                        backgroundPosition: 'center',/* Center the image */
                                        backgroundRepeat: 'no-repeat', /* Do not repeat the image */
                                        backgroundSize: 'cover', /* Resize the background image to cover the entire container */
                                    }}>

                                </div>
                            ) : (
                                <div className='multi-member-avatar  bg-lightestgray text-darkteal flex-center rounded-full font-medium'
                                    style={{ height: 28, width: 28 }}>
                                    {member?.name.at(0)}
                                </div>
                            )}
                            <div className='flex-center pl-md'>{member?.name}</div>
                        </div>
                    )
                })}
            </div>
        </div>
    )
}