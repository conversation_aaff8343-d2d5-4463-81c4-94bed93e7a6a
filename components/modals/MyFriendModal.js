import React, { useState, useEffect, useContext } from 'react'
import { UserContext } from '../../pages/_app'
import { PhoneInput, TextInput } from '../common'
import MESSAGES from '../../constants/messages'
import NameInitials from '../common/NameInitials'
import ReadMore from '../../utils/truncate/readmore'
import ENDPOINTS from '../../constants/endpoints.json'
import constantOptions from '../../constants/constantOptions'
import { ABOUT_YOURSELF_MAX_LENGTH } from '../../graphql/queries/user'
import { TextArea } from '../common'
import CustomButton from '../buttons/CustomButton'
import useCheckDeviceScreen from '../../hooks/useCheckDeviceScreen'
import toastNotification from '../../utils/notifications/toastNotification'
import useThumbnail from '../../hooks/useThumbnail'


const status = constantOptions.USER_SEARCH_STATUS

export default function MyFriendModal({ modal, setModal }) {
    const { user, token } = useContext(UserContext)
    const [name, setName] = useState('')
    const [phone, setPhone] = useState('')
    const [userData, setUserData] = useState()
    const [notes, setNotes] = useState('')
    const [loading, setLoading] = useState(false)
    const [loading2, setLoading2] = useState(false)
    const [formErrors, setFormErrors] = useState()
    const [success, setSuccess] = useState(false)
    const [searchStatus, setSearchStatus] = useState("")
    const [friendRequestId, setFriendRequestId] = useState("")
    const [blockState, setBlockState] = useState({})
    const { isMobile } = useCheckDeviceScreen()
    const { thumbnailUrl } = useThumbnail(userData?.profilePhoto, 256)



    useEffect(() => {
        setTab()
    }, [searchStatus])

    const closeModal = () => {
        setModal()
    }

    const acceptFriendRequest = async () => {
        try {
            setLoading2(true)
            await fetch(ENDPOINTS?.ACCEPT_FRIEND_REQUEST, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    ['Authorization']: `Bearer ${token}`,
                },
                body: JSON.stringify({
                    userId: user?.id,
                    requestId: friendRequestId
                }),
            })
                .then((response) => response.json())
                .then((data) => { })
                .catch(err => console.log('Error', err));
            setLoading2(false)
            setModal()
            if (modal?.activeTab === 'received') {
                modal?.setUpdateReceived(true)
            }
        } catch (error) {
            console.log(error);
        }
    }
    const declineFriendRequest = async () => {
        try {
            setLoading(true)
            await fetch(ENDPOINTS?.DECLINE_FRIEND_REQUEST, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    ['Authorization']: `Bearer ${token}`,
                },
                body: JSON.stringify({
                    userId: user?.id,
                    requestId: friendRequestId
                }),
            })
                .then((response) => response.json())
                .then((data) => { })
                .catch(err => console.log('Error', err));
            setLoading(false)
            setModal()
            if (modal?.activeTab === 'received') {
                modal?.setUpdateReceived(true)
            }
        } catch (error) {
            console.log(error);
        }
    }

    const unblockUser = async () => {
        try {
            setLoading(true)
            await fetch(ENDPOINTS?.UNBLOCK_USER, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    ['Authorization']: `Bearer ${token}`,
                },
                body: JSON.stringify({
                    userId: user?.id,
                    otherUserId: blockState?.userInfo?.userId,
                    channelId: blockState?.blockData?.streamChannelId,
                }),
            })
                .then((data) => data.json())
                .then((data) => {
                    if (!data?.status) {
                        toastNotification({
                            type: constantOptions.TOAST_TYPE.ERROR,
                            message: data?.message,
                        })
                    }
                })
                .catch((error) => {
                    console.log(error)
                })
            setLoading(false)
            handleSearch()
        } catch (error) {
            setLoading(false)
            console.log(error)
        }
    }

    const setTab = () => {
        if (searchStatus === status[1]) {
            // This returns element for no result found
            return (
                <div className='flex-center flex-col p-md'>
                    <div style={{ height: 50, width: 50 }} className='flex-center bg-lightestgray rounded-full'><img height={26} width={26} src='/svg/search.svg' /></div>
                    <div className='text-16 text-black'>No Results Found</div>
                    <div className='md:px-xxl text-12 text-inputtitle text-center'>Please confirm the exact name and mobile number of the person you are trying to friend</div>
                </div>
            )
        }
        if (searchStatus === status[2]) {
            // This returns element for match found
            return (
                <>
                    <div className='text-12 text-grayLight px-md md:px-46 mt-21 mb-xs'>We found a match</div>
                    <div className='bg-lightestgray py-md px-md mx-md md:mx-46 rounded-lg'>
                        <div className='flex'>
                            {(!userData?.profilePhoto && (user?.firstName || userData?.lastName)) ? (
                                <NameInitials
                                    user={{ first_name: userData?.firstName, last_name: userData?.lastName, }}
                                    height={67}
                                    width={67}
                                    fontSize={32}
                                    background={'bg-white'}
                                />
                            ) :
                                (
                                    <img
                                        src={thumbnailUrl}
                                        className="flex-center rounded-lg"
                                        style={{ height: 67, width: 67, objectFit: "cover" }}
                                    />
                                )
                            }
                            <div className='px-md text-black'>
                                <div className='text-18' style={{ lineHeight: 0.8 }}>{userData?.firstName} {userData?.lastName}</div>
                                <div className={'flex flex-row align-center mt-sm mb-xs'}>
                                    <img
                                        src="/svg/MailBlack.svg"
                                        height={12}
                                        width={12}
                                        style={{ marginRight: 5, marginTop: 5 }}
                                    />
                                    <div className='text-14 break-all'>
                                        {userData?.email}
                                    </div>
                                </div>
                                <div className={'flex flex-row align-center'} style={{ minWidth: 110 }}>
                                    <img
                                        src="/svg/PhoneBlack.svg"
                                        height={12}
                                        width={12}
                                        style={{ marginRight: 5 }}
                                    />
                                    <div className='text-14'>
                                        {userData?.phone}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className='w-full pt-4'>
                            <TextArea
                                title={'Add a Note (Optional)'}
                                disableError={!formErrors?.name}
                                error={formErrors?.name}
                                placeholder={"Type the note here"}
                                value={notes}
                                className={'border-none rounded-lg px-md'}
                                maxLength={300}
                                minHeight={40}
                                fontSize={"text-12"}
                                update={(val) => {
                                    setNotes(val)
                                    setFormErrors({
                                        ...formErrors, name: ''
                                    })
                                }} />
                            <div className='text-gray text-xs mt-2 text-right'>{ABOUT_YOURSELF_MAX_LENGTH - (notes ? notes.length : 0)}</div>

                        </div>
                        <div className='flex justify-end'>
                            <CustomButton
                                height={45}
                                width={"100%"}
                                borderRadius={8}
                                loading={loading}
                                text={"Send Friend Request"}
                                textSize={14}
                                onClick={sendFriendRequest}
                                marginX={"none"}
                            />
                        </div>
                    </div>
                </>
            )
        }
        if (searchStatus === status[3]) {
            // This returns element for already request sent
            return (
                <div className='bg-lightestgray py-md px-md mx-md md:mx-46 mt-md rounded-lg'>
                    <div className='flex'>
                        {(!userData?.profilePhoto && (user?.firstName || userData?.lastName)) ? (
                            <NameInitials
                                user={{ first_name: userData?.firstName, last_name: userData?.lastName, }}
                                height={67}
                                width={67}
                                fontSize={32}
                                background={'bg-white'}
                            />
                        ) :
                            (
                                <img
                                    src={thumbnailUrl}
                                    className="flex-center rounded-lg"
                                    style={{ height: 67, width: 67, objectFit: "cover" }}
                                />
                            )
                        }
                        <div className='px-md text-black'>
                            <div className='text-18 break-words' style={{ lineHeight: 0.8 }}>{userData?.firstName} {userData?.lastName}</div>
                            <div className={'flex flex-row align-center mt-sm mb-xs'}>
                                <img
                                    src="/svg/MailBlack.svg"
                                    height={12}
                                    width={12}
                                    style={{ marginRight: 5 }}
                                />
                                <div className='text-14 break-all'>
                                    {userData?.email}
                                </div>
                            </div>
                            <div className={'flex flex-row align-center'} style={{ minWidth: 110 }}>
                                <img
                                    src="/svg/PhoneBlack.svg"
                                    height={12}
                                    width={12}
                                    style={{ marginRight: 5 }}
                                />
                                <div className='text-14'>
                                    {userData?.phone}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <div className='flex mt-sm text-12'>
                            <img className='mr-sm' src='/svg/check-green.svg' />
                            You have already sent friend request to this user
                        </div>
                    </div>
                </div>
            )
        }
        if (searchStatus === status[4]) {
            // This returns element for already request received
            return (
                <div className='bg-lightestgray py-md px-md mx-md md:mx-xxl mt-md rounded-lg'>
                    <div className='flex'>
                        {(!userData?.profilePhoto && (user?.firstName || userData?.lastName)) ? (
                            <NameInitials
                                user={{ first_name: userData?.firstName, last_name: userData?.lastName, }}
                                height={67}
                                width={67}
                                fontSize={32}
                                background={'bg-white'}
                            />
                        ) :
                            (
                                <img
                                    src={thumbnailUrl}
                                    className="flex-center rounded-lg"
                                    style={{ height: 67, width: 67, objectFit: "cover" }}
                                />
                            )
                        }
                        <div className='px-md text-black'>
                            <div className='text-18 break-words' style={{ lineHeight: 0.8 }}>{userData?.firstName} {userData?.lastName}</div>
                            <div className={'flex flex-row align-center mt-sm mb-xs'}>
                                <img
                                    src="/svg/MailBlack.svg"
                                    height={12}
                                    width={12}
                                    style={{ marginRight: 5 }}
                                />
                                <div className='text-14 break-all'>
                                    {userData?.email}
                                </div>
                            </div>
                            <div className={'flex flex-row align-center'} style={{ minWidth: 110 }}>
                                <img
                                    src="/svg/PhoneBlack.svg"
                                    height={12}
                                    width={12}
                                    style={{ marginRight: 5 }}
                                />
                                <div className='text-14'>
                                    {userData?.phone}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div>
                        {userData?.requestNotes?.length ?
                            <div className='bg-white p-sm rounded-lg mt-sm text-12 text-black'>
                                {userData?.requestNotes?.length < 90 ? userData?.requestNotes : <ReadMore length={90}>{userData?.requestNotes}</ReadMore>}
                            </div>
                            : null}

                        <div className='flex mt-sm text-12'>
                            <img className='mr-sm' src='/svg/check-green.svg' />
                            You already have an open request from this user
                        </div>
                    </div>
                    <div className='w-full flex justify-between mt-md'>
                        <CustomButton
                            height={40}
                            width={isMobile ? 120 : 143}
                            borderRadius={8}
                            loading={loading2}
                            text={"Decline"}
                            textSize={14}
                            onClick={declineFriendRequest}
                            color={'red'}
                            marginX={'none'}
                        />
                        <CustomButton
                            height={40}
                            width={isMobile ? 120 : 143}
                            borderRadius={8}
                            loading={loading2}
                            text={"Accept"}
                            textSize={14}
                            onClick={acceptFriendRequest}
                            marginX={'none'}
                        />
                    </div>
                </div>
            )
        }
        if (searchStatus === status[5]) {
            // This returns element for already in friend list
            return (
                <div className='bg-lightestgray py-md px-md mx-46 mt-md rounded-lg'>
                    <div className='flex'>
                        {(!userData?.profilePhoto && (user?.firstName || userData?.lastName)) ? (
                            <NameInitials
                                user={{ first_name: userData?.firstName, last_name: userData?.lastName, }}
                                height={67}
                                width={67}
                                fontSize={32}
                                background={'bg-white'}
                            />
                        ) :
                            (
                                <img
                                    src={thumbnailUrl}
                                    className="flex-center rounded-lg"
                                    style={{ height: 67, width: 67, objectFit: "cover" }}
                                />
                            )
                        }
                        <div className='px-md text-black'>
                            <div className='text-18' style={{ lineHeight: 0.8 }}>{userData?.firstName} {userData?.lastName}</div>
                            <div className={'flex flex-row align-center mt-sm mb-xs'}>
                                <img
                                    src="/svg/MailBlack.svg"
                                    height={12}
                                    width={12}
                                    style={{ marginRight: 5 }}
                                />
                                <div className='text-14'>
                                    {userData?.email}
                                </div>
                            </div>
                            <div className={'flex flex-row align-center'} style={{ minWidth: 110 }}>
                                <img
                                    src="/svg/PhoneBlack.svg"
                                    height={12}
                                    width={12}
                                    style={{ marginRight: 5 }}
                                />
                                <div className='text-14'>
                                    {userData?.phone}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <div className='flex mt-sm text-12'>
                            <img className='mr-sm' src='/svg/check-green.svg' />
                            This golfer is already added to your Friend List
                        </div>
                    </div>
                </div>
            )
        }

        //When user is blocked
        if (searchStatus === status[6]) {
            return (
                <div className="bg-lightestgray py-md px-md mx-md md:mx-46 mt-md rounded-lg">
                    <div className="flex">
                        {!userData?.profilePhoto &&
                            (user?.firstName || userData?.lastName) ? (
                            <NameInitials
                                user={{
                                    first_name: userData?.firstName,
                                    last_name: userData?.lastName,
                                }}
                                height={67}
                                width={67}
                                fontSize={32}
                                background={'bg-white'}
                            />
                        ) : (
                            <img
                                src={thumbnailUrl}
                                className="flex-center rounded-lg"
                                style={{
                                    height: 67,
                                    width: 67,
                                    objectFit: 'cover',
                                }}
                            />
                        )}
                        <div className="px-md text-black">
                            <div
                                className="text-18"
                                style={{ lineHeight: 0.8 }}>
                                {userData?.firstName} {userData?.lastName}
                            </div>
                            <div
                                className={
                                    'flex flex-row align-center mt-sm mb-xs'
                                }>
                                <img
                                    src="/svg/MailBlack.svg"
                                    height={12}
                                    width={12}
                                    style={{ marginRight: 5 }}
                                />
                                <div className="text-14">{userData?.email}</div>
                            </div>
                            <div
                                className={'flex flex-row align-center'}
                                style={{ minWidth: 110 }}>
                                <img
                                    src="/svg/PhoneBlack.svg"
                                    height={12}
                                    width={12}
                                    style={{ marginRight: 5 }}
                                />
                                <div className="text-14">{userData?.phone}</div>
                            </div>
                        </div>
                    </div>
                    <div>
                        {blockState?.blockData?.youHaveBlocked ? (
                            <div className='flex-col flex-center pt-md w-full'>
                                <div className="flex mt-sm text-12 font-medium items-center">
                                    <img
                                        className="mr-sm w-14px"
                                        src="/svg/RedCross.svg"
                                    />
                                    <span className="font-medium text-12">
                                        You've blocked this user
                                    </span>
                                </div>
                                <CustomButton
                                    height={40}
                                    width={143}
                                    borderRadius={8}
                                    loading={loading}
                                    text={'Unblock'}
                                    textSize={14}
                                    onClick={unblockUser}
                                    marginX={'none'}
                                    cssClassName='mt-md'
                                />
                            </div>
                        ) : (
                            <div className="flex mt-sm text-12 font-medium">
                                <img
                                    className="mr-sm w-14px"
                                    src="/svg/RedCross.svg"
                                />
                                This user has blocked you
                            </div>
                        )}
                    </div>
                </div>
            )
        }
    }

    const validate = async () => {
        let formErrors = {}
        let allFormErrors = {}

        if (!name || name === '' || (name && name.trim() === '')) {
            formErrors.name = MESSAGES.SEARCH_FRIEND.NAME
        }
        if (!phone || phone === '') {
            formErrors.phone = MESSAGES.SEARCH_FRIEND.PHONE
        }
        if (phone && (phone.length < 11 || phone.length > 13)) {
            formErrors.phone = MESSAGES.SEARCH_FRIEND.INVALID_PHONE
        }
        allFormErrors = { ...formErrors }
        setFormErrors(allFormErrors)
        return (
            Object.values(allFormErrors).filter(
                (value) => Object.values(value).length > 0
            ).length === 0
        )
    }

    const sendFriendRequest = async () => {
        try {
            setLoading(true)
            await fetch(ENDPOINTS?.SEND_FRIEND_REQUEST, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    ['Authorization']: `Bearer ${token}`,
                },
                body: JSON.stringify({
                    userId: user?.id,
                    receiverId: userData?.userId,
                    requestNote: notes.trim()
                }),
            })
                .then((response) => response.json())
                .then((data) => {
                    setSuccess(true)
                })
                .catch(err => console.log('Error', err));
            setLoading(false)
            if (modal?.activeTab === 'sent') {
                modal?.setUpdateSent(true)
            }
        } catch (error) {
            console.log(error);
        }
    }

    const handleSearch = async () => {
        const isValid = await validate();
        if (isValid) {
            try {
                setLoading(true)
                await fetch(ENDPOINTS?.SEARCH_USER, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        ['Authorization']: `Bearer ${token}`,
                    },
                    body: JSON.stringify({
                        name: name.trim(),
                        phoneNumber: `+${phone}`,
                        userId: user?.id
                    }),
                })
                    .then((response) => response.json())
                    .then((data) => {
                        if (!data.status) {
                            setSearchStatus(status[1])
                        } else {
                            setUserData(data?.userInfo)
                            if (data?.status && (!data?.isFriendRequestSent || data?.isFriendRequestReceived)) {
                                setSearchStatus(status[2])
                            }
                            if (data?.isFriendRequestSent) {
                                setSearchStatus(status[3])
                            }
                            if (data?.isFriendRequestReceived) {
                                setFriendRequestId(data?.requestId)
                                setSearchStatus(status[4])
                            }
                            if (data?.isFriend) {
                                setFriendRequestId(data?.requestId)
                                setSearchStatus(status[5])
                            }
                            if (data?.isBlocked) {
                                setFriendRequestId(data?.requestId)
                                setSearchStatus(status[6])
                                setBlockState({ blockData: data?.blockStatus, userInfo: data?.userInfo })
                            }
                        }
                    })
                    .catch(err => console.log('Error', err));
                setLoading(false)
            } catch (error) {
                console.log(error);
            }
        }
    }

    return (
        <div className="flex flex-col w-full mt-sm relative">
            {success ? (<div className='md:px-100 flex-col flex-center'>
                <div className='mb-md bg-lightestgray rounded-full self-center flex-center' style={{ height: 60, width: 60 }}>
                    <img src='/svg/RequestSuccessfulFlag.svg' />
                </div>
                <div className="text-black self-center text-24 text-center mb-36">
                    Friend Request Sent Successfully
                </div>
                <CustomButton
                    text={"Dismiss"}
                    height={45}
                    width={164}
                    borderRadius={8}
                    textSize={14}
                    onClick={closeModal}
                    marginX={'none'}
                />
            </div>) : (
                <>
                    <div className='bg-lightestgray rounded-full self-center flex-center' style={{ height: 60, width: 60 }}>
                        <img src='/svg/AddFriendDialog.svg' />
                    </div>
                    <div className="text-black self-center mt-12 text-24">
                        Add Friend
                    </div>
                    <div className='px-md text-grayLight text-12 text-center font-light'>Please search an existing TG user to add them to your network.</div>
                    <div className="flex flex-col justify-center">
                        <div className="w-full mt-21">
                            {searchStatus === "" || searchStatus === status[1] ? (
                                <div className='px-md md:px-46 flex flex-col'>
                                    <div className='w-full' id="my_friends">
                                        <TextInput
                                        
                                            border={'none'}
                                            disableError={!formErrors?.name}
                                            error={formErrors?.name}
                                            placeholder={"First name or Last name*"}
                                            value={name}
                                            className={'border-none bg-lightestgray rounded-lg font-normal px-md text-16'}
                                            update={(val) => {
                                                setName(val)
                                                setFormErrors({
                                                    ...formErrors, name: ''
                                                })
                                            }} />
                                    </div>
                                    <div className='w-full mt-md'>
                                        <PhoneInput
                                            border={'none'}
                                            error={formErrors?.phone}
                                            disableError={!formErrors?.phone}
                                            placeholder={"Enter mobile number"}
                                            value={phone}
                                            className={'bg-lightestgray rounded-lg text-black font-normal text-16'}
                                            fontSizeClass='text-16'
                                            update={(value, country) => {
                                                setPhone(value)
                                                setFormErrors({
                                                    ...formErrors, phone: ''
                                                })
                                            }
                                            }
                                        />
                                    </div>
                                </div>
                            ) : (
                                <div className='text-black px-md md:px-46 flex flex-col'>
                                    <div className='mb-sm'>
                                        <div className='text-12 font-thin'>Name</div>
                                        <div className='text-16 break-words'>{userData?.firstName} {userData?.lastName}</div>
                                    </div>
                                    <div>
                                        <div className='text-12 font-thin'>Mobile Number</div>
                                        <div className='text-16 break-words'>{userData?.phone}</div>
                                    </div>
                                </div>
                            )}
                        </div>
                        {setTab()}
                        <div className="flex justify-evenly w-full px-md md:px-46 pt-lg">
                            {searchStatus === "" || searchStatus === status[1] ? (
                                <CustomButton
                                    text={"Cancel"}
                                    height={45}
                                    width={modal?.isMobile ? 140 : 164}
                                    borderRadius={8}
                                    textSize={14}
                                    onClick={closeModal}
                                    color={'lightestgray'}
                                    marginX={'none'}
                                    textColor={'black'}
                                />
                            ) : (
                                <CustomButton
                                    text={"Dismiss"}
                                    height={45}
                                    width={"100%"}
                                    borderRadius={8}
                                    textSize={14}
                                    onClick={closeModal}
                                    color={'lightestgray'}
                                    marginX={'none'}
                                    textColor={'black'}
                                />
                            )}

                            {searchStatus === "" || searchStatus === status[1] ?
                                <CustomButton
                                    height={45}
                                    width={modal?.isMobile ? 140 : 164}
                                    borderRadius={8}
                                    loading={loading}
                                    text={"Search"}
                                    textSize={14}
                                    onClick={handleSearch}
                                    marginX={'none'}
                                />
                                : null
                            }
                        </div>
                    </div>
                </>
            )
            }
        </div >
    )
}
