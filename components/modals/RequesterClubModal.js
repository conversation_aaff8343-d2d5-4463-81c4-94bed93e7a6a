import React from 'react'

export default function RequesterClubModal({ modal, setModal }) {
    return (
        <div className="w-full relative">
            <div
                onClick={() => setModal()}
                className="cursor-pointer absolute"
                style={{ right: 12, top: -10 }}>
                <img src='/svg/CrossIconBlack.svg' />
            </div>
            <div className="px-md pb-md border-b border-lightestgray text-24 text-black">{modal?.heading || "All Clubs"}</div>
            <div className='overflow-y-scroll' style={{ maxHeight: 290 }}>
                {modal?.clubs.map((club) => {
                    return (
                        <div
                            key={club}
                            className='border-b border-lightestgray px-md py-sm flex'>
                            <div className='bg-lightestgray rounded-lg flex-center h-[41px] w-[41px]'>
                                <img height={20} width={20} src='/svg/Location-Darkteal.svg' />
                            </div>
                            <div className='ml-md text-16 py-sm text-black'>{club}</div>
                        </div>
                    )
                })}
            </div>
        </div>
    )
}