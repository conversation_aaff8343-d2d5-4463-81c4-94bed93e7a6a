import React, { useState, useEffect, useContext } from 'react';
import CustomButton from '../buttons/CustomButton';
import RequesterIcon from '../icons/RequesterIcon';
import HostIcon from '../icons/HostIcon';
import { DateRange, DateSelect, PhoneInput, Select, TextInput } from '../common';
import RequestBlockedIcon from '../icons/RequestBlockedIcon';
import { EMAIL_REGEX } from '../../utils/validationConstants';
import NoSearchResults from '../chat-v2/common/NoSearchResults';
import NameInitials from '../common/NameInitials';
import useThumbnail from '../../hooks/useThumbnail';
import { UserContext } from '../../pages/_app';
import moment from 'moment';
import ENDPOINTS from "../../constants/endpoints.json"
import MESSAGES from '../../constants/messages';

const steps = {
    1: 'Warning',
    2: 'Role',
    3: 'Search',
    4: 'Select',
    5: 'No results',
    6: 'Success',
};

const GameLogModal = ({ setModal, modal }) => {
    const [userData, setUserData] = useState(null);
    const [selectedClub, setSelectedClub] = useState(null)
    const [gameDate, setGameDate] = useState(undefined)
    const [players, setPlayers] = useState(1)
    const [selectedRole, setSelectedRole] = useState(null);
    const [loading, setLoading] = useState(false);
    const [step, setStep] = useState(1);
    const [email, setEmail] = useState('');
    const [phone, setPhone] = useState('');
    const [formErrors, setFormErrors] = useState({});
    const [isFormValid, setIsFormValid] = useState(false);
    const { thumbnailUrl } = useThumbnail(userData?.profilePhoto, 256)
    const { user, token } = useContext(UserContext)
    const [errors, setErrors] = useState({})
    const [dialCode, setDialCode] = useState('')
    const [phoneNumber, setPhoneNumber] = useState('')
    const { fetchNgvLogs } = modal

    useEffect(() => {
        // Check if either email or phone has a value
        setIsFormValid(email.trim() !== '' || phoneNumber.trim() !== '');
    }, [email, phoneNumber]);

    useEffect(() => {
        if (userData?.clubs.length === 1) {
            setSelectedClub(userData?.clubs[0])
        }
    }, [userData])


    const validateEmail = (email) => {
        return EMAIL_REGEX.test(email);
    };

    const validatePhone = (phone) => {
        return (phone && (phone.length < 11 || phone.length > 13))
    }

    const validate = async () => {
        let formErrors = {}
        let allFormErrors = {}

        if (email && !validateEmail(email)) {
            formErrors.email = MESSAGES.SEARCH_FRIEND.EMAIL
        }
        if (phone && validatePhone(phone)) {
            formErrors.phone = MESSAGES.SEARCH_FRIEND.INVALID_PHONE
        }
        allFormErrors = { ...formErrors }
        setFormErrors(allFormErrors)
        return (
            Object.values(allFormErrors).filter(
                (value) => Object.values(value).length > 0
            ).length === 0
        )
    }

    const searchPlayer = async () => {
        const isValid = await validate();
        if (!isValid) {
            return
        }
        try {
            setLoading(true)
            let body = {
                userId: user?.id,
                userType: selectedRole,
            }
            if (phoneNumber) {
                body = {
                    ...body,
                    phoneNumber: `+${dialCode}${phoneNumber}`
                }
            }
            if (email) {
                body = {
                    ...body,
                    email
                }
            }
            await fetch(ENDPOINTS?.SEARCH_GAME_USER, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    ['Authorization']: `Bearer ${token}`,
                },
                body: JSON.stringify(body),
            })
                .then((data) => data.json())
                .then((data) => {
                    if (data?.data?.length) {
                        setUserData(data?.data[0])
                        setStep(4);
                    } else {
                        setStep(5)
                    }
                })
            setLoading(false)
        } catch (error) {
            console.log("Request Against Offer Club Search--------", error);
        }
    }

    const logOfflineGame = async () => {
        try {
            setLoading(true)
            await fetch(ENDPOINTS?.LOG_OFFLINE_GAME, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    ['Authorization']: `Bearer ${token}`,
                },
                body: JSON.stringify({
                    userId: user?.id,
                    requesterId: selectedRole === 'requester' ? user?.id : userData?.id,
                    hostId: selectedRole === 'host' ? user?.id : userData?.id,
                    clubId: selectedClub?.id,
                    gameDate: moment(gameDate).format('YYYY-MM-DD'),
                    numberOfPlayers: players,
                }),
            })
                .then((data) => data.json())
                .then(async (data) => {
                    if (!data?.status) {
                        setErrors(data?.message)
                    } else {
                        setStep(6)
                    }
                })
            modal?.fetchHistoryRequests()
            setLoading(false)
        } catch (error) {
            console.log("Request Against Offer Club Search--------", error);
        }
    }

    return (
        <div className="flex flex-col items-center max-w-lg w-full px-32">
            {steps[step] !== 'Warning' &&
                <div className="h-[60px] w-[60px] rounded-full bg-lightestgray flex-center mb-sm">
                    <img src='/svg/golf-club.svg' />
                </div>
            }

            {[2, 3, 4, 5].includes(step) &&
                <h1 className="text-24 font-medium text-center mb-md">Log A Played Game</h1>
            }

            {steps[step] === 'Warning' && (
                <div className="flex flex-col items-center">
                    <div className="h-[60px] w-[60px] rounded-full bg-lightYellow flex-center mb-sm">
                        <RequestBlockedIcon height="24" width="27" paddingRight="0" />
                    </div>
                    <div className="text-24 font-medium text-center mb-md">Warning!</div>
                    <p className='text-gray text-center'>We request you to use this feature only for games where no request was created on Thousand Greens' App. To properly account for cancelled requests or request where the host was different than the Accepting Host, please contact <span className='text-darkteal'><a href="mailto:<EMAIL>"><EMAIL></a></span></p>
                </div>
            )}

            {steps[step] === 'Role' && (
                <>
                    <p className="text-grayLight text-center">
                        Here you can log a game that took place offline and was not scheduled through the TG App.
                    </p>

                    <h2 className="py-md">Were you a requester or host?</h2>

                    <div className="flex justify-center gap-16">
                        <div
                            className="flex flex-col items-center cursor-pointer"
                            onClick={() => setSelectedRole('requester')}
                        >
                            <RequesterIcon isActive={selectedRole === 'requester'} />
                            <span className="text-16 mt-sm">Requester</span>
                        </div>

                        <div
                            className="flex flex-col items-center cursor-pointer"
                            onClick={() => setSelectedRole('host')}
                        >
                            <HostIcon isActive={selectedRole === 'host'} />
                            <span className="text-16 mt-sm">Host</span>
                        </div>
                    </div>
                </>
            )}

            {['Search', 'No results'].includes(steps[step]) && (
                <div className="flex flex-col w-full">
                    <div>
                        Enter {selectedRole === 'requester' ? 'host' : 'requester'}'s contact information
                    </div>
                    <div className="w-full mt-md">
                        <PhoneInput
                            border='border-none'
                            disabled={email}
                            error={formErrors?.phone}
                            disableError={!formErrors?.phone}
                            placeholder="Mobile number"
                            value={phone}
                            className="border-none bg-lightestgray rounded-lg text-black font-normal text-16"
                            fontSizeClass="text-16"
                            update={(value, country) => {
                                const dialCode = country?.dialCode;
                                // Remove country code from phone number if present
                                const phoneWithoutCode = value.startsWith(dialCode) ?
                                    value.substring(dialCode.length) : value;
                                setPhoneNumber(phoneWithoutCode);

                                setPhone(phone);
                                setDialCode(dialCode); // Assuming you've added this state
                                setFormErrors({
                                    ...formErrors, phone: ''
                                });
                            }}
                        />
                    </div>
                    <div className='text-center text-12 text-grayLight py-sm'>or</div>
                    <div className="w-full" id="my_friends">
                        <TextInput
                            border='border-none'
                            disabled={phoneNumber !== ''}
                            disableError={!formErrors?.email}
                            error={formErrors?.email}
                            placeholder="Email"
                            value={email}
                            type="email"
                            className="border-none bg-lightestgray rounded-lg font-normal px-md text-16"
                            update={(val) => {
                                setEmail(val);
                                setFormErrors({
                                    ...formErrors, email: ''
                                });
                            }}
                        />
                    </div>
                    {steps[step] === 'No results' && (
                        <div className="px-md pt-xl pb-lg">
                            <NoSearchResults message="Member with entered number/E-mail doesn’t exist in our system. Please recheck the entered details." />
                        </div>
                    )}
                </div>
            )}

            {steps[step] === 'Select' && (
                <div className="w-full ">
                    <div className='mb-md'>
                        <div className='text-12 text-gray'>{!email ? 'Mobile Number' : 'Email'}</div>
                        <div className='text-16'>{!email ? userData?.phone : userData?.email}</div>
                    </div>

                    <div>
                        <div className='text-grayLight mb-sm text-12'>We found a match</div>
                        <div className='flex bg-lightestgray py-md px-md rounded-lg'>
                            {(!userData?.profilePhoto && (user?.first_name || userData?.last_name)) ? (
                                <NameInitials
                                    user={{ first_name: userData?.first_name, last_name: userData?.last_name, }}
                                    height={67}
                                    width={67}
                                    fontSize={32}
                                    background={'bg-white'}
                                />
                            ) :
                                (
                                    <img
                                        src={thumbnailUrl}
                                        className="flex-center rounded-lg"
                                        style={{ height: 67, width: 67, objectFit: "cover" }}
                                    />
                                )
                            }
                            <div className='px-md text-black'>
                                <div className='text-18' style={{ lineHeight: 0.8 }}>{userData?.first_name} {userData?.last_name}</div>
                                <div className={'flex flex-row align-center mt-sm mb-xs'}>
                                    <img
                                        src="/svg/MailBlack.svg"
                                        height={12}
                                        width={12}
                                        style={{ marginRight: 5, marginTop: 5 }}
                                    />
                                    <div className='text-14 break-all'>
                                        {userData?.email}
                                    </div>
                                </div>
                                <div className={'flex flex-row align-center'} style={{ minWidth: 110 }}>
                                    <img
                                        src="/svg/PhoneBlack.svg"
                                        height={12}
                                        width={12}
                                        style={{ marginRight: 5 }}
                                    />
                                    <div className='text-14'>
                                        {userData?.phone}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div
                        className="z-50 relative py-md"
                        style={{ zIndex: 1002 }}>
                        <Select
                            border='border-none'
                            isClubSelected={userData?.club}
                            value={selectedClub?.name}
                            update={(value) => {
                                setSelectedClub(userData?.clubs?.find((club) => club?.name === value))
                                setErrors({ ...errors, club: '', date_range: '' })
                            }}
                            options={userData?.clubs?.map((club) => club?.name)}
                            className={'text-16'}
                            filterTier={user.tier}
                            disableError={!errors.club}
                            exclude={user?.clubs?.map(({ id }) => id)}
                            error={errors?.club}
                            title={'Select Golf Club'}
                            placeholder="Select"
                            user={user}
                            titleWeight='normal'
                            fontWeignt='normal'
                        />
                    </div>

                    <DateSelect
                        border='border-none'
                        title="Game Date"
                        placeholder="Select date"
                        error={errors?.date_range}
                        value={gameDate}
                        maxDateMonth={12}
                        update={(value) => {
                            setErrors({ ...errors, date_range: '' })
                            setGameDate(value)
                        }}
                        minDate={moment().subtract(1, 'years').format('YYYY-MM-DD')}
                        maxDate={moment().subtract(1, 'days').format('YYYY-MM-DD')}
                        calendarDisabled={!selectedClub?.name}
                        setParentErrors={() => {
                            (selectedClub?.name) ? setErrors({ ...errors, date_range: "" }) : setErrors({ ...errors, date_range: 'To select a date range, please select a club first.' })
                        }}
                        titleSize={'12'}
                        textSize='16'
                        fontWeight="normal"
                    />


                    <Select
                        border='border-none'
                        disableError={true}
                        placeholder="Select"
                        title="Number of People"
                        value={players}
                        update={(value) => {
                            setPlayers(value)
                        }}
                        className={'text-16'}
                        options={[1, 2, 3]}
                    />
                </div>
            )}

            {steps[step] === 'Success' && (
                <div className="flex flex-col items-center">
                    <div className="text-14 text-grayLight text-center my-md">You’ve logged a offline played game in TG successfully. Your NGV would be updated in sometime, check back the NGV log screen in few minutes </div>
                </div>
            )}

            <div className="flex w-full gap-md mt-lg">
                {steps[step] !== 'Success' &&
                    <CustomButton
                        loading={loading}
                        text="Dismiss"
                        onClick={() => setModal()}
                        width="100%"
                        height={48}
                        color="lightestgray"
                        textColor="black"
                    />
                }
                <CustomButton
                    loading={loading}
                    text={steps[step] === 'Warning' ? "I Understand, Continue" :
                        steps[step] === 'Role' ? "Continue" :
                            ['Search', 'No results'].includes(steps[step]) ? "Search" : steps[step] === 'Select' ? "Log a Game" : "Continue"}
                    onClick={() => {
                        if (steps[step] === 'Warning') {
                            setStep(2);
                        } else if (steps[step] === 'Role' && selectedRole) {
                            setStep(3);
                        } else if (['Search', 'No results'].includes(steps[step])) {
                            searchPlayer()
                        } else if (steps[step] === 'Select') {
                            logOfflineGame()
                        } else if (steps[step] === 'Success') {
                            setModal()
                        }
                    }}
                    width="100%"
                    height={48}
                    color="darkteal"
                    textColor="white"
                    disabled={(steps[step] === 'Role' && !selectedRole) ||
                        (steps[step] === 'Search' && !isFormValid) ||
                        (steps[step] === 'Select' && (!selectedClub?.id || !gameDate || !players))
                    }
                />
            </div>
        </div>
    );
};

export default GameLogModal;
