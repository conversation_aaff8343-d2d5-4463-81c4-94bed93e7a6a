import React from 'react'

const ImagePreviewPopup = ({ modal, setModal }) => {

    return (
        <div>
            <div className="p-md flex relative">
                <div className='cursor-pointer absolute'
                    style={{ top: -5, right: 20 }}
                    onClick={() => {
                        setModal(false)
                    }}
                >
                    <img src='/svg/CrossIconBlack.svg' />
                </div>
            </div>
            <div
                className="h-[600px] w-[600px] relative rounded-lg"
                style={{
                    backgroundImage: `url("${modal?.image}")`,
                    backgroundPosition: 'center',
                    backgroundSize: 'cover',
                    backgroundRepeat: 'no-repeat',
                    position: 'relative',
                }}>
                <div
                    style={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        backgroundImage: `url("${modal?.image}")`,
                        backgroundSize: 'cover',
                        backgroundPosition: 'center',
                        filter: 'blur(20px)',
                        opacity: 0.3,
                        zIndex: 0,
                    }}
                />
                <div
                    style={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        backgroundColor: 'rgba(0, 0, 0, 0.4)',
                        backdropFilter: 'blur(5px)',
                        zIndex: 1,
                    }}
                />
                <div
                    style={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        backgroundImage: `url("${modal?.image}")`,
                        backgroundSize: 'contain',
                        backgroundPosition: 'center',
                        backgroundRepeat: 'no-repeat',
                        zIndex: 2,
                    }}
                />
            </div>
        </div>
    )
}

export default ImagePreviewPopup