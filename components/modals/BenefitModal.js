import React, { useEffect, useState } from 'react'
import { Checkbox, Select, TextArea, TextInput } from '../common'
import firebase from 'firebase/compat/app'
import 'firebase/compat/auth'
import 'firebase/compat/storage'
import MultiplePhotoUpload from '../common/MultiplePhotoUpload'
import GreenSubmitButton from '../buttons/GreenSubmitButton'
import useClient from '../../graphql/useClient'
import { CREATE_BENEFIT, UPDATE_BENEFIT, FETCH_BENEFIT_CATEGORY } from '../../graphql/mutations/benefit'
import DateRangeField from '../common/DateRange'
import MESSAGES from '../../constants/messages'
import ImageCropModal from './ImageCropModal'

const uploadFile = async (file, path) => {
    const storageRef = firebase.storage().ref().child(`${path}/${file.name}`)
    const fileURL = await storageRef
        .put(file)
        .then((snapshot) => snapshot.ref.getDownloadURL())
        .catch(() => { })
    return fileURL
}

export default function BenefitModal({ modal, setModal }) {

    const [form, setForm] = useState({
        title: modal.benefit?.title,
        description: modal.benefit?.description,
        category: modal.benefit?.benefit_category?.name,
        category_id: modal.benefit?.category_id,
        photo: modal.benefit?.photo,
        dateRange: modal.benefit?.dateRange,
        premiumOnly: modal.benefit ? modal.benefit.premiumOnly : true,
        sendToEmail: modal.benefit?.sendToEmail,
        sendToWebsite: modal.benefit ? modal.benefit.sendToWebsite : true,
        websiteURL: modal.benefit?.websiteURL,
        promo_code: modal.benefit?.promo_code,
    })
    const [errors, setErrors] = useState({})
    const [loading, setLoading] = useState(false)
    const [showErrors, setShowErrors] = useState(false)
    const [showImageCrop, setShowImageCrop] = useState(false)
    const [base64ForCrop, setBase64ForCrop] = useState(null)
    const [benefitDp, setBenefitDp] = useState(modal.benefit?.photo || null)
    const [benefitUploadImage, setBenefitUploadImage] = useState(null)

    const client = useClient()
    const [benefitCategories, setBenefitCategories] = useState([])
    const [benefitCategory, setBenefitCategory] = useState({})

    // Handle image crop completion
    useEffect(() => {
        if (benefitUploadImage && !base64ForCrop) {
            setShowImageCrop(false)
        }
    }, [benefitUploadImage, base64ForCrop])

    // Show crop modal when base64 is set
    useEffect(() => {
        if (base64ForCrop) {
            setShowImageCrop(true)
        }
    }, [base64ForCrop])

    // Cleanup URLs on unmount
    useEffect(() => {
        return () => {
            if (benefitDp && !benefitDp.startsWith('http')) {
                URL.revokeObjectURL(benefitDp)
            }
        }
    }, [])

    useEffect(() => {
        updateForm('category', benefitCategory?.name)
    }, [benefitCategory])

    useEffect(() => {
        updateForm('category', modal.benefit?.benefit_category?.name,)
    }, [])

    useEffect(() => {
        if (showErrors) {
            validateBenefit()
        }

    }, [form, showErrors])

    useEffect(() => {
        fetchBenefitCategory()
    }, [client])

    function updateForm(name, val) {
        setForm({ ...form, [name]: val })
    }

    function validateBenefit() {
        let errors = {}
        const requiredFields = ['title', 'category', 'description', 'photo']

        requiredFields.map((field) => {
            if (!form[field]) {
                errors[field] = `${field[0].toUpperCase()}${field.slice(
                    1
                )} is required`
            }
        })

        if (form.sendToWebsite && !form.websiteURL) {
            errors.websiteURL = MESSAGES?.benefit?.required?.website
        }

        if (form.sendToEmail && !form.email) {
            errors.email = MESSAGES?.benefit?.required?.email
        }

        setErrors(errors)
        return Object.values(errors).length === 0
    }

    async function submitBenefit() {
        setShowErrors(true)
        if (validateBenefit()) {
            if (modal.benefit) {
                editBenefit()
            } else {
                createBenefit()
            }
        }
    }

    async function createBenefit() {
        setLoading(true)
        const photo = await uploadFile(form.photo, 'benefits')
        await client
            .request(CREATE_BENEFIT, {
                benefit: { ...form, photo },
            })
            .then((res) => { })
        await modal.refresh()
        setLoading(false)
        setModal()
    }

    async function fetchBenefitCategory() {
        if (client) {
            setLoading(true)
            await client
                .request(FETCH_BENEFIT_CATEGORY)
                .then((categories) => setBenefitCategories(categories?.benefit_category))
            setLoading(false)

        }
    }

    async function editBenefit() {
        setLoading(true)
        let photo = form.photo

        if (typeof photo !== 'string') {
            photo = await uploadFile(form.photo, 'benefits')
        }

        await client
            .request(UPDATE_BENEFIT, {
                benefit_id: modal.benefit.id,
                benefit: { ...form, photo },
            })
            .then((res) => { })
        await modal.refresh()
        setLoading(false)
        setModal()
    }

    return (
        <div className="flex flex-col w-full mt-xl" style={{ maxWidth: 325 }}>
            <TextInput
                title="Title"
                value={form.title}
                update={(val) => updateForm('title', val)}
                error={errors.title}
            />
            <Select
                title="Benefit"
                value={benefitCategory?.name || form?.category}
                type="benefitCategories"
                update={(val) => {
                    updateForm('category_id', val?.id)
                    setBenefitCategory(val)
                }}
                error={errors.category}
                benefitCategories={benefitCategories}
            />
            <TextArea
                title="Description"
                className={"mb-lg"}
                value={form.description}
                update={(val) => updateForm('description', val)}
                error={errors.description}
                minHeight="calc(100vh - 700px)"
            />
            <div
                className="text-xs font-thin flex-center text-red"
                style={{ height: 30 }}>
                {errors.description}
            </div>
            <TextInput
                title="Promo Code"
                value={form.promo_code}
                update={(val) => updateForm('promo_code', val)}
                error={errors.promo_code}
            />
            <DateRangeField
                value={form.dateRange}
                update={(val) => updateForm('dateRange', val)}
                maxDateMonth={Infinity}
                singular
                error={errors.dateRange}
            />
            {showImageCrop ? (
                <ImageCropModal
                    modal={{
                        type: 'create-edit-group',
                        image: base64ForCrop,
                        setDp: setBenefitDp,
                        setUploadImage: setBenefitUploadImage,
                    }}
                    setBase64ForCrop={setBase64ForCrop}
                />
            ) : (
                <MultiplePhotoUpload
                    value={benefitUploadImage}
                    update={(val) => updateForm('photo', val)}
                    error={errors.photo}
                    setBase64ForCrop={setBase64ForCrop}
                    benefitDp={benefitDp}
                    setBenefitDp={setBenefitDp}
                    setBenefitUploadImage={setBenefitUploadImage}
                />
            )}
            <Checkbox
                value={form.premiumOnly}
                update={(val) => updateForm('premiumOnly', val)}
                label="For Premium Members Only"
            />
            <Checkbox
                value={form.sendToEmail}
                update={(val) => {
                    if (val) {
                        setForm({
                            ...form,
                            sendToEmail: val,
                            sendToWebsite: false,
                        })
                    }
                }}
                label="Send to Email"
            />
            {form.sendToEmail && (
                <TextInput
                    value={form.email}
                    update={(val) => updateForm('email', val)}
                    placeholder="Email"
                    error={errors.email}
                />
            )}
            <Checkbox
                value={form.sendToWebsite}
                update={(val) => {
                    if (val) {
                        setForm({
                            ...form,
                            sendToEmail: false,
                            sendToWebsite: val,
                        })
                    }
                }}
                label="Send to Website"
            />
            {form.sendToWebsite && (
                <TextInput
                    value={form.websiteURL}
                    update={(val) => updateForm('websiteURL', val)}
                    placeholder="Website URL"
                    disableError={!errors.websiteURL}
                    error={errors.websiteURL}
                />
            )}

            <div className="flex justify-center mt-xl">
                <div className="flex flex-1 justify-center">
                    <div
                        onClick={() => setModal()}
                        className="p-md flex-center uppercase text-sm font-regular text-gray hover:underline mb-sm cursor-pointer"
                        style={{
                            height: 53,
                            borderRadius: 10,
                        }}>
                        Cancel
                    </div>
                </div>
                <GreenSubmitButton
                    loading={loading}
                    text={modal.benefit ? 'Save Benefit' : 'Create Benefit'}
                    onSubmit={submitBenefit}
                />
            </div>
        </div>
    )
}
