import React, { useState } from 'react'
import 'firebase/compat/auth'
import CustomButton from '../buttons/CustomButton';
import Link from 'next/link';

export default function LinkRedirectionConfirmationModal({ modal, setModal }) {

    return (
        <div className="flex-center flex-col px-xxl">
            <div className='text-21 text-center'>{modal?.text}</div>
            <div className="text-gray text-center pt-lg pb-lg">
                {modal?.subtext}
            </div>
            <div className="flex">
                <CustomButton
                    text='No'
                    width={170}
                    height={50}
                    color='lightestgray'
                    textColor='black'
                    loading={modal?.loading}
                    onClick={() => {
                        setModal()
                    }}
                />

                <Link href={modal?.link} as={modal?.as}>
                    <CustomButton
                    text='Yes'
                    color={modal?.buttonColor ? modal?.buttonColor : ''}
                    width={170}
                    height={50}
                    loading={modal?.loading}
                    onClick={() => {
                        setModal()
                        modal?.setDrawer()
                    }}
                />
                </Link>
            </div>
        </div>
    )
}
