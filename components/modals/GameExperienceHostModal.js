import React, { useState, useEffect, useContext } from 'react'
import { UserContext } from '../../pages/_app'
import { UPDATE_GAME } from '../../graphql/mutations/game'
import useClient from '../../graphql/useClient'
import { TextArea } from '../common'
import 'firebase/compat/auth'
import 'firebase/compat/storage'
import CustomButton from '../buttons/CustomButton'
import ThumbsUp from '../icons/ThumbsUp'
import ThumbsDown from '../icons/ThumbsDown'
import constantOptions from '../../constants/constantOptions'
import toastNotification from '../../utils/notifications/toastNotification'
import ENDPOINTS from "../../constants/endpoints.json"

export default function GameExperienceHostModal({ modal, setModal }) {
    const { token, user } = useContext(UserContext)
    const [satisfied, setSatisfied] = useState()
    const [reason, setReason] = useState()
    const [loading, setLoading] = useState()
    const client = useClient()
    const [showParticipantSelector, setShowParticipantSelector] = useState(false)
    const [participantCount, setParticipantCount] = useState(0)
    const [reasonError, setReasonError] = useState(false)

    useEffect(() => {
        setParticipantCount(modal?.request?.number_of_players)
    }, [modal?.request])

    const updateParticipants = async () => {
        let URL = ENDPOINTS?.UPDATE_PLAYERS

        let body = {
            userId: user?.id,
            players: participantCount,
            requestId: modal?.request?.request_id
        }

        setLoading(true)
        await fetch(URL, {
            method: "POST",
            headers: {
                'Content-Type': 'application/json',
                ['Authorization']: `Bearer ${token}`,
            },
            body: JSON.stringify(body)
        })
            .then((response) => response.json())
            .then(async (data) => {
                if (!data?.status) {
                    setLoading(false)
                    toastNotification({
                        type: constantOptions.TOAST_TYPE.ERROR,
                        message: data?.message,
                    })
                } else {
                    submitReview()
                }
            })
        setLoading(false)
    }

    async function submitReview() {
        await client.request(UPDATE_GAME, {
            game_id: modal.game_id,
            game: {
                hostCompleted: true,
                hostSatisfied: satisfied,
                host_experience_reason: reason
            },
        })

        if (modal.fetchRequests) {
            await modal.fetchRequests()
        }

        await modal.fetchUnreadMessageStatus()
        await modal.fetchTabsCount()
        
        toastNotification({
            type: constantOptions.TOAST_TYPE.REQUEST_MOVED,
            message: 'This request has now been completed and moved to your history tab.',
        })

        setLoading(false)
        setModal()
    }

    return (
        <div className="flex flex-col w-full px-md">
            {showParticipantSelector ? (
                <>
                    <>
                        <div className='text-24 font-medium flex-center mb-md'>Confirm No. Of Players</div>
                        <div className="w-full text-center text-lg mb-lg">Please re-confirm the number of players who played against this request.</div>
                        <div className='flex justify-evenly'>
                            {constantOptions?.SELECT_PARTICIPANTS.map((t) => {
                                return (<div
                                    onClick={() => setParticipantCount(t)}
                                    key={t} className={`w-[100px] h-[42px] text-16 flex-center cursor-pointer border-2 ${participantCount === t ? "border-darkteal text-darkteal bg-tealTierBg" : "border-lightestgray"} hover:border-darkteal hover:text-darkteal hover:bg-tealTierBg`}>{t}</div>)
                            })}
                        </div>
                    </>

                    <div className="flex justify-center mt-xl px-lg w-full">
                        <CustomButton
                            text='Cancel'
                            width={197}
                            height={45}
                            onClick={() => {
                                setModal()
                            }}
                            color='lightestgray'
                            textColor='black'
                        />
                        <CustomButton
                            text='Submit'
                            width={197}
                            height={45}
                            onClick={() => {
                                updateParticipants()
                            }}
                        />
                    </div>
                </>
            ) : (
                <>
                    <>
                        <div className='text-24 font-medium flex-center mb-md'>Rate the Experience</div>
                        <div className="w-full text-center text-grayLight text-16 mb-lg">
                            Was the game experience satisfactory?
                        </div>
                        <div className='flex justify-evenly'>
                            <div>
                                <ThumbsUp isActive={satisfied === true} onClick={() => setSatisfied(true)} />
                                <div className='text-16 flex-center pt-sm'>Yes</div>
                            </div>
                            <div>
                                <ThumbsDown isActive={satisfied === false} onClick={() => setSatisfied(false)} />
                                <div className='text-16 flex-center pt-sm'>No</div>
                            </div>
                        </div>

                        {(!satisfied && satisfied !== undefined) && (
                            <>
                                <div className='flex-center py-md'>Please let us know why you are not satisfied with the game</div>
                                <TextArea
                                    title={'Add Reason'}
                                    value={reason}
                                    update={(value) => {
                                        setReason(value);
                                        setReasonError(!value?.trim());
                                    }}
                                    placeholder="Please elaborate reason for the dissatisfaction"
                                    error={reasonError}
                                    disableError={true}
                                />
                            </>
                        )}
                    </>

                    <div className="flex justify-center mt-xl w-full">
                        <CustomButton
                            text='Cancel'
                            width={"100%"}
                            height={45}
                            onClick={() => {
                                setModal()
                            }}
                            color='lightestgray'
                            textColor='black'
                        />
                        <CustomButton
                            loading={loading}
                            text={modal?.request?.requestor_completed ? 'Submit' : "Continue"}
                            width={"100%"}
                            height={45}
                            disabled={!satisfied && !reason?.trim()}
                            onClick={() => {
                                if (!satisfied && !reason?.trim()) {
                                    setReasonError(true);
                                    return;
                                }
                                if (modal?.request?.requestor_completed) {
                                    submitReview()
                                } else {
                                    setShowParticipantSelector(true)
                                }
                            }}
                        />
                    </div>
                </>
            )}
        </div>
    )
}
