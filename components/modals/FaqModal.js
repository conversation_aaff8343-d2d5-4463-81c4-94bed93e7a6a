import React from 'react'
import XIcon from '../icons/RequestV2/XIcon'

export default function FaqModal({ modal, setModal }) {

    const { activeRequestTab, REQUEST_FAQS } = modal

    return (
        <div className="w-full relative px-sm">
            <div
                onClick={() => setModal()}
                className="cursor-pointer absolute"
                style={{ right: 12, top: -10 }}>
                <XIcon color="#333333" />
            </div>
            <div className="text-20 font-medium mb-sm">
                FAQ
            </div>

            <div className="flex flex-col">
                {REQUEST_FAQS[activeRequestTab?.key]?.map((faq, i) => {
                    if (
                        activeRequestTab?.key ===
                        'accepted'
                    ) {
                        return (
                            <div
                                key={i}
                                className={`flex text-14 px-md py-sm bg-lightestgray mb-md rounded-lg`}>
                                <div
                                    className={`h-[5px] w-[5px] min-h-[5px] min-w-[5px] rounded-full bg-${i > 1 ? 'darkteal' : 'black'} mt-sm`}></div>
                                <div className="pl-sm">
                                    {i >
                                        1 && (
                                            <span className="text-darkteal">
                                                {
                                                    faq.player
                                                }{' '}
                                                :{' '}
                                            </span>
                                        )}
                                    {
                                        faq.faq
                                    }
                                </div>
                            </div>
                        )
                    } else {
                        return (
                            <div
                                key={i}
                                className="flex text-14 px-md py-sm bg-lightestgray mb-md rounded-lg">
                                <div className="h-[5px] w-[5px] min-h-[5px] min-w-[5px] rounded-full bg-black mt-sm"></div>
                                <span className="pl-sm">
                                    {
                                        faq
                                    }
                                </span>
                            </div>
                        )
                    }
                })}
            </div>

        </div>
    )
}