import React, { useContext } from 'react'
import useDropdown from '../../../../hooks/useDropdown'
import { ModalContext } from '../../../../context/ModalContext'
import { UPDATE_USER } from '../../../../graphql/mutations/user'
import { sendMailTemplate } from '../../../../utils/mailchimp'

export default function UserActivation({ user, refetchUsers, client }) {
    const { setModal } = useContext(ModalContext)
    const status = user.account_activated
        ? 'active'
        : user.deactivated
            ? 'deactivated'
            : user.activate_later
                ? 'activate later'
                : user.declined
                    ? 'declined'
                    : 'pending'

    const { dropdownVisible, setDropdownVisible } = useDropdown()

    const deactivateUser = () => {
        if (client) {
            client.request(UPDATE_USER, {
                user_id: user.id,
                user: {
                    deactivated: true,
                    activate_later: false,
                    declined: null,
                    account_activated: false,
                    declination_date: null,
                    activate_later_date: null
                },
            })
                .then(() => {
                    refetchUsers()
                    sendMailTemplate({
                        email: user.email,
                        template_name: 'Account Deactivated',
                        template_content: [
                            {
                                name: 'first_name',
                                content: user.first_name,
                            }
                        ],
                        subject: 'Account Deactivated',
                    })
                })
        }
    }

    return (
        <div
            onClick={(e) => e.stopPropagation()}
            className="relative flex"
            style={{ maxHeight: 104 }}>
            <div>
                <div
                    onClick={() => setDropdownVisible(true)}
                    className={`${status === 'active'
                        ? 'text-black'
                        : status === 'declined' || status === 'deactivated'
                            ? 'text-red'
                            : 'text-gray'
                        } capitalize flex items-center cursor-pointer`}>
                    {status}
                    <img
                        style={{ top: 2 }}
                        src="/svg/arrow-down.svg"
                        width={10}
                        className="ml-sm relative"
                    />
                </div>
            </div>
            {dropdownVisible && (
                <div
                    className="flex flex-col absolute bg-white rounded shadow z-50"
                    style={{ width: 150 }}>
                    {user.account_activated ? (
                        <div
                            onClick={() => deactivateUser()}
                            className="py-sm px-md text-xs text-gray uppercase cursor-pointer hover:underline">
                            Deactivate
                        </div>
                    ) : (
                        <>
                            <div
                                onClick={() => {
                                    setDropdownVisible(false)
                                    setModal({
                                        title: 'Activate User',
                                        type: 'activate-user',
                                        reactivation: status === 'deactivated',
                                        user,
                                        refetchUsers,
                                    })
                                }}
                                className="py-sm px-md text-xs uppercase text-darkteal cursor-pointer hover:underline">
                                {status === 'deactivated'
                                    ? 'Reactivate'
                                    : 'Activate'}
                            </div>
                            {status !== 'deactivated' && (
                                <>
                                    {
                                        !['activate later', 'declined'].includes(status) && (
                                            <div
                                                onClick={() => {
                                                    setDropdownVisible(false)
                                                    setModal({
                                                        title: 'Activate Later User',
                                                        type: 'activate-user',
                                                        reactivation: status === 'deactivated',
                                                        user,
                                                        refetchUsers,
                                                        activateLater: true
                                                    })
                                                }}
                                                className="py-sm px-md text-xs uppercase text-gray cursor-pointer hover:underline">
                                                Activate Later
                                            </div>
                                        )
                                    }

                                    {
                                        status !== 'declined' && (
                                            <div
                                                onClick={() => {
                                                    setDropdownVisible(false)
                                                    setModal({
                                                        title: 'Decline User',
                                                        type: 'decline-user',
                                                        user,
                                                        refetchUsers,
                                                        width: 457
                                                    })
                                                }}
                                                className="py-sm px-md text-xs uppercase text-gray cursor-pointer hover:underline">
                                                Decline
                                            </div>
                                        )
                                    }
                                </>
                            )}
                        </>
                    )}
                </div>
            )}
        </div>
    )
}
