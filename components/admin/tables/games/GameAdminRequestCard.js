import React, { useContext, useState } from 'react'
import Moment from 'moment'
import { ModalContext } from '../../../../context/ModalContext'
import OptionsButton from '../../../chat-v2/common/OptionsButton'
import DotsIcon from '../../../icons/DotsIcon'

export default function GameAdminRequestCard({ request, isPnAdmin, setRefreshGames }) {
    const { setModal } = useContext(ModalContext)


    const isCompletable = request?.cancelled_by === 'system' || request?.cancelled_by === 'requester' || request?.game_status === 'declined' || request?.hosts_declined?.length === request?.hosts_sent?.length || (request?.status === 'cancelled' && request?.cancelled_by === null)

    const printColors = (request) => {
        if (request?.host_completed === null && request?.requestor_completed === null && request?.status === 'created' &&
            request?.hosts_declined?.length !== request?.hosts_sent?.length) {
            return 'created';
        } else if ((request?.cancelled_by === 'system' || request?.hosts_declined?.length === request?.hosts_sent?.length)) {
            return 'cancelled(S)';
        } else if (((request?.cancelled_by === 'requester') || (request?.status === 'cancelled' && request?.cancelled_by === null))) {
            return 'cancelled(R)';
        } else if (request?.game_status === 'declined') {
            return 'cancelled(H)';
        } else if (request?.host_completed === false && request?.requestor_completed === false && request?.status !== 'cancelled' && request?.game_status !== 'declined') {
            return 'accepted';
        } else if ((request?.host_completed === true || request?.requestor_completed === true) && request?.status !== 'cancelled') {
            return 'completed';
        } else {
            return 'NA';
        }
    }

    const showOptions = () => {
        let options = {}
        if (isCompletable) {
            options = {
                ...options,
                'COMPLETE GAME': () => {
                    setModal({
                        type: 'admin-complete-request',
                        isCompletable,
                        request,
                        setRefreshGames: setRefreshGames,
                    })
                }
            }

        }
        if (((request?.host_completed === true || request?.requestor_completed === true) && request?.status !== 'cancelled')) {
            options = {
                ...options,
                DELETE: () => {
                    setModal({
                        title: 'Delete Request',
                        width: 475,
                        type: 'delete-request',
                        request: { ...request, request_id: request?.request_id || request?.id },
                        fetchRequests: setRefreshGames,
                    })
                },
                'REVERT GAME': () => {
                    setModal({
                        title: 'Revert Game',
                        width: 475,
                        type: 'revert-game',
                        request: { ...request, request_id: request?.request_id || request?.id },
                        setRefreshGames: setRefreshGames,
                    })
                },
                'UPDATE PLAYERS': () => {
                    setModal({
                        width: 451,
                        type: 'number-of-players',
                        request: request,
                    })
                }

            }
        } else {
            options = {
                ...options,
                DELETE: () => {
                    setModal({
                        title: 'Delete Request',
                        width: 475,
                        type: 'delete-request',
                        request: { ...request, request_id: request?.request_id || request?.id },
                        fetchRequests: setRefreshGames,
                    })
                }
            }
        }

        return (
            <OptionsButton
                options={options}
                IconComponent={DotsIcon}
            />
        )
    }

    return (
        <>
            <tr
                className={`bg-white text-sm mb-md rounded-lg cursor-pointer hover:shadow`}
                style={{
                    height: 90,
                    transition: 'opacity 0.2s ease-in-out',
                }}>
                {/* <td className="rounded-l-lg pl-md">Request</td> */}
                <td className="pl-md rounded-l-lg">#{request.game_id}</td>
                <td
                    className={`pl-md capitalize ${printColors(request) === 'cancelled(H)' || printColors(request) === 'cancelled(R)' || printColors(request) === 'cancelled(S)'
                        ? 'text-red'
                        : printColors(request) === 'created'
                            ? 'text-orange-500'
                            : 'text-darkteal'
                        }`}>
                    {/*status*/ printColors(request)}
                </td>
                {request.game_date === null ?
                    <td className="pl-md">
                        {Moment(request.start_date).format('MM/DD/YYYY')} -{' '}
                        {Moment(request.end_date).format('MM/DD/YYYY')}
                    </td>
                    :
                    <td className="pl-md">
                        {Moment(request.game_date).format('MM/DD/YYYY')}
                    </td>
                }

                <td className="pl-md">{request.club_name}</td>
                <td className="pl-md">{request.number_of_players}</td>
                <td className="pl-md">
                    {request.requestor_first_name} {request.requestor_last_name}
                </td>
                <td>
                    {request?.criteria?.accompanied_only && (
                        <div className="flex-center">
                            <img
                                src="/svg/check-teal.svg"
                                style={{ height: 15, width: 15 }}
                            />
                        </div>
                    )}
                </td>
                <td className="pl-md">
                    {request?.criteria?.sendToPrivateNetwork && (
                        <div className="flex-center">
                            <img
                                src="/svg/check-teal.svg"
                                style={{ height: 15, width: 15 }}
                            />
                        </div>
                    )}
                </td>
                {!isPnAdmin && (
                    <td className="rounded-r-lg">
                        <div className="flex">
                            {showOptions()}
                        </div>
                    </td>
                )}
            </tr>
            <tr>
                <td>
                    <div className="h-3" />
                </td>
            </tr>
        </>
    )
}