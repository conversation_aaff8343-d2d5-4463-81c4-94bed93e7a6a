import React, { useState, useEffect, useContext } from 'react'
import { useRouter } from 'next/router'
import TextInput from '../common/TextInput'
import Select from '../common/Select'
import DateRange from '../common/DateRange'
import TextArea from '../common/TextArea'
import useClient from '../../graphql/useClient'
import { UserContext } from '../../pages/_app'
import Moment from 'moment'
import MultiSelect from '../common/MultiSelect2'
import calculateGuestTimeRestrictionDates from '../../utils/clubs/calculateGuestTimeRestrictionDates'
import constantOptions from '../../constants/constantOptions'
import { FETCH_LOACAL_CLUB_DURATION, FETCH_INTERNATIONAL_CLUB_DURATION } from '../../graphql/queries/system-settings'
import Checkbox from '../common/Checkbox2'
import Checkbox2 from '../chat-v2/CheckBox'
import useCheckDeviceScreen from '../../hooks/useCheckDeviceScreen'
import CustomButton from '../buttons/CustomButton'
import ENDPOINTS from "../../constants/endpoints.json"
import toastNotification from '../../utils/notifications/toastNotification'
import MESSAGES from '../../constants/messages'
import filterEligibleHost from "../../utils/requests/helper/filterEligibleHost"
import Counter from '../common/Counter'
import useLastRequestNote from '../../hooks/useLastRequestNote'
import capitalize from '../../utils/helper/capitalize'
import RequestBlockedIcon from '../icons/RequestBlockedIcon'
import { DrawerContext } from '../../context/DrawerContext'
import { ModalContext } from '../../context/ModalContext'

const {
    MALE, FEMALE, BOTH
} = constantOptions.GENDER
const {
    NGV_FAQ
} = constantOptions?.DRAWER_TYPE

function getFormDefaults(user, editRequestData) {
    if (editRequestData) {
        const {
            club,
            club_id,
            number_of_players,
            message,
            start_date,
            end_date,
        } = editRequestData
        // Get the club data from the view, filter out the user_array based on the 
        // hosts_sent and set it as the value of club in the below line
        // 
        return {
            club: {
                id: club_id,
                name: club.name,
                member_count: club.member_count,
                closurePeriods: club.closurePeriods,
                guest_time_restrictions: club?.guest_time_restrictions,
                user_array: []
            },
            number_of_players,
            message,
            date_range: { from: start_date, to: end_date },
            gender: editRequestData.criteria.gender
                ? editRequestData.criteria.gender
                : BOTH,
            handicap: Array.isArray(editRequestData.criteria.handicap)
                ? editRequestData.criteria.handicap
                : !editRequestData.criteria.handicap ||
                    editRequestData.criteria.handicap === 'all'
                    ? ['< 5', '5-10', '> 10']
                    : [editRequestData.criteria.handicap.toLowerCase()],
            englishFluency: Array.isArray(editRequestData.criteria.englishFluency)
                ? editRequestData.criteria.englishFluency
                : !editRequestData.criteria.englishFluency ||
                    editRequestData.criteria.englishFluency === 'all'
                    ? ['native', 'fluent', 'understandable', 'basic']
                    : [editRequestData.criteria.englishFluency.toLowerCase()],
            all_ages: editRequestData.criteria.all_ages === false ? false : true,
            ...editRequestData.criteria,
        }
    } else {
        return {
            gender: BOTH,
            handicap: ['< 5', '5-10', '> 10'],
            englishFluency: ['native', 'fluent', 'understandable', 'basic'],
            number_of_players: 1,
            all_ages: true,
            accompanied_only: true,
            playAsCouple: false,
        }
    }
}

export const CreateRequest = ({
    globalDropdownState,
    scrollToBottom,
    editRequestData,
    setRefresh = () => { },
    fetchTabsCount = () => { },
    setEditRequestData = () => { },
    setActiveTab = () => { },
    clubsRecommended,
    setClubsRecommended = () => { }
}) => {
    const [loading, setLoading] = useState(false)
    const { user, token, clevertap, clubForRequest, setClubForRequest } = useContext(UserContext)
    const client = useClient()
    const [requestForm, setRequestForm] = useState(getFormDefaults(user, editRequestData))
    const [errors, setErrors] = useState({})
    const [showErrors, setShowErrors] = useState(false)
    const [clubSearchValue, setClubSearchValue] = useState()
    const requestOptions = {
        gender: user.gender === MALE ? [BOTH, MALE] : [FEMALE, BOTH],
        handicap: ['< 5', '5-10', '> 10'],
        englishFluency: ['basic', 'understandable', 'native', 'fluent'],
    }
    const [closurePeriods, setClosurePeriods] = useState([])
    const [guestTimeRestrictions, setGuestTimeRestrictions] = useState({})
    const [disabledDates, setDisabledDates] = useState([])
    const [privateNetworkClubIds, setPrivateNetworkClubIds] = useState()
    const [playingAsCouple, setPlayingAsCouple] = useState(false);
    const [hosts, setHosts] = useState([]);
    const [calendarDisabled, setCalendarDisabled] = useState(false)
    const [requestDuration, setRequestDuration] = useState()
    const [filterLabels, setFilterLabels] = useState([
        'Gender',
        'Golf Index',
        'English Fluency',
        'All Ages',
        'Age Range'
    ]);
    const { isMobile } = useCheckDeviceScreen()
    const [showAdvanceFilters, setShowAdvanceFilters] = useState(true)
    const [showRequestTo, setShowRequestTo] = useState(true)
    const [allTg, setAllTg] = useState(!editRequestData ? true : editRequestData?.request_for_all)
    const { lastRequestNote = '' } = useLastRequestNote()
    const router = useRouter()
    const { setDrawer } = useContext(DrawerContext)
    const { modal, setModal } = useContext(ModalContext)


    useEffect(() => {
        if (client) {
            if (user?.phone_number_details?.countryCode?.toLowerCase() !== (requestForm?.club?.country_code)) {
                const duration = client.request(FETCH_INTERNATIONAL_CLUB_DURATION)
                    .then((data) => setRequestDuration(data?.system_setting[0]?.value?.value))

            } else {
                const duration = client.request(FETCH_LOACAL_CLUB_DURATION)
                    .then((data) => setRequestDuration(data?.system_setting[0]?.value?.value))
            }
        }
    }, [user, client, requestForm])

    function validateRequest() {

        let newErrors = {}

        if (!requestForm.club && !clubForRequest && !editRequestData) {
            newErrors.club = 'Club is required'
        }

        if (!requestForm.date_range) {
            newErrors.date_range = 'A date range is required'
        }

        // ** Commenting as on 26th Oct 2021 ** Not restricting the user for dates
        // if (disabledDates.length > 0 && requestForm.date_range) {
        //     const disabledDateArr = disabledDates.map((date) =>
        //         Moment(date).format('MM/DD/YYYY')
        //     )
        //     const { from, to } = requestForm.date_range
        //     const startDate = Moment(from)
        //     const endDate = Moment(to)
        //     const nDaysBetween = endDate.diff(startDate, 'd')
        //     new Array(nDaysBetween).fill('').map((_, index) => {
        //         const dateRangeDate = Moment(from)
        //             .add(index, 'days')
        //             .format('MM/DD/YYYY')
        //         if (disabledDateArr.includes(dateRangeDate)) {
        //             newErrors.date_range =
        //                 "The date range selected is within one of the club's closure/guest restriction periods. Please choose another date range."
        //         }
        //     })
        // }

        if (
            !requestForm.all_ages &&
            ((!requestForm.min_age || !requestForm.max_age) ||
                (!(/^\d+$/.test(requestForm.min_age)) || !(/^\d+$/.test(requestForm.max_age))))
        ) {
            newErrors.age_range = 'A valid age range is required'
        }

        if (!requestForm.message) {
            newErrors.message = 'A valid message is required'
        }

        setErrors(newErrors)
        return Object.keys(newErrors).length === 0
    }

    useEffect(() => {
        if (showErrors) {
            validateRequest()
        }
    }, [requestForm, showErrors])

    useEffect(() => {
        if (clubForRequest) {
            setRequestForm({ ...requestForm, club: clubForRequest })
        }
    }, [clubForRequest])

    useEffect(() => {
        if (requestForm?.club && !editRequestData) {
            const members = filterEligibleHost({
                filters: {
                    gender: requestForm?.gender,
                    handicap: requestForm?.handicap,
                    englishFluency: requestForm?.englishFluency,
                    all_ages: requestForm?.all_ages,
                    min_age: requestForm?.min_age,
                    max_age: requestForm?.max_age,
                    playAsCouple: requestForm?.playAsCouple,
                    requestForAll: allTg
                },
                userArray: requestForm?.club?.user_array,
            });

            setHosts(members);
            setRequestForm({
                ...requestForm,
                club: { ...requestForm?.club, member_count: members.length },
            })
        }
    }, [requestForm?.club?.id,
    requestForm?.gender,
    requestForm?.handicap,
    requestForm?.englishFluency,
    requestForm?.min_age,
    requestForm?.max_age,
    requestForm?.playAsCouple,
        allTg
    ])

    useEffect(() => {
        if (requestForm?.club) {
            const { club } = requestForm

            if (club?.closure_period && club?.closure_period !== closurePeriods) {
                setClosurePeriods(requestForm.club?.closure_period)
            } else if (club?.closurePeriods && club?.closurePeriods !== closurePeriods) {
                setClosurePeriods(club?.closurePeriods)
            }

            if (club?.guest_time_restrictions !== guestTimeRestrictions) {
                setGuestTimeRestrictions(club?.guest_time_restrictions)
            }
        }

        setPlayingAsCouple(requestForm?.playAsCouple);
        if (requestForm?.playAsCouple && requestForm?.number_of_players !== 2) {
            setRequestForm({
                ...requestForm,
                number_of_players: 2,
            })
        }

    }, [requestForm])

    useEffect(() => {
        let allDisabledDates = [];
        if (closurePeriods && closurePeriods?.length > 0) {
            const closurePeriodDates = closurePeriods
                .map(({ from, to }) => {
                    const startDate = Moment(from).add(-1, 'days');
                    const endDate = Moment(to);
                    const nDaysBetween = endDate.diff(startDate, 'd')
                    const dates = new Array(nDaysBetween)
                        .fill('')
                        .map((_, index) => {
                            return Moment(from).add(index, 'days').toDate()
                        })
                    return dates
                })
                .flat()
            allDisabledDates = [...closurePeriodDates];
        }

        // guestTimeRestrictions is actually guest time availability - the verbiage for it has been changed in recent past (23 Aug 2021)
        if (guestTimeRestrictions && typeof guestTimeRestrictions === 'object') {
            let guestTimeRestrictionDates = calculateGuestTimeRestrictionDates(guestTimeRestrictions, constantOptions?.REQUEST_CREATION_ALLOWANCE_MONTHS?.DIFFERENT_COUNTRY);
            allDisabledDates = [...allDisabledDates, ...guestTimeRestrictionDates];
        }

        setDisabledDates(allDisabledDates)

    }, [closurePeriods, guestTimeRestrictions])

    useEffect(() => {
        if (client && user.private_network) {
            const private_network_id = user?.private_network.id
            client
                .request(
                    `
                    query getPrivateNetworkClubs ($private_network_id: Int!) {
                        user_club(where: {user: {private_network_user: {private_network_id: {_eq: $private_network_id}}}}) {
                            club_id
                        }
                    }
                `,
                    { private_network_id }
                )
                .then(({ user_club }) =>
                    user_club.reduce((clubs, { club_id }) => {
                        return clubs.includes(club_id)
                            ? clubs
                            : [...clubs, club_id]
                    }, [])
                )
                .then(setPrivateNetworkClubIds)
        }
    }, [client])

    useEffect(() => {
        //This condition clears requesForm?.club when user hits delete after selecting a club
        if (!clubForRequest && !editRequestData && requestForm?.club && clubSearchValue !== requestForm?.club?.name) {
            setRequestForm({
                ...requestForm,
                club: null,
            })
            setClubSearchValue(requestForm?.club?.name);
        }
    }, [clubSearchValue])

    useEffect(() => {
        let newLabels = [];
        if (!filterLabels.includes('Members who Play as Couple') && user?.playAsCouple) {
            newLabels.push("Members who Play as Couple")
        }
        if (!filterLabels.includes('Private Network Only') && ((user?.private_network && user?.visibleToPublic) || editRequestData?.criteria?.sendToPrivateNetwork)) {
            newLabels.push("Private Network Only")
        }
        setFilterLabels([...filterLabels, ...newLabels])
    }, [user])

    useEffect(() => {
        if (clubSearchValue || clubForRequest || editRequestData?.club?.name || clubForRequest?.name) {
            setCalendarDisabled(false)
        } else {
            setCalendarDisabled(true)
        }
    }, [clubSearchValue, clubForRequest])

    useEffect(() => {
        if (hosts?.length < 1 && requestForm?.club && requestForm?.club !== undefined && !editRequestData) {
            toastNotification({
                type: constantOptions.TOAST_TYPE.ERROR,
                message: MESSAGES?.REQUEST?.NO_QUALIFIED_HOSTS
            })
        }
    }, [hosts])

    const fetchRecommendedClubs = async () => {
        setLoading(true)
        try {
            await fetch(ENDPOINTS?.GET_RECOMMENDED_CLUBS, {
                method: "POST",
                headers: {
                    'Content-Type': 'application/json',
                    ['Authorization']: `Bearer ${token}`,
                },
                body: JSON.stringify({
                    userId: user?.id,
                    clubId: requestForm?.club?.id
                })
            })
                .then((response) => response.json())
                .then(async (data) => {
                    if (!data?.status) {
                        toastNotification({
                            type: constantOptions.TOAST_TYPE.ERROR,
                            message: data?.message,
                        })
                    } else {
                        if (data?.data?.length) {
                            setModal({ type: 'recommendations', setClubsRecommended, recommendedClubs: data?.data, setClubForRequest, setActiveTab, width: 540, recommendedAgainstClub: requestForm?.club?.name })
                        }
                        window.history.go(-1)
                    }
                })
            setLoading(false)
        } catch (error) {
            console.log(error);
        }
    }

    const handleRequest = async () => {
        setShowErrors(true)
        try {
            if (validateRequest()) {
                const {
                    number_of_players,
                    message,
                    date_range: { from: start_date, to: end_date },
                    ...criteria
                } = requestForm
                const club_id = requestForm?.club?.id

                let URL = !editRequestData ? ENDPOINTS?.CREATE_REQUEST : ENDPOINTS?.EDIT_REQUEST

                let body = {
                    userId: user?.id,
                    players: number_of_players,
                    message,
                    startDate: start_date,
                    endDate: end_date,
                    requestId: editRequestData?.id,
                }
                if (!editRequestData) {
                    body = {
                        ...body, criteria, clubId: club_id, requestForAll: allTg, isForAllFriends: false
                    }
                }

                setLoading(true)
                await fetch(URL, {
                    method: "POST",
                    headers: {
                        'Content-Type': 'application/json',
                        ['Authorization']: `Bearer ${token}`,
                    },
                    body: JSON.stringify(body)
                })
                    .then((response) => response.json())
                    .then(async (data) => {
                        if (!data?.status) {
                            toastNotification({
                                type: constantOptions.TOAST_TYPE.ERROR,
                                message: data?.message,
                            })
                        } else {
                            setRefresh(true)
                            fetchTabsCount()
                            setLoading(false)
                            if (!clubsRecommended) {
                                fetchRecommendedClubs()
                            } else {
                                window.history.go(-1)

                            }
                            if (!editRequestData) {
                                toastNotification({
                                    type: constantOptions.TOAST_TYPE.REQUEST_MOVED,
                                    message: 'Request created successfully',
                                })
                            }
                        }
                    })
            }

        } catch (error) {
            console.log("Create Request ------>", error);
        }
    }

    useEffect(() => {
        if (requestForm?.club) {
            setClubSearchValue(requestForm?.club?.name)
        }
    }, [requestForm?.club])

    return (
        <div className='flex flex-col w-full md:px-xxl  lg:px-160 py-xl '>
            {isMobile ? (
                <div className='flex justify-between items-center mb-md px-xs'>
                    <div className='text-18 flex font-normal cursor-pointer'>
                        <img
                            onClick={() => {
                                window.history.go(-1)
                                setEditRequestData()
                                setClubForRequest()
                            }}
                            className='mr-sm'
                            src='/svg/BackArrowNew.svg' />
                        {editRequestData ? "Edit Request" : "Create New Request"}
                    </div>
                    <div
                        onClick={() => setDrawer({
                            type: NGV_FAQ,
                            global: true
                        })}
                        className='text-darkteal font-medium underline cursor-pointer'>Check NGV FAQ</div>
                </div>
            ) : (
                <div className='flex justify-between items-center mb-md'>
                    <div className='text-24 font-normal flex cursor-pointer'>
                        <img
                            onClick={() => {
                                window.history.go(-1)
                                setEditRequestData()
                                setClubForRequest()
                            }}
                            style={{ marginRight: 10 }} src='/svg/BackArrowNew.svg' />
                        {editRequestData ? "Edit Request" : "Create New Request"}
                    </div>
                    <div
                        onClick={() => setDrawer({
                            type: NGV_FAQ,
                            global: true
                        })}
                        className='text-16 text-darkteal font-medium underline cursor-pointer'>Check NGV FAQ</div>
                </div>
            )}

            <div className={`flex flex-col md:flex-row justify-between`}>
                <div className='flex-1 md:mr-md bg-white pt-md'>
                    <div className='text-16 bg-lightestgray py-sm pl-md'>Basic Details</div>
                    <div className='px-md'>
                        {(editRequestData || (clubForRequest?.id && ["clubs", "create-request"]?.includes(router?.query?.type))) ? (
                            <div className="border-b border-lightgray py-sm mb-md">
                                <div className='text-12 md:text-14 text-grayLight font-normal pb-sm'>Select Golf Clubs</div>
                                <div className='text-14 text-grayLight cursor-not-allowed md:text-16 font-normal'>
                                    {editRequestData
                                        ? editRequestData?.club?.name
                                        : clubForRequest?.name}
                                </div>
                            </div>
                        ) : (
                            <div
                                className="z-50 relative py-md"
                                style={{ zIndex: 1002 }}>
                                <Select
                                    isClubSelected={requestForm?.club}
                                    updateSearch={setClubSearchValue}
                                    privateNetworkClubIds={privateNetworkClubIds}
                                    value={(!editRequestData && !requestForm?.club) ? clubSearchValue : requestForm?.club?.name}
                                    update={(value) => {
                                        setRequestForm({
                                            ...requestForm,
                                            club: value,
                                        })
                                    }}
                                    className={isMobile ? 'text-14' : 'text-16'}
                                    addClubClasses={isMobile ? false : 'font-normal'}
                                    type="request-clubs"
                                    filterTier={user.tier}
                                    disableError={!errors.club}
                                    exclude={user?.clubs?.map(({ id }) => id)}
                                    error={errors.club}
                                    title={'Select Golf Clubs'}
                                    placeholder="Select"
                                    user={user}
                                    titleWeight='normal'
                                    fontWeignt='normal'
                                />
                            </div>
                        )
                        }

                        {![null, undefined].includes(requestForm?.club?.requestRestriction?.errorMessage) ? <div className={`rounded-lg flex items-start text-12 text-gray ${requestForm?.club?.requestRestriction?.isBlocked ? "bg-lightRed" : "bg-lightYellow"} py-sm px-md mb-sm`}>
                            <RequestBlockedIcon isBlocked={requestForm?.club?.requestRestriction?.isBlocked} /> {requestForm?.club?.requestRestriction?.errorMessage}
                        </div> : null}

                        {(requestForm?.club || clubForRequest) && !editRequestData && !requestForm?.club?.requestRestriction?.isBlocked ? (
                            <>
                                <div className="font-thin text-sm text-gray">
                                    {requestForm?.club?.name}:{' '}
                                    <span className={`text-${hosts.length < 1 ? 'red' : 'darkteal'} font-normal`}>
                                        {hosts.length} qualified {hosts.length > 1 ? "members" : "member"}
                                    </span>
                                </div>
                                <div className="font-thin mb-sm text-sm text-gray">
                                    Accompanied Guest Fee: {' '}
                                    <span className='text-darkteal font-normal'>
                                        {requestForm?.club?.guest_fee || 'NA'}
                                    </span>
                                </div>
                            </>
                        ) : null}
                        <div className="">
                            <DateRange
                                title='Date'
                                placeholder={'Select Date'}
                                singular
                                error={errors.date_range}
                                value={requestForm?.date_range}
                                disabledDates={disabledDates}
                                minDate={editRequestData?.offer_details?.start_date ? Moment.utc(editRequestData.offer_details.start_date).format('YYYY-MM-DD') : undefined}
                                maxDate={editRequestData?.offer_details?.start_date ? Moment.utc(editRequestData.offer_details.end_date).format('YYYY-MM-DD') : undefined}
                                maxDateMonth={requestDuration}
                                setParentErrors={() => {
                                    (clubSearchValue || clubForRequest?.name || editRequestData?.club?.name) ? setErrors({ ...errors, date_range: "" }) : setErrors({ ...errors, date_range: 'To select a date range, please select a club first.' })
                                }}
                                update={(value) => {
                                    (clubSearchValue || clubForRequest?.name || editRequestData?.club_name) && setErrors({ ...errors, date_range: "" })
                                    setRequestForm({
                                        ...requestForm,
                                        date_range: value,
                                    })
                                }}
                                calendarDisabled={calendarDisabled}
                                showNote={true}
                                scrollToBottom={scrollToBottom}
                                textSize={isMobile ? '14' : '16'}
                                fontWeight='normal'
                                titleSize={isMobile ? '12' : '14'}
                                titleWeignt='normal'
                                customClass=""
                            />
                        </div>
                        <div className="mb-md relative ">
                            {
                                playingAsCouple
                                    ? (
                                        <>
                                            <div className="items-center">
                                                <p className={`${isMobile ? 'text-12' : 'text-14'} text-gray font-normal`}>Number of People <br /></p>
                                                <p className="mt-sm text-16 text-black font-normal"><strong>{requestForm.number_of_players}</strong></p>
                                            </div>
                                        </>
                                    )
                                    : (
                                        <Select
                                            globalDropdownState={globalDropdownState}
                                            disableError={true}
                                            title="Number of People"
                                            value={
                                                requestForm?.number_of_players
                                                    ? requestForm?.number_of_players
                                                    : 1
                                            }
                                            update={(value) => {

                                                if (playingAsCouple) {
                                                    value = 2;
                                                }

                                                setRequestForm({
                                                    ...requestForm,
                                                    number_of_players: value,
                                                })
                                            }}
                                            className={isMobile ? 'text-14' : 'text-16'}
                                            addClubClasses={isMobile ? false : 'font-normal'}
                                            titleWeight='normal'
                                            fontWeignt='normal'
                                            options={[1, 2, 3]}
                                        />
                                    )
                            }

                        </div>

                        <div className='mb-md relative'>
                            <TextArea
                                title="Enter Notes Here..."
                                minHeight={85}
                                maxLength={constantOptions.REQUEST_NOTES_MAX_LENGTH}
                                error={errors.message}
                                value={requestForm?.message}
                                update={(value) =>
                                    setRequestForm({
                                        ...requestForm,
                                        message: value,
                                    })
                                }
                                fontColor={'text-gray'}
                                className={''}
                                titleSize={isMobile ? '12' : '14'}
                                fontSize={isMobile ? '12' : '14'}
                                lineHeight='17px'
                                placeholder="Please type a courteous note introducing yourself and your request to potential hosts, a polite note improves acceptance chances"
                            />
                            {
                                lastRequestNote ? <div className='text-[14px] font-normal text-darkteal flex gap-1 cursor-pointer absolute top-0 right-0' onClick={() => {
                                    setRequestForm({
                                        ...requestForm,
                                        message: lastRequestNote,
                                    })
                                }}><img src="/svg/copy.svg" />Paste previous note</div> : ''
                            }

                            <div className='flex justify-end'>
                                <Counter limit={constantOptions.REQUEST_NOTES_MAX_LENGTH} count={requestForm?.message?.length} textSize={'text-10'} color={'text-grayLight'} />
                            </div>

                        </div>
                        <Checkbox2
                            value={
                                requestForm?.accompanied_only === undefined
                                    ? true
                                    : requestForm?.accompanied_only
                            }
                            update={(value) => {
                                if (!editRequestData) {
                                    setRequestForm({
                                        ...requestForm,
                                        accompanied_only: value,
                                    })
                                }

                            }}
                            isDisabled={editRequestData}
                            label="Accompanied Only?"
                            smallLabel="text-16"
                            roundedButton={true}
                        />
                        {
                            user?.playAsCouple ?
                                <div
                                    className="mt-sm mb-sm flex items-center"
                                    style={{ height: 21 }}>
                                    <Checkbox2
                                        label={"Play as couple"}
                                        value={requestForm?.playAsCouple}
                                        update={(value) => {
                                            if (!editRequestData) {
                                                setRequestForm({
                                                    ...requestForm,
                                                    playAsCouple: value
                                                })
                                            }
                                        }}
                                        marginBottom={0}
                                        smallLabel="text-16"
                                        roundedButton={true}
                                    />
                                </div>
                                : ''
                        }
                    </div>
                </div>
                <div className='flex-1 bg-white pt-md'>
                    <div className='text-16 bg-lightestgray py-sm pl-md flex justify-between pr-md'>
                        Advance Filters
                        <img
                            className='cursor-pointer'
                            onClick={() => {
                                setShowAdvanceFilters(!showAdvanceFilters)
                            }} src={`${!showAdvanceFilters ? '/svg/Collapse-Down.svg' : '/svg/Expand-Up.svg'}`} />
                    </div>
                    {showAdvanceFilters &&
                        <div className='px-md'>
                            <div className='pt-sm'>
                                <div className='text-grayLight font-normal text-12 md:text-14'>Age</div>
                                <div className='flex mt-sm'>
                                    <div className='' style={{ width: 120 }}>
                                        <Checkbox
                                            isDisabled={editRequestData}
                                            label="All"
                                            value={requestForm.all_ages}
                                            update={(value) => {
                                                if (!editRequestData) {
                                                    setRequestForm({
                                                        ...requestForm,
                                                        all_ages: value,
                                                    })
                                                }
                                            }
                                            }
                                            roundedButton={true}
                                        />
                                    </div>
                                    <Checkbox
                                        isDisabled={editRequestData}
                                        label="Custom Age Range"
                                        value={!requestForm?.all_ages}
                                        update={(value) => {
                                            if (!editRequestData) {
                                                setRequestForm({
                                                    ...requestForm,
                                                    all_ages: false,
                                                })
                                            }
                                        }
                                        }
                                        roundedButton={true}
                                    />
                                </div>
                                {
                                    !requestForm?.all_ages && (
                                        <div className="flex mb-md">
                                            <div className="pr-sm">
                                                <TextInput
                                                    value={requestForm?.min_age}
                                                    update={(value) => {
                                                        if (!editRequestData) {
                                                            setRequestForm({
                                                                ...requestForm,
                                                                min_age: Number(value),
                                                            })
                                                        }
                                                    }

                                                    }
                                                    error={
                                                        errors.age_range &&
                                                        !requestForm?.min_age
                                                    }
                                                    disableError={true}
                                                    placeholder="Min. Age"
                                                    addClubClasses={isMobile ? false : true}
                                                />
                                            </div>
                                            <div className="pl-sm">
                                                <TextInput
                                                    value={requestForm?.max_age}
                                                    update={(value) => {
                                                        if (!editRequestData) {
                                                            setRequestForm({
                                                                ...requestForm,
                                                                max_age: Number(value),
                                                            })
                                                        }
                                                    }
                                                    }
                                                    error={
                                                        errors.age_range &&
                                                        !requestForm?.max_age
                                                    }
                                                    disableError={true}
                                                    placeholder="Max. Age"
                                                    addClubClasses={isMobile ? false : true}
                                                />
                                            </div>
                                        </div>
                                    )
                                }
                            </div>
                            <div className='pt-sm'>
                                <div className='text-grayLight font-normal text-12 md:text-14'>Gender</div>
                                <div className='flex mt-sm'>
                                    <div style={{ width: 120 }}>
                                        <Checkbox
                                            isDisabled={editRequestData}
                                            label="Both"
                                            value={requestForm?.gender === BOTH}
                                            update={(value) => {
                                                if (!editRequestData) {
                                                    setRequestForm({
                                                        ...requestForm,
                                                        gender: BOTH,
                                                    })
                                                } else {
                                                    null
                                                }
                                            }
                                            }
                                            roundedButton={true}
                                        />
                                    </div>

                                    <Checkbox
                                        isDisabled={editRequestData}
                                        label={capitalize(user?.gender)}
                                        value={user?.gender?.toLowerCase() === requestForm?.gender?.toLowerCase()}
                                        update={(value) => {
                                            if (!editRequestData) {
                                                setRequestForm({
                                                    ...requestForm,
                                                    gender: user?.gender?.toLowerCase(),
                                                })
                                            } else {
                                                null
                                            }
                                        }
                                        }
                                        roundedButton={true}
                                    />



                                </div>
                            </div>
                            <div className='py-md'>
                                <MultiSelect
                                    title={'Golf Index'}
                                    isDisabled={editRequestData}
                                    value={requestForm?.handicap}
                                    update={(value) =>
                                        setRequestForm({
                                            ...requestForm,
                                            handicap: value,
                                        })
                                    }
                                    className={"font-normal"}
                                    textSize={isMobile ? '14' : ''}
                                    disableError={true}
                                    options={requestOptions?.handicap}
                                    globalDropdownState={
                                        globalDropdownState
                                    }
                                    titleSize={isMobile && "12"}
                                />
                            </div>
                            <div className='py-sm'>
                                <MultiSelect
                                    title={'English Fluency'}
                                    isDisabled={editRequestData}
                                    value={requestForm?.englishFluency}
                                    update={(value) =>
                                        setRequestForm({
                                            ...requestForm,
                                            englishFluency: value,
                                        })
                                    }
                                    textSize={isMobile ? '14' : ''}
                                    className="font-normal"
                                    disableError={true}
                                    options={requestOptions?.englishFluency}
                                    globalDropdownState={
                                        globalDropdownState
                                    }
                                    titleSize={isMobile && "12"}
                                />
                            </div>
                        </div>
                    }
                    <div className='text-16 bg-lightestgray py-sm pl-md flex justify-between pr-md mt-sm'>
                        <div>
                            Send Request to
                        </div>
                        <img className='cursor-pointer' onClick={() => {
                            setShowRequestTo(!showRequestTo)
                        }} src={`${!showRequestTo ? '/svg/Collapse-Down.svg' : '/svg/Expand-Up.svg'}`} />
                    </div>
                    {showRequestTo &&
                        <div className={`${isMobile && 'mt-md'} flex-1 px-md py-md`}>
                            <div className=''>
                                <Checkbox
                                    customClasses={'text-16'}
                                    label="My TG community only"
                                    isDisabled={true}
                                    value={true}
                                    onClick={() => {
                                    }}
                                    roundedButton={true}
                                />
                                <div className='text-14 text-gray pl-lg'>This is your mandatory visibility in the application. You will always be visible to your “My TG Community” (Friends and group members as per Group Settings)</div>
                            </div>

                            <div className='mt-sm'>
                                <Checkbox
                                    customClasses={'text-16'}
                                    label="All Thousand Greens Members"
                                    isDisabled={editRequestData}
                                    value={allTg}
                                    onClick={() => {
                                        if (!editRequestData) {
                                            setAllTg(!allTg)
                                        }
                                    }}
                                    roundedButton={true}
                                />
                                <div className='text-14 text-gray pl-lg'>
                                    If you uncheck this option, you will no longer be visible to any TG members who are not part of your "My TG Community”
                                </div>
                            </div>

                        </div>
                    }
                </div>
                <div>

                </div>
            </div>

            <div className={`flex justify-evenly md:justify-end py-md`}>
                <CustomButton
                    loading={loading}
                    onClick={() => {
                        window.history.go(-1)
                        setEditRequestData()
                        setClubForRequest()
                    }} color='lightestgray' borderColor='black' textColor='black' width={160} height={45} text='Cancel' />
                <CustomButton
                    disabled={(hosts?.length < 1 && requestForm?.club && requestForm?.club !== undefined && !editRequestData) || requestForm?.club?.requestRestriction?.isBlocked}
                    onClick={() => {
                        handleRequest()
                        if (!editRequestData) {
                            clevertap.event.push(constantOptions?.CLEVERTAP_EVENTS.REQUESTS_POST_REQUEST);
                        }
                    }}
                    width={160} height={45}
                    text={editRequestData ? "Save" : 'Post Request'}
                    loading={loading}
                />
            </div>
        </div>
    )
}