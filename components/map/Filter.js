import React, { use, useState, useContext, useEffect } from 'react'
import MAP_FILTER from '../../constants/filterConstants'
import Checkbox from '../chat-v2/CheckBox'
import Checkbox2 from '../common/Checkbox2'
import { ThreeDots } from 'react-loader-spinner'
import CheckboxMultiselect from '../common/CheckboxMultiselect'
import useMapFilters from '../../hooks/useMapFilters'
import constantOptions from '../../constants/constantOptions'
import useCheckDeviceScreen from '../../hooks/useCheckDeviceScreen'
import { UserContext } from '../../pages/_app'

const { CLUB_TIERS } = constantOptions

// Destructure the constants we need
const { TIER, TG_COMMUNITY, CLUB_PERCENTAGE_ACCEPTANCE, CLUB_MEMBER_COUNT } = MAP_FILTER

// Destructure nested values
const { FERN, SAGE, MOSS, OLIVE } = TIER
const { FRIEND_CONTACT, TG_GROUP_MEMBER, OFFER, FAVORITE, PLAYED, COUPLE, FEMALE } = TG_COMMUNITY
const { ALL, MORE_THAN_25 } = CLUB_PERCENTAGE_ACCEPTANCE
const { ALL_MEMBERS, MORE_THAN_10 } = CLUB_MEMBER_COUNT

export const Filter = ({
  loading,
  allMyTgGroups,
  totalGroupsCount,
  page,
  pageSize,
  setListLoading,
  listLoading,
  fetchGroupsList,
  setPage,
  setRefreshMap,
  filters,
  setFilters,
  setFilterApplied,
  setFilterActive,
  setCurrentView,
  filterCount,
  isFiltersApplied,
  currentView,
  view,
  groups,
  setGroups
}) => {
  const { user } = useContext(UserContext)
  const { isDesktop, isWideScreen, isMobile, isTablet } = useCheckDeviceScreen()

  useEffect(() => {
    const mapFilters = JSON.parse(window?.sessionStorage.getItem('MAP_FILTERS'))
    if (mapFilters?.channel) {
      setGroups([{ id: mapFilters?.id, name: mapFilters?.channel?.data?.name, image: mapFilters?.channel?.data?.image, stream_channel_id: mapFilters?.channel?.id }])
    }
    window?.sessionStorage.removeItem('MAP_FILTERS')
  }, [])

  const handleSelection = (group) => {
    const temp = groups?.map(group => group?.id)
    if (temp?.includes(group?.id)) {
      const updated = groups.filter((t) => t.id !== group?.id)
      if (updated?.length === 0) {
        setFilters({ ...filters, all_groups: true })
      }
      setGroups(updated)

    } else {
      const updated = [...groups, group]
      if (updated?.length === allMyTgGroups?.length) {
        setFilters({ ...filters, all_groups: true })
        setGroups([])
      } else {
        setFilters({ ...filters, all_groups: false })
        setGroups(updated)
      }
    }
  }

  const handleClearAll = () => {
    setCurrentView(view[1])
    setFilters({
      fern: false,
      sage: false,
      moss: false,
      olive: false,
      friend_contact: false,
      tg_group_member: false,
      offer: false,
      favorite: false,
      played: false,
      couple: false,
      acceptance_all: false,
      acceptance_25: false,
      member_count_all: false,
      member_count_10: false,
      all_groups: false
    })
  }

  return (
    <div className={`flex flex-col h-full w-full md:w-[31%] lg:w-[20%] pl-md pr-md pt-md md:pr-xs md:pl-md`}>
      <div className={`flex w-full justify-between px-sm md:px-0 pt-sm lg:pt-0 pb-sm`}>
        <div className="text-18 font-medium flex"><span onClick={() => {
          const view = JSON.parse(sessionStorage.getItem('MOBILE_VIEW'))?.currentView
          setCurrentView(view)
          sessionStorage.removeItem('MOBILE_VIEW')
        }} className='pt-sm mr-sm '>
          {(isMobile) &&
            <img width={12} src="/svg/CrossIconBlack.svg" />
          }
        </span>Filters</div>
        <div className='flex items-center '>
          {isFiltersApplied &&
            <div className="font-medium ml-sm cursor-pointer" onClick={handleClearAll}>Clear All</div>
          }
        </div>
      </div>

      <div className='flex flex-col h-full overflow-y-scroll'>
        <div className='text-16 bg-white rounded-lg py-12'>
          <div className='px-12'>Tier</div>
          <div className='flex flex-col '>
            <div className='flex justify-between px-12 items-center hover:bg-tealTierBg cursor-pointer ' onClick={() => { setFilters({ ...filters, fern: !filters?.fern }) }}> <Checkbox value={filters?.fern} update={() => { setFilters({ ...filters, fern: !filters?.fern }) }} label={FERN} padding='sm' /> <div className='text-12 text-gray'>{filterCount?.fern}</div></div>
            <div className='flex justify-between px-12 items-center hover:bg-tealTierBg cursor-pointer' onClick={() => { setFilters({ ...filters, sage: !filters?.sage }) }}> <Checkbox value={filters?.sage} update={() => { setFilters({ ...filters, sage: !filters?.sage }) }} label={SAGE} padding='sm' /> <div className='text-12 text-gray'>{filterCount?.sage}</div></div>
            <div className='flex justify-between px-12 items-center hover:bg-tealTierBg cursor-pointer ' onClick={() => { setFilters({ ...filters, moss: !filters?.moss }) }}> <Checkbox value={filters?.moss} update={() => { setFilters({ ...filters, moss: !filters?.moss }) }} label={MOSS} padding='sm' /> <div className='text-12 text-gray'>{filterCount?.moss}</div></div>
            <div className='flex justify-between px-12 items-center hover:bg-tealTierBg cursor-pointer' onClick={() => { setFilters({ ...filters, olive: !filters?.olive }) }}> <Checkbox value={filters?.olive} update={() => { setFilters({ ...filters, olive: !filters?.olive }) }} label={OLIVE} padding='sm' /> <div className='text-12 text-gray'>{filterCount?.olive}</div></div>
          </div>
        </div>
        <div className='text-16 bg-white rounded-lg py-12 mt-sm'>
          <div className='px-12 pb-sm'>Club Percentage Acceptance</div>
          <div className='flex flex-col'>
            <div className='px-12 hover:bg-tealTierBg cursor-pointer py-xs'>
              <Checkbox2 labelSize='text-14' roundedButton={true} value={filters?.acceptance_all} update={() => {
                if (filters?.acceptance_all) {
                  setFilters({ ...filters, acceptance_all: false, acceptance_25: true })
                } else {
                  setFilters({ ...filters, acceptance_all: true, acceptance_25: false })
                }
              }} label={ALL} customClasses='' />
            </div>
            <div className='px-12 hover:bg-tealTierBg cursor-pointer py-xs'>
              <Checkbox2 labelSize='text-14' roundedButton={true} value={filters?.acceptance_25} update={() => {
                if (filters?.acceptance_25) {
                  setFilters({ ...filters, acceptance_all: true, acceptance_25: false })
                } else {
                  setFilters({ ...filters, acceptance_all: false, acceptance_25: true })
                }
              }} label={MORE_THAN_25} customClasses='' />
            </div>
          </div>
        </div>
        <div className='text-16 bg-white rounded-lg py-12 mt-sm'>
          <div className='px-12 pb-sm'>Club Member Count</div>
          <div className='flex flex-col'>
            <div className='px-12 hover:bg-tealTierBg cursor-pointer py-xs'>
              <Checkbox2 labelSize='text-14' roundedButton={true} value={filters?.member_count_all} update={() => {
                if (filters?.member_count_all) {
                  setFilters({ ...filters, member_count_all: false, member_count_10: true })
                } else {
                  setFilters({ ...filters, member_count_all: true, member_count_10: false })
                }
              }} label={ALL_MEMBERS} customClasses='' />
            </div>
            <div className='px-12 hover:bg-tealTierBg cursor-pointer py-xs'>
              <Checkbox2 labelSize='text-14' roundedButton={true} value={filters?.member_count_10} update={() => {
                if (filters?.member_count_10) {
                  setFilters({ ...filters, member_count_all: true, member_count_10: false })
                } else {
                  setFilters({ ...filters, member_count_all: false, member_count_10: true })
                }
              }} label={MORE_THAN_10} customClasses='' />
            </div>
          </div>
        </div>
        <div className='text-16 bg-white rounded-lg py-12 mt-sm'>
          <div className='px-12 pb-sm'>Club with My TG Community</div>
          <div className='flex flex-col'>
            <div className='px-12 hover:bg-tealTierBg cursor-pointer py-xs'>
              <Checkbox value={filters?.friend_contact} update={() => { setFilters({ ...filters, friend_contact: !filters?.friend_contact }) }} label={FRIEND_CONTACT} customClasses='mr-md' />
            </div>

            {loading ? (
              <ThreeDots
                visible={true}
                height="50"
                width="50"
                color={"#098089"}
                radius="9"
                ariaLabel="three-dots-loading"
                wrapperStyle={{}}
                wrapperClass=""
              />
            ) : (
              <>
                {allMyTgGroups?.length ? (
                  <>
                    <div className='px-12 hover:bg-tealTierBg cursor-pointer py-xs'>
                      <Checkbox value={filters?.tg_group_member} update={() => { setFilters({ ...filters, tg_group_member: !filters?.tg_group_member }), setGroups([]) }} label={TG_GROUP_MEMBER} customClasses='mr-md' />
                    </div>
                    <div className={` ml-[28px] pr-sm ${!filters?.tg_group_member ? 'pointer-events-none' : ''}`}>
                      <CheckboxMultiselect
                        title={'Select Group'}
                        placeholder={'All'}
                        options={allMyTgGroups}
                        addClubClasses={true}
                        titleWeight={'normal'}
                        fontWeignt={'normal'}
                        update={handleSelection}
                        textSize='16'
                        groups={groups}
                        setGroups={setGroups}
                        value={groups}
                        setPage={setPage}
                        totalGroupsCount={totalGroupsCount}
                        fetchGroupsList={fetchGroupsList}
                        page={page}
                        pageSize={pageSize}
                        showAll={filters?.all_groups}
                        allGroupsSelected={filters?.all_groups}
                        setAllGroupsSelected={(value) => {
                          setFilters({ ...filters, all_groups: value })
                        }}
                        disableError={true}
                        listLoading={listLoading}
                        setListLoading={setListLoading}
                      />
                    </div>
                  </>
                ) : null}
              </>
            )}
          </div>
        </div>

        {user?.playAsCouple &&
          <div className='flex justify-between hover:bg-tealTierBg cursor-pointer items-center text-16 bg-white rounded-lg px-12 py-xs mt-sm' onClick={() => { setFilters({ ...filters, couple: !filters?.couple }) }}>
            <Checkbox capitalizeLabel={false} value={filters?.couple} update={() => { setFilters({ ...filters, couple: !filters?.couple }) }} label={COUPLE} padding='sm' />
            <div className='text-12 text-gray'>{filterCount?.couple || ''}</div>
          </div>
        }
        {user?.gender !== "male" &&
          <div className='flex justify-between hover:bg-tealTierBg cursor-pointer items-center text-16 bg-white rounded-lg px-12 py-xs mt-sm' onClick={() => { setFilters({ ...filters, female: !filters?.female }) }}>
            <Checkbox value={filters?.female} update={() => { setFilters({ ...filters, female: !filters?.female }) }} label={FEMALE} padding='sm' />
            <div className='text-12 text-gray'>{filterCount?.female || ''}</div>
          </div>
        }
        <div className='flex justify-between hover:bg-tealTierBg cursor-pointer items-center text-16 bg-white rounded-lg px-12 py-xs mt-sm' onClick={() => { setFilters({ ...filters, favorite: !filters?.favorite }) }}>
          <Checkbox value={filters?.favorite} update={() => { setFilters({ ...filters, favorite: !filters?.favorite }) }} label={FAVORITE} padding='sm' />
          <div className='text-12 text-gray'>{filterCount?.favorite || ''}</div>
        </div>
        <div className='flex justify-between hover:bg-tealTierBg cursor-pointer items-center text-16 bg-white rounded-lg px-12 py-xs mt-sm' onClick={() => { setFilters({ ...filters, played: !filters?.played }) }}>
          <Checkbox value={filters?.played} update={() => { setFilters({ ...filters, played: !filters?.played }) }} label={PLAYED} padding='sm' />
          <div className='text-12 text-gray'>{filterCount?.played || ''}</div>
        </div>
      </div>
    </div>
  )
}
