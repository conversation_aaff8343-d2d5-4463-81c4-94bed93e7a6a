import React, { useState, useEffect, useCallback, useContext } from 'react'
import useDropdown from '../../hooks/useDropdown'
import { ThreeDots } from 'react-loader-spinner'
import debounce from '../../utils/helper/debounce'
import CrossIconV2 from '../icons/CrossIconV2'
import useCheckDeviceScreen from '../../hooks/useCheckDeviceScreen'
import { DrawerContext } from '../../context/DrawerContext'

const ClubAndLocationSearchBar = ({ value, update, clubList, setClubFilter, setLocationFilter, setMapView }) => {
    const { dropdownVisible, setDropdownVisible } = useDropdown()
    const [clubs, setClubs] = useState([])
    const [searchValue, setSearchValue] = useState('')
    const [loading, setLoading] = useState(false)
    const [locations, setLocations] = useState([])
    const { isMobile } = useCheckDeviceScreen()
    const { setDrawer } = useContext(DrawerContext)

    useEffect(() => {
        if (value) {
            setSearchValue(value?.properties?.name)
        }
    }, [value])

    useEffect(() => {
        if (dropdownVisible && searchValue && searchValue !== '' && !value) {
            setLoading(true)
            fetchClubs(searchValue)
        }
    }, [searchValue, value])

    const fetchClubs = useCallback(
        debounce((searchValue) => {
            const tempClubs = clubList.filter((club) => club.properties.name.toLowerCase().includes(searchValue?.trim()?.toLowerCase()))
            setClubs(tempClubs)
            setLoading(false)
        }, 300),
        [clubList]
    )

    useEffect(() => {
        if (dropdownVisible && searchValue && searchValue !== '' && !value) {
            setLoading(true)
            fetchLocations(searchValue);
        }
    }, [searchValue, value])

    const fetchLocations = useCallback(
        debounce((searchValue) => {
            fetch(`https://api.mapbox.com/geocoding/v5/mapbox.places/${encodeURI(searchValue)}.json?limit=5&proximity=ip&types=country%2Cplace%2Cregion&autocomplete=true&access_token=${process.env.NEXT_PUBLIC_MAPBOX_TOKEN}`, {
                method: 'GET',
                headers: { 'Content-Type': 'application/json' },
            }).then((data) => data.json())
                .then((data) => {
                    setLocations(data.features);
                    setLoading(false);
                })
        }, 300),
        []
    );

    const handleClearInput = () => {
        setSearchValue('')
        setClubFilter(null)
        setLocationFilter(null)
        setClubs([])
    }

    return (
        <div onClick={(e) => e.stopPropagation()} className="relative mr-md w-full h-[40px]">
            <div className="bg-white rounded-lg flex gap-2 px-sm h-full">
                <img
                    src="/icons/search.svg"
                    width={20}
                    height={20}
                />
                <input
                    onBlur={() => setDropdownVisible(false)}
                    onFocus={() => setDropdownVisible(true)}
                    value={searchValue}
                    onChange={(e) => {
                        if (clubList?.length) {
                            setSearchValue(e.target.value)
                            setClubFilter(null)
                            setLocationFilter(null)
                        }
                    }}
                    placeholder={"Search by Location or Club"}
                    className={`bg-white placeholder-gray w-full rounded-lg text-12 text-gray ${!clubList?.length ? "cursor-not-allowed" : ""}`}
                />
                <div className="self-center cursor-pointer" onClick={handleClearInput}>
                    {searchValue ? <CrossIconV2 /> : <div style={{ width: 12 }}></div>}
                </div>
            </div>

            {dropdownVisible && searchValue && !value && (
                <div
                    className="absolute flex flex-col text-sm overflow-scroll bg-lightestgray font-thin rounded-lg shadow"
                    style={{
                        top: 40,
                        minHeight: 40,
                        maxHeight: 300,
                        width: '100%',
                        zIndex: 10
                    }}>
                    {loading ? (
                        <div className="absolute inset-0 flex-center">
                            <ThreeDots
                                visible={true}
                                height="25"
                                width="25"
                                color={"#098089"}
                                radius="9"
                                ariaLabel="three-dots-loading"
                                wrapperStyle={{}}
                                wrapperClass=""
                            />
                        </div>
                    ) : (<>
                        {clubs?.length > 0 && (<div className="p-sm text-gray">Clubs</div>)}
                        {clubs?.slice(0, locations?.length > 0 ? 4 : 8)?.map((club) => (
                            <div
                                key={club.id}
                                onMouseDown={() => {
                                    setClubFilter(club)
                                    setSearchValue(club.properties.name)
                                    setMapView()
                                    setDropdownVisible(false)
                                    setClubs([])
                                    setDrawer()

                                }}
                                className={`p-sm hover:bg-lightestgray cursor-pointer capitalize bg-white`}
                            >
                                {club.properties.name}
                            </div>
                        ))}
                        {locations?.length > 0 && (<div className="p-sm text-gray">Locations</div>)}
                        {locations?.slice(0, 4).map((location) => (
                            <div
                                key={location.id}
                                onMouseDown={() => {
                                    setLocationFilter(location)
                                    setSearchValue(location.place_name)
                                    setMapView()
                                    setDropdownVisible(false)
                                    setDrawer()

                                }}
                                className={`p-sm hover:bg-lightestgray cursor-pointer capitalize bg-white`}
                            >
                                {location.place_name}
                            </div>
                        ))}
                        {clubs?.length === 0 && (locations?.length === 0 || locations === undefined) && (
                            <div className="p-sm text-gray text-center">No results found</div>
                        )}

                    </>
                    )}
                </div>
            )}
        </div>)
}

export default ClubAndLocationSearchBar