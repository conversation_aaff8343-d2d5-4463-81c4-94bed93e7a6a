import React, { useContext, useEffect } from 'react'
import useCheckDeviceScreen from '../../hooks/useCheckDeviceScreen'
import { DrawerContext } from '../../context/DrawerContext'

const MapLayout = ({ children, setRefreshMap }) => {
    const { isMobile, isTablet, isDesktop } = useCheckDeviceScreen()
    const { drawer, setDrawer, renderChatDrawer } = useContext(DrawerContext)

    return (
        <div className={`flex relative flex-col md:flex-row bg-lightestgray w-full h-full`}>
            {children}
            {renderChatDrawer()}
        </div>
    )
}

export default MapLayout
