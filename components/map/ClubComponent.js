import React, { use, useContext, useEffect, useState } from 'react'
import MapLayout from './MapLayout'
import { Filter } from './Filter'
import { ClubsList } from './ClubsList'
import { MapView } from './MapView'
import useCheckDeviceScreen from '../../hooks/useCheckDeviceScreen'
import { MapListToggle } from '../buttons/MapListToggle'
import { DrawerContext } from '../../context/DrawerContext'
import FilterIcon from '../icons/FilterIcon'
import { UserContext } from '../../pages/_app'
import ENDPOINTS from '../../constants/endpoints.json'
import ClubAndLocationSearchBar from './ClubAndLocationSearchBar'
import fetchGroupsList from '../../utils/map/fetchGroupsList'
import MAP_FILTER from '../../constants/filterConstants'
import constantOptions from '../../constants/constantOptions'
import calculateFilterCounts from '../../utils/map/calculateFilterCounts';
// import useMapFilters from '../../hooks/useMapFilters'
import canCreateOfferCheck from '../../utils/map/canCreateOffer';

const { CLUB_TIERS } = constantOptions

// Destructure the constants we need
const { TIER, TG_COMMUNITY, CLUB_PERCENTAGE_ACCEPTANCE, CLUB_MEMBER_COUNT } = MAP_FILTER

// Destructure nested values
const { FERN, SAGE, MOSS, OLIVE } = TIER
const { FRIEND_CONTACT, TG_GROUP_MEMBER, OFFER, FAVORITE, PLAYED, COUPLE, FEMALE } = TG_COMMUNITY
const { ALL, MORE_THAN_25 } = CLUB_PERCENTAGE_ACCEPTANCE
const { ALL_MEMBERS, MORE_THAN_10 } = CLUB_MEMBER_COUNT

const pageSize = process.env.CONFIG.MY_GROUP_LIST_LIMIT
const view = ["filter", "map", "list"]


const ClubComponent = () => {
    const { isMobile, isTablet, isDesktop, isWideScreen } = useCheckDeviceScreen()
    const [filters, setFilters] = useState({
        fern: false,
        sage: false,
        moss: false,
        olive: false,
        friend_contact: false,
        tg_group_member: false,
        offer: false,
        favorite: false,
        played: false,
        couple: false,
        acceptance_all: false,
        acceptance_25: false,
        member_count_all: false,
        member_count_10: false,
        all_groups: false
    });
    const [filterActive, setFilterActive] = useState(false);
    const [filterApplied, setFilterApplied] = useState(0);
    const { user, token } = useContext(UserContext)
    const [allMyTgGroups, setAllMyTgGroups] = useState([])
    const [totalGroupsCount, setTotalGroupsCount] = useState(0)
    const [page, setPage] = useState(1)
    const [loading, setLoading] = useState(false)
    const [listLoading, setListLoading] = useState(false)
    const [clubsLoading, setClubsLoading] = useState(false)
    const [currentView, setCurrentView] = useState(view[1])
    const [expandedMap, setExpandedMap] = useState(false)
    const [refreshMap, setRefreshMap] = useState(0)
    const [filteredClubsFromMap, setFilteredClubsFromMap] = useState([])
    const [mapObjectFromMap, setMapObjectFromMap] = useState(null);
    const [boundsFromMap, setBoundsFromMap] = useState(null);
    const [clubFilter, setClubFilter] = useState(null)
    const [locationFilter, setLocationFilter] = useState(null)
    const [originalClubs, setOriginalClubs] = useState([])
    const [filterCount, setFilterCount] = useState({})
    const { drawer, setDrawer } = useContext(DrawerContext)
    const [offerRestricted, setOfferRestricted] = useState(true)
    const [groups, setGroups] = useState([]);

    const isFiltersApplied = Object?.keys(filters || {})?.some(key => filters[key] === true)

    useEffect(() => {
        checkCanCreateOffer();
        return () => {
            sessionStorage.removeItem('map-zoom')
            sessionStorage.removeItem('map-bounds')
        }
    }, []);

    useEffect(() => {
        setFilterApplied(prev => prev + 1)
    }, [filters])

    useEffect(() => {
        fetchGroups();
    }, [refreshMap]);

    const checkCanCreateOffer = async () => {
        try {
            const result = await canCreateOfferCheck(user.id);
            if (result.canCreate) {
                setOfferRestricted(null);
            } else {
                setOfferRestricted(result.message);
            }
        } catch (err) {
            console.log('Error', err);
            setOfferRestricted('Error checking offer creation eligibility');
        }
    };

    const fetchGroups = async (pageToLoad = page) => {
        const isInitialLoad = !allMyTgGroups?.length;
        const isLoadingMore = allMyTgGroups?.length > 0;

        if (isInitialLoad) {
            setLoading(true);
        }

        if (isLoadingMore) {
            setListLoading(true);
        }

        await fetchGroupsList({
            token,
            userId: user?.id,
            pageSize,
            pageToLoad,
            onSuccess: (data, loadedPage) => {
                if (loadedPage !== 1) {
                    setAllMyTgGroups((prev) => [...prev, ...data?.groups]);
                    setTotalGroupsCount(data?.totalCount);
                } else {
                    setAllMyTgGroups(data?.groups);
                    setTotalGroupsCount(data?.totalCount);
                }
                setPage(loadedPage);
            },
            onComplete: () => {
                setLoading(false);
                setListLoading(false);
            }
        });
    };

    const handleMapResize = () => {
        setExpandedMap(prev => !prev)
        setRefreshMap(prev => prev + 1)
    }

    const getMapData = (mapObject, bounds, filteredClubs, originalClubs) => {
        setMapObjectFromMap(mapObject)
        setBoundsFromMap(bounds)
        setFilteredClubsFromMap(filteredClubs)
        setOriginalClubs(originalClubs)
    }

    useEffect(() => {
        if (originalClubs && originalClubs.length > 0) {
            const counts = calculateFilterCounts(originalClubs, CLUB_TIERS);
            setFilterCount(counts);
        }
    }, [originalClubs]);

    return (
        <div className='flex flex-col' style={{ height: 'calc(100vh - 72px)' }}>
            <MapLayout setRefreshMap={setRefreshMap}>
                {(currentView !== view[0] && (!isWideScreen && !isDesktop && !isTablet) && !drawer?.type) &&
                    <div className={`absolute ${isMobile ? 'left-[8px] top-[8px]' : 'left-[20px] top-[20px]'} ${isMobile ? 'w-[69%]' : expandedMap ? 'w-[40%]' : 'w-[80%]'}`} style={{ zIndex: 100 }}>
                        <ClubAndLocationSearchBar
                            value={clubFilter || locationFilter}
                            setClubFilter={setClubFilter}
                            setLocationFilter={setLocationFilter}
                            clubList={filteredClubsFromMap}
                            setMapView={() => {
                                if (currentView === view[2]) {
                                    setCurrentView(view[1])
                                }
                            }}
                        />
                    </div>}
                {((!isWideScreen && !isDesktop && !isTablet) && currentView !== view[0] && !drawer?.type) && <div className='absolute flex right-[10px] md:right-[26px] top-[10px]' style={{ zIndex: 100, transition: 'height 0.5s ease-in-out' }}>
                    <MapListToggle showMap={currentView === view[1]}
                        toggleMap={() => {
                            if (currentView === view[2]) {
                                setCurrentView(view[1])
                                return
                            } else {
                                setCurrentView(view[2])
                            }
                            setRefreshMap(prev => !prev)
                        }} />
                    {(!isWideScreen && !isDesktop && !isTablet) &&
                        <div className='pl-sm' onClick={() => {
                            setCurrentView(view[0])
                            sessionStorage.setItem('MOBILE_VIEW', JSON.stringify({ currentView }))
                        }}>
                            <FilterIcon isActive={isFiltersApplied} />
                        </div>
                    }
                </div>}
                {(isWideScreen || isDesktop) ?
                    <>
                        <Filter
                            groups={groups}
                            setGroups={setGroups}
                            currentView={currentView}
                            view={view}
                            page={page}
                            pageSize={pageSize}
                            loading={loading}
                            allMyTgGroups={allMyTgGroups}
                            totalGroupsCount={totalGroupsCount}
                            setListLoading={setListLoading}
                            listLoading={listLoading}
                            fetchGroupsList={fetchGroups}
                            setPage={setPage}
                            setRefreshMap={setRefreshMap}
                            filters={filters}
                            setFilters={setFilters}
                            filterApplied={filterApplied}
                            setFilterApplied={setFilterApplied}
                            filterActive={filterActive}
                            setFilterActive={setFilterActive}
                            filterCount={filterCount}
                            isFiltersApplied={isFiltersApplied}
                            setCurrentView={setCurrentView}

                        />
                        {(!expandedMap && drawer?.type === undefined) ?
                            <ClubsList
                                clubsLoading={clubsLoading}
                                setClubsLoading={setClubsLoading}
                                mapObject={mapObjectFromMap}
                                filteredClubsFromMap={filteredClubsFromMap}
                                bounds={boundsFromMap}
                                clubFilter={clubFilter}
                                setClubFilter={setClubFilter}
                                locationFilter={locationFilter}
                                setLocationFilter={setLocationFilter}
                                currentView={currentView}
                                setCurrentView={setCurrentView}
                                view={view}
                                setRefreshMap={setRefreshMap}
                                offerRestricted={offerRestricted}
                            /> : null}
                        <MapView
                            clubsLoading={clubsLoading}
                            setClubsLoading={setClubsLoading}
                            refreshMap={refreshMap}
                            setRefreshMap={setRefreshMap}
                            expandedMap={expandedMap}
                            handleMapResize={handleMapResize}
                            getMapData={getMapData}
                            view={view}
                            currentView={currentView}
                            setCurrentView={setCurrentView}
                            filters={filters}
                            setFilters={setFilters}
                            filterActive={filterActive}
                            setFilterActive={setFilterActive}
                            filterApplied={filterApplied}
                            setFilterApplied={setFilterApplied}
                            groups={groups}
                            setGroups={setGroups}
                            allGroupsSelected={filters?.all_groups}
                            clubFilter={clubFilter}
                            setClubFilter={setClubFilter}
                            locationFilter={locationFilter}
                            setLocationFilter={setLocationFilter}
                            setAllGroupsSelected={(value) => {
                                setFilters({ ...filters, all_groups: value })
                            }}
                            offerRestricted={offerRestricted}
                        />
                    </> : <>
                        {((isMobile && currentView === view[0]) || (isTablet)) &&
                            <Filter
                                currentView={currentView}
                                view={view}
                                page={page}
                                pageSize={pageSize}
                                loading={loading}
                                allMyTgGroups={allMyTgGroups}
                                totalGroupsCount={totalGroupsCount}
                                setListLoading={setListLoading}
                                listLoading={listLoading}
                                fetchGroupsList={fetchGroups}
                                setPage={setPage}
                                setRefreshMap={setRefreshMap}
                                filters={filters}
                                setFilters={setFilters}
                                filterApplied={filterApplied}
                                setFilterApplied={setFilterApplied}
                                filterActive={filterActive}
                                setFilterActive={setFilterActive}
                                setCurrentView={setCurrentView}
                                filterCount={filterCount}
                                isFiltersApplied={isFiltersApplied}
                            />}

                        {drawer?.type === undefined && <>
                            {((isMobile && currentView === view[1]) || isTablet) &&
                                currentView === view[1] ?
                                <MapView
                                    clubsLoading={clubsLoading}
                                    setClubsLoading={setClubsLoading}
                                    refreshMap={refreshMap}
                                    setRefreshMap={setRefreshMap}
                                    expandedMap={expandedMap}
                                    handleMapResize={handleMapResize}
                                    getMapData={getMapData}
                                    view={view}
                                    currentView={currentView}
                                    setCurrentView={setCurrentView}
                                    filters={filters}
                                    setFilters={setFilters}
                                    filterActive={filterActive}
                                    setFilterActive={setFilterActive}
                                    filterApplied={filterApplied}
                                    setFilterApplied={setFilterApplied}
                                    groups={allMyTgGroups}
                                    setGroups={setAllMyTgGroups}
                                    allGroupsSelected={filters?.all_groups}
                                    setAllGroupsSelected={(value) => {
                                        setFilters({ ...filters, all_groups: value })
                                    }}
                                    clubFilter={clubFilter}
                                    setClubFilter={setClubFilter}
                                    locationFilter={locationFilter}
                                    setLocationFilter={setLocationFilter}
                                    offerRestricted={offerRestricted}
                                /> :
                                <ClubsList
                                    clubsLoading={clubsLoading}
                                    setClubsLoading={setClubsLoading}
                                    mapObject={mapObjectFromMap}
                                    filteredClubsFromMap={filteredClubsFromMap}
                                    bounds={boundsFromMap}
                                    clubFilter={clubFilter}
                                    setClubFilter={setClubFilter}
                                    locationFilter={locationFilter}
                                    setLocationFilter={setLocationFilter}
                                    currentView={currentView}
                                    setCurrentView={setCurrentView}
                                    view={view}
                                    setRefreshMap={setRefreshMap}
                                    offerRestricted={offerRestricted}

                                />}
                        </>}
                    </>
                }

            </MapLayout>
        </div>
    )
}

export default ClubComponent
