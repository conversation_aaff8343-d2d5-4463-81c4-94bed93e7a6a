import React, { useContext, useEffect, useState } from 'react'
import { DrawerContext } from '../../context/DrawerContext'
import { UserContext } from '../../pages/_app'
import mapboxgl from 'mapbox-gl'
import RequestEmptyState from '../common/RequestEmptyState'
import { ThreeDots } from 'react-loader-spinner'
import useClient from '../../graphql/useClient'
import { UPDATE_USER } from '../../graphql/mutations/user'
import { UNFAVORITE_CLUB, FAVORITE_CLUB } from '../../graphql/mutations/club'
import analyticsEventLog from '../../utils/firebase/analytics'
import constantOptions from '../../constants/constantOptions'
import ClubListItem from './ClubListItem'
import useCheckDeviceScreen from '../../hooks/useCheckDeviceScreen'
import handleClubPlayed from '../../utils/map/handleClubPlayed'
import handleClubFavorite from '../../utils/map/handleClubFavorite'
import ClubAndLocationSearchBar from './ClubAndLocationSearchBar'
import { MapListToggle } from '../buttons/MapListToggle'
import { CLUB_DETAILS } from '../../constants/constantOptions'

export const ClubsList = ({
  filteredClubsFromMap,
  mapObject, bounds,
  clubsLoading,
  setClubsLoading,
  expandedMap,
  clubFilter,
  setClubFilter,
  locationFilter,
  setLocationFilter,
  currentView,
  setCurrentView,
  view,
  setRefreshMap,
  offerRestricted,
}) => {
  const { user, fetchUser } = useContext(UserContext)
  const { drawer, setDrawer } = useContext(DrawerContext)
  const [clubsInBounds, setClubsInBounds] = useState([])
  const [played, setPlayed] = useState(false)
  const [favorited, setFavorited] = useState(false)
  const client = useClient()
  const { selectedClub } = drawer || {}
  const { isMobile, isTablet } = useCheckDeviceScreen()

  useEffect(() => {
    let clubsInBoundsLocal = []
    filteredClubsFromMap?.map((club) => {
      if (checkInBounds(club)) {
        clubsInBoundsLocal?.push(club)
      }
    })
    setClubsInBounds(clubsInBoundsLocal)
  }, [filteredClubsFromMap, bounds])

  const checkInBounds = (club) => {
    let localbound = bounds
    if (!localbound) localbound = mapObject.getBounds()
    const itemPos = new mapboxgl.LngLat(
      club.geometry.coordinates[0],
      club.geometry.coordinates[1]
    )
    return localbound.contains(itemPos)
  }

  const handlePlayed = (clubId) => {
    // Call the utility function
     handleClubPlayed({
      clubId,
      user,
      currentPlayedStatus: user?.playedClubs?.includes(clubId),
      client,
      fetchUser,
      setLocalPlayed: setPlayed
    });

    const initialClubs = [...clubsInBounds];
    const clubIndex = initialClubs.findIndex((club) => club.id === clubId);

    if (clubIndex !== -1) {
      // Update the played property
      initialClubs[clubIndex] = {
        ...initialClubs[clubIndex],
        properties: {
          ...initialClubs[clubIndex].properties,
          played: !initialClubs[clubIndex].properties.played
        }
      };
    }

    setClubsInBounds(initialClubs);

  }

  const handleFavorite = (clubId) => {
    // Setting the local array
    const initialClubs = [...clubsInBounds];
    const club = initialClubs.find((club) => club.id === clubId);
    club.properties.favClub = !club.properties.favClub;
    setClubsInBounds(initialClubs);

    // Call the utility function
    handleClubFavorite({
      clubId,
      user,
      currentFavoriteStatus: user?.favorite_clubs?.filter(({ club_id }) => club_id === clubId)?.length === 1,
      client,
      fetchUser,
      setLocalFavorite: setFavorited
    });
  }

  return (
    <div
      className={`flex flex-col h-full w-full md:w-[68%] lg:w-[28%] lg:pt-md`}
      style={{
        transition: 'width 500ms ease-in-out, opacity 500ms ease-in-out, transform 500ms ease-in-out',
        opacity: expandedMap ? 0 : 1,
        transform: expandedMap ? 'translateX(-100%)' : 'translateX(0)',
        position: expandedMap ? 'absolute' : 'relative',
        zIndex: expandedMap ? -1 : 1
      }}
    >
      {!isMobile &&
        <div className='flex justify-between py-sm lg:py-0 lg:pb-sm'>
          <div className="flex-1 text-18 font-medium pl-sm">Clubs</div>
          {isTablet &&
            <div className='flex flex-1 justify-end px-md items-center' >
              <ClubAndLocationSearchBar
                value={clubFilter || locationFilter}
                setClubFilter={setClubFilter}
                setLocationFilter={setLocationFilter}
                clubList={filteredClubsFromMap}
                setMapView={() => {
                  if (currentView === view[2]) {
                    setCurrentView(view[1])
                  }
                }}
              />
              <MapListToggle showMap={currentView === view[1]}
                toggleMap={() => {
                  if (currentView === view[2]) {
                    setCurrentView(view[1])
                    return
                  } else {
                    setCurrentView(view[2])
                  }
                  setRefreshMap(prev => !prev)
                }} />
            </div>
          }
        </div>}
      {clubsLoading ? <div className='flex-center h-full'>
        <ThreeDots
          visible={true}
          height="50"
          width="50"
          color="#098089"
          radius="9"
          ariaLabel="three-dots-loading"
          wrapperStyle={{}}
          wrapperClass=""
        />
      </div> : <>
        {!clubsInBounds.length ? (
          <div
            className="h-full overflow-scroll w-full mt-[56px] md:mt-0 flex-center"
            style={{
              paddingTop: '8px',
              top: 0,
              zIndex: 9,
            }}>
            <RequestEmptyState background="none" shadow={false} message="No clubs within viewport on the map." />
          </div>
        ) : (
          <div
            className="h-full overflow-scroll w-full"
            style={{
              top: 0,
              zIndex: 9,
            }}>
            <div className="clubInfoWrap px-sm mt-[56px] md:mt-0">
              {clubsInBounds?.map((club) => {
                return (
                  <div key={club?.id} className="pb-sm">
                    <ClubListItem
                      isFromList={true}
                      club={club?.properties}
                      clubGeoJson={club}
                      user={user}
                      source="clubs-list"
                      played={club?.properties?.played}
                      favorited={club?.properties?.favClub}
                      handlePlayed={handlePlayed}
                      handleFavorite={handleFavorite}
                      setRefreshMap={setRefreshMap}
                      offerRestricted={offerRestricted}
                      setClubFilter={setClubFilter}
                    />
                  </div>
                )
              })}
            </div>
          </div>
        )}
      </>}
    </div>
  )
}
