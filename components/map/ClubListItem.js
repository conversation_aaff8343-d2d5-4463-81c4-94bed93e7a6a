import React, { useContext, useEffect, useState } from 'react'
import { DrawerContext } from '../../context/DrawerContext'
import constantOptions from '../../constants/constantOptions'
import ClubStatus from '../icons/ClubStatus'
import Toggle from '../common/Toggle'
import FavoriteIcon from '../icons/FavoriteIcon'
import { UserContext } from '../../pages/_app'

  const ClubListItem = ({ club, clubGeoJson, isFromList = false, source, played, favorited, handlePlayed, handleFavorite, setRefreshMap, offerRestricted, setClubFilter }) => {
  const { user } = useContext(UserContext)
  const { TIER_TAG_OPTIONS } = constantOptions
  const { setDrawer } = useContext(DrawerContext)
  const { CLUB_DETAILS } = constantOptions?.DRAWER_TYPE
  const userClubIds = user?.clubs?.map(club => club?.id)
  const [contactOnlyClub, setContactOnlyClub] = useState(false)

  useEffect(() => {

    if (!userClubIds?.includes(club?.id) &&
      (club?.isContact || club?.isFriend) &&
      ((!user?.visibleToPublic && club?.color === "teal_contact") || !club?.clubMembers)
    ) {
      setContactOnlyClub(true)
    } else {
      setContactOnlyClub(false)
    }
  }, [club, user])

  return (
    <div
      onClick={() => {
        setClubFilter(clubGeoJson)
        sessionStorage.setItem('selected-club', JSON.stringify(club))
        window?.sessionStorage.setItem("MAP_INFO_RETURN_DATA", JSON.stringify({
          type: CLUB_DETAILS,
          selectedClub: club,
        }));
        setDrawer({
          type: CLUB_DETAILS,
          selectedClub: club,
          setRefreshMap: setRefreshMap,
          offerRestricted,
          customBackClickHandler: () => {
            setDrawer({
              type: CLUB_DETAILS,
              selectedClub: club,
              setRefreshMap: setRefreshMap,
              offerRestricted,
            })
          },
        })
      }}
      id={`club-overlay-${club?.id}`}
      className={`cursor-pointer relative rounded-lg club-details-card font-ubuntu bg-white pt-lg px-md pb-sm w-full cursor-pointer hover:bg-tealTierBg hover:shadow-lg`}>
      {club?.clubDemandType !== constantOptions?.CLUB_DEMAND_TYPE?.NEUTRAL &&
        <ClubStatus status={club?.clubDemandType} />
      }
      <div className='flex justify-between'>
        {TIER_TAG_OPTIONS[club?.tier] ?
          <div className="text-10 flex-center rounded p-sm h-[22px] uppercase bg-tealTierBg text-tealTier font-bold">
            {club?.ct === constantOptions?.CLUB_TYPES?.VIRTUAL ? 'Virtual' : TIER_TAG_OPTIONS[club?.tier]}
          </div> : ''}
        {source === 'clubs-list' && (
          <div className='flex' onClick={(e) => e.stopPropagation()}>
            <Toggle onClick={() => handlePlayed(club?.id)} value={played} />
            {(!contactOnlyClub && !userClubIds?.includes(club?.id)) ? (
              <FavoriteIcon isFavorite={favorited} onClick={() => handleFavorite(club?.id)} />
            ) : ''}
          </div>
        )}
      </div>
      {/*
          This section shows club name and tier 
         */}
      <div className="flex items-center text-16 md:text-18 pt-sm">
        <img className='mr-sm' src="/svg/golf-post-small.svg" />
        {club?.name}
      </div>
      {/* 
          This section shows the club address and address icon
         */}
      <div className="mb-sm flex text-12 md:text-14 text-gray">
        <div className='pt-[2px] mr-xs'>
          <img src="/svg/location-grey.svg" style={{ minWidth: 14, maxWidth: 14, minHeight: 14, maxHeight: 14 }} />
        </div>
        <span>{club?.addr}</span>
      </div>
      <div className='flex text-darkteal hover:underline'>View Club Details</div>
    </div>
  )
}

export default ClubListItem
