import ResizeIcon from '../icons/ResizeIcon'
import React, { useState, useEffect, useContext, useRef } from 'react'
import 'firebase/compat/auth';
import { UserContext } from '../../pages/_app';
import ClubCardDetailsNew from './ClubListItem';
import { DEFAULT_COORDINATES } from '../clubs/clubHelper';
import { loadImages, loadLayer, markerClickEvents } from '../clubs/helpers/mapHelpers';
import mapboxgl from 'mapbox-gl';
import Head from 'next/head';
import constantOptions from '../../constants/constantOptions';
import mapConstants from '../../constants/mapConstants';
import 'mapbox-gl/dist/mapbox-gl.css'
import { useRouter } from 'next/router';
import { DrawerContext } from '../../context/DrawerContext';
import ClubFriendsPlayedModal from '../modals/ClubFriendsPlayedModal';
import takeValuesFromSession from '../../utils/helper/takeValuesFromSession';
import useCheckDeviceScreen from '../../hooks/useCheckDeviceScreen';
import ClubAndLocationSearchBar from './ClubAndLocationSearchBar';
import fetchAllClubs from '../../utils/map/fetchClubs';
import canCreateOfferCheck from '../../utils/map/canCreateOffer';
import { MapListToggle } from '../buttons/MapListToggle';
import moment from 'moment';
import CreateOfferIcon from '../icons/CreateOfferIcon';
import { ModalContext } from '../../context/ModalContext';

const { MAP_FILTERS, MAP_LIST_BOUND, CLUB_DETAILS } = constantOptions?.DRAWER_TYPE
const { MAP_CLUB_INFO } = constantOptions?.DRAWER_TYPE

mapboxgl.accessToken = process.env.NEXT_PUBLIC_MAPBOX_TOKEN;
const map_layers = constantOptions.MAP_LAYERS;

export const MapView = ({
    handleMapResize,
    expandedMap,
    searchActive,
    refreshMap,
    getMapData,
    setClubsLoading,
    filters,
    setFilters,
    filterActive,
    setFilterActive,
    filterApplied,
    setFilterApplied,
    groups,
    setGroups,
    clubFilter,
    setClubFilter,
    locationFilter,
    setLocationFilter,
    currentView,
    setCurrentView,
    view,
    setRefreshMap,
    offerRestricted,
}) => {


    const { user, token } = useContext(UserContext)
    const mapContainerRef = useRef(null);
    const [originalClubs, setOriginalClubs] = useState(null);
    const [clubs, setClubs] = useState(null);
    const [selectedLayer, setSelectedLayer] = useState(null);
    const [isPopupWindowOpen, setIsPopupWindowOpen] = useState(false);
    const [selectedClub, setSelectedClub] = useState(takeValuesFromSession ? JSON.parse(sessionStorage.getItem('selected-club') || "{}") : {});
    const [listingCardDialogStyle, setListingCardDialogStyle] = useState({
        position: 'absolute',
        overflow: 'hidden',
    });
    const [previousLayer, setPreviousLayer] = useState('')
    const router = useRouter()
    const [dataForClub, setDataForClub] = useState({})
    const { drawer, setDrawer } = useContext(DrawerContext)
    const { isMobile, isTablet, isDesktop, isWideScreen } = useCheckDeviceScreen()
    // Inside Clubs_new.js
    const [isListShow, setIsListShow] = useState(false);
    const [bounds, setBounds] = useState(null);
    const [filteredClubs, setFilteredClubs] = useState([])
    const [mapObject, setMapObject] = useState(null);
    const [openClub, setOpenClub] = useState({})
    const { modal, setModal } = useContext(ModalContext)

    let isMounted = true;

    useEffect(() => {
        getMapData(mapObject, bounds, filteredClubs, originalClubs)
    }, [mapObject, bounds, filteredClubs])

    useEffect(() => {
        const selectedClub = JSON.parse(sessionStorage.getItem('selected_club_for_offers'))
        if (selectedClub && filteredClubs?.length) {
            setClubFilter(filteredClubs.find(club => club?.id === selectedClub))
            sessionStorage.removeItem('selected_club_for_offers')
        }
        if (router?.query?.clubId && filteredClubs?.length) {
            const club = filteredClubs.find(club => club?.id === Number(router?.query?.clubId))
            if (club) {
                setClubFilter(club)
            }
        }
    }, [filteredClubs])

    /**
* On first load fetch clubs and initialize map
*/
    useEffect(() => {
        fetchClubsData();
        initializeMap();

        return () => {
            isMounted = false;
            setIsPopupWindowOpen(false);
            // setOpenClub({});
        }
    }, [window.location.pathname, refreshMap]);


    useEffect(() => {
        if (originalClubs) {
            let temporaryClubs = originalClubs
            //For applying tier filters
            const tierFilters = Object.entries(filters)
                .filter(([key, value]) => ["fern", "sage", "moss", "olive"].includes(key) || value)
                .reduce((obj, [key, value]) => {
                    obj[key] = value;
                    return obj;
                }, {});

            if (window?.location?.pathname === "/dashboard/offers") {
                if (filters?.startDate || filters?.endDate) {
                    temporaryClubs = temporaryClubs.map((club) => {
                        // Filter offers within each club that fall within the date range
                        const filteredOffers = club.properties.offers?.filter((offer) => {
                            const offerStart = moment(offer?.start_date);
                            const offerEnd = moment(offer?.end_date);

                            if (filters?.startDate && filters?.endDate) {
                                const filterStart = moment(filters?.startDate)
                                    .startOf('day')
                                    .utc();
                                const filterEnd = moment(filters?.endDate)
                                    .endOf('day')
                                    .utc();
                                // Check if offer dates overlap with filter date range
                                const overlaps = (
                                    offerStart.isSameOrBefore(filterEnd, 'day') &&
                                    offerEnd.isSameOrAfter(filterStart, 'day')
                                );

                                return overlaps;
                            }

                            if (filters?.startDate && !filters?.endDate) {
                                const filterStart = moment.utc(filters?.startDate);
                                // Check if offer is active on the selected start date
                                return offerStart.isSameOrBefore(filterStart, 'day') &&
                                    offerEnd.isSameOrAfter(filterStart, 'day');

                            }
                            return false; // Don't include offers if no valid date filter
                        }) || [];

                        // Return club with filtered offers
                        return {
                            ...club,
                            properties: {
                                ...club.properties,
                                offers: filteredOffers
                            }
                        };
                    }).filter(club => club.properties.offers.length > 0); // Remove clubs with no offers after filtering
                }
            }

            if (Object?.entries(tierFilters)?.length) {
                const filteredTiers = Object.entries(tierFilters)
                    .filter(([tier, value]) => value && constantOptions?.CLUB_TIERS[tier.toUpperCase()])
                    .map(([tier]) => constantOptions?.CLUB_TIERS[tier.toUpperCase()]);
                if (filteredTiers?.length) {
                    temporaryClubs = (temporaryClubs.filter((club) => filteredTiers?.includes(club?.properties?.tier)));
                }
            }

            if (filters?.friend_contact && filters?.tg_group_member) {
                let firstClubsArray = [], secondClubsArray = []
                firstClubsArray = temporaryClubs?.filter((club) => club?.properties?.isFriend || club?.properties?.isContact)
                if (!groups?.length) {
                    secondClubsArray = temporaryClubs?.filter((club) => {
                        return club?.properties?.isMyTgGroupMember
                    })
                } else {
                    const groupsArray = groups?.map((group) => group?.id)
                    // window?.sessionStorage.removeItem('MAP_FILTERS')
                    secondClubsArray = temporaryClubs?.filter((club) => {
                        return !!club?.properties?.myTgGroupIds?.filter(e => groupsArray.indexOf(e) !== -1)?.length
                    })
                }
                temporaryClubs = [...new Set([...firstClubsArray, ...secondClubsArray])]
            }

            //for applying friend/contact filter
            if (window?.location?.pathname === "/dashboard/map") {

                if (filters?.acceptance_25) {
                    temporaryClubs = temporaryClubs?.filter((club) => {
                        return club?.properties?.acceptanceRate > 25
                    })
                } else {
                    temporaryClubs = temporaryClubs?.filter((club) => {
                        return club?.properties?.acceptanceRate >= 0
                    })
                }
                if (filters?.member_count_10) {
                    temporaryClubs = temporaryClubs?.filter((club) => {
                        return club?.properties?.clubMembers > 10
                    })
                } else {
                    temporaryClubs = temporaryClubs?.filter((club) => {
                        return club?.properties?.clubMembers >= 0
                    })
                }
            }
            if (filters?.friend_contact && !filters?.tg_group_member) {
                temporaryClubs = temporaryClubs?.filter((club) => {
                    return club?.properties?.isFriend || club?.properties?.isContact
                })
            }

            //for applying tg group member filter & selected group filter
            if (filters?.tg_group_member && !filters?.friend_contact) {
                if (!groups?.length) {
                    temporaryClubs = temporaryClubs?.filter((club) => {
                        return club?.properties?.isMyTgGroupMember
                    })
                } else {
                    const groupsArray = groups?.map((group) => group?.id)
                    // window?.sessionStorage.removeItem('MAP_FILTERS')
                    temporaryClubs = temporaryClubs?.filter((club) => {
                        return !!club?.properties?.myTgGroupIds?.filter(e => groupsArray.indexOf(e) !== -1)?.length
                    })
                }
            }
            //for applying offer filter

            if (filters?.offer || filters?.favorite || filters?.played || filters?.couple || filters?.female) {//User fav clubs
                const userFavClubs = user?.favorite_clubs.map(
                    (club) => club?.club_id,
                );

                //Other filters 
                temporaryClubs = temporaryClubs?.filter(club => {
                    //Club has open offers
                    if (filters?.offer && (club?.properties?.color === constantOptions?.MAP_MARKERS?.BLUE ||
                        club?.properties?.color === constantOptions?.MAP_MARKERS?.BLUE_CONTACT)) {
                        return true
                    }

                    //Favorite clubs
                    if (filters?.favorite && userFavClubs.includes(club?.id)) {
                        return true
                    }

                    //Played clubs
                    if (filters?.played && user.playedClubs.includes(club?.id)) {
                        return true
                    }

                    //Play as Couple
                    if (filters?.couple && club?.properties?.hasPlayAsCoupleMember) {
                        return true
                    }

                    //female
                    if (filters?.female && club?.properties?.hasFemalMember) {
                        return true
                    }
                    return false
                })
            }

            loadLayer({
                map: mapObject,
                data: {
                    type: 'FeatureCollection',
                    features: temporaryClubs,
                },
                zoom: mapObject?.getZoom()
            })
            setFilteredClubs(temporaryClubs)
        }
    }, [filterActive, originalClubs, filterApplied])

    // Effect for checking the current zoom level
    useEffect(() => {
        if (bounds && mapObject) {
            const bounds = mapObject?.getBounds();
            const boundsArray = [bounds.getSouthWest().lng, bounds.getSouthWest().lat, bounds.getNorthEast().lng, bounds.getNorthEast().lat];
            setTimeout(() => {
                sessionStorage.setItem('map-zoom', mapObject.getZoom());
                sessionStorage.setItem('map-bounds', JSON.stringify(boundsArray));
            }, 200);
        }
    }, [bounds])

    /**
     * This block is called when map object is
     */
    useEffect(() => {
        console.log('Zoom--->', mapObject?.getZoom())
        if (mapObject && clubs && filterActive < 1) {
            // STEP 2 - Load Markers Data
            if (mapObject?._fullyLoaded) {
                loadLayer({
                    map: mapObject,
                    data: {
                        "type": "FeatureCollection",
                        "features": ClubFriendsPlayedModal
                    },
                    zoom: mapObject?.getZoom()

                })
            }
        }
    }, [mapObject, clubs]);

    useEffect(() => {
        if (mapObject && clubFilter) {
            mapObject.flyTo({
                center: clubFilter.geometry.coordinates,
                essential: true,
                duration: 800,
                maxDuration: 1000,
                minZoom: 3,
                speed: .2,
                zoom: 11
            });
            setTimeout(() => {
                mapObject.setLayoutProperty(`golf-club-unclustered-point-${clubFilter.properties.color}`, 'icon-size', [
                    'match',
                    ['get', 'id'],
                    +clubFilter.id,
                    mapConstants.ICON_SIZE.ON_HOVER,
                    mapConstants.ICON_SIZE.ORIGINAL,
                ]);
            }, 1500);
        } else if (mapObject && !clubFilter) {
            /**
             * mapObject?.getStyle()?.layers -> it returns the loaded layers of the map
             * Here we are checking if the layers we are expecting have been loaded or not
             */
            reSizingMarkersToSmall()
        }
    }, [clubFilter]);

    /**
       * Whenever we search a location in location search bar
       * We zoom into that area by the bounds provided from API
       */
    useEffect(() => {
        if (mapObject && locationFilter) {
            let { bbox, center, place_type } = locationFilter

            let zoomLevel = 7
            if (place_type.length) {
                zoomLevel = place_type[0] === 'country' ? 4 : 6
            }

            if (bbox) {
                mapObject?.fitBounds(bbox, {
                    linear: true,
                    duration: 300,
                    maxZoom: 4,
                })
            }
            //Fly to the center of the location
            mapObject?.flyTo({
                center: center,
                essential: true,
                duration: 800,
                maxDuration: 1000,
                speed: 0.2,
                zoom: zoomLevel,
            })
        }
    }, [locationFilter, refreshMap])

    /**
     * This block is called whenever side list is opened
     * and we update the container width accordingly 
     */
    useEffect(() => {
        handleResize();
    }, [searchActive, mapObject?._mapId, refreshMap]);

    /**
       * Listening to marker hover events of map layers
       */
    useEffect(() => {
        if (mapObject) {
            mapObject.on('mousemove', map_layers, showClubPopuop)
            mapObject.on('mouseleave', map_layers, hideClubInfoPopup)
            mapObject.on('click', map_layers, selectTheClub)
            mapObject.on('mouseout', hideClubInfoPopup)
            mapObject.on('resize', () => {
                mapObject.resize() //Resize the map whenever the window size changes
            })
            return () => {
                mapObject.off('mousemove', map_layers, showClubPopuop)
                mapObject.off('mouseleave', map_layers, hideClubInfoPopup)
                mapObject.off('click', map_layers, selectTheClub)
                mapObject.off('mouseout', hideClubInfoPopup)
                mapObject.off('resize')
            }
        }
    }, [selectedLayer, mapObject])

    useEffect(() => {
        window.addEventListener("resize", handleResize);
        return () => {
            window.removeEventListener("resize", handleResize);
        };
    }, []);

    useEffect(() => {
        const handleRouteChange = (url) => {
            // Check if the user is navigating back to the contacts page
            // Add your logic to handle the back button click
            let data = {}
            let mapFilters
            let mapInfoReturnData
            data = JSON.parse(window?.sessionStorage?.getItem("MAP_LIST_TYPE"));
            mapFilters = JSON.parse(window?.sessionStorage.getItem('MAP_FILTERS'))
            mapInfoReturnData = JSON.parse(window?.sessionStorage.getItem('MAP_INFO_RETURN_DATA'))
            if (!mapFilters) {
                if (Object?.entries(data || {})?.length !== 0) {
                    setDataForClub(data)
                    setDrawer({
                        type: data?.type,
                        club: data?.club,
                        returnToMapInfo: () => setDrawer(mapInfoReturnData),
                        // returnToMapInfo: selectTheClub,
                        customBackClickHandler: () => {

                            window?.sessionStorage?.removeItem("MAP_LIST_TYPE")
                            setDrawer({
                                type: data?.type,
                                club: data?.club,
                                returnToMapInfo: () => setDrawer(mapInfoReturnData),
                            })
                        }
                    })
                    window?.sessionStorage?.removeItem("MAP_LIST_TYPE")
                }

            } else {
                setFilterActive(true)
                if (mapFilters?.source === "groups" || mapFilters?.source === 'chat') {
                    setFilters({
                        ...filters, tg_group_member: true, friend_contact: false,
                    })
                    setGroups([])
                    if (mapFilters?.channel) {
                        setGroups([{ id: mapFilters?.id, name: mapFilters?.channel?.data?.name, image: mapFilters?.channel?.data?.image, stream_channel_id: mapFilters?.channel?.id }])
                        window?.sessionStorage?.setItem("MY_TG_GROUP_DETAILS_BACK_HANDLER", JSON.stringify({
                            channelType: mapFilters?.channel?.data?.type,
                            global: mapFilters?.source === "groups" ? true : false,
                            group: mapFilters?.id,
                            groupId: mapFilters?.channel?.id,
                            source: mapFilters?.source,
                            type: mapFilters?.source === "groups" ? 'my-group-details' : 'my-group-chat-details'
                        }))
                    }
                } else {
                    setGroups([])
                    setFilters({
                        ...filters, friend_contact: true, tg_group_member: false
                    })
                }
                setFilterApplied(prev => prev + 1)
            }
        };

        handleRouteChange(router?.pathname)

        // // Listen for route changes
        // router?.events?.on('routeChangeComplete', handleRouteChange);

        // // Clean up the event listener when the component is unmounted
        // return () => {
        //     router?.events?.off('routeChangeComplete', handleRouteChange);
        // };
    }, [router?.pathname]);

    const handleResize = () => {
        if (mapObject?._mapId) {
            const container = document.getElementById('mapbox-map');
            if (isListShow) {
                setDrawer();
                sessionStorage.removeItem('selected-club')
            } else {
                container.style.width = window?.innerWidth + 'px'
            }
            const heightOfMap = window?.innerHeight - (document.getElementById('main-nav-bar')?.clientHeight + document.getElementById('play-clubs-nav-bar')?.clientHeight + (searchActive ? 60 : 0))
            container.style.height = `${heightOfMap}px`
            mapObject?.resize()
        }
    }

    const fetchClubsData = async () => {
        setClubsLoading(true);
        try {
            const data = await fetchAllClubs(token, user?.id);
            if (isMounted) {

                setClubs(data.clubs);
                setOriginalClubs(data?.clubs);
                setFilteredClubs(data?.clubs);
            }
        } catch (err) {
            console.log('Error', err);
        } finally {
            setClubsLoading(false);
        }
    };

    /**
     * Initialize the map first instance with default coordinates
     */
    const initializeMap = () => {

        if (document.getElementsByClassName('mapboxgl-canvas')?.length) {
            return;
        }

        getUserLocation();

        const map = new mapboxgl.Map({
            container: mapContainerRef.current,
            style: 'mapbox://styles/tgadmin/cliseomtw008o01qy08jf6wk8',
            center: [DEFAULT_COORDINATES.lng, DEFAULT_COORDINATES.lat],
            zoom: takeValuesFromSession ? sessionStorage.getItem('map-zoom') : 4,
            minZoom: 4,
            maxZoom: 13,
            trackResize: true,
            bounds: (takeValuesFromSession && sessionStorage.getItem('map-bounds')) ? JSON.parse(sessionStorage.getItem('map-bounds')) : undefined,
        });

        // disable map rotation using right click + drag
        map.dragRotate.disable();

        window['mapObject'] = map;

        // STEP 1 - Load Images
        loadImages(map);

        setMapObject(map);

        setTimeout(() => {
            if (!takeValuesFromSession || !sessionStorage.getItem('map-bounds')) {
                document.querySelector('.mapboxgl-ctrl-geolocate')?.click()
            };
        }, 200);

        // STEP 3 - Add Events
        markerClickEvents(map);
        markerHoverEvents(map);


        //For ZoomIn & ZoomOut Control
        map.addControl(new mapboxgl.NavigationControl(), 'bottom-right');

        //For Follow to current position control
        map.addControl(new mapboxgl.GeolocateControl({
            fitBoundsOptions: {
                maxZoom: 8,
                minZoom: 4
            },
            showAccuracyCircle: false,
            showUserLocation: true,
            positionOptions: {
                enableHighAccuracy: true
            },
        }), 'bottom-right');

    }

    /**
     * Listen to marker hover events
     * @param {*} map 
     */
    const markerHoverEvents = (map) => {
        map.on('moveend', () => {
            setBounds(map.getBounds());
        });
        map.on('mouseenter', 'golf-clubs-clusters', () => {
            map.getCanvas().style.cursor = 'pointer';
        });
        map.on('mouseleave', 'golf-clubs-clusters', () => {
            map.getCanvas().style.cursor = '';
        });
    };

    const reSizingMarkersToSmall = () => {
        /**
    //      * mapObject?.getStyle()?.layers -> it returns the loaded layers of the map
    //      * Here we are checking if the layers we are expecting have been loaded or not
    //    */
        // if (mapObject) {
        //     mapObject?.getStyle()?.layers?.map(layer => {
        //         if (map_layers?.includes(layer?.id)) {
        //             mapObject.setLayoutProperty(layer?.id, 'icon-size', mapConstants.ICON_SIZE.ORIGINAL)
        //         }
        //     })
        // }
    }

    /**
     * Select the club to highlight
     */
    const selectTheClub = (e) => {
        // setDataForClub({})
        // setOpenClub({});
        // hideClubInfoPopup(e);
        // setIsListShow(false);
        setOpenClub(e?.features[0]?.properties);
        window?.sessionStorage.setItem("MAP_INFO_RETURN_DATA", JSON.stringify({
            type: CLUB_DETAILS,
            selectedClub: e?.features[0]?.properties,
        }));
        // window?.sessionStorage.setItem('selected_club_for_offers', JSON.stringify(e))
        setDrawer({
            type: CLUB_DETAILS,
            selectedClub: e?.features[0]?.properties,
            setRefreshMap: setRefreshMap,
            offerRestricted,
            customBackClickHandler: () => {
                setDrawer({
                    type: CLUB_DETAILS,
                    selectedClub: e?.features[0]?.properties,
                })
            },
            // dataForClub: dataForClub,
            // setDataForClub: setDataForClub,
            // filterActive: filterActive,
            // searchActive: searchActive,
            // offerRestricted: offerRestricted,
            // setShowCreateRequestForm: setShowCreateRequestForm,
            // setClubForRequest: setClubForRequest,
            // returnToMapInfo: returnToMapInfo,
            // activeTab: activeTab,
            // setActiveTab: setActiveTab,
            // setClubsRecommended
        })
    }

    // const returnToMapInfo = () => {
    //     window?.sessionStorage.setItem("MAP_INFO_RETURN_DATA", JSON.stringify({
    //         type: MAP_CLUB_INFO,
    //         // selectedClub: openClub,
    //         dataForClub: dataForClub,
    //         filterActive: filterActive,
    //         searchActive: searchActive,
    //         offerRestricted: offerRestricted,
    //         activeTab: activeTab,
    //         setActiveTab: setActiveTab,
    //         setDataForClub: () => setDataForClub,
    //         setClubForRequest: () => setClubForRequest,
    //         setClubsRecommended: () => setClubsRecommended
    //     }));
    //     setDrawer({
    //         type: MAP_CLUB_INFO,
    //         // selectedClub: openClub,
    //         dataForClub: dataForClub,
    //         setDataForClub: setDataForClub,
    //         filterActive: filterActive,
    //         searchActive: searchActive,
    //         offerRestricted: offerRestricted,
    //         setShowCreateRequestForm: setShowCreateRequestForm,
    //         setClubForRequest: setClubForRequest,
    //         returnToMapInfo: returnToMapInfo,
    //         activeTab: activeTab,
    //         setActiveTab: setActiveTab,
    //         setClubsRecommended
    //     })
    // }

    // useEffect(() => {
    //     if (Object?.entries(openClub || {})?.length > 0) {
    //         returnToMapInfo()
    //         window?.sessionStorage.removeItem('MAP_FILTERS')
    //     }
    // }, [openClub])

    // Add new useEffect for handling clubId from router query
    // useEffect(() => {
    //     if (router?.query?.clubId && clubs) {
    //         const club = clubs.find(club => club.id === parseInt(router.query.clubId));
    //         if (club) {
    //             selectTheClub({
    //                 features: [{
    //                     properties: club.properties,
    //                 }]
    //             });
    //         }
    //     }
    // }, [router?.query?.clubId, clubs]);

    /**
     * Hide the club detail popup
    */
    const hideClubInfoPopup = (e) => {
        if (mapObject) {
            mapObject.getCanvas().style.cursor = '';
            if (selectedLayer) {
                // The below code is to make the icon's size back to 0.75 from 1
                mapObject.setLayoutProperty(selectedLayer, 'icon-size', mapConstants.ICON_SIZE.ORIGINAL);
                if (e?.features?.[0]) {
                    mapObject.setLayoutProperty(e?.features?.[0]?.layer?.id, 'icon-size', [
                        'match',
                        ['get', 'id'],
                        +e?.features?.[0]?.properties?.id,
                        mapConstants.ICON_SIZE.ON_HOVER,
                        mapConstants.ICON_SIZE.ORIGINAL,
                    ]);
                }

                setIsPopupWindowOpen(false);
                setSelectedLayer(null);
                setSelectedClub({});
            }
        }
    }
    /*
    * Show the club detail popup 
    */
    const showClubPopuop = (e) => {
        if (mapObject) {
            mapObject.getCanvas().style.cursor = 'pointer';
            const newLayer = mapObject.queryRenderedFeatures(e.point)[0].layer;
            let newLayerId = newLayer?.id

            // When mouse leaving a layer and moving directly on another layer the moueleave event is not triggered
            // So, handling here with layer change on mouse move.
            // If the layer change then change the previous layer icon size.
            if (newLayerId && newLayerId !== previousLayer) {
                if (previousLayer) {
                    mapObject.setLayoutProperty(previousLayer, 'icon-size', mapConstants.ICON_SIZE.ORIGINAL);
                }
                setPreviousLayer(newLayerId)
            }

            if (e.features[0].properties.id) {
                getClubPopupDetail({
                    club: e.features[0].properties,
                    point: mapObject.project(e.features[0].geometry.coordinates),
                    layer: e.features[0].layer.id
                });
                // This increases the size of the marker when you hover it
                mapObject.setLayoutProperty(e.features[0].layer.id, 'icon-size', [
                    'match',
                    ['get', 'id'],
                    +e.features[0].properties.id,
                    mapConstants.ICON_SIZE.ON_HOVER,
                    mapConstants.ICON_SIZE.ORIGINAL,
                ]);
            }
        }
    }

    /** This one is used to show popup on map */
    const getClubPopupDetail = ({ club, point, layer }) => {
        const left = point.x;
        const top = point.y;
        let listingCardDialogLeft = left + 30;
        let listingCardDialogTop = top - 50;
        const listingCardDialogStyle = {
            width: 380 + 'px',
            height: 'auto',
            left: listingCardDialogLeft + 'px',
            top: listingCardDialogTop + 'px',
            position: 'absolute',
            overflow: 'hidden',
            background: 'white',
            borderRadius: '4px',
            justifyContent: 'space-evenly',
            display: 'flex',
            alignItems: 'center'
        };
        setListingCardDialogStyle(listingCardDialogStyle);
        setIsPopupWindowOpen(true);
        setSelectedLayer(layer);
        setSelectedClub(club);
    };

    /** 
     * Fetch User location
    */
    const getUserLocation = () => {
        if (!navigator.geolocation) {
            console.info('Geolocation is not supported by your browser');
            return;
        }
        navigator.geolocation.getCurrentPosition(geolocate, geolocateError, {
            maximumAge: 60000,
            timeout: 5000,
        });
    };

    const geolocateError = (positionError) => {
        console.error('positionError --->', positionError);
    };

    const geolocate = async (position) => {
        document.querySelector('.mapboxgl-ctrl-geolocate')?.click();
    };

    return (
        <div className={`flex relative w-full h-full ${(expandedMap && drawer?.type === undefined) ? 'md:w-[69%] lg:w-[80%]' : 'md:w-[69%] lg:w-[52%] xxl:w-[61%]'}`}
            style={{ transition: 'transform 500ms ease-in-out' }}
        >
            {!isMobile &&
                <div className={`absolute ${isMobile ? 'left-[8px] top-[8px]' : 'left-[20px] top-[20px]'} ${isMobile ? 'w-[62%]' : expandedMap ? 'w-[40%]' : 'md:w-[70%]  lg:w-[80%]'}`} style={{ zIndex: 1 }}>
                    <ClubAndLocationSearchBar
                        value={clubFilter || locationFilter}
                        setClubFilter={setClubFilter}
                        setLocationFilter={setLocationFilter}
                        clubList={filteredClubs}
                        setMapView={() => {
                            // if (currentView === view[2]) {
                            //     setCurrentView(view[1])
                            // }
                        }}
                    />
                </div>

            }
            {!isMobile && <div className='flex absolute right-[26px] top-[24px]' style={{ zIndex: 1, transition: 'height 0.5s ease-in-out' }}>
                {isTablet &&
                    <>
                        <MapListToggle
                            showMap={currentView === view[1]}
                            toggleMap={() => {
                                if (currentView === view[2]) {
                                    setCurrentView(view[1])
                                    return
                                } else {
                                    setCurrentView(view[2])
                                }
                                setRefreshMap(prev => !prev)
                            }} />
                        <div className='cursor-pointer px-sm'
                            onClick={() => {
                                if (offerRestricted) {
                                    setModal({
                                        title: 'Offer Limit',
                                        message: offerRestricted,
                                        type: 'warning',
                                    })
                                } else {
                                    setModal({
                                        title: 'Create Offer',
                                        img: {
                                            src: '/svg/offer-outline.svg',
                                            style: { height: 48, marginBottom: 10 },
                                        },
                                        width: 829,
                                        type: 'offer',
                                        refresh: setRefreshMap(prev => prev + 1),
                                    })
                                }
                            }}
                        >
                            <CreateOfferIcon />

                        </div>
                    </>
                }

                {(!isTablet && drawer?.type === undefined) && <ResizeIcon isMapExpanded={expandedMap} onClick={handleMapResize} />}
            </div>}

            <div className="flex-center flex-col w-full flex-1 bg-lightestgray " id="mapboxContainer">
                <Head>
                    <link href='https://api.tiles.mapbox.com/mapbox-gl-js/v2.12.1/mapbox-gl.css' rel='stylesheet' />
                </Head>
                <div className="w-full flex-1 flex relative" id="tg-map-wrapper">
                    <div className='map-container flex' id="mapbox-map" ref={mapContainerRef} />
                    {isPopupWindowOpen ? (
                        <div className='p-md'
                            style={{
                                ...listingCardDialogStyle,
                                background: 'transparent'
                            }}
                        >
                            <ClubCardDetailsNew
                                club={selectedClub}
                                user={user}
                            // setOpenClub={setOpenClub}
                            />
                        </div>
                    ) : null}
                </div>
            </div>
        </div>
    )
}
