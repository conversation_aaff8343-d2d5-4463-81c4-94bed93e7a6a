import React from 'react'
import AdminGamesHeader from '../../components/admin/headers/AdminGamesHeader'
import Table from '../../components/table/Table'
import GameAdminRequestCard from '../../components/admin/tables/games/GameAdminRequestCard'
import { ThreeDots } from 'react-loader-spinner'
import ListPagination from '../pagination/ListPagination'

export default function GamesHistory({
    sort,
    setSort,
    filter,
    setFilter,
    activeTab,
    setActiveTab,
    searchValue,
    updateValue: setSearchValue,
    setGames,
    setRequests,
    requestCount,
    requests,
    refresh,
    loader,
    headings,
    pnGames,
    page,
    setPage,
    isPnAdmin,
    setRefreshGames,
    limit,
    defaultSort
}) {

    return (
        <div className="w-full flex flex-col items-center px-md">
            <AdminGamesHeader
                sort={sort}
                setSort={setSort}
                filter={filter}
                setFilter={setFilter}
                activeTab={activeTab}
                setActiveTab={setActiveTab}
                searchValue={searchValue}
                updateValue={setSearchValue}
                setGames={() => setGames([])}
                setRequests={() => setRequests([])}
                requestsCount={requestCount}
                pnGames={pnGames}
                defaultSort={defaultSort}
            />
            {
                loader ?
                    <div className="pt-lg mt-lg">
                        <ThreeDots
                            visible={true}
                            height="50"
                            width="50"
                            color={"#098089"}
                            radius="9"
                            ariaLabel="three-dots-loading"
                            wrapperStyle={{}}
                            wrapperClass=""
                        />
                    </div>
                    :
                    <div className='w-[90%] min-h-[500px]'>
                        <Table headings={headings}>
                            {
                                requests?.map((request) => (
                                    <GameAdminRequestCard
                                        key={request?.game_id}
                                        request={request}
                                        refresh={refresh}
                                        isPnAdmin={isPnAdmin}
                                        setRefreshGames={setRefreshGames}
                                    />
                                ))
                            }
                        </Table>
                        <div className="w-full flex py-md items-center justify-center pr-xxl">
                            <ListPagination
                                currentPage={page}
                                totalCount={requestCount}
                                pageSize={limit}
                                onPageChange={(page) => setPage(page)}
                            />
                        </div>
                    </div>
            }
        </div>
    )
}