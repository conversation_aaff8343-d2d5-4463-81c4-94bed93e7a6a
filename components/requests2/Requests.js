import React, { useContext, useState, useEffect, useRef } from 'react'
import 'firebase/compat/auth'
import { UserContext } from '../../pages/_app'
import router from 'next/router'
import HistoryRequested from './HistoryCard/Requested'
import { ThreeDots } from 'react-loader-spinner'
import determineRequestTab from '../../utils/requests/determineRequestTab'
import ListPagination from '../pagination/ListPagination'
import ENDPOINTS from '../../constants/endpoints.json'
import { CreateRequest } from '../request-v3/CreateRequest'
import CustomButton from '../buttons/CustomButton'
import RequestEmptyState from '../common/RequestEmptyState'
import useFriends from '../../hooks/chat-v2/useFriends'
import streamOptions from '../../constants/streamOptions'
import constantOptions from '../../constants/constantOptions'
import FilterIcon from '../icons/FilterIcon'
import OptionsButton from '../chat-v2/common/OptionsButton'
import ReceivedRequestListItem from './ReceivedRequestListItem'
import AwaitingRequestListItem from './AwaitingRequestListItem'
import AcceptedRequestListItem from './AcceptedRequestListItem'
import AllHistoryCard from './HistoryCard/AllHistoryCard'
import SearchInput from '../common/SearchInput'
import { useRouter } from 'next/router';
import { ModalContext } from '../../context/ModalContext'
import useCheckDeviceScreen from '../../hooks/useCheckDeviceScreen'

const LIMIT = process.env.CONFIG.REQUEST_LIMIT
const {
    COMPLETED,
    FULFILLED,
    CANCELLED,
    DECLINED,
    ALL: ALL_REQUESTS,
} = constantOptions.REQUEST_TAB_FILTER_OPTIONS
const {
    RECEIVED_OPEN_V3,
    RECEIVED_ACCEPTED_V3,
    HISTORY_RECEIVED_V4,
    REQUESTED_OPEN,
    REQUESTED_ACCEPTED,
    HISTORY_REQUESTED,
} = ENDPOINTS
const { REQUEST_CHAT_GROUP } = streamOptions?.CHANNEL?.TYPES
const { REQUEST_TABS, REQUEST_FAQS } = constantOptions

function Requests({
    handleCreateRequest,
    filter,
    setRequestFilter,
    editRequestData,
    setEditRequestData,
    setClubsRecommended,
    tab,
    subType,
}) {
    const { user, token, streamChatClient, clevertap } = useContext(UserContext)
    const { allFriendsId, myFriends } = useFriends()
    const [requests, setRequests] = useState()
    const [loading, setLoading] = useState(false)
    const [refresh, setRefresh] = useState(0)
    const [showAcceptRequestPopup, setShowAcceptRequestPopup] = useState()
    const [showCreateRequestForm, setShowCreateRequestForm] = useState(false)
    const [page, setPage] = useState(1)
    const [totalCount, setTotalCount] = useState(0)
    const [tabsCount, setTabsCount] = useState()
    const [unreadChannels, setUnreadChannels] = useState([])
    const [redDotStatus, setRedDotStatus] = useState({})
    const [globalDropdownVisible, setGlobalDropdownVisible] = useState(false)
    const [refreshUnreadMessage, setRefreshUnreadMessage] = useState(0)
    const [streamChannels, setStreamChannels] = useState([])
    const [requestIds, setRequestIds] = useState([])
    const [hostData, setHostData] = useState([])
    const [requestCompleteData, setRequestCompleteData] = useState([])
    const [searchValue, setSearchValue] = useState('')
    /////////////////////////////////////
    const [requestId, setRequestId] = useState('')
    const router = useRouter();
    const { setModal } = useContext(ModalContext)
    const [activeRequestTab, setActiveRequestTab] = useState({})
    const { isDesktop, isWideScreen } = useCheckDeviceScreen()


    // useEffect(() => {
    //     window.scrollTo(0, 0);
    // }, [])

    useEffect(() => {
        const combinedArray = requests?.map((obj1) => {
            const obj2 = hostData?.find(
                (obj) => obj.request_id === obj1.request_id
            )
            return { ...obj1, ...(obj2 || {}) }
        })
        setRequestCompleteData(combinedArray)
        setLoading(false)
    }, [hostData])

    useEffect(() => {
        fetchTabsCount()
    }, [])

    useEffect(() => {
        if (requests?.length) {
            if (activeRequestTab?.key === 'history') {
                const requestIds = requests
                    ?.filter((req) => req?.requestor_user_id === user?.id)
                    ?.map((req) => req?.request_id)
                setRequestIds(requestIds)
                if (!requestIds?.length) {
                    setHostData([])
                }
            } else {
                const requestIds = requests?.map((req) => req?.request_id)
                setRequestIds(requestIds)
            }
        }
    }, [requests])

    useEffect(() => {
        if (
            requestIds?.length &&
            ['awaiting', 'accepted', 'history'].includes(activeRequestTab?.key)
        ) {
            fetchAllHosts()
        } else if (
            activeRequestTab?.key === 'history' &&
            filter === FULFILLED
        ) {
            setHostData([])
        }
    }, [requestIds])

    useEffect(() => {
        if (unreadChannels?.length) {
            fetchUnreadMessageStatus()
        }
    }, [unreadChannels])

    useEffect(() => {
        const fetchAllChannels = async () => {
            let allChannels = []
            let lastQueryResultLength = 0

            do {
                const filter = {
                    type: REQUEST_CHAT_GROUP,
                    members: { $in: [user?.id] },
                }
                const sort = [{ last_message_at: -1 }]
                const offset = allChannels?.length // Fetch next set of channels based on the current length
                const limit = 30

                const channels = await streamChatClient.queryChannels(
                    filter,
                    sort,
                    {
                        watch: true,
                        state: true,
                        offset,
                        limit,
                    }
                )

                lastQueryResultLength = channels.length
                allChannels = [...allChannels, ...channels]
            } while (lastQueryResultLength === 30)

            // Process the channels
            const streamChannelIds = allChannels
                .map((channel) =>
                    channel?.state?.unreadCount
                        ? channel?.data?.request_id
                        : null
                )
                .filter((id) => id !== null)

            setStreamChannels(allChannels)

            setUnreadChannels((prevUnreadChannels) => [
                ...new Set([...prevUnreadChannels, ...streamChannelIds])
            ])

        }

        if (streamChatClient) {
            fetchAllChannels()
        }
    }, [streamChatClient, refreshUnreadMessage])

    useEffect(() => {
        const newMessageListener = streamChatClient?.on(
            'message.new',
            async (e) => {
                setRefreshUnreadMessage((prev) => prev + 1)
            }
        )

        return () => {
            newMessageListener.unsubscribe()
        }
    }, [])

    useEffect(() => {
        setRequestCompleteData([])
        if (activeRequestTab?.key || refresh || filter) {
            switch (activeRequestTab?.key) {
                case 'received': {
                    fetchReceivedRequests()
                    break
                }
                case 'awaiting': {
                    fetchAwaitingRequests()
                    break
                }
                case 'accepted': {
                    fetchAcceptedRequests()
                    break
                }
                case 'history': {
                    fetchHistoryRequests()
                    break
                }
            }
        }
    }, [activeRequestTab?.key, refresh])

    useEffect(() => {
        const { tab, subtype, id } = router.query;
        setParams(tab, subtype, id)

    }, [router.query]);

    useEffect(() => {
        if (Object.keys(activeRequestTab || {}).length)
            routePath()
    }, [activeRequestTab?.key])

    // Update the tab click handlers to handle request IDs properly
    const routePath = (tab = activeRequestTab) => {
        // Clear request ID when changing tabs
        setRequestId('');

        // Determine appropriate tab/subtype
        let newTab, newSubtype;
        switch (tab.key) {
            case 'received':
                newTab = 'received';
                newSubtype = 'open';
                break;
            case 'awaiting':
                newTab = 'requested';
                newSubtype = 'open';
                break;
            case 'accepted':
                // Keep the current tab type (received/requested) for accepted
                newTab = 'received';
                newSubtype = 'accepted';
                break;
            case 'history':
                // Keep the current tab type for history
                newTab = 'received';
                newSubtype = 'history';
                break;
        }
        router.push({
            pathname: `/dashboard/play`,
            query: {
                type: 'requests',
                tab: newTab,
                subtype: newSubtype,
            },
        })
    };

    const setParams = async (tab, subtype, id) => {
        let subtab = subtype;
        if (id) {
            const response = await determineRequestTab({ request_id: id, user_id: user?.id });
            if (response?.code !== 200) {
                router.push({
                    pathname: `/dashboard/errors/404`,
                })
            }

            let res = response?.tab.split("/");
            tab = res[0];
            subtab = res[1];

            const matchingTab = (tab === 'requested' && subtab === 'open') ? 'awaiting' : (subtab === 'accepted') ? 'accepted' : (subtab === 'history') ? 'history' : tab;
            setRequestId(id);
            setActiveRequestTab(REQUEST_TABS.find((t) => t.key === matchingTab))
        } else {
            const matchingTab = (tab === 'requested' && subtab === 'open') ? 'awaiting' : (subtab === 'accepted') ? 'accepted' : (subtab === 'history') ? 'history' : tab;
            setActiveRequestTab(REQUEST_TABS.find((t) => t.key === matchingTab))
        }
    }

    const fetchAllHosts = async () => {
        try {
            await fetch(ENDPOINTS?.GET_ALL_HOSTS, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    ['Authorization']: `Bearer ${token}`,
                },
                body: JSON.stringify({
                    userId: user?.id,
                    requestIds: requestIds,
                }),
            })
                .then((response) => response.json())
                .then((data) => setHostData(data?.data))

        } catch (error) {
            console.log('Fetch Tabs Count------>', error)

        }
    }

    const fetchTabsCount = async () => {
        try {
            await fetch(ENDPOINTS?.FETCH_REQUESTS_COUNT_FOR_ALL_TABS, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    ['Authorization']: `Bearer ${token}`,
                },
                body: JSON.stringify({
                    user_id: user?.id,
                }),
            })
                .then((response) => response.json())
                .then((data) => setTabsCount(data?.data))
        } catch (error) {
            console.log('Fetch Tabs Count------>', error)
        }
    }

    const fetchReceivedRequests = async () => {
        try {
            setLoading(true)
            const receivedRequests = await fetch(RECEIVED_OPEN_V3, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    ['Authorization']: `Bearer ${token}`,
                },
                body: JSON.stringify({
                    userId: user?.id,
                }),
            })
                .then((data) => data.json())
                .then((data) => {
                    setRequests(data.data)
                })
                .catch((err) => {
                    console.log('error', err)
                })
            setLoading(false)
        } catch (error) {
            console.log(error)
        }
    }

    const fetchAwaitingRequests = async () => {
        try {
            setLoading(true)
            const awaitingRequests = await fetch(REQUESTED_OPEN, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    ['Authorization']: `Bearer ${token}`,
                },
                body: JSON.stringify({
                    userId: user?.id,
                }),
            })
                .then((data) => data.json())
                .then((data) => {
                    setRequests(data.data)
                })
                .catch((err) => {
                    setLoading(false)
                    console.log('error', err)
                })
            setLoading(false)
        } catch (error) {
            console.log(error)
        }
    }

    const fetchAcceptedRequests = async () => {
        try {
            setLoading(true)
            // Call both APIs in parallel
            const [receivedResponse, requestedResponse] = await Promise.all([
                // First API call - RECEIVED_ACCEPTED_V3
                fetch(RECEIVED_ACCEPTED_V3, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        ['Authorization']: `Bearer ${token}`,
                    },
                    body: JSON.stringify({
                        userId: user?.id,
                    }),
                }).then((data) => data.json()),

                // Second API call - REQUESTED_ACCEPTED
                fetch(REQUESTED_ACCEPTED, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        ['Authorization']: `Bearer ${token}`,
                    },
                    body: JSON.stringify({
                        userId: user?.id,
                    }),
                }).then((data) => data.json()),
            ])

            // Combine the responses
            const combinedData = [
                ...(receivedResponse.data || []),
                ...(requestedResponse.data || []),
            ]

            // Calculate total count from both responses

            setRequests(combinedData)
            setLoading(false)
        } catch (error) {
            console.log(error)
        }
    }

    const fetchHistoryRequests = async () => {
        try {
            setLoading(true)
            setTotalCount(0)

            const allHistory = await fetch(ENDPOINTS.ALL_HISTORY, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    ['Authorization']: `Bearer ${token}`,
                },
                body: JSON.stringify({
                    userId: user?.id,
                    page,
                    limit: LIMIT,
                    filter: filter,
                    search: searchValue,
                }),
            }).then((data) => data.json())
            if (activeRequestTab.key === 'history') {
                setTotalCount(allHistory?.totalCount)
                setRequests(allHistory?.data)
                if (!allHistory?.data?.length) {
                    setRequestCompleteData([])
                }
            } else {
                setTotalCount(allHistory?.totalCount)
            }
            setLoading(false)
        } catch (error) {
            console.log(error)
        }
    }

    useEffect(() => {
        if (activeRequestTab?.key === 'history' || filter) {
            fetchHistoryRequests()
        }
    }, [page, filter, searchValue])

    const fetchUnreadMessageStatus = async () => {
        try {
            await fetch(ENDPOINTS?.FETCH_UNREAD_MESSAGE_STATUS, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    ['Authorization']: `Bearer ${token}`,
                },
                body: JSON.stringify({
                    userId: user?.id,
                    requestIds: unreadChannels,
                }),
            })
                .then((response) => response.json())
                .then((data) => setRedDotStatus(data?.response))
        } catch (error) {
            console.log('Unread Message Status--------->', error)
        }
    }

    const renderTealDot = (key) => {
        switch (key) {
            case 'received':
                return redDotStatus?.receivedOpen

            case 'awaiting':
                return redDotStatus?.requestedOpen

            case 'accepted':
                return (
                    redDotStatus?.receivedAccepted ||
                    redDotStatus?.requestedAccepted
                )

            case 'history':
                return (
                    redDotStatus?.historyReceived ||
                    redDotStatus?.historyRequested
                )

            default:
                break
        }
    }

    const renderCount = (key) => {
        switch (key) {
            case 'received':
                return tabsCount?.view_requests_received_open || 0

            case 'awaiting':
                return tabsCount?.view_requests_requested_open || 0

            case 'accepted':
                return (
                    (tabsCount?.view_requests_received_accepted || 0) +
                    (tabsCount?.view_requests_requested_accepted || 0)
                )

            case 'history':
                return totalCount || 0

            default:
                break
        }
    }

    return (
        <>
            {showCreateRequestForm ? (
                <CreateRequest
                    editRequestData={editRequestData}
                    setShowCreateRequestForm={setShowCreateRequestForm}
                    setRefresh={setRefresh}
                    fetchTabsCount={fetchTabsCount}
                    setEditRequestData={setEditRequestData}
                    globalDropdownState={{
                        globalDropdownVisible,
                        setGlobalDropdownVisible,
                    }}
                />
            ) : (
                <>
                    <div className="flex flex-col w-full px-md ">
                        <div className="flex-center flex-col w-full flex-1">
                            <div className="flex flex-row flex-1 w-full">
                                <div className="relative w-full md:w-11/12 flex-1 flex flex-col ">
                                    <div className={`flex w-full pt-md justify-between ${!isDesktop && !isWideScreen ? 'overflow-x-scroll' : ''}`}>
                                        <div className="flex items-center">
                                            {REQUEST_TABS.map((tab) => {
                                                return (
                                                    <div
                                                        onClick={() => {
                                                            setActiveRequestTab(tab)
                                                            fetchTabsCount()
                                                        }}
                                                        key={tab?.key}
                                                        className={`cursor-pointer whitespace-nowrap border transition-all duration-300 transform hover:scale-105 active:scale-95 ${activeRequestTab?.key === tab?.key ? 'bg-darkteal text-white' : 'border-gray text-black'} mr-[12px] lg:mr-md rounded-full items-center px-md py-xs`}>
                                                        {tab?.label}
                                                        {tab?.key !== 'history' && (
                                                            <span className="ml-xs">
                                                                ({renderCount(tab?.key)})
                                                            </span>
                                                        )}
                                                        {renderTealDot(tab?.key) && (
                                                            <div className="absolute h-[7px] w-[7px] rounded-full bg-darkteal right-[2px] top-0 outline outline-2 outline-white"></div>
                                                        )}
                                                    </div>
                                                );
                                            })}
                                        </div>
                                        {isDesktop || isWideScreen ? (
                                            <div className="flex flex-row">
                                                {activeRequestTab?.key ===
                                                    'history' && (
                                                        <div>
                                                            <SearchInput
                                                                placeholder={
                                                                    'Search...'
                                                                }
                                                                value={searchValue}
                                                                onChange={(e) =>
                                                                    setSearchValue(
                                                                        e.target.value
                                                                    )
                                                                }
                                                                onClearInput={() =>
                                                                    setSearchValue('')
                                                                }
                                                                height={32}
                                                                className={
                                                                    'bg-white border border-lightestgray'
                                                                }
                                                                inputClassname={
                                                                    'bg-white '
                                                                }
                                                            />
                                                        </div>
                                                    )}

                                                {activeRequestTab?.key ===
                                                    'history' && (
                                                        <div className="ml-md" >
                                                            <OptionsButton
                                                                IconComponent={() => (
                                                                    <FilterIcon
                                                                        isActive={
                                                                            filter !==
                                                                            ALL_REQUESTS
                                                                        }
                                                                    />
                                                                )}
                                                                width={150}
                                                                top={35}
                                                                selectedOption={filter}
                                                                options={{
                                                                    [constantOptions
                                                                        .REQUEST_TAB_FILTER_OPTIONS
                                                                        .ALL]: () =>
                                                                            setRequestFilter(
                                                                                ALL_REQUESTS
                                                                            ),
                                                                    [constantOptions
                                                                        .REQUEST_TAB_FILTER_OPTIONS
                                                                        .COMPLETED]:
                                                                        () =>
                                                                            setRequestFilter(
                                                                                COMPLETED
                                                                            ),
                                                                    [constantOptions
                                                                        .REQUEST_TAB_FILTER_OPTIONS
                                                                        .CANCELLED]:
                                                                        () =>
                                                                            setRequestFilter(
                                                                                CANCELLED
                                                                            ),
                                                                    [constantOptions
                                                                        .REQUEST_TAB_FILTER_OPTIONS
                                                                        .DECLINED]:
                                                                        () =>
                                                                            setRequestFilter(
                                                                                DECLINED
                                                                            ),
                                                                    ...(tab ===
                                                                        'received'
                                                                        ? {
                                                                            [constantOptions
                                                                                .REQUEST_TAB_FILTER_OPTIONS
                                                                                .FULFILLED]:
                                                                                () =>
                                                                                    setRequestFilter(
                                                                                        FULFILLED
                                                                                    ),
                                                                        }
                                                                        : {}),
                                                                }}
                                                            />

                                                        </div>
                                                    )}

                                                {isDesktop || isWideScreen ? (
                                                    <>
                                                        <CustomButton color='tealTierBg' textColor='darkteal' borderColor='border-darkteal' marginX='20px' text='Log a Game' textSize='12' width={91} height={32}
                                                            onClick={() => {
                                                                setModal({ type: 'game-log', width: 450, fetchHistoryRequests })
                                                            }}
                                                        />

                                                        <CustomButton
                                                            width={151}
                                                            height={32}
                                                            onClick={() => {
                                                                setClubsRecommended(false)
                                                                handleCreateRequest()
                                                                clevertap.event.push(
                                                                    constantOptions
                                                                        ?.CLEVERTAP_EVENTS
                                                                        .REQUESTS_CREATE_NEW
                                                                )
                                                            }}
                                                            marginX="none"
                                                            text={'Create New Request'}
                                                            textSize={'12'}
                                                        />
                                                    </>
                                                ) : null}
                                            </div>
                                        ) : null}

                                    </div>
                                    <>
                                        {loading ? (
                                            <div className="flex w-full flex-center h-[500px]">
                                                <ThreeDots
                                                    visible={true}
                                                    height="50"
                                                    width="50"
                                                    color="#098089"
                                                    radius="9"
                                                    ariaLabel="three-dots-loading"
                                                    wrapperStyle={{}}
                                                    wrapperClass=""
                                                />
                                            </div>
                                        ) : (
                                            <div className="flex justify-between">
                                                <div className="w-full lg:w-[70%]">
                                                    <div className="flex flex-row justify-between">
                                                        <div className="flex flex-col py-md">
                                                            <div
                                                                key={
                                                                    activeRequestTab?.key
                                                                }
                                                                className="text-16 lg:text-18 font-medium transition-all duration-300 animate-fadeIn">
                                                                {
                                                                    activeRequestTab?.label
                                                                }
                                                                {activeRequestTab?.key ===
                                                                    'history' ? (
                                                                    <span className="ml-xs">
                                                                        (
                                                                        {totalCount}
                                                                        )
                                                                    </span>
                                                                ) : null}
                                                            </div>
                                                            <div
                                                                key={`${activeRequestTab?.key}-function`}
                                                                className="text-12 lg:text-14 text-grayLight transition-all duration-300 animate-fadeIn">
                                                                {
                                                                    activeRequestTab?.function
                                                                }
                                                            </div>
                                                        </div>

                                                        {!isDesktop && !isWideScreen ? (
                                                            <div className="flex flex-row items-center">
                                                                <OptionsButton
                                                                    IconComponent={() => (
                                                                        <FilterIcon
                                                                            isActive={
                                                                                filter !==
                                                                                ALL_REQUESTS
                                                                            }
                                                                        />
                                                                    )}
                                                                    width={150}
                                                                    top={35}
                                                                    selectedOption={filter}
                                                                    options={{
                                                                        [constantOptions
                                                                            .REQUEST_TAB_FILTER_OPTIONS
                                                                            .ALL]: () =>
                                                                                setRequestFilter(
                                                                                    ALL_REQUESTS
                                                                                ),
                                                                        [constantOptions
                                                                            .REQUEST_TAB_FILTER_OPTIONS
                                                                            .COMPLETED]:
                                                                            () =>
                                                                                setRequestFilter(
                                                                                    COMPLETED
                                                                                ),
                                                                        [constantOptions
                                                                            .REQUEST_TAB_FILTER_OPTIONS
                                                                            .CANCELLED]:
                                                                            () =>
                                                                                setRequestFilter(
                                                                                    CANCELLED
                                                                                ),
                                                                        [constantOptions
                                                                            .REQUEST_TAB_FILTER_OPTIONS
                                                                            .DECLINED]:
                                                                            () =>
                                                                                setRequestFilter(
                                                                                    DECLINED
                                                                                ),
                                                                        ...(tab ===
                                                                            'received'
                                                                            ? {
                                                                                [constantOptions
                                                                                    .REQUEST_TAB_FILTER_OPTIONS
                                                                                    .FULFILLED]:
                                                                                    () =>
                                                                                        setRequestFilter(
                                                                                            FULFILLED
                                                                                        ),
                                                                            }
                                                                            : {}),
                                                                    }}
                                                                />
                                                                <CustomButton
                                                                    buttonImage='/svg/Faq.svg'
                                                                    height={32}
                                                                    width={32}
                                                                    borderRadius={100}
                                                                    color='white'
                                                                    imageRightMargin={0}
                                                                    imageMarginBottom={0}
                                                                    onClick={() => {
                                                                        setModal({ type: 'faq', width: 343, activeRequestTab, REQUEST_FAQS })
                                                                    }}

                                                                />
                                                                <CustomButton
                                                                    displayOptions={true}
                                                                    buttonImage='/svg/CreateRequestWhite.svg'
                                                                    height={32}
                                                                    width={32}
                                                                    optionWidth={151}
                                                                    left={-110}
                                                                    top={35}
                                                                    imageRightMargin={0}
                                                                    imageMarginBottom={0}
                                                                    borderRadius={100}
                                                                    showArrow={false}
                                                                    marginX='0'
                                                                    options={{
                                                                        ["Log a Game"]: () => {
                                                                            setModal({ type: 'game-log', width: 450, fetchHistoryRequests })

                                                                        },
                                                                        ['Create New Request']: () => {
                                                                            setClubsRecommended(false)
                                                                            handleCreateRequest()
                                                                            clevertap.event.push(
                                                                                constantOptions
                                                                                    ?.CLEVERTAP_EVENTS
                                                                                    .REQUESTS_CREATE_NEW
                                                                            )
                                                                        },
                                                                    }}
                                                                />
                                                            </div>
                                                        ) : null}

                                                    </div>
                                                    {(activeRequestTab?.key ===
                                                    'history' && !isDesktop && !isWideScreen) && (
                                                        <div className='mb-sm'>
                                                            <SearchInput
                                                                placeholder={
                                                                    'Search...'
                                                                }
                                                                value={searchValue}
                                                                onChange={(e) =>
                                                                    setSearchValue(
                                                                        e.target.value
                                                                    )
                                                                }
                                                                onClearInput={() =>
                                                                    setSearchValue('')
                                                                }
                                                                height={32}
                                                                className={
                                                                    'bg-white border border-lightestgray'
                                                                }
                                                                inputClassname={
                                                                    'bg-white '
                                                                }
                                                            />
                                                        </div>
                                                    )}
                                                    {requests?.length ||
                                                        requestCompleteData?.length ? (
                                                        <div
                                                            className={`flex flex-col`}>
                                                            {(![
                                                                'awaiting',
                                                                'accepted',
                                                                'history',
                                                            ].includes(
                                                                activeRequestTab?.key
                                                            )
                                                                ? requests
                                                                : requestCompleteData
                                                            )?.map(
                                                                (
                                                                    request,
                                                                    i
                                                                ) => {
                                                                    if (
                                                                        activeRequestTab?.key ===
                                                                        'received'
                                                                    ) {
                                                                        return (
                                                                            <ReceivedRequestListItem
                                                                                key={
                                                                                    request?.request_id
                                                                                }
                                                                                request={
                                                                                    request
                                                                                }
                                                                                requestId={
                                                                                    requestId
                                                                                }
                                                                                setRequestId={setRequestId}
                                                                                isFtr={
                                                                                    request?.is_first_request
                                                                                }
                                                                                hasMessages={
                                                                                    streamChannels.filter(
                                                                                        (
                                                                                            channel
                                                                                        ) =>
                                                                                            channel
                                                                                                ?.data
                                                                                                ?.request_id ===
                                                                                            request?.request_id
                                                                                    )[0]
                                                                                        ?.state
                                                                                        ?.messageSets[0]
                                                                                        ?.messages
                                                                                        ?.length
                                                                                }
                                                                                hasUnreadMessages={
                                                                                    streamChannels.filter(
                                                                                        (
                                                                                            channel
                                                                                        ) =>
                                                                                            channel
                                                                                                ?.data
                                                                                                ?.request_id ===
                                                                                            request?.request_id
                                                                                    )[0]
                                                                                        ?.state
                                                                                        ?.unreadCount
                                                                                }
                                                                                refresh={() =>
                                                                                    setRefresh(
                                                                                        (
                                                                                            prev
                                                                                        ) =>
                                                                                            prev +
                                                                                            1
                                                                                    )
                                                                                }
                                                                                allFriendsId={
                                                                                    allFriendsId
                                                                                }
                                                                                showAcceptRequestPopup={
                                                                                    showAcceptRequestPopup
                                                                                }
                                                                                setShowAcceptRequestPopup={(
                                                                                    val
                                                                                ) =>
                                                                                    setShowAcceptRequestPopup(
                                                                                        val
                                                                                    )
                                                                                }
                                                                                fetchTabsCount={
                                                                                    fetchTabsCount
                                                                                }
                                                                                fetchUnreadMessageStatus={
                                                                                    fetchUnreadMessageStatus
                                                                                }
                                                                            />
                                                                        )
                                                                    } else if (
                                                                        activeRequestTab?.key ===
                                                                        'accepted'
                                                                    ) {
                                                                        return (
                                                                            <AcceptedRequestListItem
                                                                                key={
                                                                                    request?.request_id
                                                                                }
                                                                                isFtr={
                                                                                    request?.is_first_request
                                                                                }
                                                                                requestId={
                                                                                    requestId
                                                                                }
                                                                                setRequestId={setRequestId}

                                                                                hasMessages={
                                                                                    streamChannels.filter(
                                                                                        (
                                                                                            channel
                                                                                        ) =>
                                                                                            channel
                                                                                                ?.data
                                                                                                ?.request_id ===
                                                                                            request?.request_id
                                                                                    )[0]
                                                                                        ?.state
                                                                                        ?.messageSets[0]
                                                                                        ?.messages
                                                                                        ?.length
                                                                                }
                                                                                hasUnreadMessages={
                                                                                    streamChannels.filter(
                                                                                        (
                                                                                            channel
                                                                                        ) =>
                                                                                            channel
                                                                                                ?.data
                                                                                                ?.request_id ===
                                                                                            request?.request_id
                                                                                    )[0]
                                                                                        ?.state
                                                                                        ?.unreadCount
                                                                                }
                                                                                request={
                                                                                    request
                                                                                }
                                                                                allFriendsId={
                                                                                    allFriendsId
                                                                                }
                                                                                refresh={() =>
                                                                                    setRefresh(
                                                                                        (
                                                                                            prev
                                                                                        ) =>
                                                                                            prev +
                                                                                            1
                                                                                    )
                                                                                }
                                                                                fetchTabsCount={
                                                                                    fetchTabsCount
                                                                                }
                                                                                fetchUnreadMessageStatus={
                                                                                    fetchUnreadMessageStatus
                                                                                }
                                                                                streamChannels={
                                                                                    streamChannels
                                                                                }
                                                                            />
                                                                        )
                                                                    } else if (
                                                                        activeRequestTab?.key ===
                                                                        'awaiting'
                                                                    ) {
                                                                        return (
                                                                            <AwaitingRequestListItem
                                                                                key={
                                                                                    request?.request_id
                                                                                }
                                                                                requestId={
                                                                                    requestId
                                                                                }
                                                                                setRequestId={setRequestId}
                                                                                hasMessages={
                                                                                    streamChannels.filter(
                                                                                        (
                                                                                            channel
                                                                                        ) =>
                                                                                            channel
                                                                                                ?.data
                                                                                                ?.request_id ===
                                                                                            request?.request_id
                                                                                    )[0]
                                                                                        ?.state
                                                                                        ?.messageSets[0]
                                                                                        ?.messages
                                                                                        ?.length
                                                                                }
                                                                                hasUnreadMessages={
                                                                                    streamChannels.filter(
                                                                                        (
                                                                                            channel
                                                                                        ) =>
                                                                                            channel
                                                                                                ?.data
                                                                                                ?.request_id ===
                                                                                            request?.request_id
                                                                                    )[0]
                                                                                        ?.state
                                                                                        ?.unreadCount
                                                                                }
                                                                                streamChannels={
                                                                                    streamChannels
                                                                                }
                                                                                request={
                                                                                    request
                                                                                }
                                                                                refresh={() =>
                                                                                    setRefresh(
                                                                                        (
                                                                                            prev
                                                                                        ) =>
                                                                                            prev +
                                                                                            1
                                                                                    )
                                                                                }
                                                                                fetchTabsCount={
                                                                                    fetchTabsCount
                                                                                }
                                                                                fetchUnreadMessageStatus={
                                                                                    fetchUnreadMessageStatus
                                                                                }
                                                                                setShowCreateRequestForm={
                                                                                    setShowCreateRequestForm
                                                                                }
                                                                                setEditRequestData={
                                                                                    setEditRequestData
                                                                                }
                                                                                allFriendsId={
                                                                                    allFriendsId
                                                                                }
                                                                                myFriends={
                                                                                    myFriends
                                                                                }
                                                                            />
                                                                        )
                                                                    } else if (
                                                                        activeRequestTab?.key ===
                                                                        'history'
                                                                    ) {
                                                                        return (
                                                                            <AllHistoryCard
                                                                                key={
                                                                                    request?.request_id
                                                                                }
                                                                                request={
                                                                                    request
                                                                                }
                                                                                isFtr={
                                                                                    request?.is_first_request
                                                                                }
                                                                                requestId={
                                                                                    requestId
                                                                                }
                                                                                setRequestId={setRequestId}
                                                                                streamChannels={
                                                                                    streamChannels
                                                                                }
                                                                                allFriendsId={
                                                                                    allFriendsId
                                                                                }
                                                                                fetchTabsCount={
                                                                                    fetchTabsCount
                                                                                }
                                                                                fetchUnreadMessageStatus={
                                                                                    fetchUnreadMessageStatus
                                                                                }
                                                                                hasMessages={
                                                                                    streamChannels.filter(
                                                                                        (
                                                                                            channel
                                                                                        ) =>
                                                                                            channel
                                                                                                ?.data
                                                                                                ?.request_id ===
                                                                                            request?.request_id
                                                                                    )[0]
                                                                                        ?.state
                                                                                        ?.messageSets
                                                                                        ?.length
                                                                                }
                                                                                refresh={() =>
                                                                                    setRefresh(
                                                                                        (
                                                                                            prev
                                                                                        ) =>
                                                                                            prev +
                                                                                            1
                                                                                    )
                                                                                }
                                                                                hasUnreadMessages={
                                                                                    streamChannels.filter(
                                                                                        (
                                                                                            channel
                                                                                        ) =>
                                                                                            channel
                                                                                                ?.data
                                                                                                ?.request_id ===
                                                                                            request?.request_id
                                                                                    )[0]
                                                                                        ?.state
                                                                                        ?.unreadCount
                                                                                }
                                                                            />
                                                                        )
                                                                    }
                                                                }
                                                            )}
                                                        </div>
                                                    ) : (
                                                        <RequestEmptyState
                                                            message={`There's No request to Show/List`}
                                                        />
                                                    )}
                                                </div>
                                                {(isDesktop || isWideScreen) ? (
                                                    <div
                                                        style={{
                                                            height: 'fit-content',
                                                        }}
                                                        className={`bg-white rounded-lg w-[27%] border border-lightestgray border-[1.5px] ${activeRequestTab?.key === 'history' ? 'mt-[58px]' : 'mt-[78px]'} p-md`}>
                                                        <div className="text-20 font-medium mb-md">
                                                            FAQ
                                                        </div>
                                                        <div className="flex flex-col">
                                                            {REQUEST_FAQS[activeRequestTab?.key]?.map((faq, i) => {
                                                                if (
                                                                    activeRequestTab?.key ===
                                                                    'accepted'
                                                                ) {
                                                                    return (
                                                                        <div
                                                                            key={i}
                                                                            className={`flex text-14 px-md py-sm bg-lightestgray mb-md rounded-lg`}>
                                                                            <div
                                                                                className={`h-[5px] w-[5px] min-h-[5px] min-w-[5px] rounded-full bg-${i > 1 ? 'darkteal' : 'black'} mt-sm`}></div>
                                                                            <div className="pl-sm">
                                                                                {i >
                                                                                    1 && (
                                                                                        <span className="text-darkteal">
                                                                                            {
                                                                                                faq.player
                                                                                            }{' '}
                                                                                            :{' '}
                                                                                        </span>
                                                                                    )}
                                                                                {
                                                                                    faq.faq
                                                                                }
                                                                            </div>
                                                                        </div>
                                                                    )
                                                                } else {
                                                                    return (
                                                                        <div
                                                                            key={i}
                                                                            className="flex text-14 px-md py-sm bg-lightestgray mb-md rounded-lg">
                                                                            <div className="h-[5px] w-[5px] min-h-[5px] min-w-[5px] rounded-full bg-black mt-sm"></div>
                                                                            <span className="pl-sm">
                                                                                {
                                                                                    faq
                                                                                }
                                                                            </span>
                                                                        </div>
                                                                    )
                                                                }
                                                            })}
                                                        </div>
                                                    </div>
                                                ) : null}
                                            </div>
                                        )}
                                    </>
                                </div>
                            </div>
                        </div>

                        {['history'].includes(activeRequestTab?.key) &&
                            !loading &&
                            requestCompleteData?.length ? (
                            <div className="w-full flex-center py-md">
                                <ListPagination
                                    currentPage={page}
                                    totalCount={totalCount}
                                    pageSize={LIMIT}
                                    onPageChange={(page) => setPage(page)}
                                    loading={loading}
                                />
                            </div>
                        ) : null}
                    </div>
                </>
            )}
        </>
    )
}

export default Requests
