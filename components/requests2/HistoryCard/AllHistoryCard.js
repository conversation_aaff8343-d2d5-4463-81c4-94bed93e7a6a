import { useContext, useEffect, useState } from 'react'
import Moment from 'moment'
import { DrawerContext } from '../../../context/DrawerContext'
import { ModalContext } from '../../../context/ModalContext'
import { CustomStreamContext } from '../../../context/CustomStreamContext'
import CustomButton from '../../buttons/CustomButton'
import NameInitials from '../../common/NameInitials'
import { FtrTag } from '../../common/FtrTag'
import dateFormatter from '../../../utils/helper/dateFormatter'
import constantOptions from '../../../constants/constantOptions'
import MainIcon from '../../icons/RequestV2/MainIcon'
import { UserContext } from '../../../pages/_app'
import ReadMore from '../../../utils/truncate/readmore'
import useCheckDeviceScreen from '../../../hooks/useCheckDeviceScreen'
import Link from 'next/link'
const { GAME_INFO, GLOBAL_PROFILE_INFO } = constantOptions?.DRAWER_TYPE

const AllHistoryCard = ({
    request,
    isFtr,
    allFriendsId = [],
    refresh,
    streamChannels,
    requestId,
    setRequestId,
    fetchTabsCount,
    fetchUnreadMessageStatus,
}) => {
    const { setDrawer } = useContext(DrawerContext)
    const { setModal } = useContext(ModalContext)
    const { setOtherUserId } = useContext(CustomStreamContext)
    const { user } = useContext(UserContext)
    const hasUnread = streamChannels?.filter(channel => channel?.id === request?.stream_channel_id)[0]?.state?.unreadCount
    const { isDesktop, isWideScreen } = useCheckDeviceScreen()

    useEffect(() => {
        if (requestId && request?.request_id === requestId) {
            if (request?.requestor_user_id === user?.id) {
                setDrawer({
                    status: request?.status,
                    type: GAME_INFO,
                    global: true,
                    requestId: request?.request_id,
                    requested: true,
                    hostDeclinedCount: request?.hosts_declined?.length,
                    hosts: request?.hosts,
                    isAcceptedRequest: true,
                    isRequester: true,
                    streamChannels,
                    isAcceptedRequest: ['completed', 'fulfilled'].includes(request.status),
                    gameId: request?.game_id,
                    setRequestId,
                    refresh,
                    fetchTabsCount,
                    fetchUnreadMessageStatus,
                })
            } else {
                setDrawer({
                    status: request?.status,
                    type: GAME_INFO,
                    global: true,
                    requestId: request?.request_id,
                    received: true,
                    isFtr,
                    isAcceptedRequest: ['completed', 'fulfilled'].includes(request.status),
                    gameId: request?.game_id,
                    setRequestId,
                    refresh,
                    fetchTabsCount,
                    fetchUnreadMessageStatus,
                })
            }
        }
    }, [requestId])

    return (
        <div
            id="card"
            className="py-md px-lm bg-white rounded-xl border border-lightestgray mb-md relative shadow-lg cursor-pointer"
            onClick={(e) => {
                if (request?.requestor_user_id === user?.id) {
                    setDrawer({
                        type: GAME_INFO,
                        global: true,
                        requestId: request?.request_id,
                        requested: true,
                        hostDeclinedCount: request?.hosts_declined?.length,
                        hosts: request?.hosts,
                        isAcceptedRequest: true,
                        isRequester: true,
                        streamChannels,
                        isAcceptedRequest: ['completed', 'fulfilled'].includes(request.status),
                        gameId: request?.game_id,
                        setRequestId,
                        refresh,
                        fetchTabsCount,
                        fetchUnreadMessageStatus,
                        status: request?.status,
                        customBackClickHandler: () => {
                            setDrawer({
                                status: request?.status,
                                type: GAME_INFO,
                                global: true,
                                requestId: request?.request_id,
                                requested: true,
                                hostDeclinedCount: request?.hosts_declined?.length,
                                hosts: request?.hosts,
                                isRequester: true,
                                isAcceptedRequest: true,
                                streamChannels,
                                isAcceptedRequest: ['completed', 'fulfilled'].includes(request.status),
                                gameId: request?.game_id,
                                setRequestId,
                                refresh,
                                fetchTabsCount,
                                fetchUnreadMessageStatus,
                            })
                        }
                    })
                } else {
                    setDrawer({
                        status: request?.status,
                        type: GAME_INFO,
                        global: true,
                        requestId: request?.request_id,
                        received: true,
                        isFtr,
                        isAcceptedRequest: ['completed', 'fulfilled'].includes(request.status),
                        gameId: request?.game_id,
                        setRequestId,
                        refresh,
                        fetchTabsCount,
                        fetchUnreadMessageStatus,
                        customBackClickHandler: () => {
                            setDrawer({
                                status: request?.status,
                                type: GAME_INFO,
                                global: true,
                                requestId: request?.request_id,
                                received: true,
                                isFtr,
                                isAcceptedRequest: ['completed', 'fulfilled'].includes(request.status),
                                gameId: request?.game_id,
                                setRequestId,
                                refresh,
                                fetchTabsCount,
                                fetchUnreadMessageStatus,
                            })
                        }
                    })
                }
            }}>
            {/* Top section with game ID, status and action button */}
            <div className="flex justify-between mb-12">
                <div className={`flex items-center ${isFtr ? 'mt-sm' : ''}`}>
                    {isDesktop || isWideScreen ? (
                        <span className="text text-14">#{request.game_id}</span>
                    ) : null}
                    <span
                        className={`text-[9px] lg:text-12 font-medium  ${['completed', 'fulfilled'].includes(request?.status)
                            ? 'bg-lightGreen text-darkgreen'
                            : 'bg-lightRed text-red'
                            } p-xs rounded-md  lg:ml-sm`}>
                        {request?.status?.toUpperCase()}
                    </span>
                    {isFtr ? <FtrTag /> : null}
                </div>

                <MainIcon
                    type="delete"
                    classes="lg:mr-sm"
                    onClick={(e) => {
                        setModal({
                            img: {
                                src: '/svg/createrequest2.svg',
                                style: {
                                    height: 80,
                                },
                            },
                            type: 'delete-request-new',
                            request,
                            fetchRequests: refresh,
                            fetchTabsCount: () => { },
                            fetchUnreadMessageStatus: () => { },
                            requestType: 'requested/history',
                        })
                    }}
                />
            </div>

            {/* Club and date information */}
            <div className="flex mb-md justify-between">
                <div className='flex lg:items-center'>
                    <div className="min-w-18 lg:min-w-[24px] pt-xs mr-sm">
                        <img width={24} src="/svg/golf-post-small.svg" />
                    </div>
                    <div className="flex flex-col">
                        <div className="text-16 lg:text-18 font-medium">
                            {request?.club_name}
                        </div>
                        <div className="text-12 text-grayLight">
                            {request.status === 'completed'
                                ? Moment.utc(request.game_date).format(
                                    constantOptions?.DATE_FORMAT_Do_MM_YYYY
                                )
                                : dateFormatter(
                                    request?.start_date,
                                    request?.end_date
                                )}
                        </div>
                        <div className="">
                            <p className="text-12 lg:text-14" >
                                {!request?.message ? (
                                    "-"
                                ) : request?.message &&
                                    request?.message.length < 350 ? (
                                    request?.message
                                ) : (
                                    <ReadMore length={350}>
                                        {request?.message || ''}
                                    </ReadMore>
                                )}
                            </p>
                        </div>
                    </div>
                </div>
                {(!isDesktop && !isWideScreen) ? (
                    <span className="text-[11px] text-grayLight">#{request.game_id}</span>
                ) : null}
            </div>

            {/* Host or Requestor information */}
            <div className="flex items-center lg:mb-md">
                {request?.requestor_user_id === user?.id ? (
                    request?.hosts?.length ? (
                        <div className="w-full grid grid-cols-1 lg:grid-cols-2 gap-4">
                            {(request?.host_user_id === null ? request?.hosts : request?.hosts?.filter((host) => host?.id === request?.host_user_id)).map((host) => {
                                const hasUnread = streamChannels?.filter(
                                    (channel) =>
                                        channel?.id === host?.stream_channel_id
                                )[0]?.state?.unreadCount
                                if (request.host_user_id !== host.id && request.host_user_id !== null)
                                    return null

                                return (
                                    <>
                                        {host?.deleted_at === null ?
                                            <div
                                                className="flex h-50px items-center bg-lightestgray rounded-lg  px-sm justify-between"
                                                key={host?.id}
                                                onClick={(e) => {
                                                    e.stopPropagation()
                                                    setOtherUserId(host?.id)
                                                    setDrawer({
                                                        type: GLOBAL_PROFILE_INFO,
                                                        global: true,
                                                        fetchTabsCount,
                                                        fetchUnreadMessageStatus,
                                                        refresh
                                                    })
                                                }}>
                                                <div className="flex items-center">
                                                    {host?.profilePhoto ? (
                                                        <div
                                                            className="h-[24px] w-[24px] rounded-full mr-sm outline outline-1.5 outline-white"
                                                            style={{
                                                                backgroundImage: `url("${host?.profilePhoto}")`,
                                                                backgroundSize: 'cover',
                                                                backgroundPosition:
                                                                    'center',
                                                            }}></div>
                                                    ) : (
                                                        <NameInitials
                                                            cssClassName={'mr-sm'}
                                                            background={'bg-white'}
                                                            rounded={'full'}
                                                            height={24}
                                                            width={24}
                                                            user={{
                                                                first_name:
                                                                    allFriendsId.includes(
                                                                        host?.id
                                                                    )
                                                                        ? host?.name
                                                                        : host?.username,
                                                            }}
                                                            fontSize={16}
                                                        />
                                                    )}
                                                    <div className="">
                                                        {['completed', 'fulfilled'].includes(request?.status) &&
                                                            <div className="text-9 text-darkteal font-medium bg-tealTierBg rounded-16px px-2 py-xs w-fit mb-1">
                                                                HOST
                                                            </div>
                                                        }
                                                        <div className="text-14">
                                                            {allFriendsId.includes(
                                                                host?.id
                                                            )
                                                                ? host?.name
                                                                : host?.username}
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className="relative">
                                                    {hasUnread ? (
                                                        <div
                                                            style={{ zIndex: 100 }}
                                                            className="absolute h-[6px] w-[6px] rounded-full bg-darkteal right-[0px] top-[0px] outline outline-1 outline-white"></div>
                                                    ) : null}

                                                    <Link
                                                        href={`/dashboard/request${(!host?.stream_channel_id &&
                                                            !host?.sendbird_channel_id) ||
                                                            host?.stream_channel_id
                                                            ? '-chat'
                                                            : ''
                                                            }/${request.request_id}/${host?.id
                                                            }`}
                                                        as={`/dashboard/request${(!host?.stream_channel_id &&
                                                            !host?.sendbird_channel_id) ||
                                                            host?.stream_channel_id
                                                            ? '-chat'
                                                            : ''
                                                            }/${request.request_id}/${host?.id
                                                            }`}
                                                    >
                                                        <CustomButton
                                                            onClick={(e) => {
                                                                e.stopPropagation()
                                                            }}
                                                            marginX="none"
                                                            height={30}
                                                            width={30}
                                                            imageRightMargin={0}
                                                            imageMarginBottom="0"
                                                            color="darkteal"
                                                            buttonImage="/svg/Message-Icon-White.svg"
                                                        />
                                                    </Link>

                                                </div>
                                            </div>

                                            : <div className="flex h-50px items-center bg-lightestgray rounded-lg  px-sm  justify-between">
                                                <div className="flex items-center">
                                                    <div className='h-[24px] w-[24px] rounded-full bg-white font-bold text-grayLight flex-center'>?</div>
                                                    <div className="text-12 lg:text-14 ml-md">Deleted User</div>
                                                </div>
                                                <div className="relative">
                                                    {hasUnread ? (
                                                        <div
                                                            style={{ zIndex: 100 }}
                                                            className="absolute h-[6px] w-[6px] rounded-full bg-darkteal right-[0px] top-[0px] outline outline-1 outline-white"></div>
                                                    ) : null}

                                                    <CustomButton
                                                        onClick={(e) => {
                                                            e.stopPropagation()
                                                            setModal({
                                                                type: 'link-redirection-confirmation',
                                                                text: 'This request has already been closed. Would you still like to send a message?',
                                                                buttonColor: 'darkteal',
                                                                link: `/dashboard/request${(!host?.stream_channel_id &&
                                                                    !host?.sendbird_channel_id) ||
                                                                    host?.stream_channel_id
                                                                    ? '-chat'
                                                                    : ''
                                                                    }/${request.request_id}/${host?.id
                                                                    }`,
                                                                as: `/dashboard/request${(!host?.stream_channel_id &&
                                                                    !host?.sendbird_channel_id) ||
                                                                    host?.stream_channel_id
                                                                    ? '-chat'
                                                                    : ''
                                                                    }/${request.request_id}/${host?.id
                                                                    }`
                                                            })
                                                        }}
                                                        marginX="none"
                                                        height={30}
                                                        width={30}
                                                        imageRightMargin={0}
                                                        imageMarginBottom="0"
                                                        color="darkteal"
                                                        buttonImage="/svg/Message-Icon-White.svg"
                                                    />
                                                </div>
                                            </div>
                                        }
                                    </>
                                )
                            })}
                        </div>
                    ) : (
                        <div className="flex items-center">
                            <span className="h-[40px] w-[40px] rounded-full bg-tealTierBg flex-center mr-sm">
                                <img src="/icons/No-Hosts.svg" />
                            </span>
                            No hosts
                        </div>
                    )
                ) : (
                    <div className="w-full grid grid-cols-1 lg:grid-cols-2 gap-4">
                        {request?.requestor_username.includes("deleted@") ? (
                            <div className='flex h-50px items-center bg-lightestgray rounded-lg  px-sm  justify-between'>
                                <div className="flex items-center">
                                    <div className='h-[24px] w-[24px] rounded-full bg-white font-bold text-grayLight flex-center'>?</div>
                                    <div className="text-14 ml-md">Deleted User</div>
                                </div>
                                <div className="relative">
                                    {hasUnread ? (
                                        <div
                                            style={{ zIndex: 100 }}
                                            className="absolute h-[6px] w-[6px] rounded-full bg-darkteal right-[0px] top-[0px] outline outline-1 outline-white"></div>
                                    ) : null}

                                    <CustomButton
                                        onClick={(e) => {
                                            e.stopPropagation()
                                            setModal({
                                                type: 'link-redirection-confirmation',
                                                text: 'This request has already been closed. Would you still like to send a message?',
                                                buttonColor: 'darkteal',
                                                link: `/dashboard/request${(!request?.stream_channel_id &&
                                                    !request?.sendbird_channel_id) ||
                                                    request?.stream_channel_id
                                                    ? '-chat'
                                                    : ''
                                                    }/${request.request_id}/${request?.requestor_user_id
                                                    }`,
                                                as: `/dashboard/request${(!request?.stream_channel_id &&
                                                    !request?.sendbird_channel_id) ||
                                                    request?.stream_channel_id
                                                    ? '-chat'
                                                    : ''
                                                    }/${request.request_id}/${request?.requestor_user_id
                                                    }`
                                            })
                                        }}
                                        marginX="none"
                                        height={30}
                                        width={30}
                                        imageRightMargin={0}
                                        imageMarginBottom="0"
                                        color="darkteal"
                                        buttonImage="/svg/Message-Icon-White.svg"
                                    />
                                </div>
                            </div>
                        ) : (
                            <div
                                className="flex items-center px-4 py-2 bg-lightestgray rounded-lg mb-sm justify-between"
                                onClick={(e) => {
                                    e.stopPropagation()
                                    setOtherUserId(request?.requestor_user_id)
                                    setDrawer({
                                        type: GLOBAL_PROFILE_INFO,
                                        global: true,
                                        fetchTabsCount,
                                        fetchUnreadMessageStatus,
                                        refresh
                                    })
                                }}>
                                <div className="flex items-center">
                                    {request?.requestor_profile_photo ? (
                                        <div
                                            className="h-[24px] w-[24px] rounded-full mr-sm outline outline-1.5 outline-white"
                                            style={{
                                                backgroundImage: `url("${request?.requestor_profile_photo}")`,
                                                backgroundSize: 'cover',
                                                backgroundPosition: 'center',
                                            }}></div>
                                    ) : (
                                        <NameInitials
                                            cssClassName={'mr-sm'}
                                            background={'bg-white'}
                                            rounded={'full'}
                                            height={24}
                                            width={24}
                                            user={{
                                                first_name: allFriendsId.includes(
                                                    request?.requestor_user_id
                                                )
                                                    ? request?.requestor_full_name
                                                    : request?.requestor_username,
                                            }}
                                            fontSize={16}
                                        />
                                    )}

                                    <div className="pl-sm">
                                        <div className="text-9 text-darkteal font-medium bg-tealTierBg rounded-16px px-2 py-xs w-fit mb-1">
                                            REQUESTOR
                                        </div>
                                        <div className="text-14">
                                            {allFriendsId.includes(
                                                request?.requestor_user_id
                                            )
                                                ? request?.requestor_full_name
                                                : request?.requestor_username}{' '}
                                            {request?.number_of_players > 1
                                                ? `+${request?.number_of_players - 1}`
                                                : null}
                                        </div>
                                    </div>
                                </div>

                                <div className="relative">
                                    {hasUnread ? (
                                        <div
                                            style={{ zIndex: 100 }}
                                            className="absolute h-[6px] w-[6px] rounded-full bg-darkteal right-[0px] top-[0px] outline outline-1 outline-white"></div>
                                    ) : null}

                                    <CustomButton
                                        onClick={(e) => {
                                            e.stopPropagation()
                                            setModal({
                                                type: 'link-redirection-confirmation',
                                                text: 'This request has already been closed. Would you still like to send a message?',
                                                buttonColor: 'darkteal',
                                                link: `/dashboard/request${(!request?.stream_channel_id &&
                                                    !request?.sendbird_channel_id) ||
                                                    request?.stream_channel_id
                                                    ? '-chat'
                                                    : ''
                                                    }/${request.request_id}/${request?.requestor_user_id
                                                    }`,
                                                as: `/dashboard/request${(!request?.stream_channel_id &&
                                                    !request?.sendbird_channel_id) ||
                                                    request?.stream_channel_id
                                                    ? '-chat'
                                                    : ''
                                                    }/${request.request_id}/${request?.requestor_user_id
                                                    }`
                                            })
                                        }}
                                        marginX="none"
                                        height={30}
                                        width={30}
                                        imageRightMargin={0}
                                        imageMarginBottom="0"
                                        color="darkteal"
                                        buttonImage="/svg/Message-Icon-White.svg"
                                    />
                                </div>
                            </div>
                        )}

                    </div>
                )}
            </div>
        </div>
    )
}

export default AllHistoryCard
