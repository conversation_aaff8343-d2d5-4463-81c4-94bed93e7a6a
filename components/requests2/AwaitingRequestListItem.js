import React, { useContext, useEffect, useState } from 'react'
import CustomButton from '../buttons/CustomButton'
import { FtrTag } from '../common/FtrTag'
import dateFormatter from '../../utils/helper/dateFormatter'
import ReadMore from '../../utils/truncate/readmore'
import Link from 'next/link'
import { ModalContext } from '../../context/ModalContext'
import NameInitials from '../common/NameInitials'
import useThumbnail from '../../hooks/useThumbnail'
import { DrawerContext } from '../../context/DrawerContext'
import constantOptions from '../../constants/constantOptions'
import EditIcon from '../icons/EditIcon2'
import EditActionIcon from '../icons/EditActionIcon'
import requestDetailsForEdit from '../../utils/requests/requestDetailsForEdit'
import { CustomStreamContext } from '../../context/CustomStreamContext'

const AwaitingRequestListItem = ({
    hasMessages,
    hasUnreadMessages,
    streamChannels,
    request,
    refresh,
    activeTab,
    fetchTabsCount,
    fetchUnreadMessageStatus,
    setShowCreateRequestForm,
    setEditRequestData,
    allFriendsId,
    myFriends,
    isFtr,
    requestId,
    setRequestId,
}) => {
    const { setDrawer } = useContext(DrawerContext)
    const { setModal } = useContext(ModalContext)
    const { setOtherUserId } = useContext(CustomStreamContext)
    const [notAcceptableMessage, setNotAcceptableMessage] = useState(false)
    const [declineButtonColor, setDeclineButtonColor] = useState("lightestgray")
    const { GAME_INFO, GLOBAL_PROFILE_INFO } = constantOptions?.DRAWER_TYPE

    useEffect(() => {
        if (notAcceptableMessage) {
            setTimeout(
                () => setNotAcceptableMessage(false),
                3000
            )
        }
    }, [notAcceptableMessage])

    useEffect(() => {
        if (requestId && request?.request_id === requestId) {
            setDrawer({
                type: GAME_INFO,
                global: true,
                requestId: request?.request_id,
                requested: true,
                isFtr,
                hosts: request?.hosts,
                streamChannels,
                hostDeclinedCount: request?.hosts_declined?.length,
                setRequestId,
                fetchTabsCount,
                fetchUnreadMessageStatus,
                refresh
            })
        }
    }, [requestId])

    return (
        <div
            className="p-md bg-white rounded-lg border border-lightestgray mb-md relative shadow-lg cursor-pointer border border-lightestgray border-[1.5px]"
            onClick={(e) => {
                if (e.target.closest('button') || e.target.closest('a')) {
                    return;
                } setDrawer({
                    type: GAME_INFO,
                    global: true,
                    requestId: request?.request_id,
                    requested: true,
                    isFtr,
                    hosts: request?.hosts,
                    streamChannels,
                    hostDeclinedCount: request?.hosts_declined?.length,
                    gameId: request?.game_id,
                    setRequestId,
                    fetchTabsCount,
                    fetchUnreadMessageStatus,
                    refresh,
                    customBackClickHandler: () => {
                        setDrawer({
                            type: GAME_INFO,
                            global: true,
                            requestId: request?.request_id,
                            requested: true,
                            isFtr,
                            hosts: request?.hosts,
                            streamChannels,
                            hostDeclinedCount: request?.hosts_declined?.length,
                            gameId: request?.game_id,
                            setRequestId,
                            fetchTabsCount,
                            fetchUnreadMessageStatus,
                            refresh
                        })
                    }

                })
            }}
        >
            <div className="flex justify-between">
                <div className='text-12 lg:text-14'>#{request?.game_id}</div>
                <div className="flex items-center justify-end">
                    <EditActionIcon
                        height={32}
                        width={32}
                        borderRadius={8}
                        onClick={async (e) => {
                            e.stopPropagation()
                            const requestData = await requestDetailsForEdit(request?.request_id);
                            setEditRequestData(requestData)
                        }}
                    />
                    <div>
                        <CustomButton
                            height={32}
                            width={32}
                            buttonImage={declineButtonColor === "red" ? "/svg/Cross-Icon-White.svg" : "/svg/Cross-Icon-Gray.svg"}
                            color={declineButtonColor}
                            imageRightMargin={0}
                            imageMarginBottom='0'
                            onMouseEnter={() => setDeclineButtonColor("red")}
                            onMouseLeave={() => setDeclineButtonColor("lightestgray")}
                            onClick={() => {
                                setModal({
                                    width: 451,
                                    decliningRequest: true,
                                    img: {
                                        backgroundHeight: '80px',
                                        backgroundWidth: '80px',
                                        backgroundColor: 'tealTierBg',
                                        src: '/svg/Golf-Post-Big.svg',
                                    },
                                    type: 'delete-request',
                                    request,
                                    fetchRequests: refresh,
                                    requestType: "requested/open",
                                    fetchTabsCount,
                                    fetchUnreadMessageStatus,
                                    declineReasonMandatory: request?.hosts?.length
                                })
                            }}
                        />
                    </div>
                </div>
            </div>
            <div className="flex mb-sm">
                <div className="pt-xs mr-sm">
                    <img src="/svg/golf-post-small.svg" />
                </div>
                <div className="flex flex-col">
                    <div className="text-16 lg:text-18 font-medium">{request?.club_name}</div>
                    <div className="text-12 text-grayLight">
                        {dateFormatter(request?.start_date, request?.end_date)}
                    </div>
                </div>
            </div>
            <div className="mb-md grid lg:grid-cols-2 gap-x-8 gap-y-2">
                {request?.hosts?.length ? (
                    <>
                        {request?.hosts?.map((host) => {
                            const hasUnread = streamChannels?.filter(channel => channel?.id === host?.stream_channel_id)[0]?.state?.unreadCount
                            if (host?.deleted_at) {
                                return (
                                    <div key={host?.id} className="flex h-50px items-center bg-lightestgray rounded-lg  px-sm  justify-between mb-sm">
                                        <div className="flex items-center">
                                            <div className='h-[24px] w-[24px] rounded-full bg-white font-bold text-grayLight flex-center'>?</div>
                                            <div className="text-14 ml-md">Deleted User</div>
                                        </div>
                                        <Link
                                            shallow={true}
                                            href={`/dashboard/request${(!host?.stream_channel_id && !host?.sendbird_channel_id) || (host?.stream_channel_id) ? "-chat" : ""}/[request_id]/[host_id]`}
                                            as={`/dashboard/request${(!host?.stream_channel_id && !host?.sendbird_channel_id) || (host?.stream_channel_id) ? "-chat" : ""}/${request.request_id}/${host?.id}`}
                                            onClick={(e) => {
                                                e.stopPropagation();
                                            }}
                                        >
                                            <div className="relative" onClick={(e) => e.stopPropagation()}>
                                                {hasUnread ? (
                                                    <div style={{ zIndex: 100 }} className='absolute h-[6px] w-[6px] rounded-full bg-darkteal right-[0px] top-[0px] outline outline-1 outline-white'></div>
                                                ) : null}
                                                <CustomButton
                                                    marginX='none'
                                                    height={30}
                                                    width={30}
                                                    imageRightMargin={0}
                                                    imageMarginBottom='0'
                                                    color="darkteal"
                                                    buttonImage="/svg/Message-Icon-White.svg"
                                                    onClick={(e) => e.stopPropagation()}
                                                />
                                            </div>
                                        </Link>
                                    </div>
                                )
                            } else {
                                return (
                                    <div className="flex h-[40px] items-center bg-lightestgray rounded-lg px-sm justify-between" key={host?.id}
                                        onClick={(e) => {
                                            e.stopPropagation()
                                            setOtherUserId(host?.id)
                                            setDrawer({
                                                type: GLOBAL_PROFILE_INFO,
                                                global: true,
                                                fetchTabsCount,
                                                fetchUnreadMessageStatus,
                                                refresh
                                            })
                                        }}
                                    >
                                        <div className="flex items-center">
                                            {host?.profilePhoto ? (
                                                <div className='h-[24px] w-[24px] rounded-full mr-sm outline outline-1.5 outline-white'
                                                    style={{
                                                        backgroundImage: `url("${host?.profilePhoto}")`,
                                                        backgroundSize: 'cover',
                                                        backgroundPosition: 'center',
                                                    }}
                                                ></div>
                                            ) : (
                                                <NameInitials cssClassName={'mr-sm'} background={'bg-white'} rounded={'full'} height={24} width={24} user={{ first_name: allFriendsId.includes(host?.id) ? host?.name : host?.username }} fontSize={16} />
                                            )}
                                            <div className="">
                                                {host?.name}
                                            </div>
                                        </div>
                                        <Link
                                            shallow={true}
                                            href={`/dashboard/request${(!host?.stream_channel_id && !host?.sendbird_channel_id) || (host?.stream_channel_id) ? "-chat" : ""}/[request_id]/[host_id]`}
                                            as={`/dashboard/request${(!host?.stream_channel_id && !host?.sendbird_channel_id) || (host?.stream_channel_id) ? "-chat" : ""}/${request.request_id}/${host?.id}`}
                                            onClick={(e) => {
                                                e.stopPropagation();
                                            }}
                                        >
                                            <div className="relative" onClick={(e) => e.stopPropagation()}>
                                                {hasUnread ? (
                                                    <div style={{ zIndex: 100 }} className='absolute h-[6px] w-[6px] rounded-full bg-darkteal right-[0px] top-[0px] outline outline-1 outline-white'></div>
                                                ) : null}
                                                <CustomButton
                                                    marginX='none'
                                                    height={30}
                                                    width={30}
                                                    imageRightMargin={0}
                                                    imageMarginBottom='0'
                                                    color="darkteal"
                                                    buttonImage="/svg/Message-Icon-White.svg"
                                                    onClick={(e) => e.stopPropagation()}
                                                />
                                            </div>
                                        </Link>
                                    </div>
                                )
                            }
                        })}
                    </>
                ) : <div className="flex items-center text-12 lg:text-14">
                    <span className="h-[40px] w-[40px] rounded-full bg-tealTierBg flex-center mr-sm">
                        <img src="/icons/No-Hosts.svg" />
                    </span>
                    No hosts yet
                </div>
                }
            </div>
        </div>
    )
}

export default AwaitingRequestListItem
