import React, { useContext, useEffect } from 'react'
import CustomButton from '../buttons/CustomButton'
import Link from 'next/link'
import dateFormatter from '../../utils/helper/dateFormatter'
import NameInitials from '../common/NameInitials'
import EditActionIcon from '../icons/EditActionIcon'
import requestDetailsForEdit from '../../utils/requests/requestDetailsForEdit'
import { DrawerContext } from '../../context/DrawerContext'
import { ModalContext } from '../../context/ModalContext'
import { CustomStreamContext } from '../../context/CustomStreamContext'
import constantOptions from '../../constants/constantOptions'
import { UserContext } from '../../pages/_app'
import moment from 'moment'
import useCheckDeviceScreen from '../../hooks/useCheckDeviceScreen'

const AcceptedRequestListItem = ({
    request,
    refresh,
    streamChannels,
    fetchTabsCount,
    fetchUnreadMessageStatus,
    setEditRequestData,
    allFriendsId,
    isFtr,
    requestId,
    activeTab,
    setRequestId
}) => {
    const { setDrawer } = useContext(DrawerContext)
    const { user } = useContext(UserContext)
    const { setModal } = useContext(ModalContext)
    const { setOtherUserId } = useContext(CustomStreamContext)
    const [declineButtonColor, setDeclineButtonColor] = React.useState("lightestgray")
    const { GAME_INFO, GLOBAL_PROFILE_INFO } = constantOptions?.DRAWER_TYPE
    const hasUnread = streamChannels?.filter(channel => channel?.id === request?.stream_channel_id)[0]?.state?.unreadCount
    const { isMobile, isTablet, isDesktop, isWideScreen } = useCheckDeviceScreen()

    useEffect(() => {
        if (requestId && request?.request_id === requestId) {
            if (request?.requestor_user_id === user?.id) {
                setDrawer({
                    type: GAME_INFO,
                    global: true,
                    requestId: request?.request_id,
                    requested: true,
                    isRequester: true,
                    hostDeclinedCount: request?.hosts_declined?.length,
                    hosts: request?.hosts,
                    isAcceptedRequest: true,
                    isRequester: true,
                    streamChannels,
                    setRequestId,
                    fetchTabsCount,
                    fetchUnreadMessageStatus,
                    refresh
                })
            } else {
                setDrawer({
                    type: GAME_INFO,
                    global: true,
                    requestId: request?.request_id,
                    received: true,
                    isFtr,
                    isAcceptedRequest: true,
                    gameId: request?.game_id,
                    setRequestId,
                    fetchTabsCount,
                    fetchUnreadMessageStatus,
                    refresh
                })
            }
        }
    }, [requestId])

    return (
        <div
            className="p-lm mb-sm bg-white rounded-lg border border-lightestgray relative shadow-lg cursor-pointer border border-lightestgray border-[1.5px]"
            onClick={(e) => {
                if (e.target.closest('button') || e.target.closest('a')) {
                    return;
                }
                if (request?.requestor_user_id === user?.id) {
                    setDrawer({
                        type: GAME_INFO,
                        global: true,
                        requestId: request?.request_id,
                        requested: true,
                        hostDeclinedCount: request?.hosts_declined?.length,
                        hosts: request?.hosts,
                        isRequester: true,
                        isAcceptedRequest: true,
                        streamChannels,
                        gameId: request?.game_id,
                        setRequestId,
                        fetchTabsCount,
                        fetchUnreadMessageStatus,
                        refresh,
                        customBackClickHandler: () => {
                            setDrawer({
                                type: GAME_INFO,
                                global: true,
                                requestId: request?.request_id,
                                requested: true,
                                hostDeclinedCount: request?.hosts_declined?.length,
                                hosts: request?.hosts,
                                isRequester: true,
                                isAcceptedRequest: true,
                                streamChannels,
                                gameId: request?.game_id,
                                setRequestId,
                                fetchTabsCount,
                                fetchUnreadMessageStatus,
                                refresh
                            })
                        }
                    })
                } else {
                    setDrawer({
                        type: GAME_INFO,
                        global: true,
                        requestId: request?.request_id,
                        received: true,
                        isFtr,
                        isAcceptedRequest: true,
                        gameId: request?.game_id,
                        setRequestId,
                        fetchTabsCount,
                        fetchUnreadMessageStatus,
                        refresh,
                        customBackClickHandler: () => {
                            setDrawer({
                                type: GAME_INFO,
                                global: true,
                                requestId: request?.request_id,
                                received: true,
                                isFtr,
                                gameId: request?.game_id,
                                setRequestId,
                                fetchTabsCount,
                                fetchUnreadMessageStatus,
                                refresh
                            })
                        }
                    })
                }
            }}
        >
            <div className="flex justify-between">
                <div className='text-12 lg:text-14'>#{request?.game_id}</div>
                <div className="flex items-center justify-end">
                    {(request?.requestor_user_id !== user?.id && !request?.requestor_completed) &&
                        <EditActionIcon
                            height={32}
                            width={32}
                            borderRadius={8}
                            onClick={(e) => {
                                e.stopPropagation()
                                setModal({
                                    type: 'confirm-request',
                                    subType: 'edit',
                                    width: 451,
                                    img: {
                                        backgroundHeight: '80px',
                                        backgroundWidth: '80px',
                                        backgroundColor: 'tealTierBg',
                                        src: '/svg/Golf-Post-Big.svg',
                                    },
                                    request,
                                    refresh,
                                    fetchUnreadMessageStatus,
                                    fetchTabsCount
                                })
                            }}
                        />
                    }
                    <div>
                        <CustomButton
                            disabled={request?.requestor_completed || request?.host_completed}
                            height={32}
                            width={32}
                            buttonImage={declineButtonColor === "red" ? "/svg/Cross-Icon-White.svg" : "/svg/Cross-Icon-Gray.svg"}
                            color={declineButtonColor}
                            imageRightMargin={0}
                            imageMarginBottom='0'
                            onMouseEnter={() => setDeclineButtonColor("red")}
                            onMouseLeave={() => setDeclineButtonColor("lightestgray")}
                            onClick={() => {
                                if (request?.requestor_user_id === user?.id) {
                                    setModal({
                                        img: {
                                            backgroundHeight: '80px',
                                            backgroundWidth: '80px',
                                            backgroundColor: 'tealTierBg',
                                            src: '/svg/Golf-Post-Big.svg',
                                        },
                                        width: 451,
                                        type: 'delete-request',
                                        request,
                                        fetchRequests: refresh,
                                        requestType: "requested/accepted",
                                        fetchTabsCount,
                                        decliningRequest: true,
                                        declineReasonMandatory: true,
                                        fetchUnreadMessageStatus
                                    })
                                } else {
                                    setModal({
                                        type: 'confirm-request',
                                        subType: 'decline',
                                        width: 451,
                                        img: {
                                            backgroundHeight: '80px',
                                            backgroundWidth: '80px',
                                            backgroundColor: 'tealTierBg',
                                            src: '/svg/Golf-Post-Big.svg',
                                        },
                                        request,
                                        refresh,
                                        disabled: false,
                                        requestType: "received/accepted",
                                        fetchUnreadMessageStatus,
                                        fetchTabsCount,
                                        decliningRequest: true,
                                        declineReasonMandatory: true
                                    })
                                }
                            }}
                        />
                    </div>
                </div>
            </div>
            <div className="flex mb-sm">
                <div className="pt-xs mr-sm">
                    <img src="/svg/golf-post-small.svg" />
                </div>
                <div className="flex flex-col">
                    <div className="text-16 lg:text-18 font-medium">{request?.club_name}</div>
                    <div className="text-12 text-grayLight">
                        {moment.utc(request.game_date).format(constantOptions?.DATE_FORMAT_Do_MM_YYYY)}
                    </div>
                </div>
            </div>
            <div className={`flex ${(request?.hosts?.length > 1 && (isDesktop || isWideScreen)) ? 'w-[80%]' : ''} ${((!isDesktop && !isWideScreen)) ? 'flex-col' : 'flex-row'} justify-between items-center ${request?.requestor_username?.includes("deleted@") ? 'lg:justify-end' : ''}`}>
                <div className="w-full lg:flex-1 mb-sm grid grid-cols-1 lg:grid-cols-2 gap-1">
                    {user?.id !== request?.requestor_user_id ? (
                        <>
                            {request?.requestor_username?.includes("deleted@") ?
                                (<>
                                    <div className='flex h-50px items-center bg-lightestgray rounded-lg  px-sm  justify-between mb-sm'>
                                        <div className="flex items-center">
                                            <div className='h-[24px] w-[24px] rounded-full bg-white font-bold text-grayLight flex-center'>?</div>
                                            <div className="text-14 ml-md">Deleted User</div>
                                        </div>
                                        <div>
                                            <Link
                                                shallow={true}
                                                href={`/dashboard/request${(!request?.stream_channel_id && !request?.sendbird_channel_id) || (request?.stream_channel_id) ? "-chat" : ""}/[request_id]/[host_id]`}
                                                as={`/dashboard/request${(!request?.stream_channel_id && !request?.sendbird_channel_id) || (request?.stream_channel_id) ? "-chat" : ""}/${request.request_id}/${user?.id}`}>
                                                <div className="relative">
                                                    {hasUnread ? (
                                                        <div style={{ zIndex: 100 }} className='absolute h-[6px] w-[6px] rounded-full bg-darkteal right-[0px] top-[0px] outline outline-1 outline-white'></div>
                                                    ) : null}
                                                    <CustomButton
                                                        onClick={(e) => e.stopPropagation()}
                                                        marginX='none'
                                                        height={30}
                                                        width={30}
                                                        imageRightMargin={0}
                                                        imageMarginBottom='0'
                                                        color="darkteal"
                                                        buttonImage="/svg/Message-Icon-White.svg" />
                                                </div>
                                            </Link>
                                        </div>
                                    </div>
                                </>) :
                                (<>
                                    <div className="flex py-xs items-center bg-lightestgray rounded-lg mb-sm px-sm justify-between"
                                        onClick={(e) => {
                                            e.preventDefault()
                                            e.stopPropagation()
                                            setOtherUserId(request?.requestor_user_id)
                                            setDrawer({
                                                type: GLOBAL_PROFILE_INFO,
                                                global: true,
                                                fetchTabsCount,
                                                fetchUnreadMessageStatus,
                                                refresh
                                            })
                                        }}
                                    >
                                        <div className="flex items-center">
                                            {(request?.requestor_profile_photo) ? (
                                                <div className='h-[24px] w-[24px] rounded-full mr-sm outline outline-1.5 outline-white'
                                                    style={{
                                                        backgroundImage: `url("${request?.requestor_profile_photo}")`,
                                                        backgroundSize: 'cover',
                                                        backgroundPosition: 'center',
                                                    }}
                                                ></div>
                                            ) : (
                                                <NameInitials cssClassName={'mr-sm'} background={'bg-white'} rounded={'full'} height={24} width={24} user={{ first_name: (request?.requestor_full_name) }} fontSize={16} />
                                            )}
                                            <div className="">
                                                <div className="text-[9px] text-darkteal px-xs py-[1px] rounded-full bg-tealTierBg inline-flex items-center justify-center">{"REQUESTER"}</div>
                                                <div className="">
                                                    {request?.requestor_full_name} {request?.number_of_players > 1 && `+${request?.number_of_players - 1}`}
                                                </div>
                                            </div>
                                        </div>
                                        <Link
                                            shallow={true}
                                            href={`/dashboard/request${(!request?.stream_channel_id && !request?.sendbird_channel_id) || (request?.stream_channel_id) ? "-chat" : ""}/[request_id]/[host_id]`}
                                            as={`/dashboard/request${(!request?.stream_channel_id && !request?.sendbird_channel_id) || (request?.stream_channel_id) ? "-chat" : ""}/${request.request_id}/${user?.id}`}>
                                            <div className="relative">
                                                {hasUnread ? (
                                                    <div style={{ zIndex: 100 }} className='absolute h-[6px] w-[6px] rounded-full bg-darkteal right-[0px] top-[0px] outline outline-1 outline-white'></div>
                                                ) : null}
                                                <CustomButton
                                                    onClick={(e) => e.stopPropagation()}
                                                    marginX='none'
                                                    height={30}
                                                    width={30}
                                                    imageRightMargin={0}
                                                    imageMarginBottom='0'
                                                    color="darkteal"
                                                    buttonImage="/svg/Message-Icon-White.svg" />
                                            </div>
                                        </Link>
                                    </div>
                                </>)}
                        </>
                    ) : (
                        <>
                            {request?.hosts?.map((host) => {
                                const hasUnread = streamChannels?.filter(channel => channel?.id === host?.stream_channel_id)[0]?.state?.unreadCount
                                return (
                                    <>
                                        {host?.deleted_at === null ?
                                            <>
                                                <div className="flex py-xs items-center bg-lightestgray rounded-lg mb-sm px-sm justify-between" key={host?.id}
                                                    onClick={(e) => {
                                                        e.preventDefault()
                                                        e.stopPropagation()
                                                        setOtherUserId(host?.id)
                                                        setDrawer({
                                                            type: GLOBAL_PROFILE_INFO,
                                                            global: true,
                                                            fetchTabsCount,
                                                            fetchUnreadMessageStatus,
                                                            refresh
                                                        })
                                                    }}
                                                >
                                                    <div className="flex items-center">
                                                        {(host?.profilePhoto || request?.requestor_profile_photo) ? (
                                                            <div className='h-[24px] w-[24px] rounded-full mr-sm outline outline-1.5 outline-white'
                                                                style={{
                                                                    backgroundImage: `url("${request?.host_user_id !== undefined ? request?.requestor_profile_photo : host?.profilePhoto}")`,
                                                                    backgroundSize: 'cover',
                                                                    backgroundPosition: 'center',
                                                                }}
                                                            ></div>
                                                        ) : (
                                                            <NameInitials cssClassName={'mr-sm'} background={'bg-white'} rounded={'full'} height={24} width={24} user={{ first_name: allFriendsId.includes(host?.id) ? host?.name : host?.username }} fontSize={16} />
                                                        )}
                                                        <div className="">
                                                            {request?.game_host_user_id === host?.id ?
                                                                <div className="text-[9px] text-darkteal px-xs py-[1px] rounded-full bg-tealTierBg inline-flex items-center justify-center">{"HOST"}</div>
                                                                : null}
                                                            <div className="">
                                                                {allFriendsId.includes(host?.id) ? host?.name : host?.username}
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <Link
                                                        shallow={true}
                                                        href={`/dashboard/request${(!host?.stream_channel_id && !host?.sendbird_channel_id) || (host?.stream_channel_id) ? "-chat" : ""}/[request_id]/[host_id]`}
                                                        as={`/dashboard/request${(!host?.stream_channel_id && !host?.sendbird_channel_id) || (host?.stream_channel_id) ? "-chat" : ""}/${request.request_id}/${host?.id}`}>
                                                        <div className="relative">
                                                            {hasUnread ? (
                                                                <div style={{ zIndex: 100 }} className='absolute h-[6px] w-[6px] rounded-full bg-darkteal right-[0px] top-[0px] outline outline-1 outline-white'></div>
                                                            ) : null}

                                                            <CustomButton
                                                                onClick={(e) => e.stopPropagation()}
                                                                marginX='none'
                                                                height={30}
                                                                width={30}
                                                                imageRightMargin={0}
                                                                imageMarginBottom='0'
                                                                color="darkteal"
                                                                buttonImage="/svg/Message-Icon-White.svg" />
                                                        </div>
                                                    </Link>
                                                </div>
                                            </> : <>
                                                <div className="flex h-50px items-center bg-lightestgray rounded-lg  px-sm  justify-between mb-sm">
                                                    <div className="flex items-center">
                                                        <div className='h-[24px] w-[24px] rounded-full bg-white font-bold text-grayLight flex-center'>?</div>
                                                        <div className="text-14 ml-md">Deleted User</div>
                                                    </div>
                                                    <Link
                                                        shallow={true}
                                                        href={`/dashboard/request${(!host?.stream_channel_id && !host?.sendbird_channel_id) || (host?.stream_channel_id) ? "-chat" : ""}/[request_id]/[host_id]`}
                                                        as={`/dashboard/request${(!host?.stream_channel_id && !host?.sendbird_channel_id) || (host?.stream_channel_id) ? "-chat" : ""}/${request.request_id}/${host?.id}`}>
                                                        <div className="relative">
                                                            {hasUnread ? (
                                                                <div style={{ zIndex: 100 }} className='absolute h-[6px] w-[6px] rounded-full bg-darkteal right-[0px] top-[0px] outline outline-1 outline-white'></div>
                                                            ) : null}

                                                            <CustomButton
                                                                onClick={(e) => e.stopPropagation()}
                                                                marginX='none'
                                                                height={30}
                                                                width={30}
                                                                imageRightMargin={0}
                                                                imageMarginBottom='0'
                                                                color="darkteal"
                                                                buttonImage="/svg/Message-Icon-White.svg" />
                                                        </div>
                                                    </Link>
                                                </div>
                                            </>}
                                    </>
                                )
                            })}
                        </>
                    )}
                </div>

                {request?.hosts?.length > 1 ? (
                    null
                ) : (
                    <div className='w-full lg:w-auto flex justify-end '>
                        <CustomButton
                            marginX='none'
                            height={32}
                            width={(isMobile || isTablet) ? '100%' : 175}
                            color="green"
                            textColor="white"
                            text="Mark Complete"
                            buttonImage='/svg/tick.svg'
                            textSize={isMobile ? '12' : '14'}
                            fontWeight={isMobile ? 'normal' : 'normal'}
                            imageRightMargin='5px'
                            imageMarginBottom='0'
                            onClick={() => {
                                if (request?.requestor_user_id === user?.id) {
                                    setModal({
                                        width: 451,
                                        type: 'review-game',
                                        host: false,
                                        requestor: { id: request?.requestor_user_id },
                                        game_id: request.game_id,
                                        request,
                                        fetchRequests: refresh,
                                        fetchTabsCount,
                                        fetchUnreadMessageStatus
                                    })
                                } else {
                                    setModal({
                                        width: 451,
                                        type: 'game-experience-host',
                                        host: true,
                                        requestor: { id: request.requestor_user_id },
                                        game_id: request.game_id,
                                        request,
                                        fetchRequests: refresh,
                                        fetchUnreadMessageStatus,
                                        fetchTabsCount
                                    })
                                }
                            }}
                        />
                    </div>
                )}

            </div>
            {request?.hosts?.length > 1 &&
                <div className='w-full lg:w-auto flex justify-end '>
                    <CustomButton
                        marginX='none'
                        height={32}
                        width={(isMobile || isTablet) ? '100%' : 175}
                        color="green"
                        textColor="white"
                        text="Mark Complete"
                        buttonImage='/svg/tick.svg'
                        imageRightMargin='5px'
                        imageMarginBottom='0'
                        onClick={() => {
                            if (request?.requestor_user_id === user?.id) {
                                setModal({
                                    width: 451,
                                    type: 'review-game',
                                    host: false,
                                    requestor: { id: request?.requestor_user_id },
                                    game_id: request.game_id,
                                    request,
                                    fetchRequests: refresh,
                                    fetchTabsCount,
                                    fetchUnreadMessageStatus
                                })
                            } else {
                                setModal({
                                    width: 451,
                                    type: 'game-experience-host',
                                    host: true,
                                    requestor: { id: request.requestor_user_id },
                                    game_id: request.game_id,
                                    request,
                                    fetchRequests: refresh,
                                    fetchUnreadMessageStatus,
                                    fetchTabsCount
                                })
                            }
                        }}
                    />
                </div>
            }
        </div>
    )
}

export default AcceptedRequestListItem
