import React, { useEffect, useContext, useState } from 'react'
import dynamic from 'next/dynamic';
import { useRouter } from 'next/router'
import { UserContext } from '../../pages/_app'
import useClient from '../../graphql/useClient'
import { CHAT_REQUEST, UPDATE_REQUEST_CHAT, UPDATE_REQUEST_CHAT_REQUESTER } from '../../graphql/queries/request'
import getTier from '../../utils/tiers/getTier'
import useQuery from '../../graphql/hooks/useQuery'
import moment from 'moment'
import constantOptions from '../../constants/constantOptions'
import dateFormatter from '../../utils/helper/dateFormatter'
import ENDPOINTS from "../../constants/endpoints.json"

import {
    Chat,
    Channel,
    Thread,
    DefaultSuggestionList,
} from 'stream-chat-react';
import useCheckDeviceScreen from '../../hooks/useCheckDeviceScreen'
const DateSeparator = dynamic(() => import('../../components/chat-v2/ChannelWindow/DateSeparator'))
const ChannelWindow = dynamic(() => import('../../components/chat-v2/ChannelWindow/ChannelWindow'))
const NoConversationSelected = dynamic(() => import('../../components/chat-v2/ChannelPreview/NoConversationSelected'));
import TypingIndicator from '../chat-v2/ChannelWindow/TypingIndicator';
import EmptyMessageList from '../chat-v2/ChannelWindow/EmptyMessageList';
import CustomMessageLoadIndicator from '../common/CustomMessageLoadIndicator';
import streamOptions from '../../constants/streamOptions';
import { EmojiPicker } from 'stream-chat-react/emojis';
import { CustomStreamContext } from '../../context/CustomStreamContext';
import toastNotification from '../../utils/notifications/toastNotification';
import rollbar from '../../utils/rollbar';
import { ThreeDots } from 'react-loader-spinner';
import SystemMessage from '../chat-v2/ChannelWindow/SystemMessage';

const ChatAgainstRequest = React.memo(({ }) => {
    const router = useRouter()
    const client = useClient()
    const [loading, setLoading] = useState(false)
    const { request_id, host_id } = router.query
    const { user, streamChatClient, token } = useContext(UserContext)
    const { data, refresh } = useQuery(CHAT_REQUEST, { request_id, })
    const [channelURL, setChannelURL] = useState()
    const [userType, setUserType] = useState()
    const [game, setGame] = useState()
    const [playerInformation, setPlayerInformation] = useState()
    const { isMobile } = useCheckDeviceScreen()
    const { myFriendsId, } = useContext(CustomStreamContext)
    const [activeChannel, setActiveChannel] = useState(undefined)
    const [channelCreatedById, setChannelCreatedById] = useState()

    useEffect(() => {
        if (data) {
            getChatRequest(data?.request_by_pk)
            if (data?.request_by_pk?.chats?.length) {
                window?.sessionStorage.setItem("REQUEST_DATA_FOR_CHAT", JSON.stringify(data?.request_by_pk))
            }
        }
        if (data) {
            if (data?.request_by_pk?.chats?.length) {
                if (data?.request_by_pk?.chats?.length === 1) {
                    if (data?.request_by_pk?.chats[0].stream_channel_id === null) {
                        createChatChannel(data?.request_by_pk)
                    } else {
                        queryChannel(data?.request_by_pk?.chats[0].stream_channel_id)
                    }
                } else {
                    const channel = data?.request_by_pk?.chats.filter((channel) => channel?.club_member_id === host_id)
                    queryChannel(channel[0]?.stream_channel_id);
                }
            } else {
                createChatChannel(data?.request_by_pk)
            }
        }
    }, [data])

    useEffect(() => {
        if (data?.request_by_pk?.game && data?.request_by_pk?.game?.length === 1) {
            setGame(data?.request_by_pk?.game[0])
        }
    }, [data?.request_by_pk])

    useEffect(() => {
        if (channelURL) {
            shouldShowPlayerInformation(data?.request_by_pk)
        }
    }, [channelURL])

    // //This function sets has_message property = true when chat is initiated

    useEffect(() => {
        const newMessageListener = streamChatClient?.on('message.new', async (e) => {

            if (e?.message?.type === 'system') {
                return;
            }
            const newChatData = JSON.parse(sessionStorage.getItem("REQUEST_DATA_FOR_CHAT"))

            if (userType === 'host') {
                if (newChatData?.chats?.length > 0 && (!newChatData?.chats[0]?.has_messages || !newChatData?.chats[0]?.requester_has_message)) {
                    updateRequest(newChatData, channelCreatedById)
                }
            }
            else {
                if (newChatData?.chats?.length > 0) {
                    const requestorChat = newChatData?.chats?.filter(({ club_member_id }) => club_member_id === channelCreatedById)
                    updateRequest(newChatData, requestorChat?.[0]?.club_member_id)
                }
            }

            sendNotification(newChatData)
        })
        return () => {
            newMessageListener.unsubscribe()
        }
    }, [streamChatClient, activeChannel])

    const updateRequest = async (newChatData, clubMemberId) => {
        let URL = activeChannel?.data?.created_by?.id === user?.id ? UPDATE_REQUEST_CHAT : UPDATE_REQUEST_CHAT_REQUESTER
        try {
            await client.request(URL, {
                club_member_id: clubMemberId,
                request_id: newChatData?.id
            }).then(async () => {
                await refresh()
            })
        } catch (error) {
            console.log(error);
            rollbar.error('Error in onBeforeSendUserMessage', error);
        }
    }

    const sendNotification = async (newChatData) => {
        try {
            // if (newChatData?.chats && newChatData?.chats[0] && newChatData?.chats[0].club_member_id) {
                await fetch(ENDPOINTS?.SEND_CHAT_NOTIFICATION,
                    {
                        method: 'POST',
                        headers: {
                            ['Content-Type']: 'application/json',
                        },
                        body: JSON.stringify({
                            user,
                            host_id: newChatData?.chats[data?.request_by_pk.chats.map((chat) => chat.club_member_id).indexOf(host_id)]?.club_member_id || newChatData?.chats[0]?.club_member_id,
                            request: newChatData,
                            channelURL: newChatData?.chats[data?.request_by_pk.chats.map((chat) => chat.club_member_id).indexOf(host_id)]?.stream_channel_id || newChatData?.chats[0]?.stream_channel_id
                        }),
                    }
                ).then(() => {
                    // sessionStorage.removeItem("REQUEST_DATA_FOR_CHAT")
                })
            // }
        } catch (error) {
            rollbar.error('Error sending notification in sendNotification', error);
        }
    }

    const queryChannel = async (channelId) => {
        const [channel] = await streamChatClient.queryChannels({
            id: { $eq: channelId },
            $or: [{ hidden: { $eq: true } }, { hidden: { $eq: false } }]
        })
        setChannelCreatedById(channel?.data?.created_by?.id)
        setActiveChannel(channel)
    }

    const createChatChannel = async (request) => {
        try {
            await fetch(ENDPOINTS?.CREATE_REQUEST_CHAT_CHANNEL, {
                method: "POST",
                headers: {
                    'Content-Type': 'application/json',
                    ['Authorization']: `Bearer ${token}`,
                },
                body: JSON.stringify({
                    request_id: request?.id,
                    host_id: user?.id,
                    requestor_id: request?.user?.id,
                    channel_name: `Request #${request?.game_id} - ${request?.user?.first_name} ${request?.user?.last_name} and ${user?.first_name} ${user?.last_name}`,
                })
            })
                .then((response) => response.json())
                .then(async (data) => {
                    if (!data?.status) {
                        toastNotification({
                            type: constantOptions.TOAST_TYPE.ERROR,
                            message: data?.message,
                        })
                    } else {
                        queryChannel(data?.data?.channel_url)
                    }
                })
            setLoading(false)
            refresh()
        } catch (error) {
            console.log(error);
        }

    }

    const renderMentions = (props) => {
        const friendIds = myFriendsId
        const mentions = props?.values

        const modifiedMentions = mentions.map(user => {
            const userId = user.id;
            const isIdPresent = friendIds[userId];

            return {
                ...user,
                name: !isIdPresent ? user.username : user.name,
                first_name: !isIdPresent ? user.username : user.first_name,
                last_name: !isIdPresent ? "" : user.last_name,
            };
        });
        return <DefaultSuggestionList {...props} values={modifiedMentions} />
    }

    async function shouldShowPlayerInformation(request) {
        if (request?.user) {
            const userType = request?.user?.id === user?.id ? 'requestor' : 'host'

            let player
            let hasBeenAccepted = false
            if (userType === 'requestor') {
                if (channelURL) {
                    const {
                        club_member_id,
                        club_member,
                    } = request?.chats.filter(
                        ({ stream_channel_id }) =>
                            stream_channel_id === channelURL
                    )[0]

                    player = club_member
                    const acceptedRequest = request?.accepted_requests?.filter(
                        ({ host_id }) => host_id === club_member_id
                    )

                    hasBeenAccepted = acceptedRequest.length > 0
                }
            } else if (userType === 'host') {
                player = request.user
                hasBeenAccepted =
                    request?.accepted_requests?.filter(
                        ({ host_id }) => user.id === host_id
                    ).length > 0
            }

            if (player) {
                const clubs = player?.clubs?.map(({ club: { name } }) => name)
                let newPlayerInformation = {
                    deleted_at: player?.deleted_at,
                    ['Full Name']: `${player.first_name} ${player.last_name}`,
                    ['Phone']: player.phone,
                    ['Email']: player.email,
                    ['Tier']: getTier(player.tier),
                    ['Player\'s Clubs']: clubs.reduce(
                        (clubSentence, club, index) => {
                            if (clubs?.length === 1 || index === 0) {
                                return club
                            } else if (
                                clubs.length === 2 ||
                                index === clubs.length - 1
                            ) {
                                return `${clubSentence}${clubs.length === 2 ? '' : ','
                                    } and ${club}`
                            } else if (index < clubs.length - 1) {
                                return `${clubSentence}, ${club}`
                            }
                        },
                        ''
                    ),
                    ['Age']: player?.age,
                    ['Pace']: player?.pace,
                    ['Golf Index']: player?.handicap,
                    ['English Fluency']: player?.englishFluency,
                    ['Member Since']: player?.created_at ? moment(player.created_at).format('YYYY') : 'NA'
                }

                if (!player?.deleted_at) {
                    delete newPlayerInformation['deleted_at'];
                }

                if (userType === 'requestor' && !hasBeenAccepted) {
                    delete newPlayerInformation['Full Name']
                    delete newPlayerInformation['Email']
                    delete newPlayerInformation['Phone']
                }
                if (userType === 'requestor') {
                    delete newPlayerInformation['Age']
                }

                setPlayerInformation(newPlayerInformation)
            } else {
                setPlayerInformation()
            }
        }
    }

    async function getChatRequest(request) {
        shouldShowPlayerInformation(request)
        if (request?.user?.id === user?.id) {
            setUserType('requestor')
            const hostChatChannel = request?.chats?.filter(
                ({ club_member_id }) => club_member_id === host_id
            )
            setChannelURL(hostChatChannel[0]?.stream_channel_id)
        } else {
            setUserType('host')
            const hostChatChannel = request?.chats?.filter(
                ({ club_member_id }) => club_member_id === user?.id
            )
            if (hostChatChannel?.length === 1) {
                setChannelURL(hostChatChannel[0].stream_channel_id)
            }
        }
    }

    return (
        <div className="flex-1 flex-center flex-col">
            {!isMobile && (
                <div className="w-full lg:container py-md">
                    <div
                        style={{ top: 10 }}
                        onClick={() => router.back()}
                        className="text-gray text-sm uppercase hover:underline cursor-pointer flex items-center">
                        <img
                            style={{
                                transform: 'rotate(90deg)',
                                marginRight: 8,
                            }}
                            src="/svg/arrow-down.svg"
                            width={12}
                            height={12}
                        />
                        Back to Requests
                    </div>
                </div>

            )}
            <div className="lg:container flex w-full h-full">

                {!isMobile ?
                    <div
                        className="flex flex-col relative w-full"
                        style={{ maxWidth: 400 }}>
                        <div className="flex flex-col absolute inset-0 overflow-scroll pb-lg">
                            <div
                                className="bg-white shadow-md py-6 px-10 mr-12 rounded-lg flex flex-col"
                                style={{ maxWidth: 400 }}>
                                <div className="text-21 flex items-center mb-xs pb-sm">
                                    Request
                                    #{data?.request_by_pk?.game_id}
                                </div>

                                <div className="text-gray text-12 flex">
                                    <div>
                                        <img className='mr-sm' src="/svg/GolfPostDarkTeal.svg" />
                                    </div>
                                    <div className='flex flex-col'>
                                        <div className=''>Club Name</div>
                                        <div className="mb-md text-16 text-black">{data?.request_by_pk?.club?.name}</div>
                                    </div>
                                </div>
                                <div className="text-gray text-12 flex">
                                    <div>
                                        <img className='mr-sm' src="/svg/Calendar_New.svg" />
                                    </div>
                                    <div className='flex flex-col'>
                                        <div className=''>Requested Dates</div>
                                        <div className="mb-md text-16 text-black">{dateFormatter(data?.request_by_pk?.start_date, data?.request_by_pk?.end_date)}</div>
                                    </div>
                                </div>
                                <div className="text-gray text-12 flex">
                                    <div>
                                        <img className='mr-sm' src="/svg/Players.svg" />
                                    </div>
                                    <div className='flex flex-col'>
                                        <div className=''># of Players</div>
                                        <div className="mb-md text-16 text-black">
                                            {data?.request_by_pk?.number_of_players}
                                        </div>
                                    </div>
                                </div>

                                <div className="text-12 mb-md break-word">{data?.request_by_pk?.message}</div>


                                {data?.request_by_pk?.criteria?.accompanied_only
                                    ? <div className="">
                                        Accompanied Only
                                    </div>
                                    : null}


                            </div>
                            {/* {playerInformation && (
                        <div
                            className="bg-white shadow-md py-6 px-10 mr-12 rounded-lg flex flex-col mt-md"
                            style={{ maxWidth: 400 }}>
                            <div className="text-2xl font-roboto flex items-center mb-md border-b border-lightgray pb-sm">
                                {data?.request_by_pk?.user ? data?.request_by_pk?.user?.id === user?.id ? 'Host' : 'Requester' : 'Player'} Information
                            </div>
                            {
                                (!playerInformation?.deleted_at && playerInformation) ? Object.entries(playerInformation).map(
                                    ([label, value]) => (
                                        <div key={label}>
                                            <div className="font-normal text-gray text-sm">
                                                {label}
                                            </div>
                                            <a href={label === 'Email' ? `mailto:${value}` : label === 'Phone' ? `tel:${value}` : ''}
                                                className={`mb-md ${(label === 'Email' || label === 'Phone')
                                                    ? 'hover:underline cursor-pointer'
                                                    : 'capitalize'
                                                    }`}>
                                                {value}
                                            </a>
                                        </div>
                                    )
                                ) :
                                    <p className="text-center text-sm text-red pb-md">This user does not exist on Thousand Greens Network.</p>}
                        </div>
                    )} */}
                        </div>
                    </div>
                    : null}
                <div className="h-full flex-1 pb-lg flex flex-col rounded-lg">
                    {activeChannel === undefined ?
                        (
                            <div className='w-full flex-center h-[500px]'>
                                <ThreeDots
                                    visible={true}
                                    height="50"
                                    width="50"
                                    color="#098089"
                                    radius="9"
                                    ariaLabel="three-dots-loading"
                                    wrapperStyle={{}}
                                    wrapperClass=""
                                />
                            </div>
                        ) : (
                            <div className={`h-full rounded-lg request-chat`}>
                                <Chat client={streamChatClient} theme='' useImageFlagEmojisOnWindows={true} >
                                    <Channel
                                        MessageSystem={SystemMessage}
                                        channel={activeChannel}
                                        QuotedMessage={() => <></>}
                                        DateSeparator={DateSeparator}
                                        TypingIndicator={TypingIndicator}
                                        EmptyStateIndicator={EmptyMessageList}
                                        LoadingIndicator={() => <CustomMessageLoadIndicator />}
                                        multipleUploads={true}
                                        maxNumberOfFiles={streamOptions?.MAX_FILES_ALLOWED_TO_UPLOAD}
                                        EmptyPlaceholder={!isMobile && <NoConversationSelected />}
                                        AutocompleteSuggestionList={(props) => {
                                            if (props?.currentTrigger === '@') {
                                                return renderMentions(props)
                                            } else {
                                                return <DefaultSuggestionList {...props} />
                                            }
                                        }}
                                        EmojiPicker={EmojiPicker}
                                    >
                                        <ChannelWindow
                                            // setReRenderForcefully={setReRenderForcefully}
                                            request={data?.request_by_pk}
                                            chat={data?.request_by_pk?.chats?.filter(({ stream_channel_id }) => stream_channel_id === channelURL)}
                                        />
                                        <Thread />
                                    </Channel>
                                </Chat >
                            </div>
                        )
                    }
                </div>
            </div>
        </div>
    )
})

export default ChatAgainstRequest
