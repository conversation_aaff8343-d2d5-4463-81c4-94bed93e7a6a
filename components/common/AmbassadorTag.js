import React from 'react'

const AmbassadorTag = ({ className, smallTag }) => {
    return (
        <div className={`${className} flex border border-lightestgray bg-white h-[22px] ${smallTag ? 'w-[22px] flex-center' : 'pl-[3px] w-[117px] items-center'} rounded-full shadow-lg`}>
            <div className={`h-[16px] w-[16px] bg-green rounded-full flex-center ${smallTag ? '' : 'mr-sm'}`}>
                <img className='mb-[1px]' src='/icons/star.svg' />
            </div>
            <div className='fade-in text-10'>
                {smallTag ? '' : 'TG AMBASSADOR'}
            </div>
        </div>
    )
}

export default AmbassadorTag    