import React, { useState, useEffect, useContext } from "react";
import OtpInput from "react-otp-input";
import XIcon from "../../icons/RequestV2/XIcon";
import END_POINTS from "../../../constants/endpoints.json"
import Link from "next/link";
import { useRouter } from "next/router";
import firebase from 'firebase/compat/app'
import 'firebase/compat/auth'
import { ThreeDots } from 'react-loader-spinner'
import CustomButton from "../../buttons/CustomButton";
import { UserContext } from "../../../pages/_app";
import constantOptions from "../../../constants/constantOptions";
// import Checkbox from "../Checkbox";
import Checkbox from "../../chat-v2/CheckBox";
import MESSAGES from "../../../constants/messages";

const Verify2FaModal = ({
    email,
    password,
    setModalVisible,
    form,
    setForm,
    resendRequestOtp,
    resendOtpResData,
    oldUser = false,
    refresh = () => { },
    checkAdmin
}) => {
    const [code, setCode] = useState()
    const [error, setError] = useState(false)
    const [loading, setLoading] = useState(false)
    const [counter, setCounter] = useState(120)
    const [isPhoneVerified, setIsPhoneVerified] = useState(false)
    const [resendOtpError, setResendOtpError] = useState(false)
    const [resendOtpErrorMessage, setResendOtpErrorMessage] = useState('')
    const [rememberMe, setRememberMe] = useState(false)
    const router = useRouter()

    useEffect(() => {
        const keyDownHandler = event => {

            if (event.key === 'Enter' && isPhoneVerified) {
                setLoading(true)
                setModalVisible()
                router.push('/register/personal-profile')
            }
        };

        document.addEventListener('keydown', keyDownHandler);

        return () => {
            document.removeEventListener('keydown', keyDownHandler);
        };
    }, [isPhoneVerified]);

    useEffect(() => {
        if (resendOtpResData && resendOtpResData?.remainingTime > 0) {

            setResendOtpErrorMessage(resendOtpResData?.message)
            setCounter(resendOtpResData?.remainingTime)
            setResendOtpError(true)
        }
    }, [resendOtpResData])

    useEffect(() => {
        const timer =
            counter > 0 && setInterval(() => setCounter(counter - 1), 1000)

        if (!counter) {
            setResendOtpError(false)
        }
        return () => clearInterval(timer);
    }, [counter]);

    const verifyOtp = async (values) => {
        setLoading(true)
        const response = await fetch(END_POINTS.ADMIN_VERIFY_OTP, {
            method: 'POST',
            credentials: 'same-origin',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ email: email, otp: code, rememberDevice: rememberMe, deviceId: localStorage.getItem('device_id') }),
        })
            .then((data) => data.json())
            .then((data) => {
                if (data?.status) {
                    loginUser(values)
                } else {
                    setError(data?.message)
                }
            }).catch((e) => {
                setError(e?.message)
            })
            .finally(() => {
                setLoading(false)
            })
    }

    async function loginUser(values) {
        setLoading(true)
        const response = await fetch(END_POINTS.GET_EMAIL_BY_USERNAME, {
            method: 'POST',
            credentials: 'same-origin',
            body: JSON.stringify({ username: email }),
        })
            .then((data) => data.json())
            .catch(console.log)

        if (response?.deactivated) {

            setError(MESSAGES?.login?.deactivated)
            setLoading(false)
        } else if (response?.email || values?.email === '<EMAIL>') {

            firebase
                .auth()
                .signInWithEmailAndPassword(
                    response.email ? response.email : '<EMAIL>',
                    password
                )
                .then((user) => {
                    console.log(user)
                })
                .catch((e) => {
                    if (e?.code === "auth/too-many-requests") {
                        setError(e?.message)
                    } else {
                        setError(MESSAGES?.login?.incorrect)
                    }
                })
                .finally(() => {
                    setLoading(false)
                })
        } else {
            setError(MESSAGES?.login?.incorrect)
            setLoading(false)
        }
    }
    return (
        <div
            id="modalBackground"
            className="fixed inset-0 flex justify-center px-md pt-xxl md:pt-0 items-start md:items-center"
            style={{
                opacity: 1,
                transition: 'opacity ease-in-out 200ms',
                backgroundColor: 'rgba(0,0,0,0.5)',
                zIndex: 1001,
            }}>
            {loading ? (
                <div
                    className="font-ubuntu relative bg-white rounded shadow p-lg flex flex-col w-full"
                    style={{ maxWidth: 450, minHeight: 310 }}>
                    <div className="flex flex-center flex-1">
                        <ThreeDots
                            visible={true}
                            height="50"
                            width="50"
                            color={"#098089"}
                            radius="9"
                            ariaLabel="three-dots-loading"
                            wrapperStyle={{}}
                            wrapperClass=""
                        />
                    </div>
                </div>
            ) : isPhoneVerified ? (
                <div
                    className="font-ubuntu relative bg-white rounded shadow p-lg flex flex-col w-full"
                    style={{ maxWidth: 450, minHeight: 310 }}>
                    <div className="flex-center">
                        <img src="/svg/RoundVerified.svg" />
                    </div>
                    <div className="text-center text-24 py-lg">
                        Phone Number Successfully Verified
                    </div>
                    <div className="text-center text-14 px-md pb-lg">
                        Please make note of login credentials
                    </div>
                    <div className="flex-center">
                        <Link href={'/register/personal-profile'}>
                            <div>
                                <CustomButton
                                    text='Continue'
                                    width={280}
                                    height={45}
                                    color='darkteal'
                                    textColor='white'
                                    loading={loading}
                                    onClick={() => {
                                        setLoading(true)
                                        setModalVisible()
                                        router.push('/register/personal-profile')
                                    }}
                                    textSize={16}
                                />
                            </div>
                        </Link>
                    </div>
                </div>
            ) : (
                <form
                    onSubmit={(e) => {
                        e.preventDefault()
                        verifyOtp()
                    }}
                    className="font-ubuntu relative bg-white rounded shadow p-lg flex flex-col w-full"
                    style={{ maxWidth: 450, minHeight: 310 }}>
                    {!oldUser && (
                        <div
                            className="absolute cursor-pointer"
                            id="modalBackground"
                            style={{ right: 20, top: 20 }}
                            onClick={() => {
                                setModalVisible()
                            }}>
                            <img src="/svg/CrossIconBlack.svg" />
                        </div>
                    )}

                    <div className="flex-center flex-col flex-1">
                        <div className="text-24 text-center">Verify E-mail</div>
                        <div
                            className="text-14 text-center py-lg"
                            style={{ maxWidth: 400 }}>
                            Please enter the verification code sent to the admin email
                        </div>
                        <OtpInput
                            value={code}
                            onChange={(code) => {
                                setError()
                                if (!isNaN(code)) {
                                    setCode(code)
                                }
                            }}
                            renderInput={(props) => <input {...props} />}
                            numInputs={6}
                            renderSeparator={<span style={{ width: '8px' }}></span>}
                            isInputNum={true}
                            shouldAutoFocus={true}
                            inputStyle={{
                                width: '40px',
                                height: '40px',
                                fontSize: '16px',
                                color: '#000',
                                fontWeight: '400',
                                caretColor: 'blue',
                                backgroundColor: '#F2F2F2',
                            }}
                            focusStyle={{
                                border: '1px solid #CFD3DB',
                                outline: 'none',
                            }}
                        />
                        {error && (
                            <div className="text-12 text-error mt-md">
                                Please enter a correct verification code
                            </div>
                        )}
                        <>
                            {counter === 0 ? (
                                <div
                                    className="w-full my-lg flex-center flex-col"
                                    style={{
                                        maxWidth: 300,
                                    }}>
                                    <div
                                        onClick={() => {
                                            if (
                                                !resendOtpError &&
                                                counter === 0
                                            ) {
                                                checkAdmin({ email: email, password: password })
                                                setCounter(120)
                                            }
                                        }}
                                        className={`text-14 cursor-pointer hover:underline`}
                                        style={{ height: 1 }}>
                                        Resend Verification Code
                                    </div>
                                </div>
                            ) : !resendOtpError && counter !== 0 ? (
                                <div className="text-14 my-md">
                                    {counter < 60 && counter % 60 > 9
                                        ? `00:${counter}`
                                        : counter < 60 && counter % 60 < 10
                                            ? `00:0${counter}`
                                            : counter % 60 < 10
                                                ? `01:0${counter % 60}`
                                                : `01:${counter % 60}`}
                                </div>
                            ) : (
                                <div></div>
                            )}
                        </>
                        {resendOtpError && (
                            <div className="text-12 text-error mt-md">
                                {resendOtpErrorMessage || ''}
                            </div>
                        )}
                    </div>
                    <div className="flex-center flex-col">
                        <Checkbox label={"Remember Me"} value={rememberMe} onClick={() => setRememberMe((prev) => !prev)} customClasses="mb-sm" />
                        <CustomButton
                            text='Verify'
                            width={280}
                            height={45}
                            color='darkteal'
                            textColor='white'
                            loading={loading}
                            onClick={verifyOtp}
                            textSize={16}
                            marginX="0"
                        />
                    </div>

                </form>
            )}
        </div>
    )
}

export default Verify2FaModal
