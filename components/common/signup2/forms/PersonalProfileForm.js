import React, { useEffect, useState } from 'react';
import ProfilePhotoUpload from '../ProfilePhotoUpload';
import Select from '../Select';
import TextArea from '../TextArea';
import TextInput from '../TextInput';
import RadioSelect from '../RadioSelect';
import NameInitials from '../../NameInitials';
import extractedFileName from '../../../../utils/helper/extractFileName';
import fetchThumbnailUrl from '../../../../utils/helper/fetchThumbnailUrl';
import ImageCropPopup from '../../popups/ImageCropPopup';

const PersonalProfileForm = ({
    form,
    setForm,
    formErrors,
    setFormErrors,
    user,
    editable,
    overviewCard,
    loading,
    setLoading,
}) => {
    const [facebookInitialUrl] = useState('https://www.facebook.com/')
    const [linkedinInitialUrl] = useState('https://www.linkedin.com/in/')
    const [fileName, setFileName] = useState('')
    const [thumbnailUrl, setThumbNailUrl] = useState('')

    useEffect(() => {
        if (user?.profilePhoto) {
            const fileName = extractedFileName(user?.profilePhoto)
            setFileName(fileName)
        }

    }, [user?.profilePhoto])

    useEffect(() => {
        const getUrl = async (fileName, size) => {
            const url = await fetchThumbnailUrl(fileName, size)
            setThumbNailUrl(url)
        }
        if (fileName) {
            getUrl(fileName, 256)
        }
    }, [fileName])

    const getYearOptions = () => {
        const year = new Date().getFullYear()
        return Array(100)
            .fill('')
            .map((_, i) => {
                return year - i
            })
    }

    return (
        <>
            {overviewCard ? <>
                {!editable ?
                    <div className='md:flex w-full mt-lg'>
                        <div className='flex'>
                            {user?.profilePhoto ?
                                <>
                                    <div
                                        className='rounded-lg hidden md:block'
                                        style={{
                                            height: 127,
                                            width: 127,
                                            backgroundImage: `url('${thumbnailUrl}')`,
                                            backgroundPosition: 'center',
                                            backgroundSize: 'cover',
                                            backgroundRepeat: 'no-repeat',
                                        }}
                                    >
                                    </div>
                                    <div
                                        className='rounded-lg md:hidden'
                                        style={{
                                            height: 127,
                                            width: 127,
                                            backgroundImage: `url('${thumbnailUrl}')`,
                                            backgroundPosition: 'center',
                                            backgroundSize: 'cover',
                                            backgroundRepeat: 'no-repeat',
                                        }}
                                    >
                                    </div>
                                </>
                                :
                                <>
                                    <div className='hidden md:block'>
                                        <NameInitials
                                            user={form}
                                            height={127}
                                            width={127}
                                            fontSize={80}
                                        />
                                    </div>
                                    <div className='md:hidden'>
                                        <NameInitials
                                            user={form}
                                            height={100}
                                            width={100}
                                            fontSize={80}
                                        />
                                    </div>
                                </>
                            }
                            <div className='px-md md:hidden'>
                                <div className='flex mb-md'><img src='/svg/user_avatar.svg' /><div className='ml-sm text-14'>{user?.username}</div></div>
                                <div className='flex mb-md'><img src='/svg/email_carbon.svg' /><div className='ml-sm text-14'>{user?.email}</div></div>
                                <div className='flex'><img src='/svg/phone_call.svg' /><div className='ml-sm text-14'>{user?.phone}</div></div>
                            </div>
                        </div>

                        <div className='w-full'>
                            <div className='px-sm md:px-lg flex flex-col md:flex-wrap gap-1 md:grid md:grid-cols-3 w-full'>
                                <div className="flex flex-col text-16 my-sm text-black hidden md:flex">
                                    <div className="text-grayLight text-normal flex text-14">
                                        Username
                                    </div>
                                    {user?.username}
                                </div>
                                <div className="flex flex-col text-16 text-black my-sm md:hidden flex">
                                    <div className="text-grayLight text-normal flex text-14">
                                        Full Name
                                    </div>
                                    {user?.first_name} {user?.last_name}
                                </div>
                                <div className="flex-col text-16 text-black my-sm whitespace-no-wrap overflow-hidden text-ellipsis hidden md:flex"
                                    style={{ textOverflow: 'ellipsis' }}
                                >
                                    <div className="text-grayLight text-normal flex text-14">
                                        Email
                                    </div>
                                    {user?.email}
                                </div>

                                <div className="flex-col text-16 text-black my-sm hidden md:flex">
                                    <div className="text-grayLight text-normal text-14 flex">
                                        Phone Number
                                    </div>
                                    {user?.phone}
                                </div>

                                <div className="flex-col text-16 text-black my-sm hidden md:flex">
                                    <div className="text-grayLight text-normal text-14">
                                        Full Name
                                    </div>
                                    {user?.first_name} {user?.last_name}
                                </div>

                                <div className="flex flex-col text-16 text-black my-sm">
                                    <div className="text-grayLight text-normal text-14">
                                        Year of Birth
                                    </div>
                                    {user?.birthYear}
                                </div>

                                <div className="flex flex-col text-16 text-black my-sm capitalize">
                                    <div className="text-grayLight text-normal text-14">
                                        Gender
                                    </div>
                                    {user?.gender}
                                </div>

                                <div className="flex flex-col text-16 text-black my-sm">
                                    <div className="text-grayLight text-normal text-14">
                                        English Fluency
                                    </div>
                                    <div className='capitalize '>
                                        {user?.englishFluency}
                                    </div>
                                </div>

                                <div className="flex flex-col text-16 text-black my-sm break-all">
                                    <div className="text-grayLight text-normal text-14">
                                        LinkedIn(Optional)
                                    </div>
                                    {user?.linkedin}
                                </div>

                                <div className="flex flex-col text-16 text-black my-sm break-all">
                                    <div className="text-grayLight text-normal text-14">
                                        Facebook(Optional)
                                    </div>
                                    {user?.facebook}
                                </div>
                            </div>

                            <div className="px-sm md:px-lg flex flex-col text-16 text-black my-sm break-all">
                                <div className="text-grayLight text-normal text-14">
                                    Tell us a little about yourself
                                </div>
                                {user?.about_yourself}
                            </div>
                        </div>
                    </div> :
                    <div className='md:flex'>
                        <div className='flex md:pl-xxl pt-md md:pt-xxl'>
                            <ProfilePhotoUpload
                                initialPhoto={thumbnailUrl}
                                loading={loading}
                                setLoading={setLoading}
                                form={form}
                                setForm={setForm}
                            />
                            <div className='px-md ml-md md:hidden'>
                                <div className='flex mb-md'><img src='/svg/user_avatar.svg' /><div className='ml-sm text-14'>{user?.username}</div></div>
                                <div className='flex mb-md'><img src='/svg/email_carbon.svg' /><div className='ml-sm text-14'>{user?.email}</div></div>
                                <div className='flex'><img src='/svg/phone_call.svg' /><div className='ml-sm text-14'>{user?.phone}</div></div>
                            </div>
                        </div>
                        <div className='w-full'>
                            <div className={`flex w-full flex-wrap gap-1 grid ${overviewCard ? 'md:grid-cols-2 lg:grid-cols-3 ' : 'md:grid-cols-2'} md:px-lg pt-xl`}>
                                {overviewCard && <>
                                    <div className='relative px-lg hidden md:block'>
                                        <TextInput
                                            title={"Username"}
                                            value={user?.username || ''}
                                            optionalField={true}
                                            disabled={true}
                                        />
                                    </div>
                                    <div className='relative px-lg hidden md:block'>
                                        <TextInput
                                            title={"Email"}
                                            value={user?.email || ''}
                                            optionalField={true}
                                            disabled={true}
                                        />
                                    </div>
                                    <div className='relative px-lg hidden md:block'>
                                        <TextInput
                                            title={"Phone Number"}
                                            value={user?.phone || ''}
                                            optionalField={true}
                                            disabled={true}
                                        />
                                    </div>
                                </>}
                                <div className='relative px-sm mb-md md:mb-0 md:px-lg'>
                                    <TextInput
                                        title={"First Name"}
                                        placeholder={'Enter first name'}
                                        value={form?.first_name || ''}
                                        update={(value) => {
                                            setFormErrors({ ...formErrors, firstName: '' })
                                            setForm({ ...form, first_name: value })
                                        }}
                                        error={formErrors?.firstName}
                                        disableError={!formErrors?.firstName}
                                    />
                                </div>
                                <div className='relative px-sm mb-md md:mb-0 md:px-lg'>
                                    <TextInput
                                        title={"Last Name"}
                                        placeholder={'Enter last name'}
                                        value={form?.last_name || ''}
                                        update={(value) => {
                                            setFormErrors({ ...formErrors, lastName: '' })
                                            setForm({ ...form, last_name: value })
                                        }}
                                        error={formErrors?.lastName}
                                        disableError={!formErrors?.lastName}
                                    />
                                </div>
                                <div className='relative px-sm mb-md md:mb-0 md:px-lg'>
                                    <Select
                                        title={"Year of Birth"}
                                        placeholder={'Select a Birth Year'}
                                        value={form?.birthYear}
                                        update={(value) => {
                                            setFormErrors({ ...formErrors, birthYear: '' })
                                            setForm({ ...form, birthYear: value })
                                        }}
                                        error={formErrors?.birthYear}
                                        options={getYearOptions()}
                                    />
                                </div>
                                <div className='relative px-sm mb-md md:mb-0 md:px-lg'>
                                    <Select
                                        title={'Gender'}
                                        options={[
                                            'Male',
                                            'Female',
                                        ]}
                                        value={form?.gender ? form?.gender : "Male"}
                                        update={(value) => {
                                            setForm({ ...form, gender: value })
                                        }}
                                    />
                                </div>
                            </div>
                            <div className='px-sm md:px-xxl'>
                                <TextArea
                                    title={"Tell us a little about yourself"}
                                    value={form?.about_yourself || ''}
                                    update={(value) => {
                                        setForm({ ...form, about_yourself: value })
                                    }}
                                    error={formErrors?.aboutYourself}
                                    capitalize={false}
                                    maxLength={300}
                                />
                            </div>
                            <div className='w-full px-sm md:px-xxl md:mt-md'>
                                <RadioSelect
                                    buttonWidth="100%"
                                    value={form?.englishFluency}
                                    key='fluency'
                                    title="English Fluency"
                                    update={(value) => setForm({ ...form, englishFluency: value })}
                                    options={['native', 'fluent', 'understandable', 'basic']}
                                    alternateGrid={'grid-cols-2 md:grid-cols-4 sm:grid-cols-2 xs:grid-cols-2'}
                                />
                            </div>
                            <div className='w-full md:flex-wrap md:gap-1 md:grid lg:grid-cols-2 px-sm md:px-xxl'>
                                <div className='border-b border-lightgray hidden md:flex md:mr-md pt-4'>
                                    <TextInput
                                        value={linkedinInitialUrl || ''}
                                        update={(val) => {
                                            null
                                        }}
                                        disableError={!formErrors?.linkedin}
                                        className={' border-none text-base font-normal'}
                                        title={'LinkedIn(Optional)'}
                                        alternateWidth={193}
                                        optionalField
                                    />
                                    <TextInput
                                        noErrorMessage={!formErrors?.personalProfileError}
                                        disableError={!formErrors?.linkedin}
                                        error={formErrors?.linkedin}
                                        value={form?.linkedin || ''}
                                        className={'border-none text-base font-normal'}
                                        update={(value) => {
                                            setForm({ ...form, linkedin: value.toLowerCase() })
                                            if (formErrors?.linkedin) {
                                                setFormErrors({
                                                    ...formErrors,
                                                    facebook: "",
                                                    linkedin: "",
                                                    personalProfileError: ""
                                                })
                                            }
                                        }
                                        }
                                        type="link"
                                        hideTitle={'pt-lm'}
                                        alternateWidth={140}
                                    />
                                </div>
                                <div className='border-b border-lightgray flex md:hidden md:mr-md pt-4'>
                                    <TextInput
                                        value={linkedinInitialUrl || ''}
                                        update={(val) => {
                                            null
                                        }}
                                        disableError={!formErrors?.linkedin}
                                        className={' border-none text-16 font-normal'}
                                        title={'LinkedIn(Optional)'}
                                        alternateWidth={214}
                                        optionalField
                                    />
                                    <TextInput
                                        noErrorMessage={!formErrors?.personalProfileError}
                                        disableError={!formErrors?.linkedin}
                                        error={formErrors?.linkedin}
                                        value={form?.linkedin || ''}
                                        className={'border-none text-16 font-normal'}
                                        update={(value) => {
                                            setForm({ ...form, linkedin: value.toLowerCase() })
                                            if (formErrors?.linkedin) {
                                                setFormErrors({
                                                    ...formErrors,
                                                    facebook: "",
                                                    linkedin: "",
                                                    personalProfileError: ""
                                                })
                                            }
                                        }
                                        }
                                        type="link"
                                        hideTitle={'pt-lm'}
                                        alternateWidth={100}
                                    />
                                </div>

                                <div className='border-b border-lightgray hidden md:flex pt-4'>
                                    <TextInput
                                        title="Facebook(Optional)"
                                        value={facebookInitialUrl || ''}
                                        disableError={!formErrors?.facebook}
                                        update={(val) => {
                                            null
                                        }}
                                        className={'border-none text-base font-normal'}
                                        alternateWidth={184}
                                        optionalField
                                    />
                                    <TextInput
                                        noErrorMessage={!formErrors?.personalProfileError}
                                        disableError={!formErrors?.facebook}
                                        error={formErrors?.facebook}
                                        value={form?.facebook || ''}
                                        className={'border-none text-base font-normal'}
                                        update={(value) => {
                                            setForm({ ...form, facebook: value.toLowerCase() })
                                            if (formErrors?.facebook) {
                                                setFormErrors({
                                                    ...formErrors,
                                                    facebook: "",
                                                    linkedin: "",
                                                    personalProfileError: ""
                                                })
                                            }
                                        }
                                        }
                                        type="link"
                                        hideTitle={'pt-lm'}
                                        alternateWidth={140}
                                    />
                                </div>
                                <div className='border-b border-lightgray flex md:hidden pt-4'>
                                    <TextInput
                                        title="Facebook(Optional)"
                                        value={facebookInitialUrl || ''}
                                        disableError={!formErrors?.facebook}
                                        update={(val) => {
                                            null
                                        }}
                                        className={'border-none text-16 font-normal'}
                                        alternateWidth={205}
                                        optionalField
                                    />
                                    <TextInput
                                        noErrorMessage={!formErrors?.personalProfileError}
                                        disableError={!formErrors?.facebook}
                                        error={formErrors?.facebook}
                                        value={form?.facebook || ''}
                                        className={'border-none text-16` font-normal'}
                                        update={(value) => {
                                            setForm({ ...form, facebook: value.toLowerCase() })
                                            if (formErrors?.facebook) {
                                                setFormErrors({
                                                    ...formErrors,
                                                    facebook: "",
                                                    linkedin: "",
                                                    personalProfileError: ""
                                                })
                                            }
                                        }
                                        }
                                        type="link"
                                        hideTitle={'pt-lm'}
                                        alternateWidth={100}
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                }
            </> : <>
                <div className='md:flex md:flex-row'>
                    <div className='pl-lg md:pl-xxl pt-md md:pt-xl'>
                        <ProfilePhotoUpload
                            initialPhoto={thumbnailUrl}
                            loading={loading}
                            setLoading={setLoading}
                            form={form}
                            setForm={setForm}
                        />
                    </div>
                    <div className='w-full'>
                        <div className={`flex w-full flex-wrap gap-1 grid ${overviewCard ? 'grid-cols-3' : 'grid-cols-1 lg:grid-cols-2'} md:px-lg pt-xl`}>
                            {overviewCard && <>
                                <div className='relative px-lg'>
                                    <TextInput
                                        title={"Username"}
                                        value={user?.username || ''}
                                        optionalField={true}
                                        disabled={true}
                                    />
                                </div>
                                <div className='relative px-lg'>
                                    <TextInput
                                        title={"Email"}
                                        value={user?.email || ''}
                                        optionalField={true}
                                        disabled={true}
                                    />
                                </div>
                                <div className='relative px-lg'>
                                    <TextInput
                                        title={"Phone Number"}
                                        value={user?.phone || ''}
                                        optionalField={true}
                                        disabled={true}
                                    />
                                </div>
                            </>}

                            <div className='relative px-lg pb-md md:pb-xl'>
                                <TextInput
                                    // disabled={loading}
                                    title={"First Name"}
                                    placeholder={'Enter first name'}
                                    value={form?.first_name || ''}
                                    update={(value) => {
                                        setFormErrors({ ...formErrors, firstName: '' })
                                        setForm({ ...form, first_name: value })
                                    }}
                                    error={formErrors?.firstName}
                                    disableError={!formErrors?.firstName}
                                />
                            </div>
                            <div className='relative px-lg pb-sm md:pb-xl'>
                                <TextInput
                                    // disabled={loading}
                                    title={"Last Name"}
                                    placeholder={'Enter last name'}
                                    value={form?.last_name || ''}
                                    update={(value) => {
                                        setFormErrors({ ...formErrors, lastName: '' })
                                        setForm({ ...form, last_name: value })
                                    }}
                                    error={formErrors?.lastName}
                                    disableError={!formErrors?.lastName}
                                />
                            </div>
                            {!overviewCard &&
                                <div className='relative px-lg pb-md'>
                                    <Select
                                        title={"Year of Birth"}
                                        placeholder={'Select a Birth Year'}
                                        value={form?.birthYear}
                                        update={(value) => {
                                            setFormErrors({ ...formErrors, birthYear: '' })
                                            setForm({ ...form, birthYear: value })
                                        }}
                                        error={formErrors?.birthYear}
                                        options={getYearOptions()}

                                    />
                                </div>
                            }

                            <div className='relative px-lg pb-md'>
                                <Select
                                    title={'Gender'}
                                    options={[
                                        'Male',
                                        'Female',
                                    ]}
                                    value={form?.gender ? form?.gender : "Male"}
                                    update={(value) => {
                                        setForm({ ...form, gender: value })
                                    }}
                                />
                            </div>
                        </div>
                        <div className='px-lg md:px-xxl'>
                            <TextArea
                                title={"Tell us a little about yourself"}
                                value={form?.about_yourself || ''}
                                update={(value) => {
                                    setForm({ ...form, about_yourself: value })
                                }}
                                error={formErrors?.aboutYourself}
                                capitalize={false}
                                maxLength={300}
                            />
                        </div>

                        <div className='w-full px-lg md:px-xxl mt-md hidden md:block'>
                            <RadioSelect
                                buttonWidth="100%"
                                value={form?.englishFluency || ['native']}
                                key='fluency'
                                title="English Fluency"
                                update={(value) => setForm({ ...form, englishFluency: value })}
                                options={['native', 'fluent', 'understandable', 'basic']}
                                alternateGrid={'grid-cols-4'}
                            />
                        </div>
                        <div className='w-full px-lg md:px-xxl mt-md md:hidden'>
                            <RadioSelect
                                buttonWidth="100%"
                                value={form?.englishFluency || ['native']}
                                key='fluency'
                                title="English Fluency"
                                update={(value) => setForm({ ...form, englishFluency: value })}
                                options={['native', 'fluent', 'understandable', 'basic']}
                                alternateGrid={'grid-cols-2'}
                            />
                        </div>

                        <div className='flex w-full flex-wrap gap-1 grid lg:grid-cols-2 px-lg md:px-xxl'>
                            <div className='border-b border-lightgray mr-md pt-4 hidden md:flex'>
                                <TextInput
                                    value={linkedinInitialUrl}
                                    update={(val) => {
                                        null
                                    }}
                                    disableError={!formErrors?.linkedin}
                                    className={' border-none text-base font-normal'}
                                    title={'LinkedIn(Optional)'}
                                    alternateWidth={192}
                                    optionalField
                                />
                                <TextInput
                                    noErrorMessage={!formErrors?.personalProfileError}
                                    disableError={!formErrors?.linkedin}
                                    error={formErrors?.linkedin}
                                    value={form?.linkedin || ''}
                                    className={'border-none text-base font-normalm'}
                                    update={(value) => {
                                        setForm({ ...form, linkedin: value.toLowerCase() })
                                        if (formErrors?.linkedin) {
                                            setFormErrors({
                                                ...formErrors,
                                                facebook: "",
                                                linkedin: "",
                                                personalProfileError: ""
                                            })
                                        }
                                    }
                                    }
                                    type="link"
                                    hideTitle={'pt-lm'}
                                    alternateWidth={220}
                                />
                            </div>
                            <div className='border-b border-lightgray flex mr-md pt-4 md:hidden'>
                                <TextInput
                                    value={linkedinInitialUrl}
                                    update={(val) => {
                                        null
                                    }}
                                    disableError={!formErrors?.linkedin}
                                    className={' border-none text-16 font-normal'}
                                    title={'LinkedIn(Optional)'}
                                    alternateWidth={214}
                                    optionalField
                                />
                                <TextInput
                                    noErrorMessage={!formErrors?.personalProfileError}
                                    disableError={!formErrors?.linkedin}
                                    error={formErrors?.linkedin}
                                    value={form?.linkedin || ''}
                                    className={'border-none text-16 font-normalm'}
                                    update={(value) => {
                                        setForm({ ...form, linkedin: value.toLowerCase() })
                                        if (formErrors?.linkedin) {
                                            setFormErrors({
                                                ...formErrors,
                                                facebook: "",
                                                linkedin: "",
                                                personalProfileError: ""
                                            })
                                        }
                                    }
                                    }
                                    type="link"
                                    hideTitle={'pt-lm'}
                                    alternateWidth={130}

                                />
                            </div>

                            <div className='border-b border-lightgray w-full pt-4 hidden md:flex'>
                                <TextInput
                                    title="Facebook(Optional)"
                                    value={facebookInitialUrl}
                                    disableError={!formErrors?.facebook}
                                    update={(val) => {
                                        null
                                    }}
                                    className={' border-none text-base font-normal'}
                                    alternateWidth={184}
                                    optionalField
                                />
                                <TextInput
                                    noErrorMessage={!formErrors?.personalProfileError}
                                    disableError={!formErrors?.facebook}
                                    error={formErrors?.facebook}
                                    value={form?.facebook || ''}
                                    className={'border-none text-base font-normal'}
                                    update={(value) => {
                                        setForm({ ...form, facebook: value.toLowerCase() })
                                        if (formErrors?.facebook) {
                                            setFormErrors({
                                                ...formErrors,
                                                facebook: "",
                                                linkedin: "",
                                                personalProfileError: ""
                                            })
                                        }
                                    }
                                    }
                                    type="link"
                                    hideTitle={'pt-lm'}
                                    alternateWidth={100}
                                />
                            </div>
                            <div className='border-b border-lightgray flex w-full pt-4 md:hidden'>
                                <TextInput
                                    title="Facebook(Optional)"
                                    value={facebookInitialUrl}
                                    disableError={!formErrors?.facebook}
                                    update={(val) => {
                                        null
                                    }}
                                    className={' border-none text-16 font-normal'}
                                    alternateWidth={205}
                                    optionalField
                                />
                                <TextInput
                                    noErrorMessage={!formErrors?.personalProfileError}
                                    disableError={!formErrors?.facebook}
                                    error={formErrors?.facebook}
                                    value={form?.facebook || ''}
                                    className={'border-none text-16 font-normal'}
                                    update={(value) => {
                                        setForm({ ...form, facebook: value.toLowerCase() })
                                        if (formErrors?.facebook) {
                                            setFormErrors({
                                                ...formErrors,
                                                facebook: "",
                                                linkedin: "",
                                                personalProfileError: ""
                                            })
                                        }
                                    }
                                    }
                                    type="link"
                                    hideTitle={'pt-lm'}
                                    alternateWidth={100}
                                />
                            </div>
                        </div>
                    </div>
                </div>
            </>}

        </>

    )
}

export default PersonalProfileForm;