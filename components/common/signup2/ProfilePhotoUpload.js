import React, { useState, useRef, useEffect, useContext } from 'react'
import 'firebase/compat/auth'
import 'firebase/compat/storage'
import { UserContext } from '../../../pages/_app'
import { uploadFile } from '../../../utils/upload'
import compressImage from "../../../utils/helper/compressImage"
import { ThreeDots } from 'react-loader-spinner'
import { useRouter } from 'next/router'
import ImageCropPopup from '../popups/ImageCropPopup'

export default function ProfilePhotoUpload({ initialPhoto, form, setForm, loading, setLoading }) {
    const uploadRef = useRef()
    const [photo, setPhoto] = useState(initialPhoto)
    const [photoURL, setPhotoURL] = useState(initialPhoto)
    const { user } = useContext(UserContext);
    const [compressedPhoto, setCompressedPhoto] = useState()
    const router = useRouter()
    const [overviewPhotoLoading, setOverviewPhotoLoading] = useState(false)
    const [base64, setBase64] = useState()
    const [base64ForCrop, setBase64ForCrop] = useState()
    const [imageForUpload, setImageForUpload] = useState()
    const [isPhotoLoading, setIsPhotoLoading] = useState(false)
    useEffect(() => {
        if (imageForUpload) {
            initUpload()
            setBase64ForCrop('')

        }
    }, [imageForUpload])

    useEffect(() => {
        if (photo && typeof photo !== 'string') {
            const reader = new FileReader()
            reader.addEventListener('load', () => {
                const result = reader.result?.toString() || ''
                setBase64(result)
                setBase64ForCrop(result)
            })
            reader.readAsDataURL(photo)
        }
    }, [photo])

    const initUpload = async () => {
        setIsPhotoLoading(true)
        //Else it will close the popup
        //Converting blob to file for compression
        const newFileFromBlob = new File([imageForUpload], imageForUpload.name, { type: imageForUpload.type });
        const compressedFile = await compressImage({ file: newFileFromBlob })
        const firebaseFileURL = await uploadFile({ file: compressedFile, path: 'profilephotos' })
        setForm({ ...form, profilePhoto: firebaseFileURL })
        setIsPhotoLoading(false)
    }

    return (
        <>
            {(base64ForCrop) && <ImageCropPopup
                setBase64ForCrop={setBase64ForCrop}
                modal={{
                    image: base64ForCrop,
                    setDp: setPhotoURL,
                    setUploadImage: setImageForUpload
                }}
                setModal={() => {
                    setBase64ForCrop(null)
                }} />}

            {(photoURL) ? (
                <div className='relative'>
                    <div className='relative flex-center cursor-pointer rounded-lg'
                        onClick={() => uploadRef.current.click()}
                        style={{
                            cursor: 'pointer',
                            backgroundImage: `url('${photoURL}')`,
                            height: 100,
                            width: 100,
                            borderRadius: 10,
                            backgroundPosition: 'center',
                            backgroundSize: 'cover',
                            position: 'relative',
                        }}>
                        <div className='absolute bg-white flex-center rounded-full' style={{ height: 43, width: 43, right: -21.5, bottom: -21.5 }}>
                            <div className='bg-darkteal flex-center rounded-full' style={{
                                height: 33,
                                width: 33,
                            }}>
                                <img src='/svg/ImageSelectorIcon.svg' />
                            </div>
                        </div>
                    </div>
                    {(loading || overviewPhotoLoading) && (
                        <div
                            className="absolute flex-center w-full h-full"
                            style={{
                                top: 0,
                                backgroundColor: 'rgba(0,0,0,0.3)',
                                height: 100,
                                width: 100,
                                borderRadius: 10,
                            }}>
                            <div className='absolute bg-white flex-center rounded-full' style={{ height: 43, width: 43, right: -21.5, bottom: -21.5 }}>
                                <div className='bg-darkteal flex-center rounded-full' style={{
                                    height: 33,
                                    width: 33,
                                }}>
                                    <img src='/svg/ImageSelectorIcon.svg' />
                                </div>
                            </div>
                            <ThreeDots
                                visible={true}
                                height="25"
                                width="25"
                                color={"#FFFFFF"}
                                radius="9"
                                ariaLabel="three-dots-loading"
                                wrapperStyle={{}}
                                wrapperClass=""
                            />
                        </div>
                    )}
                </div>
            ) : (
                <div className='relative flex-center cursor-pointer rounded-lg'
                    onClick={() => uploadRef.current.click()}
                    style={{ height: 100, width: 100, backgroundColor: '#C3E5E8' }}>
                    <img src='/svg/ProfileAvatar.svg' />
                    <div className='absolute bg-white flex-center rounded-full' style={{ height: 43, width: 43, right: -21.5, bottom: -21.5 }}>
                        <div className='bg-darkteal flex-center rounded-full' style={{
                            height: 33,
                            width: 33,
                        }}>
                            <img src='/svg/ImageSelectorIcon.svg' />
                        </div>
                    </div>

                </div>
            )}

            <input
                onChange={(e) => {
                    const file = e.target.files[0]
                    if (file) {
                        setPhoto(file)
                    }
                    e.target.value = null
                }}
                ref={uploadRef}
                type="file"
                accept=".png,.jpg,.jpeg"
                style={{ display: 'none' }}
            />
        </>
    )
}