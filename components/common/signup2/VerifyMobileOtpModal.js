import React, { useState, useEffect, useContext } from "react";
import OtpInput from "react-otp-input";
import XIcon from "../../icons/RequestV2/XIcon";
import END_POINTS from "../../../constants/endpoints.json"
import Link from "next/link";
import { useRouter } from "next/router";
import firebase from 'firebase/compat/app'
import 'firebase/compat/auth'
import { ThreeDots } from 'react-loader-spinner'
import CustomButton from "../../buttons/CustomButton";
import { UserContext } from "../../../pages/_app";
import constantOptions from "../../../constants/constantOptions";

const VerifyMobileOtpModal = ({
    setModalVisible,
    form,
    setForm,
    resendRequestOtp,
    resendOtpResData,
    oldUser = false,
    refresh = () => { },
}) => {
    const [code, setCode] = useState()
    const [error, setError] = useState(false)
    const [loading, setLoading] = useState(false)
    const [counter, setCounter] = useState(120)
    const [isPhoneVerified, setIsPhoneVerified] = useState(false)
    const [resendOtpError, setResendOtpError] = useState(false)
    const [resendOtpErrorMessage, setResendOtpErrorMessage] = useState('')
    const router = useRouter()
    const { clevertap } = useContext(UserContext)

    useEffect(() => {
        const keyDownHandler = event => {

            if (event.key === 'Enter' && isPhoneVerified) {
                setLoading(true)
                setModalVisible()
                router.push('/register/personal-profile')
            }
        };

        document.addEventListener('keydown', keyDownHandler);

        return () => {
            document.removeEventListener('keydown', keyDownHandler);
        };
    }, [isPhoneVerified]);

    useEffect(() => {
        if (resendOtpResData && resendOtpResData?.remainingTime > 0) {

            setResendOtpErrorMessage(resendOtpResData?.message)
            setCounter(resendOtpResData?.remainingTime)
            setResendOtpError(true)
        }
    }, [resendOtpResData])

    useEffect(() => {
        const timer =
            counter > 0 && setInterval(() => setCounter(counter - 1), 1000)

        if (!counter) {
            setResendOtpError(false)
        }
        return () => clearInterval(timer);
    }, [counter]);

    useEffect(() => {
        if (code?.length === 6) {
            setForm({ ...form, code: code })
        }
    }, [code])

    const verifyOtp = async () => {
        setLoading(true);
        if (oldUser) {
            const response = await fetch(END_POINTS?.CHECK_VERIFICATION_CODE, {
                method: 'POST',
                credentials: 'same-origin',
                body: JSON.stringify({
                    phone: `+${form?.phoneNumber}`,
                    code: form?.code,
                }),
            })
                .then((data) => data.json())
                .then((data) => {

                    if (data?.status !== 'approved') {
                        setError(true)
                    } else {
                        setIsPhoneVerified(true)
                        clevertap.event.push(constantOptions?.CLEVERTAP_EVENTS.VERIFY_OTP);
                        refresh()
                    }
                })
                .catch((error) => {
                    setError(true)
                })
        } else {
            await fetch(END_POINTS.VERIFY_OTP, {
                method: 'POST',
                credentials: 'same-origin',
                headers: {
                    ['Content-Type']: 'application/json',
                },
                body: JSON.stringify({
                    code: form?.code,
                    username: form?.username,
                    email: form?.email,
                    confirmEmail: form?.confirmEmail,
                    phone: `+${form?.phoneNumber}`,
                    password: form?.password,
                    confirmPassword: form?.confirmPassword,
                    phone_number_details: {
                        dialCode: form?.country?.dialCode,
                        countryCode: form?.country?.countryCode,
                    },
                }),
            })
                .then((data) => data.json())
                .then((response) => {
                    if (response.status === 0) {
                        setError(true)
                    } else {
                        setIsPhoneVerified(true)
                        clevertap.event.push(constantOptions?.CLEVERTAP_EVENTS.VERIFY_OTP);
                        const loginToken = response?.user?.loginToken;
                        firebase
                            .auth()
                            .signInWithCustomToken(loginToken)
                            .then(async ({ user }) => {
                                console.log
                            })
                            .catch(console.log)
                    }
                })
                .catch((error) => console.log(error))
        }
        setLoading(false);
    }

    return (
        <div
            id="modalBackground"
            className="fixed inset-0 flex justify-center px-md pt-xxl md:pt-0 items-start md:items-center"
            style={{
                opacity: 1,
                transition: 'opacity ease-in-out 200ms',
                backgroundColor: 'rgba(0,0,0,0.5)',
                zIndex: 1001,
            }}>
            {loading ? (
                <div
                    className="font-ubuntu relative bg-white rounded shadow p-lg flex flex-col w-full"
                    style={{ maxWidth: 450, minHeight: 310 }}>
                    <div className="flex flex-center flex-1">
                        <ThreeDots
                            visible={true}
                            height="50"
                            width="50"
                            color={"#098089"}
                            radius="9"
                            ariaLabel="three-dots-loading"
                            wrapperStyle={{}}
                            wrapperClass=""
                        />
                    </div>
                </div>
            ) : isPhoneVerified ? (
                <div
                    className="font-ubuntu relative bg-white rounded shadow p-lg flex flex-col w-full"
                    style={{ maxWidth: 450, minHeight: 310 }}>
                    <div className="flex-center">
                        <img src="/svg/RoundVerified.svg" />
                    </div>
                    <div className="text-center text-24 py-lg">
                        Phone Number Successfully Verified
                    </div>
                    <div className="text-center text-14 px-md pb-lg">
                        Please make note of login credentials and continue ahead
                        to complete your registration
                    </div>
                    <div className="flex-center">
                        <Link href={'/register/personal-profile'}>
                            <div>
                                <CustomButton
                                    text='Continue'
                                    width={280}
                                    height={45}
                                    color='darkteal'
                                    textColor='white'
                                    loading={loading}
                                    onClick={() => {
                                        setLoading(true)
                                        setModalVisible()
                                        router.push('/register/personal-profile')
                                        clevertap.event.push(constantOptions?.CLEVERTAP_EVENTS.PHONE_NUMBER_VERIFIED_STEP_1);
                                    }}
                                    textSize={16}
                                />
                            </div>
                        </Link>
                    </div>
                </div>
            ) : (
                <form
                    onSubmit={(e) => {
                        e.preventDefault()
                        verifyOtp()
                    }}
                    className="font-ubuntu relative bg-white rounded shadow p-lg flex flex-col w-full"
                    style={{ maxWidth: 450, minHeight: 310 }}>
                    {!oldUser && (
                        <div
                            className="absolute cursor-pointer"
                            id="modalBackground"
                            style={{ right: 20, top: 20 }}
                            onClick={() => {
                                setModalVisible()
                            }}>
                            <img src="/svg/CrossIconBlack.svg" />
                        </div>
                    )}

                    <div className="flex-center flex-col flex-1">
                        <div className="text-24 text-center">
                            Verify Phone Number
                        </div>
                        <div
                            className="text-14 text-center py-lg"
                            style={{ maxWidth: 400 }}>
                            Please enter the verfication code sent on +
                            {form?.phoneNumber}
                        </div>
                        <OtpInput
                            value={code}
                            onChange={(code) => {
                                if (!isNaN(code)) {
                                    setError()
                                    setCode(code)
                                }
                            }}
                            renderInput={(props) => <input {...props} />}
                            numInputs={6}
                            renderSeparator={<span style={{ width: '8px' }}></span>}
                            isInputNum={true}
                            shouldAutoFocus={true}
                            inputStyle={{
                                width: '40px',
                                height: '40px',
                                fontSize: '16px',
                                color: '#000',
                                fontWeight: '400',
                                caretColor: 'blue',
                                backgroundColor: '#F2F2F2',
                            }}
                            focusStyle={{
                                border: '1px solid #CFD3DB',
                                outline: 'none',
                            }}
                        />
                        {error && (
                            <div className="text-12 text-error mt-md">
                                Please enter a correct verification code
                            </div>
                        )}
                        <>
                            {counter === 0 ? (
                                <div
                                    className="w-full my-lg flex-center flex-col"
                                    style={{
                                        maxWidth: 300,
                                    }}>
                                    <div
                                        onClick={() => {
                                            if (
                                                !resendOtpError &&
                                                counter === 0
                                            ) {
                                                resendRequestOtp()
                                                setCounter(120)
                                                clevertap.event.push(constantOptions?.CLEVERTAP_EVENTS.RESEND_OTP_STEP_1);
                                            }
                                        }}
                                        className={`text-14 cursor-pointer hover:underline`}
                                        style={{ height: 1 }}>
                                        Resend Verification Code
                                    </div>
                                </div>
                            ) : !resendOtpError && counter !== 0 ? (
                                <div className="text-14 my-md">
                                    {counter < 60 && counter % 60 > 9
                                        ? `00:${counter}`
                                        : counter < 60 && counter % 60 < 10
                                            ? `00:0${counter}`
                                            : counter % 60 < 10
                                                ? `01:0${counter % 60}`
                                                : `01:${counter % 60}`}
                                </div>
                            ) : (
                                <div></div>
                            )}
                        </>
                        {resendOtpError && (
                            <div className="text-12 text-error mt-md">
                                {resendOtpErrorMessage || ''}
                            </div>
                        )}
                    </div>
                    <div className="flex-center">
                        <CustomButton
                            text='Verify'
                            width={280}
                            height={45}
                            color='darkteal'
                            textColor='white'
                            loading={loading}
                            onClick={verifyOtp}
                            textSize={16}
                        />
                    </div>
                    {oldUser && (
                        <div className="text-sm text-darkteal mt-md text-center">
                            We were unable to verify your phone number earlier,
                            please re-enter and verify the same
                        </div>
                    )}
                </form>
            )}
        </div>
    )
}

export default VerifyMobileOtpModal
