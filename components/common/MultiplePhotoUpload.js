import React, { useState, useRef, useEffect } from 'react'
import XIcon from '../icons/XIcon'
import compressImage from '../../utils/helper/compressImage'

export default function MultiplePhotoUpload({
    value,
    update,
    error,
    setBase64ForCrop,
    benefitDp,
    setBenefitDp,
    setBenefitUploadImage
}) {
    const uploadRef = useRef()
    const [isLoading, setIsLoading] = useState(false)

    const handleFileSelect = async (file) => {
        if (!file) return

        try {
            // Create a single-use object URL for preview
            const previewUrl = URL.createObjectURL(file)
            setBenefitDp(previewUrl)

            // Convert to base64 for cropper
            const reader = new FileReader()
            reader.onload = () => {
                setBase64ForCrop(reader.result)
                // Clean up preview URL after base64 is ready
                URL.revokeObjectURL(previewUrl)
            }
            reader.readAsDataURL(file)
        } catch (error) {
            console.error('Error processing image:', error)
            setBenefitDp(null)
            setBase64ForCrop(null)
        }
    }

    const handleUpload = async (blob) => {
        if (!blob) return

        setIsLoading(true)
        try {
            const compressedFile = await compressImage({ file: blob, quality: 0.8 })
            update(compressedFile)
        } catch (error) {
            console.error('Error uploading image:', error)
        } finally {
            setIsLoading(false)
        }
    }

    // Handle initial upload when value (blob) is set from crop modal
    useEffect(() => {
        if (value && typeof value === 'object') {
            handleUpload(value)
        }
    }, [value])

    // Cleanup on unmount
    useEffect(() => {
        return () => {
            if (benefitDp && !benefitDp.startsWith('http')) {
                URL.revokeObjectURL(benefitDp)
            }
        }
    }, [])

    return (
        <div className="my-xl">
            {benefitDp ? (
                <div className="relative">
                    <div
                        className="rounded-lg"
                        style={{
                            width: '100%',
                            height: 200,
                            backgroundImage: `url("${benefitDp}")`,
                            backgroundPosition: 'center',
                            backgroundSize: 'contain',
                            borderColor: 'rgba(0,0,0,0)',
                            backgroundRepeat: 'no-repeat',
                        }}
                    />
                    <div
                        onClick={() => {
                            uploadRef.current.click()
                        }}
                        className="absolute cursor-pointer text-white p-sm rounded-lg"
                        style={{
                            bottom: 10,
                            right: "40%",
                            backgroundColor: "#00000052",
                            opacity: isLoading ? 0.5 : 1,
                            pointerEvents: isLoading ? 'none' : 'auto'
                        }}>
                        {isLoading ? 'Uploading...' : 'Change Image'}
                    </div>
                    <div
                        onClick={() => {
                            setBenefitDp(null)
                            setBenefitUploadImage(null)
                            update(null)
                        }}
                        className="absolute cursor-pointer"
                        style={{ top: 10, right: 10 }}>
                        <XIcon color="gray" />
                    </div>
                </div>
            ) : (
                <div
                    onClick={() => uploadRef.current.click()}
                    className="text-gray bg-lightestgray p-sm rounded-lg text-center cursor-pointer hover:bg-darkteal hover:text-white"
                    style={{
                        opacity: isLoading ? 0.5 : 1,
                        pointerEvents: isLoading ? 'none' : 'auto'
                    }}>
                    {isLoading ? 'Uploading...' : 'Upload Photo'}
                </div>
            )}
            <input
                onChange={({ target: { files } }) => handleFileSelect(files[0])}
                ref={uploadRef}
                type="file"
                accept="image/jpeg,image/png"
                style={{ display: 'none' }}
            />
            {error && (
                <div className="text-xs font-thin text-red text-center mt-sm">
                    {error}
                </div>
            )}
        </div>
    )
}
