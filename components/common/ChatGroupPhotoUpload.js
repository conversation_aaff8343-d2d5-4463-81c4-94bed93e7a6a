import React, { useState, useRef } from 'react'
import XIcon from '../icons/XIcon'

export default function ChatGroupPhotoUpload({
    initialPhoto,
    setInitialPhoto,
    update,
    error,
    editing,
    setBase64ForCrop,
    isUploading,
    setGroupDp = () => { }, // Add default empty function
    setGroupPhoto = () => { } // Add default empty function
}) {
    const uploadRef = useRef()
    const [photo, setPhoto] = useState(initialPhoto)
    const [photoURL, setPhotoURL] = useState(initialPhoto)

    const clearAllImageData = () => {
        // Clear local state
        setPhoto(null)
        setPhotoURL(null)

        // Clear parent state
        setInitialPhoto(null)
        setBase64ForCrop(null)
        setGroupDp(null)
        setGroupPhoto(null)
        update(null)
    }

    const handleFileSelect = async (file) => {
        if (!file) return

        try {
            // Validate file type
            if (!file.type.startsWith('image/')) {
                throw new Error('Please select an image file')
            }

            // Convert to base64 for cropper
            const reader = new FileReader()
            reader.onload = () => {
                setBase64ForCrop(reader.result)
            }
            reader.onerror = () => {
                console.error('Error reading file')
                setPhotoURL(null)
            }
            reader.readAsDataURL(file)
        } catch (error) {
            console.error('Error processing image:', error)
            setPhotoURL(null)
            setBase64ForCrop(null)
        }
    }

    return (
        <div className="flex-center">
            {(photo || initialPhoto) ? (
                <div className="relative bg-lightgray rounded-full">
                    <div
                        className="rounded-full"
                        style={{
                            width: 92,
                            height: 92,
                            backgroundImage: `url("${photoURL || initialPhoto}")`,
                            backgroundPosition: 'center',
                            backgroundSize: 'cover',
                            borderColor: 'rgba(0,0,0,0)',
                            backgroundRepeat: 'no-repeat',
                        }}
                    />
                    <div
                        onClick={clearAllImageData}
                        className="absolute"
                        style={{ top: 0, right: 0 }}>
                        <XIcon color="gray" />
                    </div>
                </div>
            ) : (
                <div className='bg-lightgray rounded-full flex-center' style={{ height: 92, width: 92 }}>
                    <div className="flex-col flex-center cursor-pointer"
                        onClick={() => !isUploading && uploadRef.current.click()}
                    >
                        {isUploading ? (
                            <div className="flex-center">
                                <div className="loader" />
                            </div>
                        ) : (
                            <>
                                <img src="/svg/Upload.svg" alt="Image" />
                                Upload
                            </>
                        )}
                    </div>
                </div>
            )}
            <input
                onChange={({ target: { files } }) => handleFileSelect(files[0])}
                ref={uploadRef}
                type="file"
                accept=".png,.jpg,.jpeg"
                style={{ display: 'none' }}
            />
            {error && (
                <div className="text-xs font-thin text-red text-center mt-sm">
                    {error}
                </div>
            )}
        </div>
    )
}
