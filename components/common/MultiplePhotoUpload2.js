import React, { useState, useRef, useEffect } from 'react'
import compressImage from '../../utils/helper/compressImage'
import { uploadFile } from '../../utils/upload'

const MultiplePhotoUpload2 = ({
    value,
    update,
    error,
    setBase64ForCrop,
    eventDp,
    setEventDp,
    setEventUploadImage,
    eventForm,
    setEventForm
}) => {
    const uploadRef = useRef()
    const [isLoading, setIsLoading] = useState(false)

    const handleFileSelect = async (file) => {
        if (!file) return

        try {
            // Validate file type
            if (!file.type.startsWith('image/')) {
                throw new Error('Please select an image file')
            }

            // Create a single-use object URL for preview
            const previewUrl = URL.createObjectURL(file)
            setEventDp(previewUrl)

            // Convert to base64 for cropper
            const reader = new FileReader()
            reader.onload = () => {
                setBase64ForCrop(reader.result)
                // Clean up preview URL after base64 is ready
                URL.revokeObjectURL(previewUrl)
            }
            reader.onerror = () => {
                console.error('Error reading file')
                URL.revokeObjectURL(previewUrl)
                setEventDp(null)
            }
            reader.readAsDataURL(file)
        } catch (error) {
            console.error('Error processing image:', error)
            setEventDp(null)
            setBase64ForCrop(null)
        }
    }

    const handleUpload = async (blob) => {
        if (!blob) return

        setIsLoading(true)
        try {
            const newFile = new File([blob], 'uploaded_image.jpg', { type: 'image/jpeg' })
            const compressedFile = await compressImage({ file: newFile })

            // Handle both event and review game cases
            if (setEventForm) {
                const firebaseFileURL = await uploadFile({ file: compressedFile, path: "event" })
                setEventForm(prev => ({ ...prev, image: firebaseFileURL }))
            }

            // Always call update if provided (needed for review game)
            if (update) {
                update(compressedFile)
            }
        } catch (error) {
            console.error('Error uploading image:', error)
            // Reset states on error
            setEventDp(null)
            if (update) update(null)
        } finally {
            setIsLoading(false)
        }
    }

    // Handle initial upload when value (blob) is set from crop modal
    useEffect(() => {
        if (value) {
            handleUpload(value)
        }
    }, [value])

    // Cleanup on unmount
    useEffect(() => {
        return () => {
            if (eventDp && !eventDp.startsWith('http')) {
                URL.revokeObjectURL(eventDp)
            }
        }
    }, [])

    return (
        <>
            {eventDp ? (
                <div className="relative">
                    <div
                        className="rounded-lg"
                        style={{
                            width: '100%',
                            height: 222,
                            backgroundImage: `url("${eventDp}")`,
                            backgroundPosition: 'center',
                            backgroundSize: 'contain',
                            borderColor: 'rgba(0,0,0,0)',
                            backgroundRepeat: 'no-repeat',
                        }}
                    />
                    <div
                        onClick={() => {
                            if (!isLoading) {
                                uploadRef.current.click()
                            }
                        }}
                        className="absolute cursor-pointer text-white p-sm rounded-lg"
                        style={{
                            bottom: 10,
                            right: "40%",
                            backgroundColor: "#00000052",
                            opacity: isLoading ? 0.5 : 1,
                            pointerEvents: isLoading ? 'none' : 'auto'
                        }}>
                        {isLoading ? 'Uploading...' : 'Change Image'}
                    </div>
                </div>
            ) : (
                <div
                    onClick={() => {
                        if (!isLoading) {
                            uploadRef.current.click()
                        }
                    }}
                    className='cursor-pointer image-upload-area flex-center flex-col'
                    style={{
                        minHeight: 222,
                        opacity: isLoading ? 0.5 : 1,
                        pointerEvents: isLoading ? 'none' : 'auto'
                    }}>
                    <img src='/svg/UploadObjects.svg' alt="Upload" />
                    <div className='text-16 font-medium text-darkteal mt-sm'>
                        {isLoading ? 'Uploading...' : 'Upload Image'}
                    </div>
                </div>
            )}

            <input
                onChange={({ target: { files } }) => handleFileSelect(files[0])}
                ref={uploadRef}
                type="file"
                accept="image/jpeg,image/png"
                style={{ display: 'none' }}
            />

            {error && (
                <div className="text-12 font-normal text-center mt-sm" style={{ color: 'red' }}>
                    {error}
                </div>
            )}
        </>
    )
}

export default MultiplePhotoUpload2