import React from 'react'
import Image from 'next/image'
import Link from 'next/link'
import useCheckDeviceScreen from '../../hooks/useCheckDeviceScreen'

const MaintenanceComponent = ({title = "Requests", description = "We’re making updates to improve the Requests experience. Requests will be unavailable temporarily. Thank you for your patience"}) => {
    const { isMobile } = useCheckDeviceScreen()
    
  return (
    <div className='flex flex-col items-center justify-center'>
    <Image className='mb-md' src="/svg/UnderMaintenance.svg" alt="bg-image" width={isMobile ? 290 : 448} height={isMobile ? 256 : 397} />
    <div className='text-20 md:text-24 font-medium'>{title} Module Under Maintenance</div>
    <div className=' text-grayLight md:w-[60%] text-center py-sm'>{description}</div>
    <div className=''>Need help? Contact us at <span className='text-darkteal underline'><Link href="mailto:<EMAIL>"><EMAIL></Link></span></div>
</div>  )
}

export default MaintenanceComponent