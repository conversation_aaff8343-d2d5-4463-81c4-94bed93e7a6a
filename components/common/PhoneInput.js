import React from 'react'
import PhoneInput from 'react-phone-input-2'

export default function PhoneInputField({
    value,
    update,
    error,
    className,
    title,
    disabled = false,
    updated = false,
    verified,
    country,
    signupClasses,
    fontSizeClass = "text-sm",
    centerError,
    addClubClasses,
    border = 'b'
}) {

    return (
        <>
            {title && (
                <div className={`${addClubClasses ? addClubClasses : 'text-sm font-thin'} text-grayLight flex`}>
                    {title}{signupClasses && <div className={error ? 'text-error' : ''}>*</div>}
                </div>

            )}
            <div className={`flex border-${border} ${error ? 'border-red' : 'border-lightgray'}`}>
                <PhoneInput
                    country={country ? country.toLowerCase() : 'us'}
                    value={value ? value : ''}
                    placeholder="Mobile Phone Number"
                    onChange={(value, country) => {
                        update(value, {
                            countryCode: country?.countryCode,
                            dialCode: country?.dialCode,
                            format: country?.format,
                            name: country?.name
                        })
                    }}
                    inputProps={{
                        className: `${className} ${fontSizeClass} w-full font-normal font-ubuntu text-black `,
                        style: {
                            paddingLeft: 48,
                            height: 42
                        },
                    }}
                    countryCodeEditable={false}
                    disabled={disabled}
                    disableCountryGuess={!!country}
                />
                {verified && <img className='ml-sm' src='/svg/Verified.svg' />}

            </div>
            {updated && <div className='inline-block cursor-pointer text-12 text-green'>Verify</div>}

            {error && (
                <div
                    className={`text-12 text-error font-normal py-md ${(title === 'Phone' && !centerError) ? '' : 'text-center'}`}
                    style={{ minHeight: 40 }}>
                    {error}
                </div>
            )}
        </>
    )
}
