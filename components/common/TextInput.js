import React, { useState } from 'react'
import { AsYouType } from 'libphonenumber-js'
import ToolTip from './ToolTip'
import { ABOUT_YOURSELF_MAX_LENGTH } from '../../graphql/queries/user'

export default function TextInput({
    placeholder,
    value,
    title,
    update,
    error,
    type,
    onFocus,
    onClick,
    onBlur,
    disableError,
    className = "rounded-none",
    disabled,
    style,
    tooltip,
    readOnly,
    pegboardClass,
    maxLength,
    editClubClasses,
    addClubClasses,
    verified,
    hideTitle = '',
    alternateWidth,
    titleSize,
    titleWeight = 'normal',
    titleColor = 'grayLight',
    lineHeight,
    border = 'b'
}) {
    const [passwordObscurity, setPasswordObscurity] = useState(true)

    function formatPhoneNumber(text) {
        const formattedNumber = new AsYouType().input(text)

        if (text.length === 4 && text.length < value.length) {
            update(text)
        } else if (text.length < 15) {
            update(formattedNumber)
        }
    }

    return (
        <div className={`${hideTitle} relative w-full`} style={{
            width: alternateWidth || '100%',
            lineHeight: lineHeight || ''
        }}>
            {title && (
                <div className="flex">
                    <div
                        className={` ${editClubClasses || addClubClasses ? 'text-16' : `text-${titleSize ? titleSize : '14'}`} ${error ? 'text-red' : `text-${titleColor}`
                            } font-${titleWeight} capitalize flex`}>
                        {title}
                    </div>
                    {tooltip && (
                        <div className="flex-1 pl-1 mt-1 relative">
                            <ToolTip tip={tooltip} />
                        </div>
                    )}
                </div>
            )}
            <div className={`flex border-${border} ${error ? 'border-red' : 'border-lightgray'}`}>
                <textarea
                    rows={1}
                    maxLength={maxLength}
                    disabled={disabled}
                    readOnly={readOnly}
                    onClick={onClick}
                    onBlur={onBlur}
                    onFocus={onFocus}
                    autoComplete={'new-password'}
                    type={passwordObscurity ? type : 'text'}
                    value={value || ''}
                    onKeyDown={(e) => {
                        if (type === 'number') {
                            if (
                                e.key.length === 1 && isNaN(e.key) && !e.ctrlKey ||
                                e.key === '.' && e.target.value.toString().indexOf('.') > -1
                            ) {
                                return e.preventDefault();
                            }
                        }
                    }}
                    onChange={({ target }) => {
                        if (type === 'phone') {
                            formatPhoneNumber(target.value)
                        } else {
                            update(target.value)
                        }
                        // Adjust height automatically
                        target.style.height = 'auto';
                        target.style.height = target.scrollHeight + 'px';
                    }}
                    placeholder={placeholder}
                    style={{
                        WebkitAppearance: 'none',
                        resize: 'none',
                        overflow: 'hidden',
                        minHeight: '24px',
                        ...style
                    }}
                    className={`${className} ${pegboardClass ? 'text-2xl bg-mediumgray' : editClubClasses || addClubClasses ? 'text-16 font-normal' : ''} w-full py-xs text-black ${disabled
                        ? 'cursor-not-allowed px-sm rounded-lg border-none bg-none'
                        : ''
                        }`}
                />
                {verified && <img className='ml-sm' src='/svg/Verified.svg' />}

            </div>

            {type === 'password' && (
                <img
                    onClick={() => {
                        setPasswordObscurity(!passwordObscurity)
                    }}
                    src={`${passwordObscurity === false ? "/images/views.png" : '/svg/ViewsCrossed.svg'}`}
                    className="absolute cursor-pointer"
                    style={{
                        height: 15,
                        top: 14,
                        right: 10,
                    }}
                />
            )}
            {
                type === 'aboutYourself' && (
                    <div className='text-xs text-gray mt-2 float-right'>{ABOUT_YOURSELF_MAX_LENGTH - (!value ? 0 : value.length)}</div>
                )
            }
            {(!disableError) && (
                <div
                    className="text-12 font-normal py-md text-center text-error"
                    style={{ minHeight: 40 }}>
                    {error}
                </div>
            )}

        </div>
    )
}
