import React from 'react'
import { ImageCropModal } from '../../modals'

const ImageCropPopup = ({ modal, setModal, setBase64ForCrop }) => {
  return (
    <div
      onClick={() => { }}
      className="fixed inset-0 flex-center px-md"
      style={{
        opacity: 1,
        transition: 'opacity ease-in-out 200ms',
        backgroundColor: 'rgba(0,0,0,0.5)',
        zIndex: 1001
      }}>
      <div className='h-auto w-[400px] bg-white rounded-lg p-md'>
        <ImageCropModal setBase64ForCrop={setBase64ForCrop} modal={modal} setModal={setModal} />
      </div>

    </div>)
}

export default ImageCropPopup