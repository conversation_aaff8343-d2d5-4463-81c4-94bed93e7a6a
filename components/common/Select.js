import React, { useState, useRef, useEffect, useCallback, useContext } from 'react'
import TextInput from './TextInput'
import { ThreeDots } from 'react-loader-spinner'
import ToolTip from './ToolTip'
import ENDPOINTS from "../../constants/endpoints.json"
import { UserContext } from '../../pages/_app'
import debounce from '../../utils/helper/debounce'

export default function Select({
    border = 'b',
    placeholder,
    title,
    error,
    type,
    club,
    value,
    options: initialOptions,
    update,
    updateSearch,
    defaultInitial,
    className,
    disableError,
    tooltip,
    globalDropdownState,
    filterTier,
    exclude,
    extraClasses = "",
    extraStyles = {},
    removeSearchError = () => { },
    addClubClasses,
    readOnly = false,
    editClubClasses,
    tooltipStyles = {},
    clubId = undefined,
    benefitCategories,
    states,
    cities,
    customAddressClasses,
    titleWeight = 'normal',
    fontWeignt = 'normal',
    marginTop = '',
    clubs,
    top = 50,
    left = 0,
    isClubSelected,
}) {

    const [options, setOptions] = useState([])
    const [dropdownVisible, setDropdownVisible] = useState(false)
    const [drop, setDrop] = useState(true)
    const [loading, setLoading] = useState(false)
    const { user, token } = useContext(UserContext)
    const [dropdownPosition, setDropdownPosition] = useState({ top, left });
    const wrapperRef = useRef(null);
    const dropDownRef = useRef(null)

    useEffect(() => {
        if (initialOptions?.length) {
            setOptions(initialOptions)
        }
    }, [initialOptions])

    useEffect(() => {
        const calculatePosition = () => {
            const dropdownElement = dropDownRef.current;
            if (dropdownElement) {
                const { bottom, top: dropdownTop } = dropdownElement.getBoundingClientRect();
                const { innerHeight } = window;
                let adjustedTop = top;
                if (bottom > innerHeight) {
                    // Adjust to prevent overflow below the viewport
                    adjustedTop = top - (bottom - innerHeight) - 70; // Add margin
                } else if (dropdownTop < 0) {
                    // Adjust to prevent overflow above the viewport
                    adjustedTop = top + Math.abs(dropdownTop) + 10; // Add margin
                }
                setDropdownPosition({ top: adjustedTop, left });
            }
        };

        if (dropdownVisible) {
            calculatePosition();
            window.addEventListener("resize", calculatePosition);
        }

        return () => {
            window.removeEventListener("resize", calculatePosition);
        };
    }, [dropdownVisible, top, left, options?.length, value]);



    function useOutsideAlerter(ref) {
        useEffect(() => {
            /**
             * Alert if clicked on outside of element
             */
            function handleClickOutside(event) {
                if (ref.current && !ref.current.contains(event.target)) {
                    setDropdownVisible(false);
                }
            }

            // Bind the event listener
            document.addEventListener("mousedown", handleClickOutside);
            return () => {
                // Unbind the event listener on clean up
                document.removeEventListener("mousedown", handleClickOutside);
            };
        }, [ref]);
    }

    useOutsideAlerter(wrapperRef);

    function toggleDropdown() {
        if (!value && dropdownVisible && defaultInitial) {
            update(options[0])
        }
        setDropdownVisible(false)
    }

    const clubType = ['clubs', 'request-clubs', 'request-new-club', 'my-contact-club', 'register-clubs', "pegboard-clubs"]

    useEffect(() => {
        if (clubType.includes(type)) {
            if (value && value.length > 0 && !isClubSelected) {
                setLoading(true)
                fetchClubs(value, false);
                setDropdownVisible(true);
            } else if (value && value.length > 0 && isClubSelected) {
                setLoading(true)
                fetchClubs(value, true);
                setDropdownVisible(true);
            }
            else {
                setOptions([]);
                setDropdownVisible(false);
            }

        } else {
            window.addEventListener('click', toggleDropdown)
            return () => {
                window.removeEventListener('click', toggleDropdown)
            }
        }
    }, [value])

    useEffect(() => {
        if (clubId) {
            setDropdownVisible(false)
            setDrop(false)
        }
    }, [clubId, drop])

    useEffect(() => {
        if (type === 'associations') {
            fetch(ENDPOINTS.ASSOCIATIONS)
                .then((data) => data.json())
                .then(({ associations }) => {
                    setOptions(associations)
                })
        }
    }, [])

    useEffect(() => {
        if (type === 'benefitCategories') {
            setOptions(benefitCategories)
        }
    }, [benefitCategories])

    useEffect(() => {
        if (type === 'state-address') {
            setOptions(states)
        }
    }, [states])
    useEffect(() => {
        if (type === 'city-address') {
            setOptions(cities)
        }
    }, [cities])
    useEffect(() => {
        if (type === 'request-clubs') {
            setOptions(clubs)
        }
    }, [clubs])

    const fetchClubs = useCallback(
        debounce((value, match) => {
            let url
            switch (type) {
                case 'register-clubs':
                    url = ENDPOINTS.REGISTER_CLUBS
                    break;
                case 'clubs':
                    url = ENDPOINTS.CLUBS
                    break;
                case 'pegboard-clubs':
                    url = ENDPOINTS.FETCH_CLUBS_FOR_PEGBOARD
                    break;
                case 'request-new-club':
                    url = ENDPOINTS.REQUEST_NEW_CLUB
                    break;
                case 'my-contact-club':
                    url = ENDPOINTS.MY_CONTACT_CLUB
                    break;
                default:
                    url = ENDPOINTS.SEARCH_CLUB
                    break;
            }
            fetch(url,
                {
                    method: 'POST',
                    credentials: 'same-origin',
                    headers: {
                        'Content-Type': ['my-contact-club', 'request-clubs', 'pegboard-clubs'].includes(type) ? "application/json" : "text/html",
                        ['Authorization']: `Bearer ${token}`,
                    },
                    body: JSON.stringify(
                        (type === 'clubs' || type === 'register-clubs')
                            ? {
                                searchValue: value ? value : '',
                                filterTier: filterTier,
                            }
                            :
                            {
                                searchValue: value ? value : '',
                                userId: user?.id,
                                exactMatch: match
                            }
                    ),
                }
            )
                .then((data) => data.json())
                .then(({ clubs }) => {
                    setLoading(false)
                    if (exclude) {
                        if (type !== 'clubs' && type !== 'register-clubs') {
                            if (match) {
                                update(clubs[0])
                            } else {
                                setOptions([...clubs])
                                setDropdownVisible(true)
                                setDropdownPosition(dropdownPosition)
                            }
                        } else {
                            setOptions(
                                clubs.filter(({ id }) => !exclude.includes(id))
                            )
                        }
                    } else {
                        setOptions(clubs)
                    }
                })
        }, 250),
        []
    );

    const {
        globalDropdownVisible,
        setGlobalDropdownVisible,
    } = globalDropdownState
            ? globalDropdownState
            : { globalDropdownVisible: true, setGlobalDropdownVisible: () => { } }

    function onKeyDown(e) {
        if (e.keyCode === 13) {
            setDropdownVisible(false)
        }
        if (e.keyCode === 40 || e.keyCode === 38) {
            const keyDirection = e.keyCode === 38 ? 'up' : 'down'
            const currentValueIndex = options.indexOf(value)

            if (
                (currentValueIndex === 0 && keyDirection === 'up') ||
                (currentValueIndex === options.length - 1 &&
                    keyDirection === 'down')
            ) {
                return
            }
            const nextIndex =
                currentValueIndex === -1
                    ? 0
                    : currentValueIndex + (keyDirection === 'up' ? -1 : 1)

            update(options[nextIndex])
        }
    }

    useEffect(() => {
        setGlobalDropdownVisible(false); // adding to close open dropdowns
        if (dropdownVisible) {
            setGlobalDropdownVisible(true)
            window.addEventListener('keydown', onKeyDown)
            return () => {
                window.removeEventListener('keydown', onKeyDown)
            }
        }
    }, [dropdownVisible, value])

    useEffect(() => {
        if (!globalDropdownVisible) {
            setDropdownVisible(false)
        }
    }, [globalDropdownVisible])

    return (
        <div
            className={`${extraClasses} flex flex-col mt-${marginTop ? '' : 'md'} ${disableError || error ? 'relative' : `${customAddressClasses ? '' : 'lg:mb-xl'}`
                } relative w-full`}
            style={{
                minHeight: 38,
                zIndex: dropdownVisible ? 60 : 50,
                ...extraStyles
            }}
            ref={wrapperRef}
        >
            <img
                src="/svg/arrow-down.svg"
                className="absolute z-20"
                style={{ right: 8, height: 10, bottom: error ? 55 : placeholder || value ? 15 : 30 }}
            />
            {title && (
                <div className="flex items-center">
                    <div
                        className={` ${addClubClasses ? 'text-14' : 'text-12'} ${error ? 'text-red' : 'text-grayLight'
                            }  font-${titleWeight}`}>
                        {title}
                    </div>
                    {tooltip && (
                        <div className={`flex-1 pl-1 relative`}>
                            <ToolTip tip={tooltip} style={tooltipStyles} />
                        </div>
                    )}
                </div>
            )}
            {clubType.includes(type) && club && (
                <div
                    className="absolute bg-darkteal flex-center text-white px-sm z-50 text-sm rounded"
                    style={{
                        top: 0,
                        paddingTop: 5,
                        paddingBottom: 5,
                    }}>
                    {club.name}
                    <img
                        onClick={() => {
                            updateSearch('')
                            update()
                        }}
                        className="relative cursor-pointer"
                        src="/svg/circle-close.svg"
                        style={{
                            height: 12,
                            width: 12,
                            marginLeft: 10,
                            bottom: 1,
                        }}
                    />
                </div>
            )}
            {clubType.includes(type) ? (
                <TextInput
                    border={border}
                    disableError={true}
                    error={error}
                    onBlur={() => {
                        setDropdownVisible(false)
                        removeSearchError()
                    }
                    }
                    onFocus={() => setDropdownVisible(true)}
                    value={value || ''}
                    update={updateSearch}
                    placeholder={placeholder}
                    addClubClasses={addClubClasses}
                    className={className}
                />
            ) : (
                <button
                    onClick={(e) => {
                        if (!readOnly) {
                            e.stopPropagation()
                            setGlobalDropdownVisible(false);
                            setDropdownVisible(true)
                        }
                    }}
                    role="button"
                    type="button"
                    style={{ outline: 0 }}
                    className={`${className} ${value ? `text-black font-${fontWeignt}` : `text-placeholder font-${fontWeignt}`
                        } cursor-pointer overflow-hidden capitalize py-xs text-left ${editClubClasses || addClubClasses ? "text-16" : customAddressClasses ? "text-14" : "text-12 "} border-b ${error ? 'border-red' : `border-lightgray`}`}>
                    {value
                        ? typeof value === 'string' || typeof value === 'number'
                            ? value
                            : value.name
                        : placeholder}
                </button>
            )}
            {((dropdownVisible && value?.length > 0 && (options?.length !== 0)) ||
                (dropdownVisible && type !== 'clubs' && type !== 'register-clubs')) && (
                    <div ref={dropDownRef}
                        className={`absolute flex flex-col ${editClubClasses || addClubClasses ? "text-16 font-normal" : "text-sm "} overflow-scroll bg-white font-thin rounded shadow`}
                        style={{
                            ...dropdownPosition,
                            minHeight: 40,
                            maxHeight: 200,
                            width: '100%',
                        }}>
                        {loading ? (
                            <div className="absolute inset-0 flex-center">
                                <ThreeDots
                                    visible={true}
                                    height="25"
                                    width="25"
                                    color={"#098089"}
                                    radius="9"
                                    ariaLabel="three-dots-loading"
                                    wrapperStyle={{}}
                                    wrapperClass=""
                                />
                            </div>
                        ) : ((clubType.includes(type)) && !(value)) ? null :
                            options?.length ?
                                (options.map((option, i) => {
                                    return (
                                        <div
                                            key={i}
                                            onMouseDown={(e) => {
                                                if (['clubs', 'request-clubs', 'register-clubs', 'request-new-club', 'my-contact-club', 'pegboard-clubs'].includes(type)
                                                ) {
                                                    e.stopPropagation()
                                                    update(option)
                                                    setDropdownVisible(false)
                                                }
                                            }}
                                            onClick={(e) => {
                                                if (
                                                    typeof option !== 'object' ||
                                                    !option.disabled
                                                ) {
                                                    e.stopPropagation()
                                                    update(option)
                                                    setDropdownVisible(false)
                                                }
                                            }}
                                            className={`p-sm hover:bg-lightestgray ${typeof option === 'object' &&
                                                option.disabled
                                                ? 'text-gray'
                                                : 'text-black'
                                                } cursor-pointer capitalize ${value
                                                    ? value === option
                                                        ? 'font-normal'
                                                        : ''
                                                    : i === 0 && defaultInitial
                                                        ? 'font-bold'
                                                        : ''
                                                }`}>
                                            {typeof option !== 'object'
                                                ? option
                                                : option.name}
                                        </div>
                                    )
                                }))
                                : <>
                                    {(type === 'state-address' || type === 'city-address') ? (
                                        <div
                                            className="p-sm hover:bg-lightestgray">
                                            There are no states/cities please enter Postal Code
                                        </div>
                                    ) : (
                                        <div
                                            className="p-sm hover:bg-lightestgray capitalize">
                                            No clubs matching the entered text
                                        </div>
                                    )}
                                </>
                        }
                    </div>
                )
            }
            {!disableError && error && (
                <div
                    className="text-12 font-normal aitems-center pt-sm"
                    style={{ minHeight: 40, color: 'red' }}>
                    {error}
                </div>
            )}
        </div >
    )
}