import React, { useEffect, useState, useContext } from 'react'
import { ToolTip } from '../../components/common'
import getTimeDescription from '../../utils/clubs/guestRestrictionsDescription'
import constantOptions from '../../constants/constantOptions'
import getTier from '../../utils/tiers/getTier'
import ProfileQuestion from './ProfileQuestion'
import Checkbox2 from '../common/Checkbox2'
import Checkbox from '../chat-v2/CheckBox'
import ENDPOINTS from '../../constants/endpoints.json'
import CustomButton from '../buttons/CustomButton'
import useCheckDeviceScreen from '../../hooks/useCheckDeviceScreen'
import useClient from '../../graphql/useClient'
import { FETCH_MIN_CLUBS_REQUIRED_FOR_FAV_VISIBILITY } from '../../graphql/queries/system-settings'
import { ThreeDots } from 'react-loader-spinner'
import { DrawerContext } from '../../context/DrawerContext'
import { useRouter } from 'next/router'
import { UPDATE_USER_CLUB_REVIEW_DISABLED } from '../../graphql/mutations/user'
import { UserContext } from '../../pages/_app'


const {
    CLUB_TIERS: { FERN, SAGE, MOSS, OLIVE },
    PAYMENTOPTIONS: paymentOptions,
    GENDER: { FEMALE, BOTH, MALE }
} = constantOptions

const { OTHER_TG_MEMBERS } = constantOptions?.DRAWER_TYPE


export const ClubInformationCard = ({ club, setEditing, setModal, userClub, getUserClubs, markClubFavouriteRestricted, markingFavorite, setMarkingFavorite }) => {

    const router = useRouter()
    const { user, token, fetchUser } = useContext(UserContext)
    const { drawer, setDrawer } = useContext(DrawerContext)
    const [clubTier, setClubTier] = useState(club?.lowest_visible_tier)
    const [visibleToTiers, setVisibleToTiers] = useState(userClub?.visible_to_tiers)
    const [isVisibleToLowerTiers, setIsVisibleToLowerTiers] = useState(userClub?.is_visibile_to_lower_tiers)
    const { isMobile, isTablet, isDesktop, isWideScreen } = useCheckDeviceScreen()
    const client = useClient()
    const [myFavClubs, setMyFavClubs] = useState([])
    const [minFavClubsRequired, setMinFavClubsRequired] = useState();
    const [genderPref, setGenderPref] = useState(userClub?.gender_preference)
    const [loading, setLoading] = useState(false)
    let isMounted = 1
    let abortController = new AbortController()

    useEffect(() => {
        if (((isVisibleToLowerTiers !== undefined && visibleToTiers !== undefined && visibleToTiers !== userClub?.visible_to_tiers) ||
            (visibleToTiers === userClub?.visible_to_tiers && isVisibleToLowerTiers !== userClub?.is_visibile_to_lower_tiers))
        ) {
            updateClubLtv()
        }
    }, [visibleToTiers, isVisibleToLowerTiers])

    useEffect(() => {
        setVisibleToTiers(userClub?.visible_to_tiers)
        setIsVisibleToLowerTiers(userClub?.is_visibile_to_lower_tiers)
        setGenderPref(userClub?.gender_preference)
    }, [userClub])

    useEffect(() => {
        if (user) {
            fetchFavoriteClubs();
        }
        return () => {
            isMounted = 0
            abortController.abort()
        }
    }, [user])

    useEffect(() => {
        if (client) {
            fetchMinFavClubsSettings();

        }
        return () => {
            isMounted = 0
            abortController.abort()
        }
    }, [client])

    useEffect(() => {
        const updateClubs = async () => {
            await getUserClubs()
        }
        if (myFavClubs?.length < minFavClubsRequired) {
            updateClubs()
        }
    }, [myFavClubs?.length])

    useEffect(() => {
        const urlClubId = router.query.clubId
        if (urlClubId !== undefined && parseInt(urlClubId) === club?.id) {
            handleOtherTGMembers()
        }
    }, [router.query.clubId])

    const updateClubLtv = async () => {
        try {
            const adminUser = await fetch(ENDPOINTS?.UPDATE_CLUB_LTV, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    ['Authorization']: `Bearer ${token}`,
                },
                body: JSON.stringify({
                    userId: user?.id,
                    clubId: club?.id,
                    visibleToTiers: !isVisibleToLowerTiers ? null : (!visibleToTiers && clubTier < MOSS) ? clubTier + 1 : (!visibleToTiers && clubTier === MOSS) ? OLIVE : visibleToTiers,
                    isVisibleToLowerTiers: isVisibleToLowerTiers
                }),
            }).then(async (response) => {
                if (response.status === 200) {
                    await getUserClubs()
                }
            })
        } catch (error) {
            console.log(error);
        }
    }

    //API - Get my favorite clubs
    const fetchFavoriteClubs = () => {
        try {
            fetch(`..${ENDPOINTS?.FETCH_FAVORITE_CLUBS}`, {
                method: 'POST',
                credentials: 'same-origin',
                headers: {
                    ['Content-Type']: 'application/json',
                },
                body: JSON.stringify({ user_id: user.id, data: true }),
            })
                .then((data) => data.json())
                .then((clubs) => {
                    setMyFavClubs(clubs.data);
                })
        } catch (error) {
            console.error(error)
        }
    }

    //API - Update gender preference of the user club
    const updateClubGenderPreference = (genderPref) => {
        try {
            fetch(`..${ENDPOINTS?.UPDATE_CLUB_GENDER_PREFRENCE}`, {
                method: 'POST',
                credentials: 'same-origin',
                headers: {
                    ['Content-Type']: 'application/json',
                    ['Authorization']: `Bearer ${token}`,
                },
                body: JSON.stringify({
                    userId: user.id,
                    clubId: club?.id,
                    genderPrefrence: genderPref
                }),
            })
                .then((data) => data.json())
                .then(async (data) => {
                    if (data.status) {
                        await getUserClubs()
                    }
                })
        } catch (error) {
            console.error(error)
        }
    }

    // Fetch fav club admin setting
    const fetchMinFavClubsSettings = async () => {
        const { system_setting } = await client.request(FETCH_MIN_CLUBS_REQUIRED_FOR_FAV_VISIBILITY)
        setMinFavClubsRequired(system_setting.length ? system_setting[0]?.value?.value : 0)
    }

    const handleOtherTGMembers = () => {
        setDrawer({
            type: OTHER_TG_MEMBERS,
            global: true,
            club
        })
    }

    const handleReviewPermission = async (val) => {
        setLoading(true)
        client.request(UPDATE_USER_CLUB_REVIEW_DISABLED, {
            user_id: user.id,
            club_id: club.id,
            review_disabled: val
        })
            .then(async () => {
                await fetchUser()
                setLoading(false)
            })
            .catch((error) => {
                console.error(error)
                setLoading(false)
            })
    }

    return (
        <div key={club.id} className={`w-full mb-lg ${!isMobile && 'pr-lg'}`}>
            <div className="bg-white rounded shadow flex flex-col">
                <div className="flex pl-lg py-md pr-sm">
                    <div className={`flex-1 pr-2 font-bold  ${isMobile ? 'flex-col text-16' : 'flex-row items-center text-20'}`}
                        style={{ color: "#098089", display: 'flex', position: 'relative' }}
                    >
                        {club.name}
                        {club.club_demand_type != constantOptions?.CLUB_DEMAND_TYPE?.NEUTRAL ?
                            <div className='text-darkteal bg-tealTierBg flex-center rounded-lg py-xs px-xs text-14 font-normal ml-md'>{club.club_demand_type === constantOptions?.CLUB_DEMAND_TYPE?.OVER_REQUESTED ? 'Highly Requested' : 'Unexplored'}</div>
                            : null}
                    </div>

                    {(!isDesktop && !isWideScreen) ? (
                        <div className={`flex-1 flex justify-end cursor-pointer ${isMobile ? 'pb-md' : ''}`}>
                            <img width={20} onClick={handleOtherTGMembers} style={{ marginRight: 10 }} src='/svg/OtherTgMembers.svg' />
                            <img width={17} onClick={() => setEditing(club)}
                                src='/svg/EditTeal.svg' />

                        </div>
                    ) : (

                        <div className="flex-1 flex justify-end items-center">
                            <div className='text-darkteal underline pr-md text-16 cursor-pointer'
                                onClick={handleOtherTGMembers}
                            >
                                Other TG Members
                            </div>
                            <CustomButton
                                buttonImage='/svg/EditBlack.svg'
                                text='Edit Club'
                                onClick={() => setEditing(club)}
                                width={95}
                                height={30}
                                borderColor={'grayLight'}
                                textColor='black'
                                color='lightestgray'
                            />
                        </div>
                    )}

                </div>
                <div className={`flex pl-lg ${isMobile ? 'flex-row items-center' : 'flex-col'} py-sm`} style={{
                    background: "#F6F4F4"
                }}>
                    <div className={`text-${isMobile ? '16' : '18'}`}>Club Information</div>
                    {isMobile ? (
                        <div className='ml-xs relative'>
                            <ToolTip
                                style={{
                                    width: 200
                                }}
                                tip={'If you change any club-related information it would get reflected to all the members of the club'}
                            />
                        </div>
                    ) : (
                        <div className="text-grayLight">
                            If you change any club-related information it would get reflected to all the members of the club
                        </div>
                    )}
                </div>


                <div className={`px-lg flex flex-wrap ga gap-1 grid grid-cols-${isMobile ? '1' : isTablet ? '2' : '3'}`}>

                    <div className={`flex flex-col ${isMobile ? 'text-12' : 'text-base'} my-md`}>
                        <div className='flex'>
                            <div className="text-grayLight">
                                Accompanied Guest Fee:
                            </div>
                            <div className="relative pl-1 mt-1 flex-1" style={{
                                minWidth: 150

                            }}>
                                <ToolTip tip={"Please add Guest Fee (including currency)"} />
                            </div>
                        </div>

                        <div className={`capitalize ${isMobile ? 'text-base' : 'text-16'}`}>
                            {club?.guestFee}
                        </div>
                    </div>

                    <div className={`flex flex-col ${isMobile ? 'text-12' : 'text-base'} my-md`}>
                        <div className="text-grayLight">
                            Caddie Required
                        </div>
                        <div className={`${isMobile ? 'text-base' : 'text-16'}`}>
                            {club.caddieRequired
                                ? `Yes`
                                : 'No'}
                        </div>

                    </div>

                    {club.caddieRequired && (
                        <div className={`flex flex-col ${isMobile ? 'text-12' : 'text-base'} my-md`}>
                            <div className="text-grayLight">
                                Caddie Fees (Incl. tips)
                            </div>
                            <div className={`${isMobile ? 'text-base' : 'text-16'}`}>
                                {club.caddieFee}
                            </div>
                        </div>
                    )}

                    <div className={`flex flex-col ${isMobile ? 'text-12' : 'text-base'} my-md`}>
                        <div className="text-grayLight">
                            Dress Code
                        </div>
                        <div className={`${isMobile ? 'text-base' : 'text-16'}`}>
                            {club.dressCode}
                        </div>
                    </div>

                    {
                        club?.closurePeriods?.length ?
                            <div className='flex'>
                                <div className={`flex flex-col ${isMobile ? 'text-12' : 'text-base'} my-md`}>
                                    <div className='flex'>
                                        <div className="text-grayLight flex">
                                            Closure Periods
                                        </div>
                                        <div className="relative pl-1 mt-1 flex-1" style={{
                                            minWidth: 150

                                        }}>
                                            <ToolTip tip={"Please indicate when your club is closed for the season or for reasons such as renovation. This only applies to clubs that shut for a significant time period each year"} />
                                        </div>
                                    </div>

                                    <div className={`capitalize ${isMobile ? 'text-base' : 'text-16'}`}>
                                        {
                                            club.closurePeriods.map((period, i) => {
                                                return <p key={i}>{`${period?.from} - ${period?.to}`}</p>
                                            })
                                        }
                                    </div>
                                </div>
                            </div>
                            : ""
                    }

                    <div className='flex'>
                        <div className={`flex flex-col my-md ${isMobile ? 'text-14' : 'text-16'} capitalize`}>
                            <div className={`text-grayLight ${isMobile ? 'text-12' : 'text-base'}`}>
                                Club Availability for Guest Play
                            </div>
                            {club.guest_time_restrictions &&
                                getTimeDescription(
                                    club.guest_time_restrictions
                                ).map((timeRange, i) => (
                                    <div key={i}>{timeRange}</div>
                                ))}
                        </div>
                        <div className="flex-1 pl-1 mt-5 relative">
                            <ToolTip tip={"Please indicate which days of the week  are allowable for guest play at your club"} />
                        </div>
                    </div>

                    <div className="flex flex-col my-md">
                        <div className={`text-grayLight ${isMobile ? 'text-12' : 'text-base'}`}>
                            Clubs's Tier
                        </div>
                        <div className={`${isMobile ? 'text-14' : 'text-16'}`}>
                            {getTier(clubTier)}
                        </div>
                    </div>

                </div>


                <div className={`flex pl-lg ${isMobile ? 'flex-row items-center' : 'flex-col'} py-sm`} style={{
                    background: "#F6F4F4"
                }}>
                    <div className={`text-${isMobile ? 16 : 18}`}>Member Specific Information</div>
                    {isMobile ? (
                        <div className='ml-xs relative'>
                            <ToolTip
                                style={{
                                    width: 200,
                                    left: -85
                                }}
                                tip={"This information would be specific to user for the selected club."}
                            />
                        </div>
                    ) : (
                        <div className="text-grayLight">This information would be specific to user for the selected club.</div>
                    )}
                </div>

                <div className='w-2/3 flex flex-wrap ga gap-1 grid grid-cols-2 border-b border-lightestgray mx-lg'>
                    <div className="flex flex-col my-md">
                        <div className={`text-grayLight ${isMobile ? "text-12" : "text-base"}`}>
                            Preferred Method of Green Fees
                            Settlement
                        </div>
                        <div className={`capitalize ${isMobile ? 'text-base' : 'text-16'}`}>
                            {club.paymentMethod ?
                                club?.paymentMethod?.type && club?.paymentMethod?.type === 'digital'
                                    ? club?.paymentMethod?.digitalPaymentType
                                    : club?.paymentMethod?.type
                                : 'Cash & Cheque'}
                        </div>
                    </div>

                    {
                        (club?.otherInstructions !== null && club?.otherInstructions !== '') ?
                            <div className="relative flex flex-col my-md">
                                <div className="text-grayLight">
                                    <div className={`flex float-left ${isMobile ? 'text-12' : 'text-14'}`}>
                                        Other Instructions
                                    </div>
                                    <div className="pl-1 flex float-left mt-1">
                                        <ToolTip tip={"Please share any other information that you would like your guest to know"} />
                                    </div>
                                </div>
                                <div className={`${isMobile ? 'text-14' : 'text-16'}`}>
                                    {club.otherInstructions}
                                </div>
                            </div>
                            : ""
                    }


                    {[paymentOptions.OTHER, paymentOptions.CREDIT_CARD].includes(club?.paymentMethod?.type) && club?.paymentMethod?.otherText && (

                        <div className="flex flex-col my-md">
                            <div className={`text-grayLight ${isMobile ? 'text-12' : 'text-14'}`}>
                                Other Payment Information
                            </div>
                            <div className={`capitalize ${isMobile ? 'text-14' : 'text-16'}`}>
                                {club?.paymentMethod?.otherText}
                            </div>
                        </div>
                    )}
                </div>

                {/* Mute club button */}
                <div className='flex py-md px-lg'>
                    <div className='flex-col flex-1'>
                        <div className={`text-${isMobile ? 16 : 18}`}>Mute Club</div>
                        <div className={`text-grayLight pr-md ${isMobile ? 'text-12' : ''}`}>Turning this on will stop any incoming request on this club for the chosen duration</div>
                    </div>
                    <div className='flex-1 flex-col flex float-start'>
                        <ProfileQuestion
                            togglePosition={!isMobile ? '' : 'justify-end'}
                            value={club?.muted}
                            update={(val) => {
                                setModal({
                                    type: 'mute-user-club',
                                    title: club?.muted ? "Unmute Club" : `Mute ${club.name}`,
                                    club_ids: [club.id],
                                    user_id: user.id,
                                    muted: !club.muted,
                                    name: club.name,
                                })
                            }}
                        /></div>
                </div>

                {/*  Request Preferences Section */}
                <div className={`flex pl-lg ${isMobile ? 'flex-row items-center' : 'flex-col'} py-sm`} style={{
                    background: "#F6F4F4"
                }}>
                    <div className={`text-${isMobile ? 16 : 18}`}>Request Preferences</div>
                </div>

                {/* Club LTV settings */}
                {![constantOptions?.CLUB_TIERS?.OLIVE, constantOptions?.CLUB_TIERS?.TEAL].includes(clubTier) && user?.visibleToPublic ? (
                    <div className='flex py-md border-b border-lightestgray px-lg'>
                        <div className='flex-col flex-1'>
                            <div className={`text-${isMobile ? '16' : '18'}`}>Receive requests from lower tiers</div>
                            <div className={`text-grayLight pr-md ${isMobile ? 'text-12' : ''}`}>Making yourself visible to lower tier members enables them to make a request to you</div>
                            {(isMobile) && (
                                <div className={`flex text-14 ${isMobile ? 'flex-col' : 'flex-row'}`}>
                                    {(isVisibleToLowerTiers && clubTier === FERN) && (
                                        <Checkbox
                                            customClasses={'text-sm font-light'}
                                            label="Visible to Sage ?"
                                            value={true}
                                            update={(val) => {
                                                if (visibleToTiers === MOSS) {
                                                    setVisibleToTiers(SAGE)
                                                } else {
                                                    null
                                                }
                                            }}
                                        />
                                    )}
                                    {(isVisibleToLowerTiers && clubTier <= SAGE) && (
                                        <Checkbox
                                            customClasses={`text-sm font-light`}
                                            label="Visible to Moss ?"
                                            value={visibleToTiers === MOSS || visibleToTiers === OLIVE || clubTier === SAGE}
                                            update={(val) => {
                                                if (clubTier === SAGE && visibleToTiers === MOSS) {
                                                    null
                                                } else if (visibleToTiers === OLIVE || visibleToTiers === SAGE) {
                                                    setVisibleToTiers(MOSS)
                                                } else if (visibleToTiers === MOSS) {
                                                    setVisibleToTiers(SAGE)
                                                }
                                            }}
                                        />
                                    )}
                                    {(isVisibleToLowerTiers && clubTier <= MOSS) && (
                                        <Checkbox
                                            customClasses={`text-sm font-light`}
                                            label="Visible to Olive ?"
                                            value={visibleToTiers === OLIVE || clubTier === MOSS}
                                            update={(val) => {
                                                if (visibleToTiers === OLIVE) {
                                                    setVisibleToTiers(MOSS)
                                                } else {
                                                    setVisibleToTiers(OLIVE)
                                                }
                                            }}
                                        />
                                    )}
                                </div>
                            )}
                        </div>
                        <div className='flex-1 flex-col flex'>
                            <ProfileQuestion
                                togglePosition={!isMobile ? '' : 'justify-end'}
                                value={isVisibleToLowerTiers}
                                update={(val) => {
                                    setIsVisibleToLowerTiers(val);
                                }}
                            />
                            {(!isMobile) && (
                                <div className='flex-col'>
                                    {(isVisibleToLowerTiers && clubTier === FERN) && (
                                        <Checkbox
                                            customClasses={'text-sm font-light'}
                                            label="Visible to Sage ?"
                                            value={true}
                                            update={(val) => {
                                                if (visibleToTiers === MOSS) {
                                                    setVisibleToTiers(SAGE)
                                                } else {
                                                    null
                                                }
                                            }}
                                        />
                                    )}
                                    {(isVisibleToLowerTiers && clubTier <= SAGE) && (
                                        <Checkbox
                                            customClasses={`text-sm font-light`}
                                            label="Visible to Moss ?"
                                            value={visibleToTiers === MOSS || visibleToTiers === OLIVE || clubTier === SAGE}
                                            update={(val) => {
                                                if (clubTier === SAGE && visibleToTiers === MOSS) {
                                                    null
                                                } else if (visibleToTiers === OLIVE || visibleToTiers === SAGE) {
                                                    setVisibleToTiers(MOSS)
                                                } else if (visibleToTiers === MOSS) {
                                                    setVisibleToTiers(SAGE)
                                                }
                                            }}
                                        />
                                    )}
                                    {(isVisibleToLowerTiers && clubTier <= MOSS) && (
                                        <Checkbox
                                            customClasses={`text-sm font-light`}
                                            label="Visible to Olive ?"
                                            value={visibleToTiers === OLIVE || clubTier === MOSS}
                                            update={(val) => {
                                                if (visibleToTiers === OLIVE) {
                                                    setVisibleToTiers(MOSS)
                                                } else {
                                                    setVisibleToTiers(OLIVE)
                                                }
                                            }}
                                        />
                                    )}
                                </div>
                            )}
                        </div>
                    </div>
                ) : (
                    null
                )}

                {/*  Restrict favourite club    */}
                <div className='flex py-md px-lg'>
                    <div className='flex-col flex-1'>
                        <div className={`text-${isMobile ? 16 : 18}`}>Receive Request only from My Favourite Club Members

                            {/* </span> */}
                        </div>
                        <div className={`text-grayLight pr-md ${isMobile ? 'text-12' : ''}`}>Receive requests only from my Favorite Club Members</div>
                    </div>
                    <div className='flex-1 flex-col flex float-start'>
                        {markingFavorite
                            ? <ThreeDots
                                visible={true}
                                height="50"
                                width="50"
                                color={"#098089"}
                                radius="9"
                                ariaLabel="three-dots-loading"
                                wrapperStyle={{}}
                                wrapperClass=""
                            />
                            : <ProfileQuestion
                                togglePosition={!isMobile ? '' : 'justify-end'}
                                value={userClub?.favorite_restricted}
                                update={(val) => {
                                    if (userClub?.favorite_restricted) {
                                        markClubFavouriteRestricted(userClub.club_id, !userClub?.favorite_restricted)
                                    } else {
                                        if (myFavClubs?.length < minFavClubsRequired) {
                                            setModal({
                                                type: 'enhance-favorite',
                                                title: '',
                                                setVisibility: () => { markClubFavouriteRestricted(club.id, true) },
                                                clubId: userClub.club_id,
                                                drawer: drawer,
                                                setDrawer: setDrawer
                                            })
                                        }
                                        else {
                                            markClubFavouriteRestricted(userClub.club_id, !userClub?.favorite_restricted)
                                        }
                                    }
                                }}
                            />}
                    </div>
                </div>

                {/* Club Gender preference */}

                {user?.visibleToPublic ? (
                    <div className='flex py-md px-lg'>
                        <div className='flex-col flex-1'>
                            <div className={`text-${isMobile ? '16' : '18'}`}>Receive request on Gender Preferences</div>
                            <div className={`text-grayLight pr-md ${isMobile ? 'text-12' : ''}`}>Set up Gender preference to receive request on this club</div>
                            {(isMobile) && (
                                <div className={`flex text-14 mt-3 ${isMobile ? 'flex-row' : 'flex-row'}`}>
                                    <div className='mr-4'> <Checkbox2
                                        label="Both"
                                        value={genderPref === BOTH}
                                        update={(value) => {
                                            setGenderPref(BOTH)
                                            updateClubGenderPreference(BOTH)
                                        }
                                        }
                                        roundedButton={true}
                                    /></div>
                                    {user?.gender == 'Male' ? <Checkbox2
                                        label="Male"
                                        value={genderPref === MALE}
                                        update={(value) => {
                                            setGenderPref(MALE)
                                            updateClubGenderPreference(MALE)
                                        }
                                        }
                                        roundedButton={true}
                                    />
                                        : <Checkbox2
                                            label="Female"
                                            value={genderPref === FEMALE}
                                            update={(value) => {
                                                setGenderPref(FEMALE)
                                                updateClubGenderPreference(FEMALE)
                                            }
                                            }
                                            roundedButton={true}
                                        />}

                                </div>
                            )}
                        </div>
                        <div className='flex-1 flex-col flex'>
                            {(!isMobile) && (
                                <div className='flex-col'>
                                    <div className='mb-1'>
                                        <Checkbox2
                                            label="Both"
                                            value={genderPref === BOTH}
                                            update={(value) => {
                                                setGenderPref(BOTH)
                                                updateClubGenderPreference(BOTH)
                                            }
                                            }
                                            roundedButton={true}
                                        />
                                    </div>
                                    {user?.gender && user?.gender?.toLowerCase() == MALE ? <Checkbox2
                                        label="Male"
                                        value={genderPref === MALE}
                                        update={(value) => {
                                            setGenderPref(MALE)
                                            updateClubGenderPreference(MALE)
                                        }
                                        }
                                        roundedButton={true}
                                    />
                                        : <Checkbox2
                                            label="Female"
                                            value={genderPref === FEMALE}
                                            update={(value) => {
                                                setGenderPref(FEMALE)
                                                updateClubGenderPreference(FEMALE)
                                            }
                                            }
                                            roundedButton={true}
                                        />}
                                </div>
                            )}
                        </div>
                    </div>
                ) : (
                    null
                )}
                <div className='flex py-md px-lg'>
                    <div className='flex-col flex-1'>
                        <div className={`text-${isMobile ? 16 : 18}`}>Disallow game reviews</div>
                        <div className={`text-grayLight pr-md ${isMobile ? 'text-12' : ''}`}>When enabled, guests who complete a game with you at this club will not be asked to submit a review.</div>
                    </div>
                    <div className='flex-1 flex-col flex float-start'>
                        {loading ? <ThreeDots
                            visible={true}
                            height="45"
                            width="45"
                            color={"#098089"}
                            radius="9"
                            ariaLabel="three-dots-loading"
                            wrapperStyle={{}}
                            wrapperClass=""
                        /> :
                            <ProfileQuestion
                                togglePosition={!isMobile ? '' : 'justify-end'}
                                value={club?.review_disabled}
                                update={(val) => {
                                    handleReviewPermission(val)
                                }}
                            />
                        }
                    </div>
                </div>
            </div>
        </div>
    )
}