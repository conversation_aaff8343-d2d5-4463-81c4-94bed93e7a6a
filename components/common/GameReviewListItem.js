import React, { useContext } from 'react';
import moment from 'moment';
import constantOptions from '../../constants/constantOptions';
import { ModalContext } from '../../context/ModalContext';
import useCheckDeviceScreen from '../../hooks/useCheckDeviceScreen';
import useThumbnail from '../../hooks/useThumbnail';
import { ChatGalleryViewModalContext } from '../modals/ChatGalleryViewModal';

/**
 * Individual review item component for displaying a single game review
 * 
 * @param {Object} props - Component props
 * @param {Object} props.review - Review data to display
 * @param {Array} props.allFriendsId - Array of friend user IDs
 */
const GameReviewListItem = ({ review, allFriendsId, customBackClickHandler, imageHeight = '169px', imageWidth = 'auto' }) => {
  const { setChatImageModal } = useContext(ChatGalleryViewModalContext);
  const { isMobile, isDesktop, isWideScreen } = useCheckDeviceScreen();
  const { thumbnailUrl } = useThumbnail(review?.photo, 256)

  return (
    <div
      key={review?.id}
      className='flex flex-col py-sm px-md border border-lightestgray border-2 rounded-lg mb-sm hover:bg-tealTierBg hover:shadow-lg'
    >
      <div className='flex items-center justify-between'>
        <div className='flex mb-sm'>
          <div className=''>
            <img style={{ minWidth: '18px', minHeight: '18px' }} src='/svg/GolfPostDarkTeal.svg' />
          </div>
          <div className='ml-sm'>
            <div className='text-16'>
              {review?.club_name}
            </div>
            <div className='text-gray'>
              Game Date: {`${moment.utc(review?.game_date).format(constantOptions?.DATE_FORMAT_Do_MM_YYYY)}`}
            </div>
          </div>
        </div>
      </div>

      {(review?.photo && (isDesktop || isWideScreen)) && (
        <div
          className={`rounded-lg my-sm cursor-pointer`}
          onClick={() => {
            setChatImageModal({
              images: [review?.photo],
              imageIndex: 0,
              fallback: review?.photo,
            });
          }}
          style={{
            width: isMobile ? '65px' : imageWidth,
            height: isMobile ? '50px' : imageHeight,
            backgroundImage: `url("${review?.photo}")`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            backgroundRepeat: 'no-repeat',
            position: 'relative',
          }}
        />
      )}

      <div className='py-sm'>
        {review?.review}
      </div>

      {(review?.photo && (!isDesktop && !isWideScreen)) && (
        <div
          className={`rounded-lg my-sm cursor-pointer`}
          onClick={() => {
            setChatImageModal({
              images: [review?.photo],
              imageIndex: 0,
              fallback: review?.photo,
            });
          }}
          style={{
            width: isMobile ? '65px' : imageWidth,
            height: isMobile ? '50px' : imageHeight,
            backgroundImage: `url("${review?.photo}")`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            backgroundRepeat: 'no-repeat',
            position: 'relative',
          }}
        />
      )}
    </div>
  );
};

export default GameReviewListItem;