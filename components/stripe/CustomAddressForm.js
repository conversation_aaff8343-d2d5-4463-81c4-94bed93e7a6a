import React, { useEffect, useState } from 'react';
import TextInput from '../../components/common/signup2/TextInput';
import { Select } from '../common';
import StaticSearchSelect from '../common/StaticSearchSelect';


const CustomAddressForm = ({
    address,
    setAddress,
    formErrors,
    setFormErrors,
    allCountries,
    setCountryCode,
    allStates,
    stateCode,
    setStateCode,
    allCities
}) => {

    const [states, setStates] = useState([])
    const [country, setCountry] = useState('')
    const [finalCountries, setFinalCountries] = useState([])
    const [filteredCountries, setFilteredCountries] = useState([])


    useEffect(() => {
        const priorityCountries = ["United States", "Canada", "United Kingdom", "Australia"];
        const rearrangedCountries = [
            ...allCountries.filter(country => priorityCountries.includes(country.name)),
            ...allCountries.filter(country => !priorityCountries.includes(country.name))
        ];
        setFinalCountries()
        setFinalCountries(rearrangedCountries)

    }, [allCountries])

    useEffect(() => {
        const searchCountries = (data, searchTerm) => {
            return data.filter(country => {
                const lowerCaseSearchTerm = searchTerm.toLowerCase();
                return country.name.toLowerCase().includes(lowerCaseSearchTerm);
            });
        };

        // Ensure finalCountries and country are properly initialized
        if (finalCountries && country) {
            const filteredCountries = searchCountries(finalCountries, country);
            setFilteredCountries(filteredCountries); // Log the filtered list of countries
        }

    }, [country])

    useEffect(() => {
        if (allStates) {
            const newStates = allStates.map((states) => states?.name)
            setStates(newStates);
        }
    }, [allStates])

    return (
        <>
            <div className='mb-md'>
                <TextInput
                    title={'Enter Address'}
                    className={'border border-inputtitle rounded-lg mt-sm px-sm'}
                    value={address?.address?.line1}
                    update={(value) => {
                        setFormErrors({ ...formErrors, line1: '' })
                        setAddress({ ...address, address: { ...address.address, line1: value } })
                    }}
                    error={formErrors?.line1}
                    disableError={!formErrors?.line1}
                    fontSizeClass="text-16"
                />
            </div>
            <div className='flex flex-wrap ga gap-1 grid grid-cols-2'>
                <div className='text-start'>
                    <StaticSearchSelect
                        border={'none'}
                        type={"address"}
                        marginTop='0'
                        addClubClasses={true}
                        title={'Select Country *'}
                        readOnly={!address?.address?.line1}
                        value={address?.address?.country || country}
                        options={country ? filteredCountries : finalCountries}
                        top={59}
                        placeholder="Select Country"
                        className={'border  border-lightgray px-sm rounded-lg mt-sm text-16'}
                        customAddressClasses
                        updateSearch={(val) => {
                            setCountry(val)
                            setAddress({ ...address, address: { ...address.address, country: '' } })
                        }}
                        update={(value) => {
                            setFormErrors({ ...formErrors, country: '' })
                            setAddress({ ...address, address: { ...address.address, country: value?.name, state: '', city: '' } })
                            setCountryCode(value?.isoCode)
                        }}
                        error={formErrors?.country}
                        disableError={!formErrors?.country}
                    />
                </div>
                <div className='mb-md ml-md text-start'>
                    <span className="text-14 text-grayLight font-normal capitalize mb-sm"></span>
                    <Select
                        top={59}
                        addClubClasses={true}
                        title={'Select State'}
                        readOnly={!address?.address?.country}
                        type={'state-address'}
                        className={'border border-black px-sm rounded-lg mt-sm text-16'}
                        marginTop=' '
                        customAddressClasses
                        value={address?.address?.state}
                        states={allStates}
                        placeholder="Select State"
                        update={(value) => {
                            setFormErrors({ ...formErrors, state: '' })
                            setAddress({ ...address, address: { ...address.address, state: value?.name, city: '' } })
                            setStateCode(value?.isoCode)
                        }}
                        error={formErrors?.state}
                        disableError={!formErrors?.state}
                    />
                </div>
                <div className='mb-md text-start'>
                    <Select
                        top={59}
                        addClubClasses={true}
                        title={'Select City'}
                        readOnly={!address?.address?.state}
                        type={'city-address'}
                        cities={allCities}
                        className={'border border-black px-sm rounded-lg mt-sm text-16'}
                        marginTop=' '
                        customAddressClasses
                        value={address?.address?.city}
                        options={allCities}
                        placeholder="Select City"
                        update={(value) => {
                            setFormErrors({ ...formErrors, city: '' })
                            setAddress({ ...address, address: { ...address.address, city: value?.name } })
                        }
                        }
                        error={formErrors?.city}
                        disableError={!formErrors?.city}
                    />
                </div>
                <div className='mb-md ml-md'>
                    <TextInput
                        title={'Enter Postal Code'}
                        className={'border border-inputtitle rounded-lg mt-sm px-sm'}
                        value={address?.address?.postal_code}
                        update={(value) => {
                            if (value.length <= 9) {
                                setFormErrors({ ...formErrors, postal_code: '' })
                                setAddress({ ...address, address: { ...address.address, postal_code: value } })
                            } else null
                        }}
                        error={formErrors?.postal_code}
                        disableError={!formErrors?.postal_code}
                        fontSizeClass="text-16"
                    />
                </div>
            </div>
        </>
    )
}

export default CustomAddressForm