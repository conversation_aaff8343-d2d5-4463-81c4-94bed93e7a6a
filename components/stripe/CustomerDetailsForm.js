import React, { useState, useEffect, useContext, useRef } from 'react';
import TextInput from '../../components/common/signup2/TextInput';
import CustomAddressForm from './CustomAddressForm';
import { EMAIL_REGEX } from '../../utils/validationConstants';
import { PhoneInput } from '../common';
import END_POINTS from '../../constants/endpoints.json'
import MESSAGES from '../../constants/messages';
import Loader, { ThreeDots } from 'react-loader-spinner';
import { UserContext } from '../../pages/_app';
import { useRouter } from 'next/router';
import { Country, State, City } from 'country-state-city';
import Logo from '../layout/Logo';
import constantOptions from '../../constants/constantOptions';
import TealButton from '../buttons/TealButton';
import useCheckDeviceScreen from '../../hooks/useCheckDeviceScreen';
import DiscountApplied from '../common/Promocode/DiscountApplied';
import validatePostalCodeForCountry from '../../utils/helper/validatePostalCodeForCountry';
import setClevertapUser from '../../utils/clevertap/setClevertapUser';
import CustomButton from '../buttons/CustomButton';

const DEFAULT_PROMOCODE = constantOptions.PROMOCODE.DEFAULT

const DEFAULT_ADDRESS = {
    line1: '',
    country: '',
    state: '',
    postal_code: ''
}

const CustomerDetailsForm = ({ setModal }) => {

    const { user, token, membershipData, fetchUser, clevertap } = useContext(UserContext);
    const [address, setAddress] = useState({ name: '', email: '', phone: '', address: DEFAULT_ADDRESS })
    const [formErrors, setFormErrors] = useState({})
    const [loading, setLoading] = useState(false)
    const router = useRouter();
    const [countryCode, setCountryCode] = useState(user?.stripe_customer_info?.countryCode)
    const [stateCode, setStateCode] = useState(user?.stripe_customer_info?.stateCode)
    const [code, setCode] = useState(DEFAULT_PROMOCODE)
    const allCountries = Country.getAllCountries()
    const allStates = State.getStatesOfCountry(countryCode)
    const allCities = City.getCitiesOfState(countryCode, stateCode)
    const { isMobile } = useCheckDeviceScreen()
    const [hasPromoCode, setHasPromoCode] = useState(false)
    const promocodeRef = useRef(null)

    useEffect(() => {
        let isMounted = true;
        if (isMounted) {
            getPromoCode()
        }
        return () => isMounted = false;
    }, [])

    useEffect(() => {
        if (user?.stripe_customer_info) {
            setAddress({ ...address, ...user?.stripe_customer_info })
        }
    }, [user?.stripe_customer_info])

    useEffect(() => {
        if (user && !user?.stripe_customer_info) {
            setAddress({
                ...address,
                // ...user?.stripe_customer_info,
                name: user?.first_name + ' ' + user?.last_name,
                email: user?.email,
                phone: user?.phone,
            })
        } else {
            setAddress({
                // ...address,
                ...user?.stripe_customer_info,
                name: user?.stripe_customer_info?.name,
                email: user?.stripe_customer_info?.email,
                phone: user?.stripe_customer_info?.phone,
            })
        }
    }, [user])

    useEffect(() => {
        if (hasPromoCode && promocodeRef.current) {
            setTimeout(() => {
                promocodeRef.current.scrollIntoView({ behavior: 'smooth', block: 'end' })
            }, 100)
        }
    }, [hasPromoCode])

    async function validate() {
        let errors = {}
        let allClubErrors = {}
        let paymentMethodErrors = {}

        if (!address?.name || address?.name === "" || (address?.name && address?.name.trim() === '')) {
            errors.name = MESSAGES.STRIPE_CUSTOMER_FORM.NAME
        }
        if (!address?.email || address?.email === "" || (address?.email && address?.email.trim() === '')) {
            errors.email = MESSAGES.STRIPE_CUSTOMER_FORM.EMAIL
        }
        if (address?.email && address?.email?.length !== 0) {
            const emailRegex = new RegExp(EMAIL_REGEX)
            if (!emailRegex.test(address?.email)) {
                errors.email = MESSAGES.STRIPE_CUSTOMER_FORM.VALID_EMAIL
            }
        }
        if (!address?.address?.line1 || address?.address?.line1 === "" || (address?.address?.line1 && address?.address?.line1.trim() === '')) {
            errors.line1 = MESSAGES.STRIPE_CUSTOMER_FORM.ADDRESS
        }
        // Date: 21.07.2022 commenting down the validation for State & City because some countries don't have states & cities
        // if (!address?.address?.city || address?.address?.city === "" || (address?.address?.city && address?.address?.city.trim() === '')) {
        //     errors.city = MESSAGES.STRIPE_CUSTOMER_FORM.CITY
        // }
        // if (!address?.address?.state || address?.address?.state === "" || (address?.address?.state && address?.address?.state.trim() === '')) {
        //     errors.state = MESSAGES.STRIPE_CUSTOMER_FORM.STATE
        // }
        if (!address?.address?.country) {
            errors.country = MESSAGES.STRIPE_CUSTOMER_FORM.COUNTRY
        }
        if (!address?.address?.postal_code || address?.address?.postal_code === "" || (address?.address?.postal_code && address?.address?.postal_code.trim() === '')) {
            errors.postal_code = MESSAGES.STRIPE_CUSTOMER_FORM.POSTAL_CODE
        }
        if (address?.address?.postal_code && address?.address?.postal_code?.length !== 0) {
            let isValid = true
            if (countryCode !== 'US') {
                isValid = true
            } else {
                isValid = await validatePostalCodeForCountry(
                    address?.address?.postal_code,
                    address?.address?.country
                )
            }
            if (!isValid) {
                errors.postal_code = MESSAGES.STRIPE_CUSTOMER_FORM.VALID_POSTAL_CODE
            }
        }
        if (!address?.phone) {
            errors.phone = MESSAGES.STRIPE_CUSTOMER_FORM.PHONE_NUMBER
        }
        if (address?.phone && (address?.phone.length < 11 || address?.phone.length > 13)) {
            errors.phone = MESSAGES.STRIPE_CUSTOMER_FORM.VALID_PHONE_NUMBER
        }

        // Add promocode validation when field is displayed
        if (hasPromoCode && (!code?.code || code?.code.trim() === '')) {
            errors.promocode = MESSAGES.PROMOCODE.REQUIRED.CODE
        }
        if (code?.code && !code?.amount && !code?.percent) {
            errors.promocode = MESSAGES?.PROMOCODE?.CODE_ENTERED_NOT_APPLIED
        }

        if (Object.keys(paymentMethodErrors).length > 0) {
            allClubErrors = {
                ...errors,
                paymentMethod: paymentMethodErrors,
            }
        } else {
            allClubErrors = { ...errors }
        }
        setFormErrors(allClubErrors)
        return (
            Object.values(allClubErrors).filter(
                (value) => Object.values(value).length > 0
            ).length === 0
        )
    }

    const submitForm = async () => {
        setLoading(true)
        const isValid = await validate()
        if (isValid) {
            var customer = await fetch(END_POINTS.STRIPE_CUSTOMER, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    ['Authorization']: `Bearer ${token}`,
                },
                body: JSON.stringify({
                    user_id: user?.id,
                    name: address.name,
                    phone: address.phone,
                    email: address.email,
                    address: address.address,
                    countryCode: countryCode,
                    stateCode: stateCode
                }),
            })
                .then(data => data.json())
                .then((data) => {
                    if (data?.status) {
                        setLoading(false)
                        clevertap.event.push(constantOptions?.CLEVERTAP_EVENTS.MEMBERSHIP_DETAILS_CONTINUE);
                        setClevertapUser(clevertap, user);
                        clevertap.profile.push({
                            "Site": {
                                [constantOptions?.CLEVERTAP.LAST_ONBOARDING_STEP_COMPLETED]: constantOptions.SIGNUP_STEPS.PROCEED_FOR_MEMBERSHIP,
                                [constantOptions?.CLEVERTAP.COUNTRY]: address?.address?.country,
                                [constantOptions?.CLEVERTAP.STATE]: address?.address?.state,
                                [constantOptions?.CLEVERTAP.CITY]: address?.address?.city,
                                [constantOptions?.CLEVERTAP.POSTAL_CODE]: address?.address?.postal_code,
                                [constantOptions?.CLEVERTAP.PROMOCODE]: code.code ? code.code : "NA"

                            }
                        });

                        fetchUser()
                        router.push('/membership/payment')
                    } else {
                        setFormErrors({ ...formErrors, API: `${data?.message}: ${data?.error}` })
                        setLoading(false)
                    }
                })
                .catch(() => {
                    setFormErrors({ ...formErrors, API: MESSAGES[400] })
                    setLoading(false)
                })
            return customer;
        }
        setLoading(false)
    }

    const getPromoCode = async () => {
        if (user?.id) {
            setLoading(true);
            await fetch(END_POINTS.GET_PROMOCODE, {
                method: 'POST',
                headers: {
                    ['Content-Type']: 'application/json',
                    ['Authorization']: `Bearer ${token}`,
                },
                body: JSON.stringify({
                    userId: user?.id
                })
            })
                .then((data) => data.json())
                .then((data) => {
                    if (data?.data?.code) {
                        setHasPromoCode(true)
                    } else {
                        setHasPromoCode(false)
                    }
                    setCode(data?.data)

                })
                .catch(error => console.log('An error occurred', error))
            setLoading(false);
        }
    }

    const applyPromoCode = async () => {
        if (user?.id && (code?.code && code?.code?.trim())) {
            setLoading(true);
            await fetch(END_POINTS.APPLY_PROMOCODE, {
                method: 'POST',
                headers: {
                    ['Content-Type']: 'application/json',
                    ['Authorization']: `Bearer ${token}`,
                },
                body: JSON.stringify({
                    userId: user?.id,
                    code: code?.code
                })
            })
                .then((data) => data.json())
                .then(async (data) => {
                    if (!data?.status || !data?.applied) {
                        setFormErrors({ ...formErrors, promocode: data?.message })
                    } else {
                        await getPromoCode()
                    }
                })
                .catch(error => setFormErrors({ ...formErrors, promocode: error || error?.message }))
            setLoading(false);
        } else {
            setFormErrors({ ...formErrors, promocode: MESSAGES.PROMOCODE.REQUIRED.CODE })
        }
    }

    const removePromoCode = async () => {
        if (user?.id && (code?.amount || code?.percent)) {
            setLoading(true);
            await fetch(END_POINTS.REMOVE_PROMOCODE, {
                method: 'POST',
                headers: {
                    ['Content-Type']: 'application/json',
                    ['Authorization']: `Bearer ${token}`,
                },
                body: JSON.stringify({
                    userId: user?.id,
                    code: code?.code
                })
            })
                .then((data) => data.json())
                .then(async (data) => {
                    if (!data?.status) {
                        setFormErrors({ ...formErrors, promocode: data?.message })
                    } else {
                        setCode(DEFAULT_PROMOCODE)
                        setHasPromoCode(false)
                    }
                })
                .catch(error => setFormErrors({ ...formErrors }))
            setLoading(false);
        }
    }


    return (
        <div className='flex flex-col w-full xl:px-420 lg:px-200 xxs:px-md ' ref={promocodeRef}>
            <div className='hidden md:block text-center font-medium xxs:text-18 sm:text-32 py-sm'>Thousand Greens Membership</div>
            <div className='flex justify-between mt-md px-sm mb-md md:hidden'>
                <Logo height={26} width={35} />
                <div className='font-medium text-18'>Member's Details</div>
                <div onClick={() => {
                    setModal(true)
                }}>
                    <img height={18} width={18} className='' src='/svg/SignupLogoutIcon.svg' />
                </div>
            </div>
            <div className='hidden md:block text-center font-medium text-32 py-sm'>Member's Details</div>

            {
                /**
                 * Let's show an error if the last payment of the user was declined due to any reason
                 */
                (
                    membershipData?.lastIntentStatus?.isCompleted &&
                    membershipData?.lastIntentStatus?.intentStatus === constantOptions?.STRIPE_RESPONSE_CODES?.DECLINED?.key &&
                    membershipData?.lastIntentStatus?.source === constantOptions?.PAYMENT_ATTEMPTED_FROM?.BACK_END) ?
                    <p className="text-red my-sm text-center">The attempt to your last payment was declined.
                        <br />
                        Please review and retry with correct payment details.
                    </p>
                    : ""
            }

            <div className='py-lm justify-between flex flex-col'>
                <div className='mb-md'>
                    <TextInput
                        title={'Enter Name'}
                        className={'border border-inputtitle rounded-lg mt-sm px-sm'}
                        value={address?.name}
                        update={(value) => {
                            setFormErrors({ ...formErrors, name: '' })
                            setAddress({ ...address, name: value })
                        }}
                        error={formErrors?.name}
                        disableError={!formErrors?.name}
                        fontSizeClass="text-16"
                    />
                </div>
                <div className='mb-md'>
                    <TextInput
                        title={'Enter Email'}
                        className={'border border-inputtitle rounded-lg mt-sm px-sm'}
                        value={address?.email}
                        update={(value) => {
                            setFormErrors({ ...formErrors, email: '' })
                            setAddress({ ...address, email: value })
                        }}
                        error={formErrors?.email}
                        disableError={!formErrors?.email}
                        fontSizeClass="text-16"
                    />
                </div>

                <div className='mb-md' style={{ zIndex: 100 }}>
                    <span className="text-14 text-grayLight font-normal capitalize flex mb-sm">Phone</span>
                    <PhoneInput
                        className={'border border-inputtitle rounded-lg font-normal'}
                        value={address?.phone}
                        update={(value) => {
                            setFormErrors({ ...formErrors, phone: '' })
                            setAddress({ ...address, phone: value })
                        }}
                        error={formErrors?.phone}
                        fontSizeClass="text-16"
                    />
                </div>

                <CustomAddressForm
                    address={address}
                    setAddress={setAddress}
                    formErrors={formErrors}
                    setFormErrors={setFormErrors}
                    allCountries={allCountries}
                    countryCode={countryCode}
                    setCountryCode={setCountryCode}
                    allStates={allStates}
                    stateCode={stateCode}
                    setStateCode={setStateCode}
                    allCities={allCities}
                />
                {/* <TextInput
                    optionalField={true}
                    title={'Enter Promocode(Optional)'}
                    className={'border border-inputtitle rounded-lg mt-sm px-sm'}
                /> */}
                <div className='text-14'><span className='text-darkteal'>Note:</span> This information is used to create a Stripe account</div>
            </div>


            <>
                <div className="mb-xl flex-col flex z-10" ref={promocodeRef}>
                    <span className="text-14 text-grayLight font-normal capitalize flex mb-sm">Do you have a promo code?</span>
                    <div className="flex col-gap-3 mb-sm">
                        <CustomButton
                            text="Yes"
                            width={80}
                            height={38}
                            color={hasPromoCode ? "darkteal" : "white"}
                            textColor={hasPromoCode ? "white" : "darkteal"}
                            borderColor="darkteal"
                            onClick={() => setHasPromoCode(true)}
                            cssClassName="mr-sm transition-all duration-300 transform hover:scale-105"
                            marginX='0'
                        />
                        <CustomButton
                            text="No"
                            width={80}
                            height={38}
                            color={!hasPromoCode ? "darkteal" : "white"}
                            textColor={!hasPromoCode ? "white" : "darkteal"}
                            borderColor="darkteal"
                            onClick={() => {
                                setHasPromoCode(false);
                                setFormErrors({ ...formErrors, promocode: undefined });
                            }}
                            cssClassName="transition-all duration-300 transform hover:scale-105"
                        />
                    </div>
                    <div className={`overflow-hidden transition-all duration-500 ease-in-out ${hasPromoCode ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'}`}>
                        <div className="flex col-gap-3 mt-sm" style={{ flex: "0 0 100%" }}>
                            <div className="relative flex-grow-2 mr-sm">
                                <input
                                    className={`border transition-all duration-300 ${formErrors?.promocode
                                        ? "border-error text-error"
                                        : "border-inputtitle hover:border-darkteal"
                                        } ${(code?.amount || code?.percent) && "bg-lightestgray"} 
                                    rounded-lg px-sm border-b text-16 w-full py-sm placeholder-gray`}
                                    style={{
                                        borderRadius: 8,
                                        height: 38,
                                        outline: 'none',
                                        transition: 'all 0.3s ease-in-out'
                                    }}
                                    value={code?.code}
                                    title={"Enter Promo Code"}
                                    placeholder="Enter Promo Code (Optional)"
                                    maxLength={15}
                                    readOnly={code?.amount || code?.percent}
                                    onChange={(e) => {
                                        if (code?.amount || code?.percent) {
                                            // No changes if code is already there
                                        } else {
                                            setFormErrors({ ...formErrors, promocode: undefined })
                                            setCode({
                                                ...code,
                                                code: (e.target.value)?.toUpperCase()
                                            })
                                        }
                                    }}
                                />
                            </div>
                            <TealButton
                                loading={loading}
                                disabled={loading}
                                disableArrow={true}
                                height={38}
                                width={"auto"}
                                textSize={13}
                                minWidth={120}
                                text={`${(code?.amount || code?.percent) ? 'Remove ' : 'Apply '}${isMobile ? "" : "Promo Code"}`}
                                onClick={() => {
                                    if (!loading) {
                                        (code?.amount || code?.percent) ?
                                            removePromoCode()
                                            :
                                            applyPromoCode()
                                    }
                                }}
                                cssClassName="transition-all duration-300 transform hover:scale-105"
                            />
                        </div>
                        <div className="flex mt-sm" style={{ columnGap: 3, flex: "0 0 100%" }}>
                            {
                                formErrors?.promocode ?
                                    <span className="text-12 font-normal text-error animate-fade-in">{formErrors?.promocode}</span>
                                    :
                                    <DiscountApplied
                                        code={code}
                                        plan={membershipData?.planToAssign}
                                    />
                            }
                        </div>
                    </div>
                </div>
            </>

            <div className='flex-center my-md text-red'>
                {formErrors?.API}
            </div>
            <div className='flex-center my-md '>
                <CustomButton
                    text="Continue"
                    width={331}
                    height={45}
                    color="darkteal"
                    textColor="white"
                    borderColor="darkteal"
                    onClick={() => {
                        submitForm(true)
                    }}
                    cssClassName="transition-all duration-300 transform hover:scale-105"
                    marginX='0'
                    loading={loading}
                />
            </div>
        </div >
    )
}

export default CustomerDetailsForm