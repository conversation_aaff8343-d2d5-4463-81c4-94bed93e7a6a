import React, { useState, useEffect, useContext } from 'react'
import firebase from 'firebase/compat/app'
import 'firebase/compat/auth'
import { UserContext } from '../../../pages/_app'
import useClient from '../../../graphql/useClient'
import { ModalContext } from '../../../context/ModalContext'
import { PhoneInput } from '../../common'
import { TextInput } from '../../common'
import Select from '../../common/Select'
import { TextArea } from '../../common'
import { ABOUT_YOURSELF_MAX_LENGTH } from '../../../graphql/queries/user'
import { EMAIL_REGEX } from '../../../utils/validationConstants'
import MESSAGES from "../../../constants/messages"
import { VALIDATE_PHONE } from '../../../graphql/queries/user'
import adminClient from '../../../graphql/adminClient'
import useCheckDeviceScreen from '../../../hooks/useCheckDeviceScreen'
import constantOptions from '../../../constants/constantOptions'
import Checkbox2 from '../../common/Checkbox2'

const { MALE, FEMALE } = constantOptions.GENDER

const UPDATE_USER = `
mutation updateUser($id: uuid!, $changes: user_set_input) {
    update_user(where: {id: {_eq: $id}}, _set: $changes) {
      affected_rows
    }
}
`
const VALIDATE_USERNAME = `
query validateUsername($username: String, $id: uuid!) {
    user(where: {username: {_eq: $username}, id: {_neq: $id}}) {
      username
    }
}
`
const VALIDATE_EMAIL = `
query validateEmail($email: String, $id: uuid!) {
    user(where: {email: {_ilike: $email}, id: {_neq: $id}}) {
      email
    }
}
`
export default function EditPersonalProfileForm({ cancel }) {
    const { user, fetchUser } = useContext(UserContext)
    const [clubs, setClubs] = useState(user.clubs)
    const [firstName, setFirstName] = useState(user.first_name)
    const [lastName, setLastName] = useState(user.last_name)
    const [userName, setUserName] = useState(user.username)
    const [gender, setGender] = useState(user.gender)
    const [birthYear, setBirthyear] = useState(user.birthYear)
    const [email, setEmail] = useState(user.email)
    const [phone, setPhone] = useState(user.phone)
    const [phoneNumberDetails, setPhoneNumberDetails] = useState({ ...user?.phone_number_details });
    const [verfiedPhone, setVerifiedPhone] = useState(user?.verified_on?.phone)
    const [verfiedEmail, setVerifiedEmail] = useState(user?.verified_on?.email)
    const [facebook, setFacebook] = useState(user.facebook)
    const [facebookInitialUrl] = useState('https://www.facebook.com/')
    const [linkedin, setLinkedin] = useState(user.linkedin)
    const [linkedinInitialUrl] = useState('https://www.linkedin.com/in/')
    const [aboutYourself, setAboutYourself] = useState(user.about_yourself)
    const [golfAssociation, setGolfAssociation] = useState(user.association)
    const [ghin, setGhin] = useState(user.user_association_number)
    const [pace, setPace] = useState(user.pace)
    const [golfIndex, setGolfIndex] = useState(user.handicap)
    const [englishFluency, setEnglishFluency] = useState(user.englishFluency)
    const [couple, setCouple] = useState(user.playAsCouple ? 'yes' : 'no')
    const [editing, setEditing] = useState()
    const client = useClient()
    const { modal, setModal } = useContext(ModalContext)
    const [newEmail, setNewEmail] = useState(false)
    const [newPhone, setNewPhone] = useState(false)
    const [formErrors, setFormErrors] = useState(false)
    const [emailError, setEmailError] = useState()
    const [personalProfileError, setPersonalProfileError] = useState(false)
    const [loading, setLoading] = useState(false)
    const [globalDropdownVisible, setGlobalDropdownVisible] = useState(false);
    const [duplicatePhoneError, setDuplicatePhoneError] = useState();
    const { isMobile, isTablet, isDesktop, isWideScreen } = useCheckDeviceScreen()
    const { GENDER: { FEMALE, MALE } } = constantOptions




    useEffect(() => {
        if (user.phone !== phone) {
            setNewPhone(phone && phone.length > 10)
            setVerifiedPhone(false)
        } else {
            setNewPhone(false)
        }
    }, [phone])

    useEffect(() => {
        if (user.email !== email) {
            setNewEmail(true)
            setVerifiedEmail(false)
        } else {
            setNewEmail(false)
        }
    }, [email])

    useEffect(() => {
        if (linkedin) {
            if (linkedin?.toLowerCase().includes('http') || linkedin.toLowerCase().includes('://') || linkedin.toLowerCase().includes(`linkedin.com/`)) {
                const regex = /(https?:\/\/)/i;
                const regex1 = /w{3}\./i;
                const newValue = linkedin?.toLowerCase().replace(regex, '').replace(regex1, '').replace(`linkedin.com/in/`, '');
                setLinkedin(newValue);
            } else {
                setLinkedin(linkedin)
            }
        }
    }, [linkedin])

    useEffect(() => {
        if (facebook) {
            if (facebook?.toLowerCase().includes('http') || facebook.toLowerCase().includes('://') || facebook.toLowerCase().includes(`facebook.com/`)) {
                const regex = /(https?:\/\/)/i;
                const regex1 = /w{3}\./i;
                const newValue = facebook?.toLowerCase().replace(regex, '').replace(regex1, '').replace(`facebook.com/`, '');
                setFacebook(newValue);
            } else {
                setFacebook(facebook)
            }
        }
    }, [facebook])

    const validatePhone = async () => {
        const { user_aggregate: { aggregate: { count: duplicatePhone } } } = await adminClient.request(VALIDATE_PHONE, {
            phone: `%${phone}`
        })
        if (duplicatePhone > 0) {
            setDuplicatePhoneError(MESSAGES.edit_personal_profile.errors.validation.duplicate_phone)
        } else {
            setModal({
                type: 'verify-phone',
                title: 'Verify Phone',
                user,
                newNumber: phone,
                fetchUser,
                phoneNumberDetails
            })
        }
    }

    const handleVerification = async () => {
        let errorMessage = "";

        if (!email && email.length === 0) {
            errorMessage = MESSAGES.edit_personal_profile.errors.required.email;
        } else if (email) {
            const validate_email = await validateEmail(email);
            if (!validate_email) {
                errorMessage = MESSAGES.edit_personal_profile.errors.validation.email
            } else {
                const { user: validateByEmail } = await client.request(VALIDATE_EMAIL, {
                    email: email?.trim(),
                    id: user?.id
                })

                if (validateByEmail && validateByEmail.length) {
                    errorMessage = MESSAGES.edit_personal_profile.errors.validation.email_duplicate
                }
            }
        }

        if (errorMessage) {
            setEmailError(errorMessage)
        } else {
            setModal({
                type: 'verify-email',
                title: 'Verify Email',
                user,
                new_email: email,
                fetchUser
            })
        }
    }

    async function validateEmail(email) {
        if (firebase.auth().currentUser.email === email) {
            return true
        }
        const emailRegex = new RegExp(EMAIL_REGEX)

        if (!emailRegex.test(email)) {
            return false
        }
        else return true
    }



    const validateForm = async () => {
        let formErrors = {}
        let allFormErrors = {}

        if (!firstName || (firstName && firstName.trim() === '') || firstName.length === 0) {
            formErrors.firstName = MESSAGES.edit_personal_profile.errors.required.first_name
        }
        if (!lastName || (lastName && lastName.trim() === '') || lastName.length === 0) {
            formErrors.lastName = MESSAGES.edit_personal_profile.errors.required.last_name
        }
        if (!userName || (userName && userName.trim() === '') || userName.length === 0) {
            formErrors.userName = MESSAGES.edit_personal_profile.errors.required.username
        } else if (userName !== user.username && !formErrors?.userName) {
            const duplicateUsername = await client.request(VALIDATE_USERNAME, {
                username: userName,
                id: user.id,
            })
            if (duplicateUsername && duplicateUsername.user.length > 0) {
                formErrors.userName = MESSAGES.edit_personal_profile.errors.validation.username
            }
        }
        if (!gender || gender.length === 0) {
            formErrors.gender = MESSAGES.edit_personal_profile.errors.required.gender
        }
        if (!birthYear || birthYear.length === 0) {
            formErrors.birthYear = MESSAGES.edit_personal_profile.errors.required.birthYear
        }
        if ((!aboutYourself || aboutYourself.trim() === "")) {
            formErrors.aboutYourself = MESSAGES.edit_personal_profile.errors.validation.mandatory_field
        }
        if (!golfAssociation || golfAssociation.length === 0) {
            formErrors.golfAssociation = MESSAGES.edit_personal_profile.errors.required.golfAssociation
        }
        if (!ghin || ghin.length === 0) {
            formErrors.ghin = MESSAGES.edit_personal_profile.errors.required.ghin
        }
        if (!pace || pace.length === 0) {
            formErrors.pace = MESSAGES.edit_personal_profile.errors.required.pace
        }
        if (!golfIndex || golfIndex.length === 0) {
            formErrors.golfIndex = MESSAGES.edit_personal_profile.errors.required.golfIndex
        }
        if (!englishFluency || englishFluency.length === 0) {
            formErrors.englishFluency = MESSAGES.edit_personal_profile.errors.required.englishFluency
        }
        else {
            allFormErrors = { ...formErrors }
        }
        setFormErrors(allFormErrors)
        return (
            Object.values(allFormErrors).filter(
                (value) => Object.values(value).length > 0
            ).length === 0
        )
    }

    const handleForm = async () => {
        const isValid = await validateForm()
        if (isValid && !personalProfileError) {
            const changes = {
                first_name: (firstName.charAt(0).toUpperCase() + firstName.slice(1)).trim(),
                last_name: (lastName.charAt(0).toUpperCase() + lastName.slice(1)).trim(),
                username: userName.trim(),
                gender: gender,
                birthYear: birthYear,
                facebook: (facebook && facebook.trim() !== '') ? facebookInitialUrl + facebook : '',
                linkedin: (linkedin && linkedin.trim() !== '') ? linkedinInitialUrl + linkedin : '',
                about_yourself: aboutYourself,
                association_id: golfAssociation.id,
                user_association_number: ghin,
                pace: pace,
                handicap: golfIndex,
                englishFluency: englishFluency,
                playAsCouple: couple
            }

            client
                .request(UPDATE_USER, { id: user.id, changes })
                .then(() => {
                    setLoading(false)
                    fetchUser()
                    cancel()
                })
                .catch(console.log)


        }

    }

    function getYearOptions() {
        const year = new Date().getFullYear()
        return Array(100)
            .fill('')
            .map((_, i) => {
                return year - i
            })
    }

    return (
        <>
            <div className="flex flex-col mb-xl w-full">
                <div className='flex w-full mb-md'>
                    <div className="text-lg text-black flex items-end flex-1" style={{
                        fontSize: 26
                    }}>
                        Edit Profile
                    </div>

                    <div
                        className="bg-lightestgray cursor-pointer text-gray px-lg py-sm flex-center"
                        onClick={() => cancel()}
                        style={{
                            borderRadius: 8,
                            width: 160,
                            height: 45,
                            fontSize: 14
                        }}
                    >
                        Cancel
                    </div>

                    <div className='cursor-pointer flex flex-center bg-darkteal py-sm px-md mr-lg rounded-lg text-white ml-sm' style={{ width: 138, height: 42, fontSize: 14 }}
                        onClick={handleForm}
                    >
                        <button>
                            Save
                        </button>
                    </div>
                </div>
                <div className="flex flex-col">
                    <div className='flex flex-col lg:flex-row gap-4'>
                        <div className='flex flex-1 flex-col p-md bg-white rounded-lg'>
                            <div className='text-18 font-medium mb-md'>Personal Information</div>
                            <div className='mb-md'>
                                <TextInput
                                    title={'First Name'}
                                    disableError={!formErrors?.firstName}
                                    error={formErrors?.firstName}
                                    placeholder={"Enter First Name"}
                                    value={firstName}
                                    className='text-16 font-normal '
                                    update={(val) => {
                                        setFirstName(val);
                                        setFormErrors({ ...formErrors, firstName: "" })
                                    }} />
                            </div>
                            <div className='mb-md'>

                                <TextInput
                                    title={'Last Name'}
                                    disableError={!formErrors?.lastName}
                                    error={formErrors?.lastName}
                                    placeholder={"Enter Last Name"}
                                    value={lastName}
                                    className='text-16 font-normal'
                                    update={(val) => {
                                        setLastName(val)
                                        setFormErrors({ ...formErrors, lastName: "" })

                                    }} />
                            </div>
                            <div className='mb-md'>
                                <TextInput
                                    title={'Username'}
                                    disableError={!formErrors?.userName}
                                    error={formErrors?.userName}
                                    placeholder={"Enter Username"}
                                    value={userName}
                                    className='text-16 font-normal'
                                    update={(val) => {
                                        setUserName(val)
                                        setFormErrors({ ...formErrors, userName: "" })
                                    }} />
                            </div>
                            <div className='mb-md w-full relative'>
                                <TextInput
                                    // readOnly={true}
                                    disableError={!emailError}
                                    title="Email ID"
                                    value={email}
                                    className='text-16 font-normal'
                                    update={(value) => {
                                        setEmailError()
                                        setEmail(value)
                                    }}
                                    error={emailError}
                                    globalDropdownState={{
                                        globalDropdownVisible,
                                        setGlobalDropdownVisible,
                                    }}
                                    verified={verfiedEmail}
                                />
                                {newEmail && <div onClick={
                                    handleVerification
                                } className='cursor-pointer text-green text-sm mt-2 hover:underline'
                                    style={{
                                        position: "absolute",
                                        right: 25,
                                        top: 7
                                    }}
                                >Verify</div>}
                            </div>
                            <div className='mb-md' style={{ zIndex: 101 }}>
                                <Select
                                    title="Year of Birth"
                                    disableError={!formErrors?.birthYear}
                                    value={birthYear}
                                    addClubClasses={'text-14'}
                                    titleWeight='normal'
                                    fontWeignt='normal'
                                    marginTop='0'
                                    update={(value) => {
                                        setBirthyear(value)
                                    }}
                                    error={formErrors?.birthYear}
                                    defaultInitial={true}
                                    options={getYearOptions()}
                                    globalDropdownState={{
                                        globalDropdownVisible,
                                        setGlobalDropdownVisible,
                                    }}
                                />
                            </div>
                            <div className='mb-md w-full relative' style={{ zIndex: 100 }}>
                                <PhoneInput
                                    country={phoneNumberDetails?.countryCode}
                                    disableError={!formErrors?.phone}
                                    title={"Phone"}
                                    value={phone}
                                    addClubClasses={'text-14 font-normal'}
                                    fontSizeClass="text-16"
                                    update={(value, country) => {
                                        setPhone(value)
                                        setPhoneNumberDetails(country)
                                        if (formErrors.phone || duplicatePhoneError) {
                                            setDuplicatePhoneError('')
                                            setFormErrors({ ...formErrors, phone: "" })
                                        }
                                    }
                                    }
                                    error={duplicatePhoneError ? duplicatePhoneError : formErrors?.phone}
                                    verified={verfiedPhone}
                                />
                                {newPhone && <div onClick={() => {
                                    validatePhone()
                                }} className='cursor-pointer text-green text-sm mt-2 hover:underline'
                                    style={{
                                        position: "absolute",
                                        right: 25,
                                        top: 7
                                    }}
                                >Verify</div>}
                            </div>
                            <div className='mb-md'>
                                <div className='text-gray mb-sm'>Gender</div>
                                <div className={`flex text-14 gap-4`}>
                                    <Checkbox2
                                        label="Female"
                                        value={gender === FEMALE}
                                        update={(value) => {
                                            setGender(FEMALE)
                                        }}
                                        roundedButton={true}
                                    />

                                    <Checkbox2
                                        label="Male"
                                        value={gender === MALE}
                                        update={(value) => {
                                            setGender(MALE)
                                        }}
                                        roundedButton={true}
                                    />
                                </div>
                            </div>
                            <div className='mb-md flex w-full '>
                                <TextInput
                                    title="Facebook"
                                    value={facebookInitialUrl}
                                    disableError={!formErrors?.facebook}
                                    update={(val) => {
                                        null
                                    }}
                                    addClubClasses={'text-14 font-normal'}
                                    alternateWidth={"205px"}
                                />
                                <TextInput
                                    noErrorMessage={!formErrors?.personalProfileError}
                                    disableError={!formErrors?.facebook}
                                    error={formErrors?.facebook}
                                    value={facebook}
                                    addClubClasses={'text-14 font-normal'}
                                    update={(value) => {
                                        setFacebook(value.toLowerCase())
                                        if (formErrors?.facebook) {
                                            setFormErrors({
                                                ...formErrors,
                                                facebook: "",
                                                linkedin: "",
                                                personalProfileError: ""
                                            })
                                        }
                                    }
                                    }
                                    alternateWidth={"51%"}
                                    type="link"
                                    lineHeight={'47px'}
                                    hideTitle={'pt-[24px]'}
                                />
                            </div>
                            <div className='mb-md flex w-full'>
                                <TextInput
                                    value={linkedinInitialUrl}
                                    update={(val) => {
                                        null
                                    }}
                                    disableError={!formErrors.linkedin}
                                    addClubClasses={'text-14 font-normal'}
                                    title={'Linkedin'}
                                    alternateWidth={"214px"}
                                />

                                <TextInput
                                    noErrorMessage={!formErrors?.personalProfileError}
                                    disableError={!formErrors.linkedin}
                                    error={formErrors?.linkedin}
                                    value={linkedin}
                                    addClubClasses={'text-14 font-normal'}
                                    update={(value) => {
                                        setLinkedin(value.toLowerCase())
                                        if (formErrors?.linkedin) {
                                            setFormErrors({
                                                ...formErrors,
                                                facebook: "",
                                                linkedin: "",
                                                personalProfileError: ""
                                            })
                                        }
                                    }
                                    }
                                    type="link"
                                    lineHeight={'47px'}
                                    hideTitle={'pt-[24px]'}
                                    alternateWidth={"49%"}
                                />
                            </div>
                            <div className='w-full flex flex-col'>
                                <TextArea
                                    title="About Yourself"
                                    disableError={!formErrors?.aboutYourself}
                                    error={formErrors?.aboutYourself}
                                    value={aboutYourself}
                                    titleSize='14'
                                    fontSize='16'
                                    className='mt-0'
                                    update={(value) => {
                                        setAboutYourself(value)

                                        if (formErrors?.aboutYourself) {
                                            setFormErrors({
                                                ...formErrors,
                                                aboutYourself: ""
                                            })
                                        }
                                    }
                                    }
                                    maxLength={300}
                                />
                                <div className='flex justify-between py-sm'>
                                    <div className='text-12' style={{ color: 'red' }}>{formErrors?.personalProfileError ? formErrors?.personalProfileError : formErrors.aboutYourself}</div>

                                    <div className='text-gray text-12 text-right'>{ABOUT_YOURSELF_MAX_LENGTH - (aboutYourself ? aboutYourself.length : 0)}</div>
                                </div>
                            </div>
                        </div>

                        <div className='h-fit p-md bg-white rounded-lg'>
                            <div className='text-18 font-medium'>Golfer Profile</div>
                            <div className='mb-md'>
                                <Select
                                    value={golfAssociation}
                                    type="associations"
                                    placeholder="Select Your Golf Association"
                                    addClubClasses={'text-14 font-normal'}
                                    fontWeignt='normal'
                                    update={(value) => {
                                        setGolfAssociation(value)
                                    }}
                                    title="Golf Association"
                                    globalDropdownState={{
                                        globalDropdownVisible,
                                        setGlobalDropdownVisible,
                                    }}
                                    disableError={!formErrors?.golfAssociation}
                                />
                            </div>
                            <TextInput
                                title="GHIN/Association Number"
                                error={formErrors?.ghin}
                                value={ghin}
                                className='text-16 font-normal'
                                type="number"
                                placeholder=""
                                update={(value) => {
                                    setGhin(value)
                                    setFormErrors({ ...formErrors, ghin: "" })
                                }}
                                disableError={!formErrors?.ghin}
                            />
                            <Select
                                title={"Pace of Play"}
                                value={pace}
                                addClubClasses={'text-14 font-normal'}
                                fontWeignt='normal'
                                disableError={!formErrors?.pace}
                                error={formErrors?.pace}
                                update={(value) =>
                                    setPace(value)
                                }
                                options={[
                                    'Fast', 'Average', 'Leisure'
                                ]}
                                globalDropdownState={{
                                    globalDropdownVisible,
                                    setGlobalDropdownVisible,
                                }}
                            />
                            <Select
                                title={"Golf Index"}
                                value={golfIndex}
                                addClubClasses={'text-14 font-normal'}
                                fontWeignt='normal'
                                disableError={!formErrors?.golfIndex}
                                error={formErrors?.golfIndex}
                                update={(value) =>
                                    setGolfIndex(value)
                                }
                                options={[
                                    '< 5', '5-10', '> 10'
                                ]}
                                globalDropdownState={{
                                    globalDropdownVisible,
                                    setGlobalDropdownVisible,
                                }}
                            />
                            <Select
                                title={"English Fluency"} it
                                value={englishFluency}
                                addClubClasses={'text-14 font-normal'}
                                fontWeignt='normal'
                                disableError={!formErrors?.englishFluency}
                                error={formErrors?.englishFluency}
                                update={(value) =>
                                    setEnglishFluency(value)
                                }
                                options={[
                                    'native', 'fluent', 'understandable', 'basic'
                                ]}
                                globalDropdownState={{
                                    globalDropdownVisible,
                                    setGlobalDropdownVisible,
                                }}
                            />
                            <Select
                                title={'Play as Couple'}
                                value={couple}
                                addClubClasses={'text-14 font-normal'}
                                fontWeignt='normal'
                                disableError={!formErrors?.couple}
                                error={formErrors?.couple}
                                update={(value) =>
                                    setCouple(value)
                                }
                                options={[
                                    'yes', 'no'
                                ]}
                                globalDropdownState={{
                                    globalDropdownVisible,
                                    setGlobalDropdownVisible,
                                }}
                            />
                        </div>
                    </div>
                </div>
            </div>
        </>

    )
}