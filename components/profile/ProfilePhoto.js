import React, { useState, useRef, useEffect, useContext } from 'react'
import firebase from 'firebase/compat/app'
import 'firebase/compat/auth'
import 'firebase/compat/storage'
import { ThreeDots } from 'react-loader-spinner'
import { GraphQLClient } from 'graphql-request'
import { UserContext } from '../../pages/_app'
import NameInitials from '../common/NameInitials'
import { uploadFile } from '../../utils/upload'
import compressImage from '../../utils/helper/compressImage'
import { ModalContext } from '../../context/ModalContext'

import 'firebase/compat/auth'
import 'firebase/compat/storage'


const UPDATE_USER_PHOTO = `
mutation updateUserPhoto($id: uuid!, $photoURL: String) {
    update_user(where: {id: {_eq: $id}}, _set: {profilePhoto: $photoURL}) {
      affected_rows
    }
}
`

export default function ProfilePhoto({
    initialPhoto,
    source,
    height = 110,
    width = 110,
    radius = "lg"
}) {
    const uploadRef = useRef()
    const { user, setUser, token } = useContext(UserContext)
    const [photo, setPhoto] = useState()
    const [photoURL, setPhotoURL] = useState(initialPhoto)
    const [loading, setLoading] = useState(false)
    const [compressedPhoto, setCompressedPhoto] = useState()
    const [base64, setBase64] = useState()
    const { modal, setModal } = useContext(ModalContext)
    const [dp, setDp] = useState()
    const [uploadImage, setUploadImage] = useState()
    const [isPhotoLoading, setIsPhotoLoading] = useState(false)
    const [photoError, setPhotoError] = useState()

    useEffect(() => {
        if (initialPhoto) {
            setDp(initialPhoto)
        }
    }, [initialPhoto])

    useEffect(() => {
        if (photo) {
            const reader = new FileReader()
            reader.addEventListener('load', () =>
                setBase64(reader.result?.toString() || ''),
            )
            reader.readAsDataURL(photo)
        }
    }, [photo])

    useEffect(() => {
        if (base64) {
            //Setting base64 data in CreateGroupModal as we cannot open 2 popups at a time.
            setModal({
                type: 'crop-image',
                image: base64,
                width: 343,
                setDp,
                setUploadImage
            })

        }
    }, [base64])

    useEffect(() => {
        if (uploadImage) {
            initUpload()
            setBase64()
        }
    }, [uploadImage])

    const initUpload = async () => {
        setIsPhotoLoading(true)

        //Converting blob to file for compression
        const newFileFromBlob = new File([uploadImage], uploadImage.name, { type: uploadImage.type });
        const compressedFile = await compressImage({ file: newFileFromBlob })
        const firebaseFileURL = await uploadFile({ file: compressedFile, path: 'profilephotos' })
        setIsPhotoLoading(false)
        const client = new GraphQLClient(
            process.env.CONFIG.graphQLEndpoint,
            {
                headers: {
                    ['Authorization']: `Bearer ${token}`,
                },
            }
        )
        client
            .request(UPDATE_USER_PHOTO, {
                id: user?.id,
                photoURL: firebaseFileURL,  
            })
            .then(() => {
                setUser({ ...user, profilePhoto: firebaseFileURL })
                setLoading(false)
            })
    }

    return (
        <div className={`relative rounded-${radius}`}>
            {dp ? (
                <div className={` rounded-${radius}`}
                    style={{
                        height,
                        width,
                        backgroundImage: `url("${dp}")`,
                        backgroundPosition: 'center',
                        backgroundSize: 'cover',
                    }}>
                    {loading && (
                        <div
                            className="flex-center w-full h-full"
                            style={{
                                backgroundColor: 'rgba(0,0,0,0.3)',
                            }}>
                            <ThreeDots
                                visible={true}
                                height="25"
                                width="25"
                                color={"#FFFFFF"}
                                radius="9"
                                ariaLabel="three-dots-loading"
                                wrapperStyle={{}}
                                wrapperClass=""
                            />
                        </div>
                    )}
                </div>
            ) : (
                <div className={`rounded-${radius}`}
                    style={{
                        height,
                        width,

                    }}>
                    <NameInitials
                        height={110}
                        width={110}
                        user={user}
                        fontSize={50}
                        source={source}
                    />
                </div>
            )}

            {source !== "profile-info" &&
                <>
                    <div
                        className="flex-center rounded bg-white absolute cursor-pointer shadow-lg"
                        style={{
                            width: 30,
                            height: 30,
                            top: source === 'profile' ? 95 : -15,
                            right: -15,
                        }}>
                        <img
                            onClick={() => uploadRef.current.click()}
                            src="/images/edit.svg"
                            width={15}
                            height={15}
                        />
                    </div>
                    <input
                        onChange={({ target: { files } }) => setPhoto(files[0])}
                        ref={uploadRef}
                        type="file"
                        accept=".png,.jpg,.jpeg"
                        style={{ display: 'none' }}
                    />
                </>
            }
        </div>
    )
}
