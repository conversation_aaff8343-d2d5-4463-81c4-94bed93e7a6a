import React, { useState, useContext } from 'react'

import Checkbox from '../chat-v2/CheckBox'
import { ThreeDots } from 'react-loader-spinner'
import CheckboxMultiselect from '../common/CheckboxMultiselect'
import useCheckDeviceScreen from '../../hooks/useCheckDeviceScreen'
import { UserContext } from '../../pages/_app'
import DateRangeSmall from '../common/DateRangeSmall'
import moment from 'moment'
import MAP_FILTER from '../../constants/filterConstants'

const {
    TIER: { FERN, SAGE, MOSS, OLIVE },
    TG_COMMUNITY: { FRIEND_CONTACT, TG_GROUP_MEMBER, OFFER, FAVORITE, PLAYED, COUPLE, FEMALE },
    CLUB_PERCENTAGE_ACCEPTANCE: { ALL, MORE_THAN_25 },
    CLUB_MEMBER_COUNT: { ALL_MEMBERS, MORE_THAN_10 }
} = MAP_FILTER


export const OfferFilter = ({
    loading,
    allMyTgGroups,
    totalGroupsCount,
    page,
    pageSize,
    setListLoading,
    listLoading,
    fetchGroupsList,
    setPage,
    setRefreshMap,
    filters,
    setFilters,
    setCurrentView,
    filterCount,
    isFiltersApplied
}) => {


    const [groups, setGroups] = useState([]);
    const { user } = useContext(UserContext)
    const { isDesktop, isWideScreen, isMobile, isTablet } = useCheckDeviceScreen()
    const [clearDate, setClearDate] = useState(0)

    const handleSelection = (group) => {
        const temp = groups?.map(group => group?.id)
        if (temp?.includes(group?.id)) {
            const updated = groups.filter((t) => t.id !== group?.id)
            if (updated?.length === 0) {
                setFilters({ ...filters, all_groups: true })
            }
            setGroups(updated)

        } else {
            const updated = [...groups, group]
            if (updated?.length === allMyTgGroups?.length) {
                setFilters({ ...filters, all_groups: true })
                setGroups([])
            } else {
                setFilters({ ...filters, all_groups: false })
                setGroups(updated)
            }
        }
    }

    const handleClearAll = () => {
        setFilters({
            fern: false,
            sage: false,
            moss: false,
            olive: false,
            friend_contact: false,
            tg_group_member: false,
            offer: false,
            favorite: false,
            played: false,
            couple: false,
            acceptance_all: false,
            acceptance_25: false,
            member_count_all: false,
            member_count_10: false,
            all_groups: false,
            startDate: null,
            endDate: null
        })
        setClearDate(prev => prev + 1)

    }

    return (
        <div className={`flex flex-col h-full w-full md:w-[31%] lg:w-[20%] pl-md pr-md lg:pt-md md:pr-xs md:pl-md`}>
            <div className={`flex w-full justify-between px-sm md:px-0 pt-sm lg:pt-0 pb-sm`}>
                <div className="text-18 font-medium flex"><span onClick={() => {
                    const view = JSON.parse(sessionStorage.getItem('MOBILE_VIEW'))?.currentView
                    setCurrentView(view)
                    sessionStorage.removeItem('MOBILE_VIEW')
                }} className='pt-sm mr-sm '>
                    {(isMobile) &&
                        <img width={12} src="/svg/CrossIconBlack.svg" />
                    }
                </span>Filters</div>
                <div className='flex items-center '>
                    {isFiltersApplied &&
                        <div className="font-medium ml-sm cursor-pointer" onClick={handleClearAll}>Clear All</div>
                    }
                </div>
            </div>

            <div className='flex flex-col h-full overflow-y-scroll md:pt-md lg:pt-0'>
                <div className='text-16 bg-white rounded-lg p-12'>
                    <DateRangeSmall clearDate={clearDate} update={(value) => {
                        if (value[0] && value[1]) {
                            setFilters({ ...filters, startDate: moment.utc(value[0]), endDate: moment.utc(value[1]) })
                        } else if (value[0] && !value[1]) {
                            setFilters({ ...filters, startDate: moment.utc(value[0]), endDate: null })
                        } else {
                            setFilters({ ...filters, startDate: null, endDate: null })
                        }
                    }} displayedMonths={1} titleSize='16' placeholder="Select Range" titleColor='black' minWidth={280} />
                </div>
                <div className='text-16 bg-white rounded-lg py-12 mt-sm'>
                    <div className='px-12 pb-sm'>Tier</div>
                    <div className='flex flex-col '>
                        <div className='px-12 hover:bg-tealTierBg cursor-pointer '>
                            <Checkbox value={filters?.fern} update={() => { setFilters({ ...filters, fern: !filters?.fern }) }} label={FERN} padding='sm' />
                        </div>
                        <div className='px-12 hover:bg-tealTierBg cursor-pointer '>
                            <Checkbox value={filters?.sage} update={() => { setFilters({ ...filters, sage: !filters?.sage }) }} label={SAGE} padding='sm' />
                        </div>
                        <div className='px-12 hover:bg-tealTierBg cursor-pointer '>
                            <Checkbox value={filters?.moss} update={() => { setFilters({ ...filters, moss: !filters?.moss }) }} label={MOSS} padding='sm' />
                        </div>
                        <div className='px-12 hover:bg-tealTierBg cursor-pointer '>
                            <Checkbox value={filters?.olive} update={() => { setFilters({ ...filters, olive: !filters?.olive }) }} label={OLIVE} padding='sm' />
                        </div>
                    </div>
                </div>

                <div className='text-16 bg-white rounded-lg py-12 mt-sm'>
                    <div className='px-12 pb-sm'>Club with My TG Community</div>
                    <div className='flex flex-col'>
                        <div className='px-12 hover:bg-tealTierBg cursor-pointer py-xs'>
                            <Checkbox value={filters?.friend_contact} update={() => { setFilters({ ...filters, friend_contact: !filters?.friend_contact }) }} label={FRIEND_CONTACT} customClasses='mr-md' />
                        </div>

                        {loading ? (
                            <ThreeDots
                                visible={true}
                                height="50"
                                width="50"
                                color={"#098089"}
                                radius="9"
                                ariaLabel="three-dots-loading"
                                wrapperStyle={{}}
                                wrapperClass=""
                            />
                        ) : (
                            <>
                                {allMyTgGroups?.length ? (
                                    <>
                                        <div className='px-12 hover:bg-tealTierBg cursor-pointer py-xs'>
                                            <Checkbox value={filters?.tg_group_member} update={() => { setFilters({ ...filters, tg_group_member: !filters?.tg_group_member }), setGroups([]) }} label={TG_GROUP_MEMBER} customClasses='mr-md' />
                                        </div>
                                        <div className={`ml-[28px] pr-sm ${!filters?.tg_group_member ? 'pointer-events-none' : ''}`}>
                                            <CheckboxMultiselect
                                                title={'Select Group'}
                                                placeholder={'All'}
                                                options={allMyTgGroups}
                                                addClubClasses={true}
                                                titleWeight={'normal'}
                                                fontWeignt={'normal'}
                                                update={handleSelection}
                                                textSize='16'
                                                groups={groups}
                                                setGroups={setGroups}
                                                value={groups}
                                                setPage={setPage}
                                                totalGroupsCount={totalGroupsCount}
                                                fetchGroupsList={fetchGroupsList}
                                                page={page}
                                                pageSize={pageSize}
                                                showAll={filters?.all_groups}
                                                allGroupsSelected={filters?.all_groups}
                                                setAllGroupsSelected={(value) => {
                                                    setFilters({ ...filters, all_groups: value })
                                                }}
                                                disableError={true}
                                                listLoading={listLoading}
                                                setListLoading={setListLoading}
                                            />
                                        </div>
                                    </>
                                ) : null}
                            </>
                        )}
                    </div>
                </div>

                {user?.playAsCouple &&
                    <div className='flex justify-between hover:bg-tealTierBg cursor-pointer items-center text-16 bg-white rounded-lg px-12 py-xs mt-sm'>
                        <Checkbox value={filters?.couple} update={() => { setFilters({ ...filters, couple: !filters?.couple }) }} label={COUPLE} padding='sm' />
                    </div>
                }
                {user?.gender !== "male" &&
                    <div className='flex justify-between hover:bg-tealTierBg cursor-pointer items-center text-16 bg-white rounded-lg px-12 py-xs mt-sm'>
                        <Checkbox value={filters?.female} update={() => { setFilters({ ...filters, female: !filters?.female }) }} label={FEMALE} padding='sm' />
                    </div>
                }
                <div className='flex justify-between hover:bg-tealTierBg cursor-pointer items-center text-16 bg-white rounded-lg px-12 py-xs mt-sm'>
                    <Checkbox value={filters?.favorite} update={() => { setFilters({ ...filters, favorite: !filters?.favorite }) }} label={FAVORITE} padding='sm' />
                </div>
            </div>
        </div>
    )
}
