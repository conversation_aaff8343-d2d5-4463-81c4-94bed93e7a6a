import React, { useContext, useEffect, useState } from 'react'
import MapLayout from '../map/MapLayout'
import { OfferFilter } from './OfferFilter'
import OffersList from './OffersList'
import useCheckDeviceScreen from '../../hooks/useCheckDeviceScreen'
import { UserContext } from '../../pages/_app'
import { MapView } from '../map/MapView'
import canCreateOfferCheck from '../../utils/map/canCreateOffer'
import fetchGroupsList from '../../utils/map/fetchGroupsList'
import { DrawerContext } from '../../context/DrawerContext'
import calculateFilterCounts from '../../utils/map/calculateFilterCounts'
import constantOptions from '../../constants/constantOptions'
import ClubAndLocationSearchBar from '../map/ClubAndLocationSearchBar'
import { MapListToggle } from '../buttons/MapListToggle'
import FilterIcon from '../icons/FilterIcon'
import CreateOfferIcon from '../icons/CreateOfferIcon'
import { ModalContext } from '../../context/ModalContext'

const { CLUB_TIERS } = constantOptions
const view = ["filter", "map", "list"]


const OfferComponent = () => {
    const { isMobile, isTablet, isDesktop, isWideScreen } = useCheckDeviceScreen()
    const { modal, setModal } = useContext(ModalContext)
    const { user, token } = useContext(UserContext)
    const [clubsLoading, setClubsLoading] = useState(false)
    const [filters, setFilters] = useState({
        startDate: null,
        endDate: null,
        fern: false,
        sage: false,
        moss: false,
        olive: false,
        friend_contact: false,
        tg_group_member: false,
        offer: false,
        favorite: false,
        played: false,
        couple: false,
        all_groups: false,
        startDate: null,
        endDate: null
    })
    const [filterApplied, setFilterApplied] = useState(0)
    const [expandedMap, setExpandedMap] = useState(false)
    const [offerRestricted, setOfferRestricted] = useState(false)
    const [allMyTgGroups, setAllMyTgGroups] = useState([])
    const [loading, setLoading] = useState(false)
    const [listLoading, setListLoading] = useState(false)
    const [page, setPage] = useState(1)
    const [filterActive, setFilterActive] = useState(false);
    const [totalGroupsCount, setTotalGroupsCount] = useState(0)
    const [currentView, setCurrentView] = useState(view[1])
    const [refreshMap, setRefreshMap] = useState(0)
    const [filteredClubsFromMap, setFilteredClubsFromMap] = useState([])
    const [mapObjectFromMap, setMapObjectFromMap] = useState(null);
    const [boundsFromMap, setBoundsFromMap] = useState(null);
    const [clubFilter, setClubFilter] = useState(null)
    const [locationFilter, setLocationFilter] = useState(null)
    const [originalClubs, setOriginalClubs] = useState([])
    const [filterCount, setFilterCount] = useState({})
    const { drawer, setDrawer } = useContext(DrawerContext)
    const pageSize = process.env.CONFIG.MY_GROUP_LIST_LIMIT
    const { OFFER_DETAILS } = constantOptions?.DRAWER_TYPE

    const isFiltersApplied = Object.keys(filters || {}).some(key => {
        if (key === 'startDate' || key === 'endDate') {
            return filters[key] !== null
        }
        return filters[key] === true
    })

    useEffect(() => {
        const search = window.location.search;
        const params = new URLSearchParams(search);
        const clubname = params.get('clubname');
        const offerId = params.get('offerId');

        if (offerId !== null) {
            setDrawer({
                type: OFFER_DETAILS,
                offerId,
                refresh: setRefreshMap(prev => prev + 1),
            })
        }
    }, [window.location.search])

    useEffect(() => {
        checkCanCreateOffer();

        return () => {
            setClubFilter(null)
            sessionStorage.removeItem('map-zoom')
            sessionStorage.removeItem('map-bounds')
        }

    }, []);

    useEffect(() => {
        setFilterApplied(prev => prev + 1)
    }, [filters])

    const checkCanCreateOffer = async () => {
        try {
            const result = await canCreateOfferCheck(user.id);
            if (result.canCreate) {
                setOfferRestricted(null);
            } else {
                setOfferRestricted(result.message);
            }
        } catch (err) {
            console.log('Error', err);
            setOfferRestricted('Error checking offer creation eligibility');
        }
    };

    useEffect(() => {
        fetchGroups();
    }, [refreshMap]);

    const handleMapResize = () => {
        setExpandedMap(prev => !prev)
        setRefreshMap(prev => prev + 1)
    }

    const fetchGroups = async (pageToLoad = page) => {
        const isInitialLoad = !allMyTgGroups?.length;
        const isLoadingMore = allMyTgGroups?.length > 0;

        if (isInitialLoad) {
            setLoading(true);
        }

        if (isLoadingMore) {
            setListLoading(true);
        }

        await fetchGroupsList({
            token,
            userId: user?.id,
            pageSize,
            pageToLoad,
            onSuccess: (data, loadedPage) => {
                if (loadedPage !== 1) {
                    setAllMyTgGroups((prev) => [...prev, ...data?.groups]);
                    setTotalGroupsCount(data?.totalCount);
                } else {
                    setAllMyTgGroups(data?.groups);
                    setTotalGroupsCount(data?.totalCount);
                }
                setPage(loadedPage);
            },
            onComplete: () => {
                setLoading(false);
                setListLoading(false);
            }
        });
    };
    const getMapData = (mapObject, bounds, filteredClubs, originalClubs=[]) => {
        setMapObjectFromMap(mapObject)
        setBoundsFromMap(bounds)
        setFilteredClubsFromMap(filteredClubs)
        setOriginalClubs(originalClubs || [])
    }

    useEffect(() => {
        if (originalClubs && originalClubs.length > 0) {
            const counts = calculateFilterCounts(originalClubs, CLUB_TIERS);
            setFilterCount(counts);
        }
    }, [originalClubs]);

    return (
        <div className='flex flex-col' style={{ height: 'calc(100vh - 72px)' }}>
            <MapLayout setRefreshMap={setRefreshMap}>
                {(currentView !== view[0] && (!isWideScreen && !isDesktop && !isTablet) && !drawer?.type) &&
                    <div className={`absolute ${isMobile ? 'left-[8px] top-[8px]' : 'left-[20px] top-[20px]'} ${isMobile ? 'w-[53%]' : expandedMap ? 'w-[40%]' : 'w-[80%]'}`} style={{ zIndex: 100 }}>
                        <ClubAndLocationSearchBar
                            value={clubFilter || locationFilter}
                            setClubFilter={setClubFilter}
                            setLocationFilter={setLocationFilter}
                            clubList={filteredClubsFromMap}
                            setMapView={() => {
                                if (currentView === view[2]) {
                                    setCurrentView(view[1])
                                }
                            }}
                        />
                    </div>}
                {((!isWideScreen && !isDesktop && !isTablet) && currentView !== view[0] && !drawer?.type) && <div className='absolute flex right-[10px] md:right-[26px] top-[10px]' style={{ zIndex: 100, transition: 'height 0.5s ease-in-out' }}>
                    <MapListToggle showMap={currentView === view[1]}
                        toggleMap={() => {
                            if (currentView === view[2]) {
                                setCurrentView(view[1])
                                return
                            } else {
                                setCurrentView(view[2])
                            }
                            setRefreshMap(prev => !prev)
                        }} />
                    {(!isWideScreen && !isDesktop && !isTablet) &&
                        <div className='pl-sm flex items-center'>
                            <div onClick={() => {
                                setCurrentView(view[0])
                                sessionStorage.setItem('MOBILE_VIEW', JSON.stringify({ currentView }))
                            }}>
                                <FilterIcon isActive={isFiltersApplied} />
                            </div>
                            <div className='cursor-pointer px-sm'
                                onClick={() => {
                                    if (offerRestricted) {
                                        setModal({
                                            title: 'Offer Limit',
                                            message: offerRestricted,
                                            type: 'warning',
                                        })
                                    } else {
                                        setModal({
                                            title: 'Create Offer',
                                            img: {
                                                src: '/svg/offer-outline.svg',
                                                style: { height: 48, marginBottom: 10 },
                                            },
                                            width: 829,
                                            type: 'offer',
                                            refresh: setRefreshMap,
                                        })
                                    }
                                }}
                            >
                                <CreateOfferIcon />

                            </div>
                        </div>
                    }
                </div>}
                {(isWideScreen || isDesktop) ? (
                    <>
                        <OfferFilter
                            filters={filters}
                            setFilters={setFilters}
                            filterApplied={filterApplied}
                            setFilterApplied={setFilterApplied}
                            isFiltersApplied={isFiltersApplied}
                            allMyTgGroups={allMyTgGroups}
                            totalGroupsCount={totalGroupsCount}
                            page={page}
                            pageSize={pageSize}
                            setListLoading={setListLoading}
                            listLoading={listLoading}
                            fetchGroupsList={fetchGroups}
                            setPage={setPage}
                            setRefreshMap={setRefreshMap}
                            setCurrentView={setCurrentView}
                            filterCount={filterCount}
                        />
                        {(!expandedMap && drawer?.type === undefined) &&
                            <OffersList
                                filters={filters}
                                clubsLoading={clubsLoading}
                                filteredClubsFromMap={filteredClubsFromMap}
                                mapObjectFromMap={mapObjectFromMap}
                                boundsFromMap={boundsFromMap}
                                view={view}
                                currentView={currentView}
                                setCurrentView={setCurrentView}
                                allMyTgGroups={allMyTgGroups}
                                totalGroupsCount={totalGroupsCount}
                                page={page}
                                pageSize={pageSize}
                                setListLoading={setListLoading}
                                listLoading={listLoading}
                                fetchGroupsList={fetchGroups}
                                setRefreshMap={setRefreshMap}
                                originalClubs={originalClubs}
                                offerRestricted={offerRestricted}
                            />
                        }
                        <MapView
                            clubsLoading={clubsLoading}
                            setClubsLoading={setClubsLoading}
                            refreshMap={refreshMap}
                            setRefreshMap={setRefreshMap}
                            expandedMap={expandedMap}
                            handleMapResize={handleMapResize}
                            getMapData={getMapData}
                            view={view}
                            currentView={currentView}
                            setCurrentView={setCurrentView}
                            filters={filters}
                            setFilters={setFilters}
                            filterActive={filterActive}
                            setFilterActive={setFilterActive}
                            filterApplied={filterApplied}
                            setFilterApplied={setFilterApplied}
                            groups={allMyTgGroups}
                            setGroups={setAllMyTgGroups}
                            allGroupsSelected={filters?.all_groups}
                            setAllGroupsSelected={(value) => {
                                setFilters({ ...filters, all_groups: value })
                            }}
                            clubFilter={clubFilter}
                            setClubFilter={setClubFilter}
                            locationFilter={locationFilter}
                            setLocationFilter={setLocationFilter}
                            offerRestricted={offerRestricted}
                        />
                    </>
                ) : (
                    <>
                        {((isMobile && currentView === view[0]) || (isTablet && drawer?.type === undefined)) &&
                            <OfferFilter
                                currentView={currentView}
                                view={view}
                                page={page}
                                pageSize={pageSize}
                                loading={loading}
                                allMyTgGroups={allMyTgGroups}
                                totalGroupsCount={totalGroupsCount}
                                setListLoading={setListLoading}
                                listLoading={listLoading}
                                fetchGroupsList={() => { setRefreshMap(prev => prev + 1) }}
                                setPage={setPage}
                                setRefreshMap={setRefreshMap}
                                filters={filters}
                                setFilters={setFilters}
                                filterApplied={filterApplied}
                                setFilterApplied={setFilterApplied}
                                filterActive={filterActive}
                                setFilterActive={setFilterActive}
                                setCurrentView={setCurrentView}
                                filterCount={filterCount}
                                isFiltersApplied={isFiltersApplied}
                            />}

                        {drawer?.type === undefined && <>
                            {((isMobile && currentView === view[1]) || isTablet) &&
                                currentView === view[1] ?
                                <MapView
                                    clubsLoading={clubsLoading}
                                    setClubsLoading={setClubsLoading}
                                    refreshMap={refreshMap}
                                    setRefreshMap={setRefreshMap}
                                    expandedMap={expandedMap}
                                    handleMapResize={handleMapResize}
                                    getMapData={getMapData}
                                    view={view}
                                    currentView={currentView}
                                    setCurrentView={setCurrentView}
                                    filters={filters}
                                    setFilters={setFilters}
                                    filterActive={filterActive}
                                    setFilterActive={setFilterActive}
                                    filterApplied={filterApplied}
                                    setFilterApplied={setFilterApplied}
                                    groups={allMyTgGroups}
                                    setGroups={setAllMyTgGroups}
                                    allGroupsSelected={filters?.all_groups}
                                    setAllGroupsSelected={(value) => {
                                        setFilters({ ...filters, all_groups: value })
                                    }}
                                    clubFilter={clubFilter}
                                    setClubFilter={setClubFilter}
                                    locationFilter={locationFilter}
                                    setLocationFilter={setLocationFilter}
                                    offerRestricted={offerRestricted}
                                /> : <>
                                    {
                                        isMobile && currentView !== view[0] &&
                                        <OffersList
                                            offerRestricted={offerRestricted}
                                            filters={filters}
                                            clubsLoading={clubsLoading}
                                            filteredClubsFromMap={filteredClubsFromMap}
                                            mapObjectFromMap={mapObjectFromMap}
                                            boundsFromMap={boundsFromMap}
                                            view={view}
                                            currentView={currentView}
                                            setCurrentView={setCurrentView}
                                            setRefreshMap={setRefreshMap}
                                            allMyTgGroups={allMyTgGroups}
                                            totalGroupsCount={totalGroupsCount}
                                            page={page}
                                            pageSize={pageSize}
                                            setListLoading={setListLoading}
                                            listLoading={listLoading}
                                            fetchGroupsList={fetchGroups}
                                            originalClubs={originalClubs}
                                        />
                                    }
                                </>
                            }
                        </>}
                    </>
                )}
            </MapLayout>
        </div>
    )
}

export default OfferComponent
