import React, { useContext } from 'react'
import NameInitials from '../common/NameInitials'
import { ModalContext } from '../../context/ModalContext'
import Moment from 'moment'
import constantOptions from '../../constants/constantOptions'
import { useRouter } from 'next/router'
import { CustomStreamContext } from '../../context/CustomStreamContext'
import { DrawerContext } from '../../context/DrawerContext'
import { UserContext } from '../../pages/_app'
import canUserSeeProfileDetails from '../../utils/helper/canUserSeeProfileDetails'
import dateFormatter from '../../utils/helper/dateFormatter'


const ChatOfferCard = ({ creator, refresh, selectedChannelId, isFriend }) => {
    const { setModal } = useContext(ModalContext)
    const router = useRouter()
    const { setOtherUserId, setSelectedChannelId } = useContext(CustomStreamContext)
    const { drawer, setDrawer } = useContext(DrawerContext)
    const { user } = useContext(UserContext)
    const canSeeProfile = canUserSeeProfileDetails(user?.id, creator?.userId)
    const { EVENTS_AND_OFFERS, PROFILE } = constantOptions?.DRAWER_TYPE

    return (
        <div className='shadow-lg rounded-lg p-10px'>
            <div className='flex justify-between'>
                <div className='flex items-center cursor-pointer'
                    onClick={() => {
                        if (canSeeProfile) {
                            setOtherUserId(creator?.userId)
                            setDrawer({
                                type: PROFILE,
                                source: "chat",
                                customBackClickHandler: () => {
                                    setSelectedChannelId(drawer?.channel?.id)
                                    setDrawer({
                                        type: EVENTS_AND_OFFERS, source: "chat", channel: drawer?.channel
                                    })
                                }
                            })
                        }
                    }}
                >
                    {creator?.profilePhoto ? (
                        <div className='rounded-full' style={{
                            height: 32,
                            width: 32,
                            backgroundPosition: "center",
                            backgroundRepeat: "no-repear",
                            backgroundSize: "cover",
                            backgroundImage: `url(${creator?.profilePhoto})`
                        }}></div>
                    ) : (
                        <NameInitials rounded={'full'} height={32} width={32} user={isFriend ? { first_name: creator?.firstName, last_name: creator?.lastName } : { first_name: creator?.userName || creator?.username, last_name: creator?.userName || creator?.username }} />
                    )}
                    <div className='text-16 font-medium ml-md'>{isFriend ? creator?.firstName + ' ' + creator?.lastName : creator?.userName || creator?.username}</div>
                </div>
                <div className='text-12 text-gray'>
                    {`${creator?.offers?.length ? `${creator?.offers?.length} Offer` : ''}${creator?.offers?.length > 1 ? "s" : ''} ${(creator?.offers?.length && creator?.events?.length) ? "&" : ''} ${creator?.events?.length ? `${creator?.events?.length} Event` : ''}${creator?.events?.length > 1 ? "s" : ''}`}
                </div>
            </div>
            {creator?.offers?.length ? (
                <div className='flex mt-sm'>
                    <span className='flex-center mr-xs rounded-full bg-lighttealbackground2' style={{ height: 28, minWidth: 28 }}><img width={18} src='/svg/OfferDarkTeal.svg' /></span>
                    <div className='overflow-x-scroll flex'>
                        {creator?.offers?.map((item) => {
                            return (
                                <div key={item?.offerId} className='border-2 border-lightestgray rounded-lg p-sm min-w-fit cursor-pointer mr-sm'
                                    onClick={() => {
                                        setModal({
                                            type: "offer-details",
                                            width: 768,
                                            offerId: item?.offerId,
                                            refresh: refresh
                                        })
                                    }}
                                >
                                    <span className='font-medium'>Offer
                                        <span className='underline text-darkteal ml-xs font-normal cursor-pointer'
                                        >#{item?.offerId}</span></span>
                                    <div>{item?.clubName}</div>
                                </div>
                            )
                        })}
                    </div>
                </div>
            ) : (
                null
            )}

            {creator?.events?.length ? (
                <div className='flex mt-sm'>
                    <span className='flex-center mr-xs rounded-full bg-lighttealbackground2' style={{ height: 28, minWidth: 28 }}><img width={18} src='/svg/EventDarkTeal.svg' /></span>
                    <div className='overflow-x-scroll flex'>
                        {creator?.events?.map((item) => {
                            return (
                                <div className='bg-lightestgray rounded-lg p-sm min-w-fit cursor-pointer mr-sm'
                                    key={item?.offerId}
                                    onClick={() => {
                                        router.push({
                                            pathname: "/dashboard/events",
                                            search: `type=event&id=${item?.id}`
                                        })
                                        window?.sessionStorage.setItem("MY_TG_GROUP_CHANNEL_ID_OFFER_EVENT_PANEL", selectedChannelId);
                                        window?.localStorage.setItem("MY_TG_GROUP_CHANNEL_ID", JSON.stringify({ channelId: selectedChannelId }));
                                    }}
                                >
                                    <span className=''>
                                        <span className=' text-darkteal font-normal cursor-pointer'
                                        >{item?.title}</span></span>
                                    <div className='text-gray'>{dateFormatter(item.startDate, item.endDate)}</div>
                                </div>
                                // </Link>

                            )
                        })}
                    </div>
                </div>
            ) : (
                null
            )}
        </div>
    )
}

export default ChatOfferCard