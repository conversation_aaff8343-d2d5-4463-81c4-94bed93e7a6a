import React, { useEffect, useState, useContext } from 'react'
import useCheckDeviceScreen from '../../hooks/useCheckDeviceScreen'
import ClubAndLocationSearchBar from '../map/ClubAndLocationSearchBar'
import { MapListToggle } from '../buttons/MapListToggle'
import { ThreeDots } from 'react-loader-spinner'
import RequestEmptyState from '../common/RequestEmptyState'
import OfferListItem from './OfferListItem'
import mapboxgl from 'mapbox-gl'
import { UserContext } from '../../pages/_app'
import ENDPOINTS from '../../constants/endpoints'
import moment from 'moment'
import CustomButton from '../buttons/CustomButton'
import { ModalContext } from '../../context/ModalContext'
import toastNotification from '../../utils/notifications/toastNotification'
import constantOptions from '../../constants/constantOptions'
import useMaintenanceStatus from '../../hooks/useMaintenanceStatus'

const OffersList = ({
  expandedMap,
  clubFilter,
  locationFilter,
  setClubFilter,
  setLocationFilter,
  filteredClubsFromMap,
  currentView,
  setCurrentView,
  view,
  setRefreshMap,
  setClubsLoading,
  mapObjectFromMap,
  boundsFromMap,
  filters,
  offerRestricted,
  clubsLoading,
  originalClubs = []
}) => {
  const { isMobile, isTablet } = useCheckDeviceScreen()
  const [clubsInBounds, setClubsInBounds] = useState([])
  const [filteredOffers, setFilteredOffers] = useState([])
  const [showAllOffers, setShowAllOffers] = useState(1)
  const { user } = useContext(UserContext)
  const [requestRestricted, setRequestRestricted] = useState(null)
  const [showAcceptRequestPopup, setShowAcceptRequestPopup] = useState();
  const [showCreateRequestPopup, setShowCreateRequestPopup] = useState();
  const { setModal } = useContext(ModalContext)
  const { maintenanceStatus } = useMaintenanceStatus()
  const offerType = [{ type: 1, label: 'all' }, { type: 0, label: 'my offers' }]

  useEffect(() => {
    let clubsInBoundsLocal = []
    filteredClubsFromMap?.map((club) => {
      if (checkInBounds(club)) {
        clubsInBoundsLocal?.push(club)
      }
    })
    setClubsInBounds(clubsInBoundsLocal)
  }, [filteredClubsFromMap, boundsFromMap])
  // ... existing code ...
  useEffect(() => {
    if (showAllOffers !== 1) {
      const filtered = originalClubs
        ?.map(club => ({
          ...club,
          properties: {
            ...club.properties,
            offers: club.properties.offers.filter(offer => offer.user_id === user?.id)
          }
        }))
        .filter(club => club.properties.offers.length > 0); // Only keep clubs that have user's offers
      setFilteredOffers(filtered)
    } else {
      setFilteredOffers(clubsInBounds);
    }
  }, [clubsInBounds, showAllOffers, user?.id, filters?.startDate, filters?.endDate]);
  // ... existing code ...


  const checkInBounds = (club) => {
    let localbound = boundsFromMap
    if (!localbound) localbound = mapObjectFromMap.getBounds()
    if (!club?.geometry?.coordinates) return false
    const itemPos = new mapboxgl.LngLat(
      club?.geometry?.coordinates[0],
      club?.geometry?.coordinates[1]
    )
    return localbound.contains(itemPos)
  }

  useEffect(() => {
    if (user) {
      setShowAcceptRequestPopup(user?.additional_settings?.showAcceptRequestPopup);
      setShowCreateRequestPopup(user?.additional_settings?.showCreateRequestPopup);
    }
  }, [user])

  return (
    <div
      className={`flex flex-col h-full w-full md:w-[68%] lg:w-[28%] lg:pt-md`}
      style={{
        transition: 'width 500ms ease-in-out, opacity 500ms ease-in-out, transform 500ms ease-in-out',
        opacity: expandedMap ? 0 : 1,
        transform: expandedMap ? 'translateX(-100%)' : 'translateX(0)',
        position: expandedMap ? 'absolute' : 'relative',
        zIndex: expandedMap ? -1 : 1
      }}
    >
      {!isMobile &&
        <div className='flex justify-between py-sm lg:py-0 lg:pb-sm'>
          <div className='flex items-center justify-between w-full'>
            <div className="flex-1 text-18 font-medium pl-sm">Offers</div>
            <CustomButton
              width={isMobile ? 28 : 138}
              height={isMobile ? 28 : 32}
              onClick={() => {
                if (maintenanceStatus?.request) {
                  toastNotification({
                    type: constantOptions.TOAST_TYPE.ERROR,
                    message: "Request flow is currently under maintenance. Please refer Request Tab for more details"
                  })
                } else {
                  if (offerRestricted) {
                    setModal({
                      title: 'Offer Limit',
                      message: offerRestricted,
                      type: 'warning',
                    })
                  } else {
                    setModal({
                      title: 'Create Offer',
                      img: {
                        src: '/svg/offer-outline.svg',
                        style: { height: 48, marginBottom: 10 },
                      },
                      width: 829,
                      type: 'offer',
                      refresh: setRefreshMap,
                    })
                  }
                }
              }}
              text={isMobile ? "" : 'Create Offer'}
              buttonImage={isMobile ? "/svg/Create-Offer.svg" : ""}
              imageMarginBottom='0'
              imageRightMargin={0}
            />
          </div>
          {isTablet &&
            <div className='flex flex-1 justify-end px-md items-center' >
              <ClubAndLocationSearchBar
                value={clubFilter || locationFilter}
                setClubFilter={setClubFilter}
                setLocationFilter={setLocationFilter}
                clubList={filteredClubsFromMap}
                setMapView={() => {
                  if (currentView === view[2]) {
                    setCurrentView(view[1])
                  }
                }}
              />
              <MapListToggle showMap={currentView === view[1]}
                toggleMap={() => {
                  if (currentView === view[2]) {
                    setCurrentView(view[1])
                    return
                  } else {
                    setCurrentView(view[2])
                  }
                  setRefreshMap(prev => !prev)
                }} />
            </div>
          }
        </div>}
      <div className='flex pl-sm pb-sm mt-[56px] lg:mt-0 gap-2'>
        {offerType.map((item) => {
          return <div key={item.type} className={`cursor-pointer text-12 font-medium py-sm px-md rounded-full capitalize border ${showAllOffers === item.type ? 'bg-darkteal text-white' : ''}`}
            onClick={() => setShowAllOffers(item.type)}>{item.label}</div>
        })}
      </div>
      {clubsLoading ? <div className='flex-center h-full'>
        <ThreeDots
          visible={true}
          height="50"
          width="50"
          color="#098089"
          radius="9"
          ariaLabel="three-dots-loading"
          wrapperStyle={{}}
          wrapperClass=""
        />
      </div> : <>
        {!filteredOffers.length ? (
          <div
            className="h-full overflow-scroll w-full mt-xxl md:mt-0 flex-center"
            style={{
              paddingTop: '8px',
              top: 0,
              zIndex: 9,
            }}>
            <RequestEmptyState background="none" shadow={false} message={showAllOffers === 1 ? "No offers available right now." : "You don’t have any active offers right now."} />
          </div>
        ) : (
          <div
            className="h-full overflow-scroll w-full"
            style={{
              top: 0,
              zIndex: 9,
            }}>
            <div className="clubInfoWrap px-sm md:mt-0">
              {filteredOffers?.map((club) => {
                return club?.properties?.offers?.map((item, index) => (
                  <OfferListItem
                    requestRestricted={requestRestricted}
                    showCreateRequestPopup={showCreateRequestPopup}
                    showAcceptRequestPopup={showAcceptRequestPopup}
                    setShowCreateRequestPopup={setShowCreateRequestPopup}
                    setShowAcceptRequestPopup={setShowAcceptRequestPopup}
                    setRefreshMap={setRefreshMap}
                    offer={item}
                    key={index}
                    item={item}
                    club={club?.properties}
                  />
                ));
              })}
            </div>
          </div>
        )}
      </>}
    </div>)
}

export default OffersList