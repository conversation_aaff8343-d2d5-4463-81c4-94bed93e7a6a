import React, { useState, useEffect } from 'react'
import Header from '../../pages/Header'
import GlobalModal from '../../context/ModalContext'
import NotificationsDrawer from '../notifications/NotificationDrawer'
import { useRouter } from 'next/router'
import ChatImageViewGlobalModal from '../modals/ChatImageViewModal'
import ChatGalleryViewGlobalModal from '../modals/ChatGalleryViewModal'
import DrawerModal from '../../context/DrawerContext'
import useCheckDeviceScreen from '../../hooks/useCheckDeviceScreen'
import MaintenanceComponent from '../common/MaintenanceComponent'
import useMaintenanceStatus from '../../hooks/useMaintenanceStatus'

export default function UserLayout(props) {
    const router = useRouter()
    const { isMobile } = useCheckDeviceScreen()
    const { user, children, setIsRedirectedFromDeeplink } = props
    const [notificationsActive, setNotificationsActive] = useState(false)
    const [showMaintenanceMode, setShowMaintenanceMode] = useState(false)
    const { maintenanceStatus } = useMaintenanceStatus()

    useEffect(() => {

        if (maintenanceStatus) {
            const queryType = router.query.type
            let activeTab = ''
            switch (queryType || router.pathname) {
                case 'chat':
                    activeTab = 'chat'
                    break
                case 'groups':
                    activeTab = 'group'
                    break
                case 'my-friends':
                    activeTab = 'friend'
                    break
                case '/dashboard/map':
                    activeTab = 'map'
                    break
                case 'requests':
                    activeTab = 'request'
                    break
                case '/dashboard/request-chat/[request_id]/[host_id]':
                    activeTab = 'request'
                    break
                case '/dashboard/offers':
                    activeTab = 'offer'
                    break
                case 'pegboards':
                    activeTab = 'pegboards'
                    break
                case '/dashboard/events':
                    activeTab = 'event'
                    break
                case '/dashboard/benefits':
                    activeTab = 'benefit'
                    break
                default:
            }


            if (maintenanceStatus[activeTab]) {
                setShowMaintenanceMode(true)
            } else {
                setShowMaintenanceMode(false)
            }
        }

    }, [maintenanceStatus, router.query])

    useEffect(() => {
        if (router.query.deeplink === 'true') {
            delete (router.query.deeplink)
            router.push(router)
            setIsRedirectedFromDeeplink(true)
        } else {
            setIsRedirectedFromDeeplink(false)
        }
    }, [])

    return (
        <>
            <div
                className="flex-col flex-center font-ubuntu"
                style={{ width: '100%', minHeight: '100vh' }}>
                <GlobalModal user={user}>
                    <ChatImageViewGlobalModal>
                        <ChatGalleryViewGlobalModal>
                            <DrawerModal user={user}>
                                <Header
                                    role={user.role}
                                    notificationsActive={notificationsActive}
                                    setNotificationsActive={setNotificationsActive}
                                />
                                <NotificationsDrawer
                                    notificationsActive={notificationsActive}
                                    setNotificationsActive={setNotificationsActive}>
                                    {showMaintenanceMode ?
                                        <MaintenanceComponent /> : <div className="flex-1 bg-mediumgray flex flex-col overflow-y-hidden overflow-x-hidden">
                                            {children}
                                        </div>}
                                </NotificationsDrawer>
                            </DrawerModal>
                        </ChatGalleryViewGlobalModal>
                    </ChatImageViewGlobalModal>
                </GlobalModal>
            </div>
        </>
    )
}
