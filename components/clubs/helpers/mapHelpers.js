import mapConstants from "../../../constants/mapConstants";

// Load all required marker images
export const loadImages = (map) => {
  const markerNames = [
    'golf-cluster', 'blue', 'blue_contact', 'green', 'green_contact',
    'grey', 'grey_contact', 'teal', 'teal_contact', 'yellow', 'yellow_contact'
  ];

  markerNames.forEach((name) => {
    map.loadImage(`/markers/new/${name}.png`, (error, image) => {
      if (error) throw error;
      if (!map.hasImage(name)) {
        map.addImage(name, image);
      }
    });
  });
};

// Add label layer based on zoom level
export const addZoomBasedLabelLayer = (map) => {
  const addLabelLayer = () => {
    const zoom = map.getZoom();
    const layerExists = map.getLayer('golf-club-labels');

    if (zoom >= 11 && !layerExists) {
      map.addLayer({
        id: 'golf-club-labels',
        type: 'symbol',
        source: 'golf-clubs',
        filter: ['!', ['has', 'point_count']],
        layout: {
          'text-field': ['get', 'name'],
          'text-size': 14,
          'text-offset': [0, 1.2],
          'text-anchor': 'top',
        },
        paint: {
          'text-color': '#098089',
          'text-halo-color': '#fff',
          'text-halo-width': 1.5,
        },
      });
    } else if (zoom <= 12 && layerExists) {
      map.removeLayer('golf-club-labels');
    }
  };

  addLabelLayer();
  map.on('zoom', addLabelLayer);
};

// Load GeoJSON source and cluster/unclustered layers
export const loadLayer = ({ map, data, zoom }) => {
  if (!map) return;

  const sourceExists = map.getSource('golf-clubs');
  if (sourceExists) {
    sourceExists.setData(data);
    return;
  }

  map.addSource('golf-clubs', {
    type: 'geojson',
    data,
    cluster: true,
    clusterMinPoints: 10,
    clusterMaxZoom: 6,
    clusterRadius: 50,
    tolerance: 0.45,
  });

  map.addLayer({
    id: 'golf-clubs-clusters',
    type: 'symbol',
    source: 'golf-clubs',
    filter: ['has', 'point_count'],
    layout: {
      'icon-allow-overlap': true,
      'icon-ignore-placement': true,
      'symbol-avoid-edges': true,
      'icon-image': 'golf-cluster',
      'icon-size': 0.45,
      'text-field': '{point_count}',
      'text-variable-anchor': ['top'],
      'text-font': ['Roboto Bold'],
      'text-size': 10,
      'text-line-height': 2,
    },
    paint: {
      'icon-opacity': 1,
    },
  });

  const colors = [
    'grey', 'grey_contact', 'teal', 'teal_contact',
    'blue', 'blue_contact', 'green', 'green_contact',
    'yellow', 'yellow_contact'
  ];

  colors.forEach((color) => {
    map.addLayer({
      id: `golf-club-unclustered-point-${color}`,
      type: 'symbol',
      source: 'golf-clubs',
      filter: ['all', ['!has', 'point_count'], ['==', 'color', color]],
      layout: {
        'icon-image': color,
        'icon-allow-overlap': true,
        'symbol-z-order': 'auto',
        'icon-size': mapConstants.ICON_SIZE.ORIGINAL,
      },
      paint: {
        'icon-opacity': 1,
      },
    });
  });

  // Add zoom-based label layer
  addZoomBasedLabelLayer(map);
};

// Cluster click behavior
export const clusterClickEvent = ({ event, map, layer }) => {
  const features = map.queryRenderedFeatures(event.point, { layers: [layer] });
  const clusterId = features[0]?.properties?.cluster_id;

  if (!clusterId) return;

  map.getSource('golf-clubs').getClusterExpansionZoom(clusterId, (err, zoom) => {
    if (err) return;

    map.easeTo({
      center: features[0].geometry.coordinates,
      zoom: zoom + 1,
    });
  });
};

// Attach click event for clusters
export const markerClickEvents = (map) => {
  map.on('click', 'golf-clubs-clusters', (e) => {
    clusterClickEvent({ map, event: e, layer: 'golf-clubs-clusters' });
  });
};
