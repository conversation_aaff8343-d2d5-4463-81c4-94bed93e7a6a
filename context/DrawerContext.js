import React, { useContext, useEffect, useState } from 'react'
import ProfileInfo from '../components/drawers/ProfileInfo'
import MyGroupDetails from '../components/drawers/MyGroupDetails'
import MyTGSettings from '../components/drawers/MyTGSettings'
import constantOptions from '../constants/constantOptions'
import MapFiltersDrawer from '../components/drawers/MapFiltersDrawer'
import LeaderboardDrawer from '../components/drawers/LeaderboardDrawer'
import FriendsPlayedDrawer from '../components/drawers/FriendsPlayedDrawer'
import OfferEventDetails from '../components/drawers/OfferEventDetails'
import disableGlobalScroll from '../utils/helper/disableGlobalScroll'
import EventDetailsDrawer from '../components/drawers/EventDetailsDrawer'
import useMapFilters from '../hooks/useMapFilters'
import OtherTgMembersDrawer from '../components/drawers/OtherTgMembersDrawer'
import BenefitDetailsDrawer from '../components/drawers/BenefitDetailsDrawer'
import MapInfoDrawer from '../components/drawers/MapInfoDrawer'
import MapListBoundDrawer from '../components/drawers/MapListBoundDrawer'
import { MapFriendsPlayedDrawer } from '../components/drawers/MapFriendsPlayedDrawer.js'
import { MapAllMyFriendsDrawer } from '../components/drawers/MapAllMyFriendsDrawer.js'
import { TotalTgGroupMemberDrawer } from '../components/drawers/TotalTgGroupMemberDrawer.js'
import useModalActive from '../hooks/useModalActive'
import { useRouter } from 'next/router.js'
import GameInfoDrawer from '../components/drawers/GameInfoDrawer.js'
import NotificationDrawer from '../components/drawers/NotificationDrawer.js'
import ClubVisibilityDetailsDrawer from '../components/drawers/ClubVisibilityDetailsDrawer.js'
import NgvFaqDrawer from '../components/drawers/NgvFaqDrawer.js'
import MyFavClubDrawer from '../components/drawers/MyFavClubDrawer'
import FavClubSearchDrawer from '../components/drawers/FavClubSearchDrawer'
import ReferralsDrawer from '../components/drawers/ReferralsDrawer.js'
import PollDetailsDrawer from '../components/drawers/PollDetailsDrawer.js'
import ClubDetailsDrawer from '../components/drawers/ClubDetailsDrawer.js'
import OfferDetailsDrawer from '../components/drawers/OfferDetailsDrawer.js'
import AllGameReviewsDrawer from '../components/drawers/AllGameReviewsDrawer.js'
import GlobalProfileInfo from '../components/drawers/GlobalProfileInfo.js'
import AdminSendMailDrawer from '../components/drawers/AdminSendMailDrawer.js.js'
import GlobalGameReviewsDrawer from '../components/drawers/GlobalGameReviewsDrawer.js'

const {
    EVENTS_AND_OFFERS,
    CHAT_GROUP,
    GROUPS,
    PROFILE,
    MY_GROUP_DETAILS,
    GLOBAL_FRIENDS_PLAYED,
    GLOBAL_LEADERBOARD,
    GLOBAL_PROFILE_INFO,
    GLOBAL_MY_TG_SETTINGS,
    EVENT_DETAILS,
    OTHER_TG_MEMBERS,
    BENEFIT_DETAILS,
    MAP_CLUB_INFO,
    MAP_LIST_BOUND,
    MAP_TOTAL_TG_GROUP_MEMBER,
    MAP_FRIENDS_PLAYED,
    MAP_ALL_MY_FRIENDS,
    GAME_INFO,
    NOTIFICATION,
    CLUB_VISIBILITY_DETAILS,
    NGV_FAQ,
    MY_FAV_CLUB,
    FAV_CLUB_SEARCH,
    REFERRAL,
    POLL_DETAILS,
    CLUB_DETAILS,
    OFFER_DETAILS,
    ALL_GAME_REVIEWS,
    ADMIN_SEND_MAIL,
    GLOBAL_GAME_REVIEWS
} = constantOptions?.DRAWER_TYPE


const DrawerContext = React.createContext({ drawer: null, setDrawer: () => { } })
export { DrawerContext }

function Modal({ drawer, setDrawer }) {
    const [globalDropdownVisible, setGlobalDropdownVisible] = useState(false)
    const { isModalActive, setIsModalActive } = useModalActive()

    useEffect(() => {
        if (drawer) {
            window?.sessionStorage?.setItem('DRAWER_OPEN', true)
            disableGlobalScroll(Object?.entries(drawer)?.length > 0)
            setIsModalActive(true)
        } else {
            window?.sessionStorage?.removeItem('DRAWER_OPEN', false)
            disableGlobalScroll(false)
            setIsModalActive(false)
        }
        return () => {
            window?.sessionStorage?.removeItem('DRAWER_OPEN', false)
            disableGlobalScroll(false)
            setIsModalActive(false)
        }
    }, [drawer])

    function renderDrawer() {
        switch (drawer?.type) {
            case MY_GROUP_DETAILS:
                return <MyGroupDetails drawer={drawer} setDrawer={setDrawer} />
            case GLOBAL_PROFILE_INFO:
                return (
                    <GlobalProfileInfo
                        source={drawer?.origin || 'non-chat'}
                        drawer={drawer}
                        setDrawer={setDrawer}
                    />
                )
            case GLOBAL_MY_TG_SETTINGS:
                return <MyTGSettings drawer={drawer} setDrawer={setDrawer} />
            case GLOBAL_LEADERBOARD:
                return (
                    <LeaderboardDrawer drawer={drawer} setDrawer={setDrawer} />
                )
            case REFERRAL:
                return (
                    <ReferralsDrawer drawer={drawer} setDrawer={setDrawer} />
                )
            case GLOBAL_FRIENDS_PLAYED:
                return (
                    <FriendsPlayedDrawer
                        drawer={drawer}
                        setDrawer={setDrawer}
                    />
                )
            case EVENT_DETAILS:
                return (
                    <EventDetailsDrawer drawer={drawer} setDrawer={setDrawer} />
                )
            case OTHER_TG_MEMBERS:
                return (
                    <OtherTgMembersDrawer
                        drawer={drawer}
                        setDrawer={setDrawer}
                    />
                )
            case BENEFIT_DETAILS:
                return (
                    <BenefitDetailsDrawer
                        drawer={drawer}
                        setDrawer={setDrawer}
                    />
                )
            case GAME_INFO:
                return <GameInfoDrawer drawer={drawer} setDrawer={setDrawer} />
            case NOTIFICATION:
                return (
                    <NotificationDrawer drawer={drawer} setDrawer={setDrawer} />
                )
            case CLUB_VISIBILITY_DETAILS:
                return <ClubVisibilityDetailsDrawer drawer={drawer} setDrawer={setDrawer} />
            case NGV_FAQ:
                return <NgvFaqDrawer drawer={drawer} setDrawer={setDrawer} />
            case MY_FAV_CLUB:
                return <MyFavClubDrawer drawer={drawer} setDrawer={setDrawer} />
            case FAV_CLUB_SEARCH:
                return <FavClubSearchDrawer drawer={drawer} setDrawer={setDrawer} />
            case OFFER_DETAILS:
                return <OfferDetailsDrawer drawer={drawer} setDrawer={setDrawer} />
            case ADMIN_SEND_MAIL:
                return <AdminSendMailDrawer drawer={drawer} setDrawer={setDrawer} />
            case GLOBAL_GAME_REVIEWS:
                return <GlobalGameReviewsDrawer drawer={drawer} setDrawer={setDrawer} />
            default:
                break
        }
    }

    return (
        <div
            id="DrawerContext"
            className="absolute inset-0"
            style={{
                backgroundColor: 'rgba(0,0,0,0.5)',
                boxSizing: 'border-box',
                minHeight: '100vh',
                zIndex: 1000,
            }}>
            <div
                className="w-full flex justify-end h-full">
                <div
                    onClick={(e) => {
                        e.stopPropagation()
                        setGlobalDropdownVisible(false)
                    }}
                    className={`bg-white shadow flex relative w-full`}
                    style={{
                        maxWidth: drawer?.width ? drawer?.width : 500,
                    }}>
                    {renderDrawer()}
                </div>
            </div>
        </div>
    )
}

export default function DrawerModal({ children }) {
    const [drawer, setDrawer] = useState()

    //Drawer context is not available inside ModalContext so if you want to use them in any popup pass them in setModal function

    //renderChatDrawer function renders the drawer inside chat window or can be used to render a rectangular drawer anywhere in the app but you need to control its position manually
    //you don't need to pass global for rendering this

    //This effect clears the filter when user navigates in main header

    const renderChatDrawer = () => {
        switch (drawer?.type) {
            case PROFILE:
                return <ProfileInfo />
            case EVENTS_AND_OFFERS:
                return <OfferEventDetails channel={drawer?.channel} />
            case CHAT_GROUP:
                return <MyGroupDetails />
            case MAP_CLUB_INFO:
                return <MapInfoDrawer />
            case MAP_LIST_BOUND:
                return <MapListBoundDrawer />
            case MAP_TOTAL_TG_GROUP_MEMBER:
                return <TotalTgGroupMemberDrawer />
            case MAP_FRIENDS_PLAYED:
                return <MapFriendsPlayedDrawer />
            case MAP_ALL_MY_FRIENDS:
                return <MapAllMyFriendsDrawer />
            case POLL_DETAILS:
                return <PollDetailsDrawer drawer={drawer} setDrawer={setDrawer} />
            case CLUB_DETAILS:
                return <ClubDetailsDrawer drawer={drawer} setDrawer={setDrawer} />
            case OFFER_DETAILS:
                return <OfferDetailsDrawer drawer={drawer} setDrawer={setDrawer} />
            case ALL_GAME_REVIEWS:
                return <AllGameReviewsDrawer drawer={drawer} setDrawer={setDrawer} />
            case ADMIN_SEND_MAIL:
                return <AdminSendMailDrawer drawer={drawer} setDrawer={setDrawer} />

            default:
                break
        }
    }

    return (
        <DrawerContext.Provider
            value={{
                drawer,
                setDrawer,
                renderChatDrawer,
            }}>
            {children}
            {/* If global drawer is required then pass global=true in the setDrawer function */}
            {drawer && drawer?.global && (
                <Modal drawer={drawer} setDrawer={setDrawer} />
            )}
        </DrawerContext.Provider>
    )
}
